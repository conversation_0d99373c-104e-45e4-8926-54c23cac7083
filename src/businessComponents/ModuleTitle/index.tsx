import React from "react";
import styles from "./styles/index.module.less";
import classnames from "classnames";

type ModuleTitleType = {
  title: string;
  className?: string;
  wrapperClass?: string;
  children?: React.ReactNode;
  description?: string;
  id?: string;
};
const ModuleTitle: React.FC<ModuleTitleType> = ({
  id,
  title,
  className,
  wrapperClass,
  children,
  description,
}: ModuleTitleType) => {
  return (
    <div id={id} className={`${styles.wrap} ${wrapperClass || ""}`}>
      <div className={classnames(styles.moduleTitleBox, [className])}>
        <p>{title}</p>
        {description && <p className={styles.desc}>{description}</p>}
      </div>
      {children}
    </div>
  );
};

export default ModuleTitle;
