import React from "react";
import styles from "./styles/index.module.less";

type DetailAnalysisItemType = {
  title: string;
  list: Array<{
    id: string;
    name: string;
    num: number;
  }>;
};
const EvionaluatDetailAnalysisItem: React.FC<DetailAnalysisItemType> = ({
  title,
  list = [],
}: DetailAnalysisItemType) => {
  return (
    <div className={styles.testResultBox}>
      <p className={styles.right}>{title}</p>
      <ul className={styles.list}>
        {list.map((i,index) => {
          return (
            <li key={index}>
              有 {i.num} 人表现{i.name}
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default EvionaluatDetailAnalysisItem;
