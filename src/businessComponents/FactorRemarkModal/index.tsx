import React from "react";
import styles from './styles/index.module.less';
import { cls } from '~/utils/tools';
import { IFactorRemark } from '~/pages/mental-health-report/types';
import StartRemark from '../StartRemark';

interface IFactorRemarkModalProps {
  riskLevel: number;
  factorRemark: IFactorRemark;
  onClose: () => void;
}
// 因子说明弹窗
const FactorRemarkModal: React.FC<any> = (props: IFactorRemarkModalProps) => {
  const { factorRemark, onClose } = props;

  return (
    <div className={styles['factor-remark-modal-container']}>
      <div className={styles['factor-remark-modal-mask']} />
      <div className={cls([
        styles['factor-remark-modal-content'],
        // @ts-ignore
        styles[`risk-level-bg-${factorRemark?.riskLevel || 1}`]
      ])}>
        <p className={styles.title}>{factorRemark?.latitudeName}</p>
        <div className={styles['content-box']}>
          <StartRemark title="因子说明" text={factorRemark?.factorDescription} />
          <StartRemark title="高分表现" text={factorRemark?.highScorePerformance} />
          <StartRemark title="低分表现" text={factorRemark?.lowScorePerformance} />
        </div>
        <div className={styles['close-button']} onClick={() => onClose()}>我知道了</div>
      </div>
    </div>
  );
};

export default FactorRemarkModal;
