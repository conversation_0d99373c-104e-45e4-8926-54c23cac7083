.factor-remark-modal-container {
  width: 100%;
  height: 100%;

  .factor-remark-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .5);
    z-index: 8;
  }

  .factor-remark-modal-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 920px;
    border-radius: 8px;
    z-index: 9;

    &.risk-level-bg-1 {
      background-image: linear-gradient(92deg, #5CDBD3 0%, #5cdbd34d 100%);
    }

    &.risk-level-bg-2 {
      background-image: linear-gradient(92deg, #40A9FF 0%, #40a9ff4d 100%);
    }

    &.risk-level-bg-3 {
      background-image: linear-gradient(92deg, #FFA940 0%, #ffa9404d 100%);
    }

    &.risk-level-bg-4 {
      background-image: linear-gradient(92deg, #FF4D4F 0%, #ff4d4f4d 100%);
    }

    .title {
      color: rgba(0,0,0,.6);
      font-weight: bold;
      font-size: 24px;
      line-height: 48px;
      margin: 12px 32px;
    }

    .content-box {
      background-color: #fff;
      margin: 0 8px 8px;
      border-radius: 4px;
      min-height: 200px;
      max-height: 432px;
      overflow: auto;
      padding-bottom: 24px;
    }

    .close-button {
      outline: none;
      color: rgba(255,255,255,.85);
      background-color: rgba(0,0,0,.5);
      font-size: 18px;
      font-weight: bold;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: -62px;
      text-align: center;
      border-radius: 24px;
      width: 180px;
      height: 48px;
      line-height: 48px;
      cursor: pointer;
    }
  }
}