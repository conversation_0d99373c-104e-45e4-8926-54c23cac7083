.evaluationStatusBarBox {
  width: 100%;
  border: 8px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  color: #4c536f;
  font-size: 24px;
  border-radius: 8px;
  font-weight: 600;
  -webkit-print-color-adjust: exact;

  .leftTitle {
    color: #fff;
  }

  .remark {
    margin-left: 64px;
    display: inline-block;
    min-width: 100px;
    text-align: right;
  }
}

.statusSituationBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  letter-spacing: 0;
  padding: 4px 24px 0;
}

.analysisRemark {
  margin-left: 94px;
}
