import classnames from "classnames";
import styles from "./styles/index.module.less";
/**
 * 测评结果的状态条
 * 依据程度等级（序号）确定等级
 * className 自定义样式类
 * data 当前所需数据，后续改为types
 * colorList 自定义的颜色值数组，不传则会使用默认的十个
 * colorIndex 指定某个色值，可单独使用，也可和colorList配合使用
 */

interface IProps {
  className?: string;
  data: any;
  colorList?: any[];
  colorIndex?: number;
  customText?: string;
  showTip?: boolean;
}
const EvaluationStatusBar = (props: IProps) => {
  const {
    className,
    data,
    colorList,
    colorIndex,
    customText,
    showTip = false,
  } = props;
  const { name, level, score, totalScore, remark } = data;
  const showCopywriting: any = customText || {
    nameText: "维度名称",
    scoreText: "维度得分",
    totalScoreText: "维度总分",
    remarkText: "程度名称",
  };

  const evaluationResultColor = colorList || [
    "#ff5858",
    "#ff8831",
    "#ffc314",
    "#99ba00",
    "#00c322",
    "#00dfa6",
    "#00c7ff",
    "#4b80ff",
    "#9550ff",
    "#f4309e",
  ];
  const curColorValue = evaluationResultColor[colorIndex || level - 1 || 0];

  return (
    <>
      <div
        className={classnames(styles.evaluationStatusBarBox, [className])}
        style={{
          backgroundImage: `linear-gradient(270deg, #fff, ${curColorValue})`,
        }}
      >
        {/* 左侧的结果说明 */}
        <span className={styles.leftTitle}>{name}</span>
        {/* 右侧区域 */}
        <span>
          {/* 有分数时才显示分数内容 */}
          <span>
            <span style={{ color: curColorValue }}>{score}</span>
            {/* 有总分时才显示i总分 */}
            {totalScore && <span>/{totalScore}</span>}
          </span>
          {/* 最右侧的附加说明或者副标题类 */}
          <span className={styles.remark}>{remark}</span>
        </span>
      </div>
      {showTip ? (
        <div className={styles.statusSituationBox}>
          <span>{showCopywriting.nameText}</span>
          {score && (
            <p>
              <span>
                {showCopywriting.scoreText}
                {totalScore && `/${showCopywriting.totalScoreText}`}
              </span>
              <span className={styles.analysisRemark}>
                {showCopywriting.remarkText}
              </span>
            </p>
          )}
        </div>
      ) : null}
    </>
  );
};

export default EvaluationStatusBar;
