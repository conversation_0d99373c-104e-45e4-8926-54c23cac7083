import React from "react";
import styles from "./styles/index.module.less";
import classnames from "classnames";

type TestResultType = {
  title: string;
  className?: string;
  children: any;
};
const TestResult: React.FC<TestResultType> = ({
  title,
  className,
  children,
}: TestResultType) => {
  return (
    <div className={styles.testResultBox}>
      <div className={styles.right}>{title}</div>
      <div className={classnames(styles.left, [className])}>{children}</div>
    </div>
  );
};

export default TestResult;
