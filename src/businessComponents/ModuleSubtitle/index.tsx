import styles from "./styles/index.module.less";
import classnames from "classnames";

/**
 * 模块副标题
 * @title 副标题的内容
 */
const ModuleSubtitle = (props: { title: string; className?: string }) => {
  const { title, className } = props;
  return (
    <div className={classnames(styles.moduleSubtitleBox, [className])}>
      <span className={styles.title}>{title}</span>
    </div>
  );
};

export default ModuleSubtitle;
