.scoreMarginStyle {
    margin: 0 0 32px 36px;
    border: 1px solid #e8e8e8;
  }
.reportResultTableBox {
  background-color: #fff;

  :global {
    // 标题设置
    .ant-table-title {
      color: #5B5A87;
      font-size: 18px;
      padding: 11px 0 !important;
      text-align: center;
      font-weight: 600;
      border: 1px solid #ECEBFB!important;
    }

    // 容器设置
    .ant-table-wrapper {
      border-radius: 0;
    }

    // 标头设置
    .ant-table-thead th {
      background-color: #ECEBFB;
      color: #5B5A87;
      font-weight: 600;
      font-size: 18px;
      border-right: 0;
    }

    // 内容单元格设置
    .ant-table-tbody td {
      font-size: 18px;
      color: #666;
      font-weight: 400;
    }
    
  }
}
