import React from "react";
import { Table } from "antd";
import styles from "./styles/index.module.less";

type TableListType={
  TableTitle: string;
  WarningList: any;
}
// export default function TableList() {
const TableList: React.FC<TableListType> = ({
  TableTitle,
  WarningList,
}: TableListType) => {

  return (
    <div className={styles.reportResultTableBox}>
    <Table
      rowKey={() => Math.random()}
      // title={TableTitle}
      className={styles.scoreMarginStyle}
      pagination={false}
      columns={[
        {
          title: '班级',
          dataIndex: 'name',
          align: 'center'
        },
        {
          title: '人数',
          dataIndex: 'num',
          align: 'center'
        },
        {
          title: '占比',
          dataIndex: 'percentage',
          align: 'center',
          render: (value) => `${value}%`
        }
      ]}
      dataSource={WarningList} 
     
    >
      <div>{TableTitle}</div>
    </Table>
    </div>
  );
}

export default TableList;
