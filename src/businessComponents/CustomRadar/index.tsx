import React from "react";
import ChartForRadar from '~/components/ChartForRadar';
import styles from './styles/index.module.less';
import { EReportType } from '~/pages/mental-health-report/types.d';
import type { IFactorItem } from '~/pages/mental-health-report/types';

const CustomRadar: React.FC<any> = (props: any) => {
  const { list, reportType } = props;
  const factorCodeMap: any = {
    '5060': {
      typeName: '当前表现',
      color: '#D46B08',
      rgbColor: 'rgba(212,107,8, 0.1)',
      list: [],
      indicators: [],
      warnings: []
    },
    '5030': {
      typeName: '人格原因',
      color: '#1677FF',
      rgbColor: 'rgba(22,119,255,.1)',
      list: [],
      indicators: [],
      warnings: []
    },
    '5040': {
      typeName: '环境原因',
      color: '#389E0D',
      rgbColor: 'rgba(56,158,13,.1)',
      list: [],
      indicators: [],
      warnings: []
    },
    '5050': {
      typeName: '认知原因',
      color: '#9254DE',
      rgbColor: 'rgba(146,84,222,.1)',
      list: [],
      indicators: [],
      warnings: []
    }
  };

  // 最后的series渲染数组
  const resultArr: any[] = [];
  // 雷达图展示的类型顺序
  const factorCodeSort: number[] = [5050, 5040, 5030, 5060];
  // 所有分数数组，为了找出最大分、个数等业务需要
  const allScore: number[] = [];
  // 一级分类的数组长度最终要和数据一致，因此依次在数组前缀补0，需要记录补0的个数
  // let fillZeroNum: number = 0;
  // 首先按照分类将数据分组
  list?.map((item: IFactorItem) => {
    if (item?.latitudeGroupId) {
      // @ts-ignore
      factorCodeMap[item.latitudeGroupId]?.list.unshift(item);
      // @ts-ignore
      factorCodeMap[item.latitudeGroupId]?.indicators.unshift({
        name: item.latitudeName,
        // @ts-ignore
        color: factorCodeMap[item.latitudeGroupId]?.color,
        score: item?.score
      });
      factorCodeMap[item.latitudeGroupId]?.warnings.unshift(item.warnningScore);
      allScore.unshift(item.score);
    }
    return '';
  });

  let allIndicator: any[] = [];
  const len = allScore?.length; // 总个数
  let tmpArr: number = 0;
  let allWarnings: number[] = [];
  // 生成legend需要的数组，因为示例需要透明背景色+不透明边框，只能单独生成
  const legendDataList: any[] = [];

  factorCodeSort.forEach((item: number) => {
    let itemScores: number[] = [];
    if (tmpArr) {
      const preArr = Array.from({ length: tmpArr }, () => 0);
      itemScores = [...preArr];
    }
    // 当前类型配置
    // @ts-ignore
    const curTypeObj: any = factorCodeMap[item];
    const curValues = curTypeObj.list?.map((item: any) => item.score);
    itemScores = [...itemScores, ...curValues];
    const diffNum = len - curValues.length; // 差值
    if (diffNum) {
      const preArr = Array.from({ length: diffNum }, () => 0);
      itemScores = [...itemScores, ...preArr];
    }
    tmpArr += curTypeObj.list?.length;

    legendDataList.push({
      name: curTypeObj?.typeName,
      itemStyle: {
        color: curTypeObj?.rgbColor,
        borderColor: curTypeObj?.color,
        borderWidth: 1,
        borderRadius: 1
      }
    });

    resultArr.push({
      value: [...itemScores],
      name: curTypeObj.typeName,
      areaStyle: {
        opacity: 0.1, // 背景区域的颜色要有透明度
        color: curTypeObj.color,
      },
      lineStyle: {
        color: curTypeObj.color
      },
      label: {
        show: true, // 要显示拐点的数字，
        symbol: "diamond",  // 图表中各个图区域的边框线拐点 图形
        formatter: (params: any) => {
          return params.value;
        }
      }
    });
    allIndicator = allIndicator.concat([...curTypeObj?.indicators])
    allWarnings = allWarnings.concat([...curTypeObj.warnings]);
  });

  // 找出得分、预警分的最大值并设置
  const maxScore = Math.max(...allScore);
  const maxWarningScore = Math.max(...allWarnings);

  const maxNum = Math.max(maxScore, maxWarningScore);
  const indicators = allIndicator.map((item: IFactorItem) => ({
    ...item,
    max: maxNum + 1,
    score: item?.score
  }));

  resultArr.push({
    value: allWarnings,
    name: '预警分',
    symbol: "none", // 预警分不需要显示拐点数值
    lineStyle: {
      color: '#FF0E0D' // 预警分线的颜色
    }
  });

  // 预警分/常模均分是固定的
  legendDataList.reverse().push({
    name: '预警分',
    itemStyle: {
      color: 'rgba(255,0,0,.1)',
      borderColor: '#FF0000',
      borderWidth: 1,
      borderRadius: 1
    }
  });

  // 公共雷达图组件的配置参数
  const customOption: any = {
    color: ['#9254DE', '#389E0D', '#1677FF', '#D46B08', '#FF0000'],
    legend: {
      data: legendDataList,
      orient: 'horizontal',
      x: 'center',
      y: 'bottom',
      textStyle: {
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 12,
    },
    radar: {
      indicator: indicators,
      splitNumber: maxScore >= 8 ? 10 : maxScore + 2,
      radius: '68%',
      axisName: {
        formatter: (value: string, indicator: any) => `${value} \n ${indicator.score}`,
      },
    },
    series: {
      type: "radar",
      symbol: 'none',
      symbolSize: 0,
      data: resultArr
    }
  };

  return (
    <div className={styles['custom-radar-container']}>
      <ChartForRadar height={440} customOption={customOption} />
    </div>
  );
};

export default CustomRadar;
