import React from 'react';
import classnames from 'classnames';
// import PropTypes from 'prop-types'
import styles from "./styles/index.module.less";


/**
 * 表单项式的标题和文字行
 * @className 自定义样式类
 * @label 左侧label文字
 * @text 右侧内容文字
 */
type LabelTextItemType = {
    label: any;
    text?: any;
    className?: any

};
const LabelTextItem: React.FC<LabelTextItemType> = ({
    label,
    text,
    className,
}: LabelTextItemType) => {

    // 转换内容中的换行符问题
    const covertChangeLineTag = (text = '') => {
        if (typeof text === 'string') {
            const result = (text || '').replace(/\n/ig, '<br />');
            return <span dangerouslySetInnerHTML={{ __html: result }} />;
        }
        return text;
    };

    return (
        <div className={classnames(styles.LabelTextItemBox, [className])}>
            <span>{label}</span>
            {
                typeof text === 'string' ? (
                    <p dangerouslySetInnerHTML={{ __html: text }} />
                ) : (
                    <React.Fragment>
                        {covertChangeLineTag(text)}
                    </React.Fragment>
                )
            }
        </div>
    );
};

export default LabelTextItem;
