import React from "react";
import styles from "./styles/index.module.less";
const PieRemarks = (props: any) => {
  const { type, version } = props;
  const explain = () => {
    switch (type) {
      case 1:
        return version === 2 ? (
          <div>
            <h5>注：</h5>
            <div className={styles["Entrance-mTop"]}>
              a.风险等级采用学生总体风险指数与学生不同等级风险因子个数的双重预警筛查机制。
            </div>
            <div className={styles["Entrance-mTop"]}>
              b.风险指数区间为0-100分，指数越高，提示心理健康风险越高。
            </div>
            <div className={styles["Entrance-mTop"]}>
              c.风险因子共7个，每个因子划分了4个风险等级，分别为高风险、较高风险、轻微风险和健康。
            </div>
            <div
              className={styles["Entrance-mTop"]}
              dangerouslySetInnerHTML={{
                __html:
                  "d.健康：高风险因子个数=0且较高风险因子个数≤2且轻微风险因子个数≤3或0≤风险指数<61.29；</br>轻微风险：0<高风险因子个数≤1或2<较高风险因子个数≤3或3<轻微风险因子个数≤4或61.29≤风险指数<68.55；<br/> 较高风险：1<高风险因子个数≤3或3<较高风险因子个数≤5或4<轻微风险因子个数或68.55≤风险指数<83.06；<br/>高风险：3<高风险因子个数或5<较高风险因子个数或1<高风险因子个数≤3且3<较高风险因子个数≤5或83.06≤风险指数≤100。",
              }}
            ></div>
          </div>
        ) : (
          ""
        );
      case 2:
        return version && version === 2 ? (
          <div>
            <h5>注：</h5>
            <div className={styles["Entrance-mTop"]}>
              无风险：总体预警人数占比 高风险≤4%且较高风险＜8%且轻微风险＜15%;
            </div>
            <div className={styles["Entrance-mTop"]}>
              轻微风险：总体预警人数占比 4%＜高风险＜ 5% 或 8%≤较高风险＜9.5% 或
              轻微风险≥15%;
            </div>
            <div className={styles["Entrance-mTop"]}>
              较高风险：总体预警人数占比 5%≤高风险≤6% 或 较高风险≥9.5%;
            </div>
            <div className={styles["Entrance-mTop"]}>
              高风险：总体预警人数占比 高风险 {">"} 6%。
            </div>
          </div>
        ) : (
          <div>
            <h5>注：</h5>
            <div className={styles["Entrance-mTop"]}>
              无风险：总体预警人数占比高风险＜5% 且较高风险＜5%;
            </div>
            <div className={styles["Entrance-mTop"]}>
              轻微风险：总体预警人数占比 高风险＜5%且5%≤较高风险＜10%;
            </div>
            <div className={styles["Entrance-mTop"]}>
              较高风险：总体预警人数占比 5%≤高风险＜10%且较高风险≥5% 或
              较高风险≥10%;
            </div>
            <div className={styles["Entrance-mTop"]}>
              高风险：总体预警人数占比 高风险≥10%;
            </div>
          </div>
        );
      case 3:
        return version && version === 2 ? (
          <div>
            <h5>注：</h5>
            <div className={styles["Entrance-mTop"]}>
              无风险：总体预警人数占比 高风险≤4%且较高风险＜8%且轻微风险＜15%
            </div>
            <div className={styles["Entrance-mTop"]}>
              轻微风险：总体预警人数占比 4%＜高风险＜ 5% 或 8%≤较高风险＜9.5% 或
              轻微风险≥15%;
            </div>
            <div className={styles["Entrance-mTop"]}>
              较高风险：总体预警人数占比 5%≤高风险≤6% 或 较高风险≥9.5%;
            </div>
            <div className={styles["Entrance-mTop"]}>
              高风险：总体预警人数占比 高风险{">"}6%。
            </div>
          </div>
        ) : (
          <div>
            <h5>注：</h5>
            <div className={styles["Entrance-mTop"]}>
              无风险：总体预警人数占比 高风险＜5%且较高风险＜5%
            </div>
            <div className={styles["Entrance-mTop"]}>
              轻微风险：总体预警人数占比 高风险＜5%且5%≤较高风险＜10%
            </div>
            <div className={styles["Entrance-mTop"]}>
              较高风险：总体预警人数占比 5%≤高风险＜10%且较高风险≥5% 或
              较高风险≥10%
            </div>
            <div className={styles["Entrance-mTop"]}>
              高风险：总体预警人数占比 高风险≥10%
            </div>
          </div>
        );
      case 21:
        return (
          <div>
            <h5>注：</h5>
            <div className={styles["Entrance-mTop"]}>
              a.
              0~51.6分，表示学习准备度低；51.6~59.9分，表示学习准备度一般；59.9~68.3分，表示学习准备度良好；68.3~100分，表示学习准备度优秀。
            </div>
            <div className={styles["Entrance-mTop"]}>
              b. 指数越高，提示学习准备度越高
            </div>
          </div>
        );
    }
  }
  return (
    <div>
      <div
        className={styles.remarks}
      >
        {explain()}
      </div>
    </div>
  );
};

export default PieRemarks;