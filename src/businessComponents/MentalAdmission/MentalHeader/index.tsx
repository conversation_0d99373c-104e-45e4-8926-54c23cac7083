import React from 'react';
import classnames from 'classnames'
import styles from './styles/index.module.less';
import moment from 'moment';
const EntranceReportInfo = (props: any) => {
  const { infoData, type = 1, bool, schoolCount } = props;
  return (
    <div>
      {!bool && (
        <div className={styles["vita-info"]}>
          {type === 1 ? (
            <div className={styles["vitaInfo-list"]}>
              <h3 className={styles["info-left"]}>
                姓名:{" "}
                <span>{infoData.userName || "--"}</span>
              </h3>
              <h3 className={styles["info-left"]}>
                班级:{" "}
                <span>{infoData.className || "--"}</span>
              </h3>
              <h3 className={styles["info-left"]}>
                毕业年份:{" "}
                <span>{infoData.gradeName || "--"}</span>
              </h3>
              <h3 className={styles["info-left-school"]}>
                学校:{" "}
                <span>{infoData.schoolName || "--"}</span>
              </h3>
              <h3 className={styles["info-left"]}>
                地区:{" "}
                <span>{infoData.areaName || "--"}</span>
              </h3>
              <h3 className={styles["info-left"]}>
                测评布置日期:{" "}
                <span className={styles["vita-infoData"]}>
                  {moment(infoData.reportTime).format(
                    "YYYY-MM-DD"
                  )}
                </span>
              </h3>
              <h3 className={styles["info-left-max"]}>
                报告编码:{" "}
                <span>{infoData.reportCode || "--"}</span>
              </h3>
              <h3 className={styles["info-left"]} />
            </div>
          ) : null}
          {type === 2 ? (
            <div className={styles["vitaInfo-list"]}>
              <div>
                <h3 className={styles["info-left"]}>
                  班级:{" "}
                  <span>
                    {infoData.className || "--"}
                  </span>
                </h3>
                <h3 className={styles["info-left"]}>
                  毕业年份:{" "}
                  <span>
                    {infoData.gradeName || "--"}
                  </span>
                </h3>
                <h3 className={styles["info-left"]}>
                  学校:{" "}
                  <span>
                    {infoData.schoolName || "--"}
                  </span>
                </h3>
              </div>
              <div>
                <h3 className={styles["info-left"]}>
                  地区:{" "}
                  <span>{infoData.areaName || "--"}</span>
                </h3>
                <h3 className={styles["info-left"]}>
                  报告日期:{" "}
                  <span
                    className={styles["vita-infoData"]}
                  >
                    {moment(infoData.reportTime).format(
                      "YYYY-MM-DD"
                    )}
                  </span>
                </h3>
                <h3 className={styles["info-left-max"]}>
                  报告编码:{" "}
                  <span>
                    {infoData.reportCode || "--"}
                  </span>
                </h3>
              </div>
              <div>
                <h3 className={styles["info-left"]}>
                  测评时间:{" "}
                  <span
                    className={styles["vita-infoData"]}
                  >
                    {moment(infoData.startTime).format(
                      "YYYY-MM-DD"
                    )}
                  </span>
                  &nbsp;--
                  <span
                    className={styles["vita-infoData"]}
                  >
                    {moment(infoData.endTime).format(
                      "YYYY-MM-DD"
                    )}
                  </span>
                </h3>
                <h3 className={styles["info-left"]}></h3>
                <h3 className={styles["info-left-max"]}>
                  参与报告人数:{" "}
                  <span>
                    {/* 完成人数/总人数:{" "} */}
                    {infoData.evaluatedPeople || "--"}
                    {/* /{infoData.totalPeople || "--"} */}
                  </span>
                </h3>
              </div>

              <h3 className={styles["info-left"]}></h3>
            </div>
          ) : null}
          {type === 3 ? (
            <div className={styles["vitaInfo-list"]}>
              <div>
                <h3 className={styles["info-left"]}>
                  毕业年份


                  :{" "}
                  <span>
                    {infoData.gradeName || "--"}
                  </span>
                </h3>
                <h3
                  className={classnames(
                    styles["info-left-school"],
                    styles["info-left-school-w600"]
                  )}
                >
                  学校:{" "}
                  <span>
                    {infoData.schoolName || "--"}
                  </span>
                </h3>
              </div>
              <div>
                <h3 className={styles["info-left"]}>
                  地区:{" "}
                  <span>{infoData.areaName || "--"}</span>
                </h3>
                <h3 className={styles["info-left"]}>
                  报告日期:{" "}
                  <span
                    className={styles["vita-infoData"]}
                  >
                    {moment(infoData.reportTime).format(
                      "YYYY-MM-DD"
                    )}
                  </span>
                </h3>
                <h3 className={styles["info-left-max"]}>
                  报告编码:{" "}
                  <span>
                    {infoData.reportCode || "--"}
                  </span>
                </h3>
              </div>
              <div>
                <h3 className={styles["info-left"]}>
                  测评时间:{" "}
                  <span
                    className={styles["vita-infoData"]}
                  >
                    {moment(infoData.startTime).format(
                      "YYYY-MM-DD"
                    )}
                  </span>
                  &nbsp;--
                  <span
                    className={styles["vita-infoData"]}
                  >
                    {moment(infoData.endTime).format(
                      "YYYY-MM-DD"
                    )}
                  </span>
                </h3>
                <h3 className={styles["info-left"]}></h3>
                <h3 className={styles["info-left-max"]}>
                  参与报告人数:{" "}
                  {/* <span>完成人数/总人数:</span>{" "} */}
                  <span>
                    {infoData.evaluatedPeople}
                    {/* /{infoData.totalPeople} */}
                  </span>
                </h3>
              </div>

              <h3 className={styles["info-left"]}></h3>
            </div>
          ) : null}
          {type === 4 ? (
            <div className={styles["vitaInfo-list"]}>
              <h3 className={styles["info-left"]}>
                年级:{" "}
                <span>{infoData.gradeName || "--"}</span>
              </h3>
              <h3 className={styles["info-left"]}>
                地区:{" "}
                <span>{infoData.areaName || "--"}</span>
              </h3>
              <h3 className={styles["info-left"]}>
                报告日期:{" "}
                <span className={styles["vita-infoData"]}>
                  {moment(infoData.reportTime).format(
                    "YYYY-MM-DD"
                  )}
                </span>
              </h3>
              <h3 className={styles["info-left-max"]}>
                报告编码:{" "}
                <span>{infoData.reportCode || "--"}</span>
              </h3>
              <h3 className={styles["info-left"]}></h3>
            </div>
          ) : null}
        </div>
      )}
      {type === 5 ? (
        <div
          className={classnames(
            styles["vitaInfo-list"],
            styles["vitaInfo-collective"]
          )}
        >
          已有<span>{schoolCount || 0}</span>
          所学校生成有效集体报告
        </div>
      ) : null}
    </div>
  );

}

export default EntranceReportInfo
