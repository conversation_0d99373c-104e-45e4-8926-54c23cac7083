

.vita-info {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0px 0px 20px 0px rgba(230, 236, 233, 1);

    width: 1000px;
    //height: 117px;
    margin: -60px auto 0;
    position: relative;
    z-index: 2;
    padding: 30px 20px 20px 20px;
    .vitaInfo-list {
        margin-top: -20px;
    }
    .vitaInfo-list,
    .vitaInfo-list1 {
        .info-left,
        .info-left-max,
        .info-left-school {
            display: inline-block;
            font-size: 18px;
            color: #333;
            font-weight: 700;
            margin-right: 10px;
            margin-top: 20px;
            width: 240px;
            &:nth-child(4n + 4) {
                width: 200px;
            }
            span {
                font-weight: normal;
                display: inline-block;
                vertical-align: middle;
                // min-width: 178px;
                // max-width: 178px;
                padding-left: 10px;
                // float: right;
            }
            .vita-data,
            .vita-infoData {
                max-width: 149px;
                min-width: 149px;
            }
        }
        .info-left-school-w600 {
            width: 600px;
        }
        .info-left-max {
            width: 260px !important;
        }
        .info-left,
        .info-left-max {
            white-space: nowrap;
            // overflow: hidden;
            span {
                max-width: 90%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: bottom;
            }
        }
        .info-left-school {
            position: relative;
            span {
                position: absolute;
            }
            // max-height: 80px;
        }
    }
}
.vitaInfo-collective {
    width: 474px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    color: #542c14;
    font-size: 27px;
    opacity: 1;
    background: linear-gradient(90deg, #f4c78e, #ffb05a);
    border-radius: 30px;
    margin: -30px auto;
    position: relative;
    z-index: 2;
    span {
        font-weight: bold;
        padding: 0 5px;
    }
}
