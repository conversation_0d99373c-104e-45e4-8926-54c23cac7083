import React, { Component } from 'react';
import Image from 'next/image'
import styles from './styles/index.module.less';
import report4010 from '@/assets/MentalAdmission/4010.png';
import report4020 from '@/assets/MentalAdmission/4020.png';
import report4030 from '@/assets/MentalAdmission/4030.png';
import report4040 from '@/assets/MentalAdmission/4040.png';

import type { familyEducationProps } from '~/service/entrance/person/types';
class EntranceUpbringing extends Component {
  static defaultProps = {
    type: 0,
    name: '',
    onlyChild: '',
    fatherEducation: '',
    motherEducation: '',
    familyEnvironment: '',
    parentingStyleName: '',
    counselingAdvice: ''
  };

  constructor(props: familyEducationProps) {
    super(props);
  }

  render() {
    const { type, name, onlyChild, fatherEducation, motherEducation, familyEnvironment, parentingStyleName } : any = this.props;

    return (
      <div>
        <div className={styles['EntranceViewMerge-upbringing']}>
          <h3 className={styles['report-model-title']}>从测评结果来看，该学生的家庭教养方式属于：<span>{name}</span></h3>
          <div className={styles['upbringing-img']}>
            {
              type === 4010 && <img src={report4010} width="50%" alt="report4010" />
            }
            {
              type === 4020 && <img src={report4020} width="50%"  alt="report4020"/>
            }
            {
              type === 4030 && <img src={report4020} width="50%" alt="report4030"/>
            }
            {
              type === 4040 && <img src={report4020} width="50%"  alt="report4040"/>
            }
          </div>
          <ol>
            <li>独生子女：<span>{onlyChild}</span></li>
            <li>父亲文化程度：<span>{fatherEducation}</span></li>
            <li>母亲文化程度：<span>{motherEducation}</span></li>
            <li>日常家庭环境：<span>{familyEnvironment}</span></li>
            <li>家庭教养风格：<span>{parentingStyleName}</span></li>
          </ol>
        </div>

        {/* <div className={'EntranceViewMerge-upbringing'}>
                    <div className={'upbringing-suggest'}>
                        {
                            counselingAdvice
                        }
                    </div>
                </div> */}
      </div>
    )
  }
}

export default EntranceUpbringing;
