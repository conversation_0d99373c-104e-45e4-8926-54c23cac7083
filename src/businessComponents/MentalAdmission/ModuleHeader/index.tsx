import React from 'react'
import classnames from 'classnames'
import styles from "./styles/index.module.less"

const ModuleHeader = (props: any) => {
  const { title, subtitle, id, isPrint } = props;
  return (
    <div className={classnames(styles['vita-headline'], { typePrint: !!isPrint })} >
      <h3 className={styles['vita-headline-ts']} ><a id={id} />{title}</h3>
      <h3 className={styles['vita-headline-ts1']}>{subtitle}</h3>
    </div>
  )
}

export default ModuleHeader