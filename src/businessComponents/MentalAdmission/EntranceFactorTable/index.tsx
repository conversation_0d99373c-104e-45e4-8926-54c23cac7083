import React from "react";
// import styles from './index.modules.scss';
import styles from "./styles/index.module.less";
import classnames from "classnames";

const EntranceFactorTable = (props: any) => {
  const { factorAnalysis, type, version } = props;
  const { latitudeList } = factorAnalysis;
  return (
    <div
      className={
        classnames(styles["report-model-subject-table"], styles["EntranceTable-report-model-subject-table-flex"])
      }
    >
      <ul>
        <li
          className={
            classnames(styles["report-model-table"], styles["report-model-table-header"], styles["EntranceTable-Top"])
          }
        >
          <span />
          {latitudeList
            ? latitudeList.map((item: any) => {
              return <span key={item.id}>{item.name}</span>;
            })
            : null}
        </li>
        <li
          className={
            classnames(styles["report-model-table"], styles["report-model-table-header"], styles["EntranceTable-li-leida"])
          }
        >
          <span>得分</span>
          {latitudeList
            ? latitudeList.map((item: any) => {
              return (
                <span key={item.id}>{item.score}</span>
              );
            })
            : null}
        </li>
        {(!type && version === 2) || +version === 2 ? (
          <li
            className={
              classnames(styles["report-model-table"], styles["report-model-table-header"], styles["EntranceTable-li-leida"])
            }
          >
            <span>参考分</span>
            {latitudeList
              ? latitudeList.map((item: any) => {
                return (
                  <span key={item.id}>
                    {item.referenceScore}
                  </span>
                );
              })
              : null}
          </li>
        ) : (
          ""
        )}
        {(!type && version === 2) || +version === 2 ? (
          <li
            className={
              classnames(styles["report-model-table"], styles["report-model-table-header"], styles["EntranceTable-li-leida"])
            }
          >
            <span>预警分</span>
            {latitudeList
              ? latitudeList.map((item: any) => {
                return (
                  <span key={item.id}>
                    {item.warnningScore}
                  </span>
                );
              })
              : null}
          </li>
        ) : (
          ""
        )}

        {type && (
          <li className={styles["report-model-table"]}>
            <span>参考分</span>
            {latitudeList
              ? latitudeList.map((item: any) => {
                return (
                  <span key={item.id}>
                    {item.referenceScore}
                  </span>
                );
              })
              : null}
          </li>
        )}
      </ul>
    </div>
  );
}
export default EntranceFactorTable
