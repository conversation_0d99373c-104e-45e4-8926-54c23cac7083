.report-model-subject-table {
  width: 920px;
  margin: 30px auto 0;
  background: #ffffff;
  border-radius: 1px;
  &.newBackground {
    li:nth-child(odd) {
      background: rgba(179, 127, 235, 0.1);
    }
  }

  ul {
    border: 1px solid #f2f7ff;
    li {
      display: flex;
      flex-flow: row nowrap;
      justify-content: flex-start;
      height: 60px;
      line-height: 60px;

      &:nth-child(odd) {
        background: rgba(0, 200, 101, 0.1);
      }

      span {
        &:first-child {
          width: 110px;
          padding: 0;
        }
      }
    }

    .report-model-table {
      span {
        padding-right: 14px;
        width: 93px;
        text-align: center;
        color: #243355;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;

        &:nth-last-child(1) {
          width: 100px;
        }
      }
    }

    .report-model-table-header {
      span {
        color: #243355;
        font-size: 18px;
        line-height: 22px;
        display: flex;
        flex-direction: column;
      }
    }

    .EntranceTable-li-leida {
      span {
        line-height: 42px;
      }
    }
  }

}



.EntranceTable-report-model-subject-table-flex {
  .report-model-table {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;

    span {
      font-size: 18px !important;
      color: #001139 !important;
      padding-right: 10px !important;
    }
  }

  .report-model-table-header {
    border: 1px solid #e5eaf6;
    border-radius: 4px 4px 0px 0px;
  }

  ul li:nth-child(odd) {
    background: #f3f7ff;
  }

  ul .EntranceTable-Top {
    min-height: 40px;
    height: auto !important;
    background: #e3ebff !important;

    // line-height: 1.5;
    span {
      line-height: 1.3 !important;
      padding: 5px 0;
    }
  }
}
