import styles from "./styles/index.module.less";
import moment from "moment";
import entrancebannerregoin from "@/assets/MentalAdmission/entrance-banner-footer.png";

const  EntranceReportDetails =(props:any) => {
   
        const { infoData, type, needHigher, version } = props;
        return (
            <div className={styles.EntranceReportDetails}>
                <div className={styles["EntranceReportDetails-min"]}>
                    {(type === 2 || type === 1 || type === 3 || type === 4) && (
                        <div className={styles["Entrance-message"]}>
                            {(type === 2 || type === 4) && (
                                <h3>
                                    区/县：<span>{infoData.districtName}</span>
                                </h3>
                            )}

                            <h3>
                                所属地区：<span>{infoData.areaName}</span>
                            </h3>
                            {version && version === 2 ? (
                                <h3>
                                    毕业年份：<span>{infoData.gradeName}</span>
                                </h3>
                            ) : (
                                <h3>
                                    年级：<span>{infoData.gradeName}</span>
                                </h3>
                            )}
                        </div>
                    )}
                    <div className={styles["Entrance-message-details"]}>
                        <div>
                            <h3 style={{ color: "#243355" }}>
                                完成人数/总人数：
                                <span>
                                    {infoData.evaluatedPeople}/
                                    {infoData.totalPeople}
                                </span>
                            </h3>
                            <h3>
                                参与情况：
                                <span>参与学校{infoData.overSchoolCnt}个</span>
                            </h3>
                        </div>
                        <div>
                            <h3>
                                测评起止日期：
                                <span>
                                    {moment(infoData.startTime).format(
                                        "YYYY-MM-DD"
                                    )}
                                    /
                                    {moment(infoData.endTime).format(
                                        "YYYY-MM-DD"
                                    )}
                                </span>
                            </h3>
                        </div>
                    </div>
                </div>
                <div
                    className={styles.EntranceReportFooter}
                    style={{
                        background: `url(${ entrancebannerregoin
                            }) no-repeat center`,
                        marginTop: needHigher ? "120px" : "100px",
                    }}
                >
                    <div className={styles["EntranceReportFooter-bom"]}>
                        <span className={styles["EntranceReportFooter-bom-l"]}>
                            报告编号：{infoData.reportCode}
                        </span>
                        {type !== 3 && type !== 4 && (
                            <span
                                className={styles["EntranceReportFooter-bom-r"]}
                            >
                                报告生成日期：
                                {moment(infoData.reportTime).format(
                                    "YYYY-MM-DD"
                                )}
                            </span>
                        )}
                    </div>
                </div>
            </div>
        );
}

export default EntranceReportDetails