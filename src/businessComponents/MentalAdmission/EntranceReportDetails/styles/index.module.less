.EntranceReportDetails {
  width: 1000px;
  margin: 0 auto;
  background-color: #fff;

  .EntranceReportDetails-min {
    padding: 60px 90px 0 90px;
    .Entrance-message {
      text-decoration: underline;
      h3 {
        font-size: 30px;
        color: #243355;
        line-height: 1.8;
        span {
          font-weight: bold;
        }
      }
    }
    .Entrance-message-details {
      background: rgba(35, 49, 85, 0.04);
      padding: 20px 40px;
      margin-left: -30px;
      width: 880px;
      border-radius: 10px;
      margin-top: 70px;
      color: #243355;
      font-size: 22px;
      span {
        font-weight: bold;
      }
      div {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
      }
    }
  }
  .EntranceReportFooter {
    width: 100%;
    background-size: 100%;
    height: 157px;
    position: relative;
    margin-top: 100px;
    .EntranceReportFooter-bom {
      position: absolute;
      bottom: 0;
      width: 936px;
      padding: 0 33px 17px 33px;
      .EntranceReportFooter-bom-r,
      .EntranceReportFooter-bom-l {
        color: rgba(255, 255, 255, 0.6);
        font-size: 22px;
        line-height: 1;
      }
      .EntranceReportFooter-bom-l {
        float: left;
      }
      .EntranceReportFooter-bom-r {
        float: right;
      }
    }
  }
}
