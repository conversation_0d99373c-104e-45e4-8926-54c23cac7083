import React, { useEffect } from 'react';
import classnames from 'classnames'
import styles from "./styles/index.module.less"

const AnalyPage = (props: any) => {
    useEffect(() => { initEcharts() }, [])
    const initEcharts = () => {
    }
    const { serial, title, children, id, isPrint } = props;
    return <div className={classnames(styles['Entrance-Analy-page'], { typePrint: !!isPrint })} >
        <div >
            <div className={styles['Analy-page-title']}>
                <span className={styles.serial}>{serial}</span>
                <a id={id}><span>{title}</span></a>
            </div>
        </div>

        {
            children
        }
    </div>
}

export default AnalyPage