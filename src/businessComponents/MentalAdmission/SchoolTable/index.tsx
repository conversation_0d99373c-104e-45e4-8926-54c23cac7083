import React from 'react';
import { Table, } from 'antd';
import styles from './styles/index.module.less';
import moment from "moment";

const COLUMNS_COUNTY = [
    {
        title: '区/县',
        dataIndex: 'areaName',
        key: 'areaName',
        align: 'center',
    },
    {
        title: '学校',
        key: 'schoolName',
        dataIndex: 'schoolName',
        align: 'center'
    },
    {
        title: '布置班级数',
        key: 'classNum',
        dataIndex: 'classNum',
        align: 'center',
    },
    {
        title: '布置学生数',
        key: 'studentNum',
        dataIndex: 'studentNum',
        align: 'center'
    },
    {
        title: '完成数',
        key: 'completeNum',
        dataIndex: 'completeNum',
        align: 'center',
    },
    {
        title: '完成率',
        key: 'completionRate',
        dataIndex: 'completionRate',
        align: 'center',
        render: (value: number) => (value ? `${value.toFixed(2)}%` : "0.00%")
    },
];
const SchoolTable = ({ columns=COLUMNS_COUNTY, type, evaluationSchoolVOS, deadlineTime }:any) => {
    if (type === 1) {
        columns.unshift({
            title: `城市`,
            dataIndex: 'cityName',
            key: 'cityName',
            align: 'center'
        })
    }




    return <div className={styles['entrance-evaluationList-table-class']}>
        <h3 className={styles['entrance-evaluationList-table-title']}> <i className={styles.iconfont} />参与测评学校清单</h3>
        <p className={styles['entrance-evaluationList-table-time']}>· 以下清单中的数据为截止{moment(deadlineTime).format('YYYY-MM-DD')}的数据</p>
        <Table className={styles['entrance-evaluationList-table']} columns={columns} dataSource={evaluationSchoolVOS} pagination={false} />
    </div>
}
export default SchoolTable
