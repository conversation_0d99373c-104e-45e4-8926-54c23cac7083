import React from 'react';
import classnames from 'classnames'
import styles from "./styles/index.module.less"

const EntranceGuide = (props: any) => {
  return <div className={styles['hospital-content']}>
    <p>您好！非常感谢您使用升学e网通《高中生入学状态综合测评》。《高中生入学状态综合测评》是升学e网通心理团队紧扣初升高入学节点，围绕学校对新高一学生的心理健康及学习状态方面的评估需求，定制设计的专业心理测评量表。该测评从心理健康、学习准备度、性格类型和家庭教养风格四个方面，全面了解学生的心理及学习综合状态，帮助学校和老师对学生当前状态有全面、直观的掌握。</p>
    <p className={styles.border}>在阅读本报告前，请注意以下几点：</p>
    <p className={styles.border}> 1.参考时效</p>
    <p>2个月以内：可以参考<span className={styles["padding-right"]} />2~6个月：谨慎参考<span className={styles["padding-right"]} />6个月以上：不建议参考</p>
    <p className={styles.border}>2. 风险程度说明</p>
    <p><span className={classnames(styles.diamonds, styles['diamonds-red'])}>高风险：</span>表示该生的因子得分高于绝大部分高中生，在对应因子上有极大的可能性存在异常风险，建议老师优先关注并及时沟通干预。</p>
    <p><span className={classnames(styles.diamonds, styles['diamonds-yellow'])}>较高风险：</span>表示该生的因子得分高于大部分高中生，在对应因子上有较大可能性存在异常风险，建议老师保持密切关注。</p>
    <p><span className={classnames(styles.diamonds, styles['diamonds-blue'])}>轻微风险：</span>表示该生的因子得分高于一般高中生，在对应因子上有一定可能性存在异常风险，建议老师保持密切关注。</p>
    <p><span className={classnames(styles.diamonds, styles['diamonds-green'])}>无风险：</span>表示该生的因子得分与大部分高中生一致，不存在明显的异常风险。</p>
    <p className={styles.border}>3. 保密原则</p>
    <p>本报告内容仅校方相关人员可查看。如需与学生沟通报告内容，请遵循“一对一沟通”的原则，尽可能保护学生隐私和自尊心。</p>
    <p className={styles.border}>4. 客观原则</p>
    <p>学生的心理状态及性格特点并非不可改变。尤其是对于处于青春期的高中学生来说，心智和性格本就具有较大的可塑性。因此，请勿以测评结果作为对学生的定性评估结论，切忌给学生贴上标签。</p>
    <div >
      <p className={styles.border}>5. 误差说明</p>
      <p>学生在测评时的情绪状态、对测评的态度等主观因素，以及测评过程中的环境干扰等客观因素都会影响测评结果。因此在阅读本测评报告时，请参照学生在日常生活的实际表现来理解。</p>
    </div>
  </div>
}

export default EntranceGuide