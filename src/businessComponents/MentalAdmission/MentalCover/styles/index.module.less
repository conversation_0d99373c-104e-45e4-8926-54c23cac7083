
.banner {
  // width: 100%;
  // height: 470px;
  // position: relative;
  width: 1000px;
  margin: 0 auto;
  .Toptis {
    width: 1000px;
    margin: 0 auto;
    padding: 20px 0 0 14px;
    font-size: 18px;
    color: #fff;
  }

  .content {
    display: flex;
    padding-top: 246px;
    // padding-left: 366px;
    width: 1000px;
    margin: 0 auto;
    height: 24px;
    font-weight: 700;
    font-family: MicrosoftYaHei;
    font-size: 14px;
    color: #003963;
    line-height: 24px;
    p {
      &:first-child {
        margin-left: 24px;
      }
    }
    & > span {
      margin-left: 8px;
    }
  }
}

.bannercity {
  width: 1000px;
  margin: 0 auto;
}

.head-grade {
  position: absolute;
  left: 50%;
  top: 70%;
  transform: translate(-50%, -50%);
}
