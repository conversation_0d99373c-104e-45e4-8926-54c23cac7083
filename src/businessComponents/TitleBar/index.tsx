import React from "react";
import styles from "./styles/index.module.less";
import "antd/dist/antd.css";
type TitleBarType = {
  title: string;
  wrapperClass?: string;
  children?: any;
};
const TitleBar: React.FC<TitleBarType> = ({
  title,
  children = [],
  wrapperClass = ''
}: TitleBarType) => {
  return (
    <div className={`${styles.wrap} ${wrapperClass}`}>
      <div className={styles.title}>{title}</div>
      {children}
    </div>
  );
};

export default TitleBar;
