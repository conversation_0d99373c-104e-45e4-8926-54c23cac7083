import React from "react";
import styles from './styles/index.module.less';
import { riskLevelColors } from '~/utils/const';

const RiskDemoList: React.FC<object> = () => {

  return (
    <ul className={styles['risk-demo-list-container']}>
      <li>
        <b style={{ backgroundColor: `${riskLevelColors[3]}` }} />
        <span>高风险</span>
      </li>
      <li>
        <b style={{ backgroundColor: `${riskLevelColors[2]}` }} />
        <span>较高风险</span>
      </li>
      <li>
        <b style={{ backgroundColor: `${riskLevelColors[1]}` }} />
        <span>轻微风险</span>
      </li>
      <li>
        <b style={{ backgroundColor: `${riskLevelColors[0]}` }} />
        <span>无风险</span>
      </li>
    </ul>
  );
};

export default RiskDemoList;
