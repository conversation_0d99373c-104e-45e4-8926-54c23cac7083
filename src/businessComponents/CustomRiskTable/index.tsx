import React, { useEffect, useState } from "react";
import { riskLevelClassName } from '@/utils/const';
import { Table } from 'antd';
import styles from './styles/index.module.less';
import CommonStyles from '~/styles/common.module.less';
import { cls } from '~/utils/tools';
import { IUserFactorAnalysis } from '~/service/mental-health-report/person-report/types';
import { EReportType } from '~/pages/mental-health-report/types.d';

interface ICustomRiskTableProps {
  data: IUserFactorAnalysis[],
  children?: any;
  reportType: EReportType;
}

const CustomRiskTable: React.FC<any> = (props: ICustomRiskTableProps) => {
  const { children, data, reportType } = props;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const riskNameMap = {
    '轻微风险': '轻微',
    '较高风险': '较高',
    '高风险': '高',
  };
  const columns = [
    {
      title: '维度名称',
      dataIndex: 'firstTypeName',
      rowScope: 'row',
      colSpan: 3,
      width: 34,
      onCell: (record: any) => ({
        rowSpan: record.firstRowSpan,
        className: cls([
          CommonStyles['text-center'],
          CommonStyles['table-head']
        ])
      })
    },
    {
      title: '因子二级分类',
      dataIndex: 'secondTypeName',
      colSpan: 0,
      width: 50,
      onCell: (record: any) => ({
        rowSpan: record.secondRowSpan,
        className: cls([
          CommonStyles['text-center'],
          CommonStyles['table-head'],
          CommonStyles['font-size-14']
        ])
      })
    },
    {
      title: '因子名称',
      dataIndex: 'latitudeName',
      colSpan: 0,
      width: 78,
      onCell: () => ({
        className: cls([
          CommonStyles['text-center'],
          styles['normal-td']
        ])
      })
    },
    {
      title: '匹配等级',
      width: 56,
      dataIndex: 'riskLevelName',
      render: (text: string) => {
        /* 后端使用的大JSON维护数据,不希望单独处理，暂时前端先行处理 */
        // @ts-ignore
        const curText = riskNameMap[text];
        return (
          <span style={{ color: "#fff" }}>
            {curText || text}
          </span>
        );
      },
      onCell: (record: any) => ({
        className: cls([
          `${riskLevelClassName[record.riskLevel - 1]}`,
          CommonStyles['text-center'],
          CommonStyles['font-size-12']
        ])
      }),
    },
    {
      title: '得分',
      width: 52,
      dataIndex: 'score',
      onCell: () => ({
        className: cls([
          CommonStyles['text-center'],
          styles['normal-td']
        ])
      })
    },
    {
      title: '常模均分',
      width: 52,
      dataIndex: 'referenceScore',
      onCell: () => ({
        className: cls([
          CommonStyles['text-center'],
          styles['normal-td']
        ])
      })
    },
    {
      title: '预警分',
      width: 38,
      dataIndex: 'warnningScore',
      onCell: () => ({
        className: cls([
          CommonStyles['text-center'],
          styles['normal-td']
        ])
      })
    },
    {
      title: `${reportType === EReportType.person ? '个人' : reportType === EReportType.class ? '班级' : '年级'}情况`,
      width: 572,
      dataIndex: 'studentSituation',
      onCell: (record: any) => ({
        className: cls([
          CommonStyles['font-size-12'],
          styles['normal-light']
        ])
      }),
    },
  ];

  const initData = () => {
    let resultDataList: any[] = [];
    data?.forEach((itemFirst) => {
      let itemFirstArr: any[] = [];
      itemFirst.latitudeListBeanGroups.forEach((itemSecond) => {
        const itemSecondArr: any[] = [];
        itemSecond.latitudeListBeanList.forEach((itemThird) => {
          const tempData = {
            ...itemThird,
            firstTypeName: itemFirst.positiveFlag ? "保护因子" : "风险因子",
            firstRowSpan: 0,
            secondRowSpan: 0,
            secondTypeName: itemSecond.message,
          };
          itemSecondArr.push(tempData);
        });
        itemSecondArr[0].secondRowSpan = itemSecondArr.length;
        itemFirstArr = [...itemFirstArr, ...itemSecondArr];
      });
      itemFirstArr[0].firstRowSpan = itemFirstArr.length;
      resultDataList = [...resultDataList, ...itemFirstArr];
    });
    setDataSource(resultDataList);
  };

  useEffect(() => { initData(); }, []);

  return (
    <div className={styles['result-analysis-container']}>
      <Table
        rowKey={'latitudeName'}
        columns={columns}
        dataSource={dataSource}
        bordered={true}
        pagination={false}
      />
      {children}
    </div>
  );
};

export default CustomRiskTable;
