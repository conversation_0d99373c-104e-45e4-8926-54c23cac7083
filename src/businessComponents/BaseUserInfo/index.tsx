import classnames from 'classnames';
import moment from 'moment';
import styles from './styles/index.module.less';

export interface IUserBaseInfo {
  reportStyle: string;
  userName: string;
  className: string;
  gradeName: string;
  reportCode: string;
  reportTime: string;
}

interface IUserBaseInfoMap extends Omit<IUserBaseInfo, 'reportStyle'> {
  schoolName: string;
  [key: string]: string;
}

interface IProps {
  style?: string | number;
  userBaseInfo: any;
}

type MapKey = keyof IUserBaseInfoMap | string;

/**
 * 基础用户信息组件
 * @userBasicInfo 用户基本信息
 * {
 *  userName: '姓名',
    className: '班级',
    gradeName: '年级',
    reportCode: '编号',
    reportTime: '时间'
    reportStyle: 1
 * }
 * @style 主题 可强制覆盖userBaseInfo内主题
 */
const BaseUserInfo = (props: IProps) => {
  const { style, userBaseInfo } = props;
  const useStyle = Number(style || userBaseInfo.reportStyle); // 外部传入了则优先使用外部配置，否则使用内部配置
  const mappingObj: IUserBaseInfoMap = {
    userName: '姓名',
    className: '班级',
    gradeName: '毕业年份',
    schoolName: '学校',
    reportCode: '编号',
    reportTime: '时间',
  }; // 映射对象，用于遍历出展示信息
  const loopKeys = Object.keys(mappingObj); // 取出映射的所有key

  return (
    <div
      className={classnames(styles.baseUserInfoBox, {
        [styles.colorStyleGreen]: useStyle === 1,
        [styles.colorStyleBlue]: useStyle === 2,
        [styles.colorStyleRed]: useStyle === 3,
        [styles.colorStyleOrange]: useStyle === 4,
      })}
    >
      {userBaseInfo &&
        loopKeys.map((item: MapKey, index) =>
          userBaseInfo[item] ? (
            <p key={`baseUserInfo_${index}`}>
              <span className={styles.label}>{mappingObj[item]}：</span>
              <span className={styles.labelText}>
                {item === 'reportTime'
                  ? moment(userBaseInfo[item]).format('YYYY-MM-DD')
                  : userBaseInfo[item]}
              </span>
            </p>
          ) : null
        )}
    </div>
  );
};

export default BaseUserInfo;
