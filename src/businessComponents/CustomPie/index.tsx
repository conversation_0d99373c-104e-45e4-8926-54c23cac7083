import React from "react";
import ChartForPie from '~/components/ChartForPie';
import styles from './styles/index.module.less';
import { IStatusDistributionBean } from '~/service/mental-health-report/class-report/types';
import { ELayoutType } from './types.d';
import { cls } from '~/utils/tools';
import RiskDemoList from '../RiskDemoList';
import { riskLevelColors } from '~/utils/const';

interface ICustomPieProps {
  list: IStatusDistributionBean[];
  title: string;
  subTitle?: string;
  classType: string;
  riskType: string;
  layout: ELayoutType
}

const CustomPie: React.FC<ICustomPieProps> = (props) => {
  const { list = [], title, subTitle, classType, riskType, layout } = props;
  const pieData = list?.map((item: IStatusDistributionBean) => ({
    value: item.number,
    name: item.riskName
  })); // 饼图需要的数据集合



  // 公共雷达图组件的配置参数
  const customOption: any = {
    color: [...riskLevelColors].reverse(),
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: '67%',
        data: pieData,
        selectedMode: "single",
        label: {
          position: "outside",
          color: "#001139",
          formatter: (params: { name: string;}) => {
            return params.name;
          },
        },
        itemStyle: {
          borderWidth: 1,
          borderColor: '#fff'
        }
      }
    ]
  };

  return (
    <div className={cls([
      styles['custom-pie-container'],
      layout === ELayoutType.horizontal ? styles[ELayoutType.horizontal] : ''
    ])}>
      <ChartForPie
        warpClassName={styles['pie-warp-box']}
        childrenClassName={styles['pie-children-box']}
        height={360}
        customOption={customOption} />
      <div className={styles['detail-box']}>
        <p className={cls([
          styles['pie-title'],
          layout === ELayoutType.horizontal && styles['h-title']
        ]) }>{title}</p>
        <RiskDemoList />

        <p className={styles['overview-result']}>
          从测评结果来看，该{classType}
          {riskType}风险状态分布：
        </p>
        <ul className={styles['risk-detail-list']}>
          {list.map((v: IStatusDistributionBean, i: number) => {
            return (
              <li key={i}>
                {v.riskName}的学生人数为
                <span className={styles['strong-text']}>{v.number}</span>人，占比
                <span className={styles['strong-text']}>{v.percentage}%</span>;
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default CustomPie;
