import React, { useEffect } from "react";
import styles from "./styles/index.module.less";

type ScoreDescriptType = {
  description?: string;
};
const ScoreDescript: React.FC<ScoreDescriptType> = ({
  description,
}: ScoreDescriptType) => {
  // 转换内容中的换行符问题
  const covertChangeLineTag = (text = "") => {
    const result = text.replace(/\\n/gm, "<br/>");
    return <span dangerouslySetInnerHTML={{ __html: result }} />;
  };
  console.log(
    covertChangeLineTag(description),
    "covertChangeLineTag(description)"
  );
  useEffect(() => {}, []);

  return (
    <div className={styles.ScoreDescriptWraper}>
      <div className={styles.scoreRemark}>
        <span>得分说明：</span>
        <p>{covertChangeLineTag(description)}</p>
      </div>
    </div>
  );
};

export default ScoreDescript;
