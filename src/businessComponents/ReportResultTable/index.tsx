import React from "react";
import classnames from "classnames";
import styles from "./styles/index.module.less";
import { Table } from "antd";

/**
 * 报告结果类表格
 * @title 表格标题，不传会隐藏
 * @dataSource 表格依赖的数据结构
 * @columns 表格需要展示的列key和value数组
 * @className 自定义的样式类
 * @rest 其他表格Api，可参考antd
 */
type ReportResultTableType = {
  title?: any;
  dataSource?: any;
  columns?: any;
  children?: React.ReactNode;
  className?: any;
  rowKey?: any;
};
const ReportResultTable: React.FC<ReportResultTableType> = ({
  title,
  dataSource,
  columns,
  className,
  rowKey,
  ...rest
}: ReportResultTableType) => {
  return (
    <div className={classnames(styles.reportResultTableBox, [className])}>
      <Table
        size="small"
        rowKey={rowKey}
        columns={columns}
        bordered={true}
        dataSource={dataSource}
        pagination={false}
        {...rest}
      />
    </div>
  );
};

export default ReportResultTable;
