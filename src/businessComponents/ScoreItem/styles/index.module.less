.scoreSituationItem {
  margin: 0 20px 40px;
  background-color: #d0defa;
  padding: 20px 10px 10px;
  border-radius: 8px;

  .resultRemark {
    margin-top: 20px;
    padding: 20px;
    font-size: 18px;
    min-height: 110px;
    background: #FFFFFF;
    border-radius: 8px;
    .resultLabel {
      font-size: 18px;
      color: #666;
      font-size: 18px;
      line-height: 30px;

      & + span {
        color: #666;
      }
    }
    .render {
      color: #666;
      margin:10px 0;
    }
  }
  .scoreInfo{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left{
      width: 520px;
      max-height: 64px;
      font-weight: 700;
      font-size: 20px;
      color: rgba(0, 17, 57, 0.85);
      line-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .right{
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .score{
        text-align: right;
        min-width: 220px;
        padding: 0 10px;
        height: 36px;
        background: #FFFFFF;
        border-radius: 4px; 
        font-weight: 700;
        font-size: 20px;
        color: rgba(0, 17, 57, 0.85);
        line-height: 36px;
      }
      .scoreRemark{
        white-space: nowrap;
        // display: flex;
        margin-top: 8px;
        height: 24px;
        opacity: 0.85;
        font-size: 14px;
        color: rgba(0, 17, 57, 0.85);
        text-align: right;
        line-height: 24px;
      }
    }
  }

  .scoreRightRemark,
  .suggestRemark {
    color: #666;
    font-weight: 400;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 24px;
    display: flex;
    flex: 0 0 80px;
    margin-top: 25px;

    & > span {
      flex: 0 0 80px;
    }
  }
}
