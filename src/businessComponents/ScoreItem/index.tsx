import React from 'react';
import classnames from 'classnames';

import styles from './styles/index.module.less';
import { useClient } from '@/hooks';

interface IScoreData {
  latitudeScoreLevel: number;
  latitudeTitle: string;
  score: number;
  latitudeLevelTitle: string;
  description: string;
  totalScore: number;
  result: any;
  suggest: string;
}

interface IProps {
  className?: string;
  data: IScoreData;
  resultLabelAfter?: string;
  children?: React.ReactNode;
  resultAfter?: React.ReactNode;
  color: string;
}

const ScoreSituationItem = (props: IProps) => {
  const {
    className,
    data,
    resultLabelAfter,
    children,
    resultAfter,
    color,
  } = props;
  const [render] = useClient();
  const {
    latitudeScoreLevel: level,
    latitudeTitle: name,
    score,
    latitudeLevelTitle: remark,
    description,
    totalScore,
    result,
    suggest,
  } = data;


  // 转换内容中的换行符问题
  const covertChangeLineTag = (text = '') => {
    const result = (text || '').replace(/\n/gi, '<br />');
    return (
      <span dangerouslySetInnerHTML={{ __html: (render && result) || '' }} />
    );
  };

  return (
    <li className={classnames(styles.scoreSituationItem, [className])} style={{backgroundColor: color}}>
      <div className={styles.scoreInfo}>
        <div className={styles.left}>{name}</div>
        <div className={styles.right}>
          <div className={styles.score}>
            {/* 有分数时才显示分数内容 */}
            <span>
              <span>{score} &nbsp;</span>
              {/* 有总分时才显示i总分 */}
              {totalScore && <span> / &nbsp;{totalScore} &nbsp; </span>}
            </span>
            {/* 最右侧的附加说明或者副标题类 */}
            <span>{remark}</span>
          </div>
          <div
            className={styles.scoreRemark}
          >
            <span>得分说明：</span>
            <span dangerouslySetInnerHTML={{ __html: (render && description) || '' }} />
            {/* <p>{covertChangeLineTag(description)}</p> */}
          </div>
        </div>
      </div>
      <div className={styles.resultRemark}>
        <span className={styles.resultLabel}>
          从测评结果来看{resultLabelAfter}：
        </span>
        <div
          className={styles.render}
          dangerouslySetInnerHTML={{
            __html: (render && result) || '',
          }}
        />
      </div>
      {children}
      {suggest && (
        <div className={styles.suggestRemark}>
          <span>整体建议说明：</span>
          <p>{covertChangeLineTag(suggest)}</p>
        </div>
      )}
      {resultAfter}
    </li>
  );
};

export default ScoreSituationItem;
