import React from "react";
import styles from './styles/index.module.less';
import { cls } from '~/utils/tools';
import { StarFilled } from '@ant-design/icons';

interface IStartRemarkProps {
  title: string;
  text: string;
  className?: string;
  customStyle?: React.CSSProperties;
}
// 带星星开头的说明
const StartRemark: React.FC<any> = (props: IStartRemarkProps) => {
  const { title, text, className, customStyle = {} } = props;

  return (
    <div className={cls([styles['start-remark-container'], className])} style={{ ...customStyle }}>
      <p className={styles.title}>
        {/* @ts-ignore */}
        <StarFilled className={styles['icon-class']} />
        {title}
      </p>
      <p className={styles.text}>{text}</p>
    </div>
  );
};

export default StartRemark;
