import React, { useCallback, useState, useRef } from 'react';
import { BLOCK_F_MAP } from '../logic/constants';
import { BLOCK_F_STRING, LEVEL_HIG_INT } from '../types';
import { CLIENT_TYPE } from '@/lib/answer/shared/types';

const useReport = (data: any, { clientType = CLIENT_TYPE.pc, safeHeight = 0 }) => {
  const _getMajorList = (source: any) => {
    if (source?.length <= 0) {
      return [];
    }
    return source?.reduce((pre: any, cur: { majorList: any }) => {
      return [...pre, ...cur.majorList];
    }, []);
  };

  /** 潜能专业推荐 */
  const academicPotentialRecommendMajorList =
    data?.academicPotentialRecommend?.categoryMajorList?.reduce((pre: any[], cur: any) => {
      const specialMajorList = (cur?.specialMajorList || [])?.map((it: any) => ({
        majorId: it.majorId,
        majorName: it.majorName,
      }));
      return [...pre, ...cur.majorList, ...specialMajorList];
    }, []);

  /** 职业兴趣推荐 */
  const categoryMajorRecommendMajorList = _getMajorList(
    data?.categoryMajorRecommend?.categoryMajorInfoList
  );
  /** 适合专业 */
  const suitableProfessionMajorList = data?.majorList;

  /** 价值观推荐 */
  const valuesRecommend = () => {
    const { occupationalValuesRecommend } = data || {};
    const { intentionResult, tacticalResult } =
      occupationalValuesRecommend || {};
    const listString = [...(intentionResult || []), ...(tacticalResult || [])]
      ?.map((v) => v.latitudeName)
      ?.join('、');
    return listString;
  };

  /** 价值观推荐职业 */
  const OFFSET_SIZE = clientType === CLIENT_TYPE.pc ? 22 : 14;
  const occupationals = `${
    data?.occupationalValuesRecommend?.occupationals?.length > OFFSET_SIZE
      ? `${data?.occupationalValuesRecommend?.occupationals?.slice(
          0,
          OFFSET_SIZE
        )}...`
      : data?.occupationalValuesRecommend?.occupationals || ''
  }`;
  const showOccupationalsMore =
    data?.occupationalValuesRecommend?.occupationals?.length > OFFSET_SIZE;
  const currentTotalLevel =
    data?.decisionMakingEfficacyRecommend?.currentTotalLevel;

  const hasSuitableProfession = Boolean(
    suitableProfessionMajorList?.length ||
      academicPotentialRecommendMajorList?.length ||
      categoryMajorRecommendMajorList?.length
  );

  const toBlock = (key: string) => {
    const element = document.getElementById(key);
    const tabBar = document.getElementById('tabBar');
    const mHeaderOffset = 40 + tabBar?.offsetHeight! + safeHeight; // 导航栏高度 + tab高度
    const headerOffset = clientType === CLIENT_TYPE.h5 ? mHeaderOffset : 0; // 导航栏高度
    if (element) {
      const elementPosition =
        element.getBoundingClientRect().top + window.scrollY;
      const offsetPosition = elementPosition - headerOffset;
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  const list = useCallback(() => {
    const visibleMap = {
      [BLOCK_F_STRING.overview]: true,
      [BLOCK_F_STRING.suitableProfession]: hasSuitableProfession,
      [BLOCK_F_STRING.professionalPotential]:
        Object.values(data?.academicPotentialRecommend || {}).length > 0,
      [BLOCK_F_STRING.vocationalInterest]:
        Object.values(data?.categoryMajorRecommend || {}).length > 0,
      [BLOCK_F_STRING.professionalValues]:
        Object.values(data?.occupationalValuesRecommend || {}).length > 0,
      [BLOCK_F_STRING.professionalDecisionMakingEfficacy]:
        Object.values(data?.decisionMakingEfficacyRecommend || {}).length > 0,
      [BLOCK_F_STRING.conclusion]: true,
    };

    const cList = Object.values(BLOCK_F_MAP)?.map((v) => ({
      label: v.label,
      key: v.value,
      visible: visibleMap[v.value],
    }));

    return cList.filter((v) => v.visible);
  }, [hasSuitableProfession, data]);

  return {
    state: {
      academicPotentialRecommendMajorList,
      categoryMajorRecommendMajorList,
      suitableProfessionMajorList,
      valuesRecommend: valuesRecommend(),
      showOccupationalsMore,
      occupationals,
      hasSuitableProfession,
      currentTotalLevel,
      list: list(),
    },
    methods: {
      toBlock,
    },
  };
};

export default useReport;
