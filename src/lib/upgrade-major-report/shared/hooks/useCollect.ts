import { useRequest, useDeepCompareEffect, useMount } from 'ahooks';
import { CLIENT_TYPE } from '@/lib/answer/shared/types';
import api from '~/service/psychology/upgradeMajorReport';
import SafeLogger from '~/utils/safe-logger';

const useCollect = (data: any, { clientType = CLIENT_TYPE.pc }) => {


   /** 获取关注总数 */
   const {
    runAsync: runCollectTotal,
    data: { data: collectTotal = 0 } = {},
  } = useRequest(api.getCount, {
    manual: true,
    onError: (error) => {
      SafeLogger?.baseLogger?.error('async-collect-total-error', {
        reason: '获取关注总数异常',
        error
      });
    },
  });

  /** 获取关注列表的关注状态 */
  const {
    runAsync: runCollectList,
    data: { data: collectList = [] } = {},
  } = useRequest(api.getCollectList, {
    manual: true,
    onError: (error) => {
      SafeLogger?.baseLogger?.error('async-collect-list-error', {
        reason: '获取关注列表的关注状态异常',
        error
      });
    },
  });
  /** 查询单个状态 */
  const {
    runAsync: runCollectOne,
  } = useRequest((payload: any) => api.getCollectList({
    contentType: payload.contentType,
    contentIds: [payload.contentId],
  }), {
    manual: true,
    onError: (error) => {
      SafeLogger?.baseLogger?.error('async-collect-one-error', {
        reason: '查询单个状态关注异常',
        error
      });
    },
  });

  /** 添加关注 */
  const {
    runAsync: runAddCollect,
    loading: addCollectLoading,
  } = useRequest(api.addCollect, {
    manual: true,
    onError: (error) => {
      SafeLogger?.baseLogger?.error('async-collect-add-error', {
        reason: '添加关注异常',
        error
      });
    },
  });

  /** 取消关注 */
  const {
    runAsync: runCancelCollect,
    loading: cancelCollectLoading,
  } = useRequest(api.cancelCollect, {
    manual: true,
    onError: (error) => {
      SafeLogger?.baseLogger?.error('async-collect-cancel-error', {
        reason: '取消关注数异常',
        error
      });
    },
  });

  useMount(() => {
    runCollectTotal({
      contentType: 4,
    });
  });

  useDeepCompareEffect(() => {
    runCollectList({
      contentType: 4,
      contentIds: data,
    });
  }, [data]);

  return {
    state: {
      collectList,
      collectTotal,
    },
    run: {
      runAddCollect,
      runCancelCollect,
      runCollectOne,
      runCollectTotal,
    },
  };
};

export default useCollect;
