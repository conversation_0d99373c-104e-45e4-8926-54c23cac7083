@viewportMWidth: 375px;


.conclusion-wrapper {
  display: flex;
  .left,
  .right {
    width: 50%;
    .list {
      display: flex;
      flex-direction: column;
      padding: 16px;
      width: 100%;
      height: 130px;
      background: #fff6e6;
      border-radius: 8px;
      > ul{
        flex: 1;
      }
      li {
        display: flex;
        align-items: flex-start;
        font-weight: 400;
        font-size: 13px;
        color: #944a0f;
        line-height: 20px;
        > span {
          flex: 1;
        }
        &::before {
          line-height: 20px;
          font-size: 30px;
          display: block;
          content: '·';
          width: 16px;
          color: #944a0f;
        }
      }
    }
  }
  .left {
    margin-right: 20px;
  }
  .re-test {
    cursor: pointer;
    align-self: flex-end;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    color: #ffffff;
    width: 100px;
    height: 28px;
    background-image: linear-gradient(270deg, #fdac57 0%, #fc8103 99%);
    border-radius: 32px;
  }
}
.link {
  position: relative;
  z-index: 1;
  display: block;
  cursor: pointer;
  width: 1024px;
  max-width: 100vw;
  margin: -20px auto 0;
}

@media screen and (max-width: 768px) {
  .conclusion-wrapper {
    display: flex;
    flex-direction: column;
    .left,
    .right {
      width: 100%;
      .list {
        padding: 0;
        background: transparent;
        min-height: auto;
      }
    }
    .left {
      margin-right: 0;
      padding-bottom: (12px / @viewportMWidth) * 100vw;
      border-bottom: 1px dashed #c18255;
    }
    .c-wrapper {
      margin-top: (12px / @viewportMWidth) * 100vw;
      margin-bottom: 6px;
      :global {
        .card-sub-title-inner {
          font-size: 14px;
          color: #944a0f;
          &::after {
            top: -4px;
            left: -5px;
            width: 16px;
            height: 16px;
            opacity: 0.4;
            border-radius: 50%;
            background-image: linear-gradient(
              -45deg,
              rgba(255, 191, 84, 0) 0%,
              #ff7609 100%
            );
          }
        }
      }
    }
    .re-test {
      margin-top: (8px / @viewportMWidth) * 100vw;
      width: 100%;
      height: (32px / @viewportMWidth) * 100vw;
    }
  }
  .link {
    position: relative;
    z-index: 1;
    display: block;
    cursor: pointer;
    margin: (-30px / @viewportMWidth) * 100vw (-12px / @viewportMWidth) * 100vw 0;
  }
}
