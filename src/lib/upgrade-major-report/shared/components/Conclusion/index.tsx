import React from 'react';
import Card from '../UI/Card';
import { BLOCK_F_STRING, IComponentProps } from '../../types';
import { BLOCK_F_MAP, Conclusion_FirstBlock, Conclusion_SecondBlock } from '../../logic/constants';
import { CLIENT_TYPE } from '~/lib/answer/shared/types';

import LinkPng from '@/assets/upgrade-major-report/link.png';
import H5LinkPng from '@/assets/upgrade-major-report/h5-link.png';
import { openRoute } from '@/utils/tools';

import styles from './index.module.less';

const Conclusion: React.FC<IComponentProps> = (props) => {
  const { clientType = CLIENT_TYPE.pc, data, basicData } = props;
  const constants = BLOCK_F_MAP[BLOCK_F_STRING.conclusion];
  const [first, second] = constants.children || [];

  const pic = clientType === CLIENT_TYPE.pc ? LinkPng : H5LinkPng;

  const hasSuitableProfession = data?.hasSuitableProfession;

  const toAnswer = () => {
    if(clientType === CLIENT_TYPE.pc) {
      window.open(`${origin}/psychology-service/answer?evaluationTemplateType=upgrade_major&clientType=1&multipleAnswer=1`);
      return
    }
    openRoute({
      domain: 'career_plann',
      action: 'evaluation_self_cognition',
      params: {
        templateType: 'upgrade_major',
      },
    });
  };

  return (
    <>
      <Card
        id={BLOCK_F_MAP[BLOCK_F_STRING.conclusion].value}
        description={BLOCK_F_MAP[BLOCK_F_STRING.conclusion].descriptions[0]}
      >
        <div className={styles['conclusion-wrapper']}>
          <div className={styles.left}>
            <Card.SubTitle
              name={first.label}
              className={styles['c-wrapper']}
              id={first.value}
            />
            <div className={styles.list}>
              <ul>
                {Conclusion_FirstBlock.map((v, i) => (
                  <li key={i}>
                    <span>{v}</span>
                  </li>
                ))}
              </ul>
              {!hasSuitableProfession && (
                <div className={styles['re-test']} onClick={toAnswer}> 重新测评 </div>
              )}
            </div>
          </div>
          <div className={styles.right}>
            <Card.SubTitle
              name={second.label}
              className={styles['c-wrapper']}
              id={second.value}
            />
            <div className={styles.list}>
              <ul>
                {Conclusion_SecondBlock.map((v, i) => (
                  <li key={i}>
                    <span>{v}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </Card>
      <img
        src={pic}
        onClick={() => {
          if (clientType === CLIENT_TYPE.pc) {
            const url = `${origin}/career/careerlibs/index.html#/volunteer-new?queryType=1`;
            window.open(url);
          } else {
            openRoute({
              domain: 'career_plann',
              action: 'voluntary_report',
            });
          }
        }}
        className={styles.link}
        alt=""
      />
    </>
  );
};

export default Conclusion;
