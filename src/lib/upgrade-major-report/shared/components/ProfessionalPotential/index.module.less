@viewportMWidth: 375px;

.professional-potential-wrapper{
  .tip{
    margin: 17px 0 13px;
    font-weight: 400;
  }
  .warn {
    font-weight: 500;
    color: #fb9300;
  }
  .wrapper{
    margin-bottom: 20px;
    &:last-child{
      margin-bottom: 0;
    }
  }
  .all-button{
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 104px;
    height: 28px;
    font-size: 12px;
    border: 1px solid #FB9300;
    border-radius: 32px;
    color: #FB9300;
    font-weight: 500;
  }
}

/** 响应式复用 **/
@media screen and (max-width: 768px) {
  .professional-potential-wrapper{
    .wrapper{
      margin-bottom: (12px / @viewportMWidth) * 100vw;
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
}