import React, { useState } from 'react';
import { Tabs, Table } from 'antd';

import { BLOCK_F_STRING, IComponentProps } from '../../types';
import { BLOCK_F_MAP } from '../../logic/constants';
import { CLIENT_TYPE } from '~/lib/answer/shared/types';

import Card from '../UI/Card';
import MajorList from '../UI/MajorList';
import List from '../UI/List';

import styles from './index.module.less';

const ProfessionalPotential: React.FC<IComponentProps> = (props) => {
  const { clientType = CLIENT_TYPE.pc, data, state } = props;
  const constants = BLOCK_F_MAP[BLOCK_F_STRING.professionalPotential];
  const [first, second] = constants.children || [];

  const [fActiveKey, setFActiveKey] = useState(
    data?.categoryMajorList?.[0]?.categoryId
  );
  const [sActiveKey, setSActiveKey] = useState(
    data?.categoryMajorList?.[0]?.categoryId
  );

  const onChange = (key: string) => {
    setFActiveKey(key);
  };

  const sOnChange = (key: string) => {
    setSActiveKey(key);
  };

  const items = data?.categoryMajorList?.map((v: any) => {
    const isChildren = !Boolean(v.categoryMajorLatitudeScoreList?.length);
    return {
      label: v.categoryName,
      key: v.categoryId,
      children: (
        <List.Group accordion>
          {isChildren ? (
            <>
              {v?.majorList?.map((mjChi: any, mjChiIndex: string) => (
                <List
                  key={mjChiIndex}
                  dataSource={{
                    majorList: [
                      {
                        majorId: mjChi.majorId,
                        majorName: mjChi.majorName,
                      },
                    ],
                    categoryMajorLatitudeScoreList:
                      mjChi?.categoryMajorLatitudeScoreList,
                  }}
                  wrapperClass={styles['wrapper']}
                  name={`first_children_${mjChiIndex}`}
                  clientType={clientType}
                />
              ))}
            </>
          ) : (
            <List
              dataSource={v}
              wrapperClass={styles['wrapper']}
              name={`first_${v.categoryId}`}
              clientType={clientType}
            />
          )}

          {v.specialMajorList?.length > 0 && (
            <div className={styles['tip']}>
              注意：<span className={styles['warn']}>以下专业对应高中学科知识</span>
              与同专业大类下其他专业
              <span className={styles['warn']}>差异较大</span>,
              因此单独呈现。
            </div>
          )}
          {v.specialMajorList?.map((it: any, id: number) => (
            <List
              key={id}
              wrapperClass={styles['wrapper']}
              name={`second_${it.categoryId}_${id}`}
              dataSource={{
                majorList: [
                  {
                    majorId: it.majorId,
                    majorName: it.majorName,
                  },
                ],
                categoryMajorLatitudeScoreList:
                  it?.categoryMajorLatitudeScoreList,
              }}
            />
          ))}
        </List.Group>
      ),
    };
  });

  const sItems = data?.categoryMajorList?.map((v: any) => {
    const specialMajorList = (v?.specialMajorList || [])?.map((it: any) => ({
      majorId: it.majorId,
      majorName: it.majorName,
    }));
    const majorList = v.majorList || [];
    return {
      label: v.categoryName,
      children: (
        <MajorList
          dataSource={[...majorList, ...specialMajorList]}
          clientType={clientType}
        />
      ),
      key: v.categoryId,
    };
  });

  const needShowMajor = data?.categoryMajorList?.reduce(
    (pre: any, cur: any) => {
      const specialMajorList = (cur?.specialMajorList || [])?.map(
        (it: any) => ({
          majorId: it.majorId,
          majorName: it.majorName,
        })
      );
      const majorList = cur.majorList || [];
      return [...pre, ...specialMajorList, ...majorList];
    },
    []
  );

  const { academicPotentialRecommendMajorList } = state || {};
  const description =
    academicPotentialRecommendMajorList?.length > 0
      ? BLOCK_F_MAP[BLOCK_F_STRING.professionalPotential].descriptions[0]
      : BLOCK_F_MAP[BLOCK_F_STRING.professionalPotential].descriptions[1];

  return (
    <Card
      id={BLOCK_F_MAP[BLOCK_F_STRING.professionalPotential].value}
      description={description}
    >
      <div className={styles['professional-potential-wrapper']}>
        {data?.categoryMajorList?.length > 0 ? (
          <>
            <Card.SubTitle name={first.label} id={first.value} />
            <div>
              <Tabs
                hideAdd
                onChange={onChange}
                activeKey={fActiveKey}
                type="card"
                items={items}
              />
            </div>
          </>
        ) : null}

        {needShowMajor?.length > 0 ? (
          <>
            <Card.SubTitle name={second.label} id={second.value} />
            <div>
              <Tabs
                hideAdd
                onChange={sOnChange}
                activeKey={sActiveKey}
                type="card"
                items={sItems}
              />
            </div>
          </>
        ) : null}
      </div>
    </Card>
  );
};

export default ProfessionalPotential;
