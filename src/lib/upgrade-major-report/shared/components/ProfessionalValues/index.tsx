import React, { useState } from 'react';
import Card from '../UI/Card';
import GreenCard from '../UI/GreenCard';
import { Highlight } from '../UI/List';

import { Tabs, Table, Tooltip } from 'antd';
import { BLOCK_F_STRING, IComponentProps } from '../../types';
import { BLOCK_F_MAP } from '../../logic/constants';
import { CLIENT_TYPE } from '~/lib/answer/shared/types';
import styles from './index.module.less';

import ValuesPic from '@/assets/upgrade-major-report/values-pic.png';
import H5ValuesPic from '@/assets/upgrade-major-report/h5-values-pic.png';

const ProfessionalValues: React.FC<IComponentProps> = (props) => {
  const { clientType = CLIENT_TYPE.pc, data } = props;
  const constants = BLOCK_F_MAP[BLOCK_F_STRING.professionalValues];
  const [first, second, third] = constants.children || [];

  const [activeKey, setActiveKey] = useState('intentionOccupational');

  const pic = clientType === CLIENT_TYPE.pc ? ValuesPic : H5ValuesPic;

  const onChange = (key: string) => {
    setActiveKey(key);
  };

  const source = [
    {
      label: '目的性价值观',
      key: 'intentionOccupational',
      dataSource: data?.intentionOccupationalList,
    },
    {
      label: '手段性价值观',
      key: 'tacticalOccupational',
      dataSource: data?.tacticalOccupationalList,
    },
  ];

  const items = source?.map((v) => ({
    label: v.label,
    key: v.key,
    children: (
      <Table
        size="middle"
        rowKey={(record) => record.latitudeId}
        columns={[
          {
            title: '价值观',
            width: clientType === CLIENT_TYPE.pc ? 112 : 72,
            align: 'center',
            dataIndex: 'latitudeName',
            key: 'latitudeName',
          },
          {
            title: '情况描述',
            align: 'center',
            dataIndex: 'description',
            key: 'description',
            render: (v) => {
              return (
                <div dangerouslySetInnerHTML={{ __html: v }} style={{ wordBreak: 'break-all', textAlign: 'left' }} />
              );
            },
          },
          {
            title: '程度',
            align: 'center',
            width: clientType === CLIENT_TYPE.pc ? 198 : 60,
            dataIndex: 'latitudeLevelName',
            key: 'latitudeLevelName',
            render: (_t, v) => {
              return (
                <Highlight value={v} showLine={clientType === CLIENT_TYPE.pc} />
              );
            },
          },
        ]}
        dataSource={v.dataSource}
        pagination={false}
        bordered={clientType === CLIENT_TYPE.h5}
      />
    ),
  }));
  /** 目的性价值观 */
  const intentionOccupationalStr = data?.intentionResult
  ?.map((v: any) => v.latitudeName)
  .join('、');
  /** 手段性价值观 */
  const tacticalOccupationalStr = data?.tacticalResult
    ?.map((v: any) => v.latitudeName)
    .join('、');

  const [f = {}, s = {}] = data?.tacticalResult || [];

  /** 目的性选择思路 */
  const intentionalAdviceList = data?.intentionalAdviceList || [];
  /**  手段性选择思路 */
  const tacticalAdviceList = data?.tacticalAdviceList || [];

  return (
    <Card
      id={BLOCK_F_MAP[BLOCK_F_STRING.professionalValues].value}
      description={
        BLOCK_F_MAP[BLOCK_F_STRING.professionalValues].descriptions[0]
      }
    >
      <div className={styles['professional-values-wrapper']}>
        <Card.SubTitle name={first.label} id={first.value} />
        <div className={styles.content}>
          <div className={styles.left}>
            <div className={styles.title}>
              你的目的性价值观
              <Tooltip
                arrowPointAtCenter
                overlayClassName={styles['tooltip-wrapper']}
                placement="bottomRight"
                title="目的性价值观是你职业发展的终极目标，代表了你希望实现的最终意义和价值。它像是你职业道路的终点，是你最终要达到的理想状态。比如，你可能希望通过职业带来社会的改变，或是获得个人成长和满足感。"
              >
                &nbsp;
                <i className="iconfont iconshengxuecebaogao-shujushuoming" />
              </Tooltip>
            </div>
            <div className={styles.value}>{intentionOccupationalStr }</div>
          </div>
          <div className={styles.center}>
            <div className={styles.title}>
              你的手段性价值观
              <Tooltip
                arrowPointAtCenter
                overlayClassName={styles['tooltip-wrapper']}
                placement="bottomRight"
                title="手段性价值观是你为实现目的性价值观而选择的具体方式，代表了你到达终点的路径。不同的人可能通过不同的方式去达到同一个目的，比如有些人选择当普通小学老师来影响社会，有些人则立志成为领导人来影响社会。"
              >
                &nbsp;
                <i className="iconfont iconshengxuecebaogao-shujushuoming" />
              </Tooltip>
            </div>
            <div className={styles.value}>{tacticalOccupationalStr}</div>
          </div>
          <div className={styles.right}>
            <div className={styles.text}>
              你未来会以
              <span style={{ color: '#056862', fontWeight: 600 }}>
                &nbsp;{intentionOccupationalStr}&nbsp;
              </span>
              作为自己选择职业的目的，为了实现这一目的，你会将
              <span style={{ color: '#944A0F', fontWeight: 600 }}>
                &nbsp;{f.latitudeName}&nbsp;
              </span>
              或
              <span style={{ color: '#944A0F', fontWeight: 600 }}>
                &nbsp;{s.latitudeName}&nbsp;
              </span>
              作为自己选择职业的条件。
            </div>
            <img className={styles.pic} src={pic} alt="" />
          </div>
        </div>
        <Card.SubTitle name={second.label} id={second.value} />
        <Tabs
          hideAdd
          onChange={onChange}
          activeKey={activeKey}
          type="card"
          items={items}
        />
        <Card.SubTitle name={third.label} id={third.value} />
        <GreenCard clientType={clientType} title="1）职业选择思路" className={styles['first-card']}>
          <div className={styles['first-card-inner']}>
            <div className={styles.right}>
              <div className={styles.list}>
                <div className={styles.title}>
                  ① 对于{intentionOccupationalStr}
                  这一目的性价值观，你需要考虑：
                </div>
                <ul>
                  {intentionalAdviceList.map((v: string, i: number) => (
                    <li key={i}>
                      <span>{v}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className={styles.left}>
              <div className={styles.list}>
                <div className={styles.title}>
                  ② 同时在做选择时，你需要考虑职业符合以下条件：
                </div>
                <ul>
                  {tacticalAdviceList.map((v: string, i: number) => (
                    <li key={i}>
                      <span>{v}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </GreenCard>
        <GreenCard clientType={clientType} title="2）职业推荐方向" className={styles['second-card']}>
          <div className={styles['content']}>{data?.occupationals}</div>
          <div className={styles['tips']}>
            {data.notice}
          </div>
        </GreenCard>
      </div>
    </Card>
  );
};

export default ProfessionalValues;
