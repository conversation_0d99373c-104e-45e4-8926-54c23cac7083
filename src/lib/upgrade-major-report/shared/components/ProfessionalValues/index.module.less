@viewportMWidth: 375px;

.professional-values-wrapper {
  .content {
    display: flex;
    .left,
    .center {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      width: 200px;
      height: 102px;
      padding: 16px 0 12px;
      margin-right: 12px;
      background-image: linear-gradient(179deg, #dafcf3 0%, #a8f1ed 50%);
      border-radius: 8px;
      .title {
        align-items: center;
        font-weight: 500;
        font-size: 14px;
        line-height: 1;
        color: #3c928c;
        :global {
          .iconfont {
            font-size: 14px;
            cursor: pointer;
          }
        }
      }
      .value {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 16px;
        color: #056862;
        width: 176px;
        height: 48px;
        background: #ffffff;
        border-radius: 8px;
      }
    }
    .center {
      background-image: linear-gradient(179deg, #faf8db 0%, #ffdfbf 50%);
      .title {
        color: #c18255;
      }
      .value {
        color: #944a0f;
      }
    }
    .right {
      position: relative;
      width: 536px;
      background-image: linear-gradient(180deg, #ecf4d0 0%, #ffffff 100%);
      border-radius: 8px;
      .text{
        width: 300px;
        padding: 12px 0 12px 12px;
        color: #50596f;
        font-size: 13px;
        font-weight: 400;
      }
      .pic {
        position: absolute;
        top: 0;
        right: 0;
        width: 250px;
      }
    }
  }
  .first-card{
    margin-bottom: 12px;
    .first-card-inner{
      display: flex;
      .right,
      .left{
        width: 50%;
        .list{
          .title{
            font-weight: 600;
            font-size: 14px;
            color: #50596F;
            margin-bottom: 8px;
          }
          > ul{
            flex: 1;
          }
          li {
            display: flex;
            align-items: flex-start;
            font-weight: 400;
            font-size: 13px;
            color: #5B6F8C;
            flex: 1;
            line-height: 20px;
            > span {
              flex: 1;
            }
            &::before{
              line-height: 20px;
              font-size: 30px;
              display: block;
              content: '·';
              width: 16px;
              opacity: 0.8;
              color: #598A67;
            }
          }
        }
      }
      .right{
        margin-right: 24px;
      }
    }
  }
  .second-card{
    .content{
      font-weight: 500;
      font-size: 14px;
      color: #50596F;
    }
    .tips{
      font-weight: 400;
      font-size: 13px;
      color: #944A0F;
    }
  }
}
.tooltip-wrapper{
  :global{
    .ant-tooltip-inner{
      width: 180px;
      padding: 10px;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 8px;
    }
  }
}

@media screen and (max-width: 768px) {
  .professional-values-wrapper {
    .content {
      flex-wrap: wrap;
      .left,
      .center {
        padding: (12px / @viewportMWidth) * 100vw 0 (6px / @viewportMWidth) * 100vw;
        width: (160px / @viewportMWidth) * 100vw;
        height: (85px / @viewportMWidth) * 100vw;
        margin-right: (6px / @viewportMWidth) * 100vw;
        .title {
          font-weight: 500;
          font-size: 13px;
          color: #3c928c;
          :global {
            .iconfont {
              font-size: 13px;
            }
          }
        }
        .value {
          font-size: 13.75px;
          color: #056862;
          width: (146px / @viewportMWidth) * 100vw;
          height: (42px / @viewportMWidth) * 100vw;
          background: #ffffff;
          border-radius: (8px / @viewportMWidth) * 100vw;
        }
      }
      .center {
        background-image: linear-gradient(179deg, #faf8db 0%, #ffdfbf 50%);
        margin-right: 0;
        .title {
          color: #c18255;
        }
        .value {
          color: #944a0f;
        }
      }
      .right {
        position: relative;
        margin-top: (136px / @viewportMWidth) * 100vw;
        width: 100%;
        background: rgba(222, 235, 182, 0.5);
        border-radius: (8px / @viewportMWidth) * 100vw;
        .text{
          width: auto;
          color: #50596f;
          font-weight: 400;
          line-height: 20px;
          padding: (12px / @viewportMWidth) * 100vw;
          color: #50596f;
          font-size: 13px;
          font-weight: 400;
        }
        .pic {
          position: absolute;
          top: 0;
          right: 0;
          width: auto;
          transform: translateY(-75%);
          margin: 0 (-12px / @viewportMWidth) * 100vw;
          height: (170px / @viewportMWidth) * 100vw;
        }
      }
    }
    .first-card{
      margin-bottom: (12px / @viewportMWidth) * 100vw;
      .first-card-inner{
        display: flex;
        flex-direction: column;
        .right,
        .left{
          width: 100%;
          .list{
            .title{
              font-weight: 600;
              font-size: 13px;
              color: #50596F;
              margin-bottom: (6px / @viewportMWidth) * 100vw;
            }
            li{
              display: flex;
              align-items: flex-start;
              font-weight: 400;
              font-size: 12px;
              color: #5B6F8C;
              flex: 1;
              &::before{
                line-height: 20px;
                font-size: 30px;
                display: block;
                content: '·';
                width: 16px;
                opacity: 0.8;
                color: #598A67;
                // border-radius: 50%;
              }
            }
          }
        }
        .right{
          margin-right: 0;
        }
      }
    }
    .second-card{
      .content{
        font-weight: 500;
        font-size: 13px;
        color: #50596F;
      }
      .tips{
        font-weight: 400;
        font-size: 12px;
        color: #944A0F;
      }
    }
    :global{
      .ant-table-bordered{
        .ant-table-content > table{
          border-top-color: #E1E6F2 !important;
        }
        .ant-table-container{
          border-left-color: #E1E6F2 !important;
        }
        .ant-table-cell{
          border-color: #E1E6F2;
          border-right-color: #E1E6F2 !important;
        }
      }
    }
  }
}
