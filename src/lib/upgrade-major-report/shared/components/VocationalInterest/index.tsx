import React, { useState } from 'react';
import { Tabs } from 'antd';

import { BLOCK_F_STRING, IComponentProps } from '../../types';
import { BLOCK_F_MAP, CODE_MAP_DES_KEY } from '../../logic/constants';
import { CLIENT_TYPE } from '~/lib/answer/shared/types';
import ChartRadar from '@/components/career/ChartRadar';
import styles from './index.module.less';
import deepClone from 'lodash/cloneDeep';

import Card from '../UI/Card';
import MajorList from '../UI/MajorList';

const VocationalInterest: React.FC<IComponentProps> = (props) => {
  const { clientType = CLIENT_TYPE.pc, data, state } = props;
  const constants = BLOCK_F_MAP[BLOCK_F_STRING.vocationalInterest];
  const [fActiveKey, setFActiveKey] = useState(
    data?.categoryMajorInfoList?.[0]?.categoryId
  );

  const indicator =
    data?.careerInterestScores?.map((v: any) => ({
      name: `${v.latitudeName}(${v.latitudeLabel})`,
      value: v.latitudeScore,
      max: 100,
      label: v.latitudeName,
      key: v.latitudeLabel,
    })) || [];

  const options = {
    radar: {
      indicator,
      nameGap: 4,
      name: {
        formatter: (value: number, indicator: any) => {
          const needN =
            ['I', 'A', 'E', 'C'].includes(indicator.key) &&
            clientType === CLIENT_TYPE.h5;
          return `${value}{green|${needN ? '\n' : ''}${indicator.value}}`;
        },
        textStyle: {
          rich: {
            green: {
              color: '#7077FF',
            },
          },
          overflow: 'truncate', // 防止文本太长溢出
          color: '#333333',
        },
      },
      splitNumber: 5,
      axisLine: {
        lineStyle: {
          color: 'rgba(160,188,255, 1)',
        },
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(160,188,255,0.5)', '#ffffff'],
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: 'rgba(160,188,255,1)',
        },
      },
      center: ['50%', '50%'],
      radius: 100,
    },
    series: {
      type: 'radar',
      itemStyle: {
        normal: {
          opacity: 1,
          color: '#A0BCFF',
          lineStyle: {
            color: '#7077FF',
          },
        },
      },
      data: [
        {
          value: indicator.map((v: any) => v.value),
          areaStyle: {
            opacity: 0.7,
            color: '#A0BCFF',
          },
        },
      ],
    },
  };

  const sortIndicator =
    deepClone(indicator)
      .sort((a: { value: number }, b: { value: number }) => b.value - a.value)
      ?.slice(0, 3) || [];
  const [first, second, third] = constants.children || [];

  const [activeH, setActiveH] = useState(sortIndicator[0]?.key);

  const sOnChange = (key: string) => {
    setFActiveKey(key);
  };

  const sItems = data?.categoryMajorInfoList?.map(
    (v: { categoryName: any; majorList: any; categoryId: any }) => ({
      label: v.categoryName,
      children: <MajorList dataSource={v.majorList} clientType={clientType} />,
      key: v.categoryId,
    })
  );

  const { categoryMajorRecommendMajorList } = state || {};
  const description =
    categoryMajorRecommendMajorList?.length > 0
      ? BLOCK_F_MAP[BLOCK_F_STRING.vocationalInterest].descriptions[0]
      : BLOCK_F_MAP[BLOCK_F_STRING.vocationalInterest].descriptions[1];

  return (
    <Card
      id={BLOCK_F_MAP[BLOCK_F_STRING.vocationalInterest].value}
      description={description}
    >
      <div className={styles['vocational-interest-wrapper']}>
        <div className={styles['block-wrapper']}>
          <div className={styles.left}>
            <Card.SubTitle name={first.label} id={first.value} />
            <div className={styles.content}>
              <div className={styles.result}>
                <div className={styles.title}>
                  {sortIndicator?.map((v: { key: any }) => v.key)}
                </div>
                <div className={styles.desc}>
                  {sortIndicator
                    ?.map((v: { label: any }) => v.label)
                    ?.join('+')}
                </div>
              </div>
              {data?.categoryMajorInfoList && (
                <ChartRadar
                  areaColor="#A0BCFF"
                  fullDisplay={true}
                  height={276}
                  customOption={options}
                  wrapperStyle={{
                    margin: '0 auto',
                    width: '100%',
                  }}
                />
              )}
            </div>
          </div>
          <div className={styles.right}>
            <Card.SubTitle name={second.label} id={second.value} />
            <div className={styles.content}>
              <div className={styles.result}>
                <div className={styles['hig-tab']}>
                  {sortIndicator.map(
                    (v: {
                      key: React.Key | null | undefined;
                      name:
                        | boolean
                        | React.ReactChild
                        | React.ReactFragment
                        | React.ReactPortal
                        | null
                        | undefined;
                    }) => (
                      <div
                        className={`${styles.hig} ${
                          activeH === v.key ? styles.active : ''
                        }`}
                        key={v.key}
                        onClick={() => setActiveH(v.key)}
                      >
                        {v.name}
                      </div>
                    )
                  )}
                </div>
              </div>
              <div className={styles.list}>
                <div className={styles.title}>基本特点：</div>
                <ul>
                  {CODE_MAP_DES_KEY[(activeH) as keyof typeof CODE_MAP_DES_KEY]?.map(
                    (
                      v: string,
                      i: number
                    ) => (
                      <li key={i}>
                        <span>{v}</span>
                      </li>
                    )
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
        {data?.categoryMajorInfoList?.length > 0 ? 
          <>
            <Card.SubTitle name={third.label} id={third.value} />
            <div>
              <Tabs
                hideAdd
                onChange={sOnChange}
                activeKey={fActiveKey}
                type="card"
                items={sItems}
              />
            </div>
          </>
        : null }
      </div>
    </Card>
  );
};

export default VocationalInterest;
