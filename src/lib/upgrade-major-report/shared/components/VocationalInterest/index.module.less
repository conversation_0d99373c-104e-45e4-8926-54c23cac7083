@viewportMWidth: 375px;

.vocational-interest-wrapper {
  .block-wrapper {
    display: flex;
    .left,
    .right {
      width: 50%;
      margin-right: 20px;
      .content{
        width: 100%;
        height: 350px;
        background: #F7F9FB;
        border-radius: 8px;
        overflow: hidden;
        .result{
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 12px 0;
          margin: 0 20px;
          border-bottom: 1px dashed rgba(80, 89, 111, 0.2);
          .title{
            font-weight: 600;
            font-size: 30px;
            color: #50596F;
            line-height: 30px;
          }
          .desc{
            margin-top: 4px;
            font-weight: 400;
            font-size: 14px;
            color: #5B6F8C;
            line-height: 14px;
          }
          .hig-tab{
            height: 49px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            .hig{
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 102px;
              height: 32px;
              background: #F7F9FB;
              border-radius: 16px;
              font-weight: 500;
              font-size: 14px;
              color: #8B97B6;
              &.active{
                color: #fff;
                background: #5A8BFF;
              }
            }
          }
        }
        .list{
          padding: 16px 20px;
          .title{
            font-weight: 600;
            font-size: 14px;
            color: #50596F;
            margin-bottom: 12px;
          }
          > ul{
            margin-left: -2px;
            padding-left: 16px;
            list-style: disc;
            li{
              font-weight: 400;
              font-size: 13px;
              color: #5B6F8C;
              letter-spacing: 0;
            }
          }
        }  
      }
    }
    .right{
      margin-right: 0px;
    }
  }
  .all-button{
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 104px;
    height: 28px;
    font-size: 12px;
    border: 1px solid #FB9300;
    border-radius: 32px;
    color: #FB9300;
    font-weight: 500;
  }
}


@media screen and (max-width: 768px) {
  .vocational-interest-wrapper {
    .block-wrapper {
      display: block;
      .left,
      .right {
        width: 100%;
        margin: 0;
        .content{
          width: 100%;
          height: auto;
          background: #fff;
          .result{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: (12px / @viewportMWidth) * 100vw 0 (14px / @viewportMWidth) * 100vw;
            border-radius: (8px / @viewportMWidth) * 100vw;
            margin: 0;
            border: none;
            background: #F7F8FA;
            .title{
              font-weight: 600;
              font-size: 30px;
              color: #50596F;
              line-height: 30px;
            }
            .desc{
              margin-top: 4px;
              font-weight: 400;
              font-size: 14px;
              color: #5B6F8C;
              line-height: 14px;
            }
          }
        }
      }
      .right{
        .content{
          width: 100%;
          height: auto;
          background: #fff;
          .result{
            padding: 0;
            height: (40px / @viewportMWidth) * 100vw;
            border-radius: (20px / @viewportMWidth) * 100vw;
          }
          .list{
            .title{
              margin-bottom: (10px / @viewportMWidth) * 100vw ;
            }
            padding:  (12px / @viewportMWidth) * 100vw;
            margin-top: (6px / @viewportMWidth) * 100vw;
            min-height: (204px / @viewportMWidth) * 100vw;
            background: #F7F9FB;
            border-radius: (8px / @viewportMWidth) * 100vw;
          }
        }
      }
    }
  }
}