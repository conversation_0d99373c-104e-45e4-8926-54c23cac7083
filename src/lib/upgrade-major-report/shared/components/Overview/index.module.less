@viewportMWidth: 375px;

.overview-wrapper {
  display: flex;
  &.wrap {
    flex-wrap: wrap;
    > div {
      flex: 0 0 100%;
    }
    .empty-wrapper {
      width: 100%;
    }
  }
  > div {
    flex: 1;
  }
  .empty-wrapper-layout {
    .empty-wrapper {
      width: 470px;
      height: 130px;
      max-width: 100%;
      background: #fef4e5;
      border: 1px solid #fb930033;
      border-radius: 8px;
      padding: 16px 16px 0;
      .empty-top {
        color: #50596f;
        font-size: 13px;
        font-weight: 400;
        line-height: 18px;
        padding-bottom: 12px;
        border-bottom: 1px dashed #fb9300;
      }
      .empty-bottom {
        padding: 12px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          font-weight: 500;
          font-size: 14px;
          color: #50596f;
          line-height: 14px;
        }
        .btn {
          cursor: pointer;
          line-height: 14px;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 92px;
          height: 28px;
          background-image: linear-gradient(270deg, #fdac57 0%, #fc8103 99%);
          border-radius: 32px;
        }
      }
    }
    .warn {
      color: #fb9300;
    }
    .reason {
      margin-top: 8px;
      a {
        font-weight: 400;
        font-size: 12px;
        color: #5a8bff;
        text-decoration: underline;
      }
    }
  }

  .has-one-wrapper {
    display: flex;
    margin-right: 10px;
    .right,
    .left {
      display: flex;
      flex-direction: column;
      width: 230px;
      height: 232px;
      margin-right: 10px;
      background: #fffae7;
      border-radius: 8px;
      font-weight: 400;
      font-size: 14px;
      color: #986d00;
      line-height: 14px;
      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 44px;
        background-image: linear-gradient(270deg, #fddd67 0%, #fcc500 100%);
        border-radius: 8px;
        mix-blend-mode: multiply;
        font-weight: 600;
        font-size: 16px;
        color: #ffffff;
        line-height: 16px;
        text-shadow: 0 3px 10px #e09700;
      }
      .list {
        display: flex;
        flex-direction: column;
        padding: 16px;
        flex: 1;
        overflow: hidden;
        > ul {
          flex: 1;
          overflow: hidden;
        }
        li {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          overflow: hidden;
          a {
            flex: 1;
            color: inherit;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            &:hover {
              text-decoration: underline;
            }
          }
          &::before {
            line-height: 20px;
            font-size: 30px;
            display: block;
            content: '·';
            width: 12px;
            opacity: 0.8;
          }
        }
        .more {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          align-self: center;
          width: 96px;
          height: 28px;
          border: 1px solid #986d00;
          border-radius: 14px;
        }
      }
      .empty {
        padding: 0 16px;
        flex: 1;
        line-height: 1.5;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .right {
      color: #d45e00;
      background: #fef4eb;
      .title {
        background-image: linear-gradient(270deg, #fdba68 0%, #fc8800 99%);
      }
      .list {
        .more {
          border: 1px solid #d45e00;
        }
      }
    }
  }
  .has-mixed-wrapper {
    position: relative;
    display: flex;
    .list {
      width: 180px;
      position: absolute;
      top: 42px;
      left: 124px;
      .title {
        position: relative;
        height: 16px;
        font-weight: 700;
        font-size: 16px;
        line-height: 16px;
        text-align: center;
        margin-bottom: 16px;
        &::before{
          position: absolute;
          content: '';
          display: block;
          width: 100px;
          bottom: -4px;
          height: 8px;
          opacity: 0.15;
          left: 50%;
          transform: translateX(-50%);
          background: #986D00;
          border-radius: 8px;
          border-radius: 6px;
          mix-blend-mode: multiply;
        }
      }
      li {
        display: flex;
        font-weight: 400;
        font-size: 13px;
        overflow: hidden;
        a {
          flex: 1;
          color: inherit;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:hover {
            text-decoration: underline;
          }
        }
        &::before {
          line-height: 14px;
          font-size: 30px;
          display: block;
          content: '·';
          width: 12px;
          opacity: 0.8;
        }
      }
      .more {
        margin: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 96px;
        height: 28px;
        border: 1px solid #986d00;
        border-radius: 14px;
      }
    }
    .right,
    .left {
      color: #986d00;
      width: 640px;
      height: 260px;
      border-radius: 50%;
      background: rgba(252, 231, 154, 0.3);
      mix-blend-mode: multiply;
      border: 1px solid #ffc776;
      display: flex;
    }
    .center {
      z-index: 10;
      color: #c5452c;
      width: 180px;
      position: absolute;
      top: 42px;
      left: 50%;
      transform: translate(-50%);
      .list {
        position: static;
        .title{
          &::before{
            background-color: #C5452C;
          }
        }
        .more {
          border: 1px solid #c5452c;
        }
      }
    }
    .right {
      color: #d45e00;
      position: absolute;
      top: 0;
      right: 0;
      background: rgba(253, 201, 155, 0.3);;
      border: 1px solid #ffceae;
      .list {
        left: auto;
        right: 124px;
        .title{
          &::before{
            background-color: #D45E00;
          }
        }
        .more {
          border: 1px solid #d45e00;
        }
      }
    }
  }
  .right-wrapper {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    &.flex {
      .values-wrapper{
        margin-right: 20px;
      }
      > div {
        width: 100%;
        flex: 1;
      }
    }
    .values-wrapper {
      .card-wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-top: 4px;
        .left,
        .right {
          flex: 1;
          padding: 12px;
          height: 88px;
          background: #f0f5ff;
          border-radius: 8px;
          font-size: 13px;
          color: #6b7884;
          margin-right: 10px;
          .content {
            font-weight: 500;
            font-size: 13px;
            color: #5a8bff;
          }
        }
        .right {
          position: relative;
          margin-right: 0;
          .more {
            cursor: pointer;
            position: absolute;
            right: 0;
            bottom: 0;
            display: flex;
            font-size: 11px;
            color: #fff;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 20px;
            background: #5a8bff;
            border-radius: 12px 0 8px 12px;
            :global(.iconfont) {
              font-size: 10px;
            }
          }
        } 
      }
    }
    .tip {
      margin-top: 8px;
      font-weight: 400;
      font-size: 12px;
      color: #fb9300;
      line-height: 12px;
    }
    .sub-desc {
      margin-top: -7px;
      font-weight: 400;
      font-size: 13px;
      color: #50596f;
      line-height: 18px;
    }
  }
}

/** 仅移动端的dom **/
.has-mixed-wrapper-m {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  .left,
  .right,
  .center {
    display: flex;
    width: 100%;
    min-height: (121px / @viewportMWidth) * 100vw;
    height: auto;
    flex-direction: row;
    background: #fffae7;
    border-radius: (8px / @viewportMWidth) * 100vw;
    font-weight: 400;
    font-size: (14px / @viewportMWidth) * 100vw;
    color: #986d00;
    line-height: (14px / @viewportMWidth) * 100vw;
    .title {
      position: relative;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      flex-shrink: 0;
      width: (66px / @viewportMWidth) * 100vw;
      font-size: (14px / @viewportMWidth) * 100vw;
      border-radius: (8px / @viewportMWidth) * 100vw;
    }
    .list {
      display: flex;
      padding: (16px / @viewportMWidth) * 100vw;
      overflow: hidden;
      flex: 1;
      ul {
        overflow: hidden;
        flex: 1;
      }
      li {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: (13px / @viewportMWidth) * 100vw;
        line-height: (20px / @viewportMWidth) * 100vw;
        a {
          flex: 1;
          color: inherit;
          text-decoration: underline;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        &::before {
          line-height: (20px / @viewportMWidth) * 100vw;
          font-size: 30px;
          display: block;
          content: '·';
          width: (12px / @viewportMWidth) * 100vw;
          opacity: 0.8;
        }
      }
      .more {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        align-self: center;
        border: none;
        width: (14px / @viewportMWidth) * 100vw;
        margin-left: (16px / @viewportMWidth) * 100vw;
        height: auto;
        border-radius: (14px / @viewportMWidth) * 100vw;
      }
    }
  }
  .right,
  .center {
    margin-top: (6px / @viewportMWidth) * 100vw;
    margin-left: 0;
  }
  .center {
    background: rgba(253, 210, 210, 0.25);
    color: #c5452c;
    .title {
      border-radius: 50%;
      height: (121px / @viewportMWidth) * 100vw;
    }
  }
  .right {
    color: #d45e00;
    background: rgba(253, 201, 155, 0.2);
  }
  .bg{
    display: block;
    position: absolute;
    height: 100%;
    opacity: 0.8;
    width: (66px / @viewportMWidth) * 100vw;
    mix-blend-mode: multiply;
    left: 0;
    top: 0;
    z-index: 10;
  }
}

/** 响应式复用 **/
@media screen and (max-width: 768px) {
  .overview-wrapper {
    flex-direction: column;
    .empty-wrapper-layout {
      .empty-wrapper {
        margin-top: (-6px / @viewportMWidth) * 100vw;
        padding: 0;
        background-color: transparent;
        border: none;
        width: 100%;
        .empty-top {
          border-bottom: none;
        }
        .empty-bottom {
          height: (50px / @viewportMWidth) * 100vw;
          background: #fef4e5;
          border: 2px solid rgba(251, 147, 0, 0.2);
          border-radius: (8px / @viewportMWidth) * 100vw;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 (10px / @viewportMWidth) * 100vw;
          .left {
            flex: 1;
          }
          .left,
          .btn {
            font-size: (13px / @viewportMWidth) * 100vw;
            line-height: (13px / @viewportMWidth) * 100vw;
          }
          .btn {
            height: (28px / @viewportMWidth) * 100vw;
            border-radius: (32px / @viewportMWidth) * 100vw;
            width: (76px / @viewportMWidth) * 100vw;
          }
        }
      }
     
      .reason {
        margin-top: (8px / @viewportMWidth) * 100vw;
        font-size: (13px / @viewportMWidth) * 100vw;
        a {
          font-size: (12px / @viewportMWidth) * 100vw;
          color: #5a8bff;
          line-height: 1;
        }
      }
    }
    .has-one-wrapper {
      flex-direction: column;
      margin-right: 0;
      .left,
      .right {
        width: 100%;
        min-height: (121px / @viewportMWidth) * 100vw;
        height: auto;
        flex-direction: row;
        .title {
          flex-shrink: 0;
          padding: 0;
          height: auto;
          width: (66px / @viewportMWidth) * 100vw;
          font-size: (14px / @viewportMWidth) * 100vw;
          border-radius: (8px / @viewportMWidth) * 100vw;
          line-height: 1;
        }
        .list {
          flex: 1;
          flex-direction: row;
          li {
            a {
              font-size: (13px / @viewportMWidth) * 100vw;
              flex: 1;
              line-height: 1;
              color: inherit;
              text-decoration: underline;
            }
          }
          .more {
            cursor: pointer;
            line-height: 1;
            border: none;
            width: (14px / @viewportMWidth) * 100vw;
            font-size: (14px / @viewportMWidth) * 100vw;
            margin-left: (16px / @viewportMWidth) * 100vw;
            height: auto;
          }
        }
        .empty {
          padding: 0 (16px / @viewportMWidth) * 100vw;
          font-size: (14px / @viewportMWidth) * 100vw;
        }
      }
      .right {
        margin-top: (6px / @viewportMWidth) * 100vw;
        margin-left: 0;
      }
    }
    .left-wrapper {
      margin-top: (-10px / @viewportMWidth) * 100vw;
      width: 100%;
      overflow: hidden;
    }
    .right-wrapper {
      display: flex;
      flex-direction: column;
      width: 100%;
      > div {
        width: 100%;
        flex: auto;
      }
      .values-wrapper {
        .card-wrapper {
          .left,
          .right {
            font-size: (12px / @viewportMWidth) * 100vw;
            min-height: (84px / @viewportMWidth) * 100vw;
            .content {
              font-size: (12px / @viewportMWidth) * 100vw;
            }
          }
          .left {
            margin-right: (6px / @viewportMWidth) * 100vw;
          }
          .right {
            margin-right: 0px;
          }
        }
      }
      .sub-desc {
        margin-top: (-6px / @viewportMWidth) * 100vw;
      }
    }
  }
}
