import React from 'react';
import { BLOCK_F_STRING, IComponentProps, LEVEL_INT } from '../../types';
import { BLOCK_F_MAP, LEVEL_TO_STRING_MAP } from '../../logic/constants';
import { CLIENT_TYPE } from '~/lib/answer/shared/types';

import Card from '../UI/Card';
import styles from './index.module.less';
import { openRoute, openWebView } from '@/utils/tools';

import h5MixBg from '@/assets/upgrade-major-report/h5-mix-bg.png';


const OFFSET_LENGTH = 5;
const Overview: React.FC<IComponentProps> = (props) => {
  const { clientType = CLIENT_TYPE.pc, state, methods } = props;
  const constants = BLOCK_F_MAP[BLOCK_F_STRING.overview];
  const [first, second, third] = constants.children || [];

  const {
    academicPotentialRecommendMajorList,
    categoryMajorRecommendMajorList,
    suitableProfessionMajorList,
    valuesRecommend,
    occupationals,
    showOccupationalsMore,
    hasSuitableProfession,
    currentTotalLevel,
  } = state || {};

  const { toBlock } = methods || {};

  const toProfessional = (majorId: string) => {
    if (clientType === CLIENT_TYPE.pc) {
      const url = `${origin}/career-new-prod/#/major/detail?majorId=${majorId}`;
      window.open(url);
    } else {
      const url = `${origin}/career/careerapp/index/#/professional-details?majorId=${majorId}&showTopBar=false`;
      openWebView(url);
    }
  };

  const empty = (
    <div className={styles['empty-wrapper-layout']}>
      <div className={styles['empty-wrapper']}>
        <div className={styles['empty-top']}>
        根据测评结果综合分析，你的兴趣专业推荐结果和潜能专业推荐结果
          <span className={styles['warn']}>均不在你的选考科目可选范围内</span>。
          以下报告仅供发展参考，
          <span className={styles['warn']}>不作为专业选择推荐</span>。
          建议你按照<span className={styles['warn']}>自身选考科目</span>进行专业选择。
        </div>
        <div className={styles['empty-bottom']}>
          <div className={styles['left']}>去志愿填报系统查看可报考院校专业</div>
          <div
            className={styles['btn']}
            onClick={() => {
              if (clientType === CLIENT_TYPE.pc) {
                const url = `${origin}/career/careerlibs/index.html#/volunteer-new?queryType=1`;
                window.open(url);
              } else {
                openRoute({
                  domain: 'career_plann',
                  action: 'voluntary_report',
                });
              }
            }}
          >
            立即查看
          </div>
        </div>
      </div>
      <div className={`${styles['warn']} ${styles['reason']}`}>
        *温馨提示: 对报告结果有疑问？&nbsp;
        <a onClick={() => toBlock(BLOCK_F_STRING.conclusion)}>查看原因</a>
      </div>
    </div>
  );

  const hasOne = (
    <div className={styles['has-one-wrapper']}>
      <div className={styles.left}>
        <div className={styles.title}>潜能专{clientType === CLIENT_TYPE.h5 && <br /> }业推荐</div>
        {academicPotentialRecommendMajorList?.length > 0 ? (
          <div className={styles.list}>
            <ul>
              {academicPotentialRecommendMajorList
                ?.slice(0, OFFSET_LENGTH)
                ?.map((v: any) => (
                  <li key={v.majorId} onClick={() => toProfessional(v.majorId)}>
                    <a>{v.majorName}</a>
                  </li>
                ))}
            </ul>
            {academicPotentialRecommendMajorList?.length > OFFSET_LENGTH ? (
              <div
                className={styles.more}
                onClick={() => toBlock(`${BLOCK_F_STRING.professionalPotential}${LEVEL_INT.second}`)}
              >
                查看更多
              </div>
            ) : null}
          </div>
        ) : (
          <div className={styles.empty}>
            从测评结果看，你更有学习潜能的专业方向不在你的选科组合覆盖范围内，建议按照兴趣专业推荐结果进行专业决策。
          </div>
        )}
      </div>
      <div className={styles.right}>
        <div className={styles.title}>职业兴{clientType === CLIENT_TYPE.h5 && <br /> }趣推荐</div>
        {categoryMajorRecommendMajorList?.length > 0 ? (
          <div className={styles.list}>
            <ul>
              {categoryMajorRecommendMajorList
                ?.slice(0, OFFSET_LENGTH)
                ?.map((v: any) => (
                  <li key={v.majorId} onClick={() => toProfessional(v.majorId)}>
                    <a>{v.majorName}</a>
                  </li>
                ))}
            </ul>
            {categoryMajorRecommendMajorList?.length > OFFSET_LENGTH ? (
              <div
                className={styles.more}
                onClick={() => toBlock(`${BLOCK_F_STRING.vocationalInterest}${LEVEL_INT.third}`)}
              >
                查看更多
              </div>
            ) : null}
          </div>
        ) : (
          <div className={styles.empty}>
           从测评结果看，你感兴趣的专业方向不在你的选科组合覆盖范围内，建议按照潜能专业推荐结果进行专业决策。
          </div>
        )}
      </div>
    </div>
  );

  const hasMixed = (
    <div className={styles['has-mixed-wrapper']}>
      <div className={styles.left}>
        <div className={styles.list}>
          <div className={styles.title}>潜能专业推荐</div>
          <ul>
            {academicPotentialRecommendMajorList
              ?.slice(0, OFFSET_LENGTH)
              ?.map((v: any) => (
                <li key={v.majorId}  onClick={() => toProfessional(v.majorId)}>
                  <a>{v.majorName}</a>
                </li>
              ))}
          </ul>
          {academicPotentialRecommendMajorList?.length > OFFSET_LENGTH ? (
            <div
              className={styles.more}
              onClick={() => toBlock(`${BLOCK_F_STRING.professionalPotential}${LEVEL_INT.second}`)}
            >
              查看更多
            </div>
          ) : null}
        </div>
      </div>
      <div className={styles.center}>
        <div className={styles.list}>
          <div className={styles.title}>适合你的专业</div>
          <ul>
            {suitableProfessionMajorList
              ?.slice(0, OFFSET_LENGTH)
              ?.map((v: any) => (
                <li key={v.majorId} onClick={() => toProfessional(v.majorId)}>
                  <a>{v.majorName}</a>
                </li>
              ))}
          </ul>
          {suitableProfessionMajorList?.length > OFFSET_LENGTH ? (
            <div
              className={styles.more}
              onClick={() => toBlock(BLOCK_F_STRING.suitableProfession)}
            >
              查看更多
            </div>
          ) : null}
        </div>
      </div>
      <div className={styles.right}>
        <div className={styles.list}>
          <div className={styles.title}>职业兴趣推荐</div>
          <ul>
            {categoryMajorRecommendMajorList
              ?.slice(0, OFFSET_LENGTH)
              ?.map((v: any) => (
                <li key={v.majorId} onClick={() => toProfessional(v.majorId)}>
                  <a>{v.majorName}</a>
                </li>
              ))}
          </ul>
          {categoryMajorRecommendMajorList?.length > OFFSET_LENGTH ? (
            <div
              className={styles.more}
              onClick={() => toBlock(`${BLOCK_F_STRING.vocationalInterest}${LEVEL_INT.third}`)}
            >
              查看更多
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );

  const hasMixedM = (
    <div className={styles['has-mixed-wrapper-m']}>
      <img className={styles.bg} src={h5MixBg} alt="" />
      <div className={styles.left}>
        <div className={styles.title}>潜能专<br />业推荐</div>
        <div className={styles.list}>
          <ul>
            {academicPotentialRecommendMajorList
              ?.slice(0, OFFSET_LENGTH)
              ?.map((v: any) => (
                <li key={v.majorId} onClick={() => toProfessional(v.majorId)}>
                  <a>{v.majorName}</a>
                </li>
              ))}
          </ul>
          {academicPotentialRecommendMajorList?.length > OFFSET_LENGTH ? (
            <div
              className={styles.more}
              onClick={() => toBlock(`${BLOCK_F_STRING.professionalPotential}${LEVEL_INT.second}`)}
            >
              查看更多
            </div>
          ) : null}
        </div>
      </div>
      <div className={styles.center}>
        <div className={styles.title}>适合你<br />的专业</div>
        <div className={styles.list}>
          <ul>
            {suitableProfessionMajorList
              ?.slice(0, OFFSET_LENGTH)
              ?.map((v: any) => (
                <li key={v.majorId} onClick={() => toProfessional(v.majorId)}>
                  <a>{v.majorName}</a>
                </li>
              ))}
          </ul>
          {suitableProfessionMajorList?.length > OFFSET_LENGTH ? (
            <div
              className={styles.more}
              onClick={() => toBlock(BLOCK_F_STRING.suitableProfession)}
            >
              查看更多
            </div>
          ) : null}
        </div>
      </div>
      <div className={styles.right}>
        <div className={styles.title}>职业兴<br />趣推荐</div>
        <div className={styles.list}>
          <ul>
            {categoryMajorRecommendMajorList
              ?.slice(0, OFFSET_LENGTH)
              ?.map((v: any) => (
                <li key={v.majorId} onClick={() => toProfessional(v.majorId)}>
                  <a>{v.majorName}</a>
                </li>
              ))}
          </ul>
          {categoryMajorRecommendMajorList?.length > OFFSET_LENGTH ? (
            <div
              className={styles.more}
              onClick={() => toBlock(`${BLOCK_F_STRING.vocationalInterest}${LEVEL_INT.third}`)}
            >
              查看更多
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );

  const hasMinedDom = clientType === CLIENT_TYPE.h5 ? hasMixedM : hasMixed;

  const renderDom = () => {
    /** 存在交集 */
    if (suitableProfessionMajorList?.length > 0) {
      return hasMinedDom;
    }
    /** 只有一个 */
    if (
      academicPotentialRecommendMajorList?.length ||
      categoryMajorRecommendMajorList?.length
    ) {
      return hasOne;
    }
    /** 不存在交集 */
    return empty;
  };

  return (
    <Card id={BLOCK_F_MAP[BLOCK_F_STRING.overview].value}>
      <div
        className={`${styles['overview-wrapper']} ${
          suitableProfessionMajorList?.length > 0 ? styles['wrap'] : ''
        }`}
      >
        <div className={styles['left-wrapper']}>
          <Card.SubTitle
            name={first.label}
            id={first.value}
            desc={hasSuitableProfession ? '(点击专业可查看详情)' : ''}
          />
          {renderDom()}
        </div>
        <div
          className={`${styles['right-wrapper']} ${
            suitableProfessionMajorList?.length > 0 ? styles['flex'] : ''
          }`}
        >
          <div className={styles['values-wrapper']}>
            <Card.SubTitle name={second.label} id={second.value} />
            <div className={styles['sub-desc']}>
              专业选择一定程度上决定了你未来的职业方向，选专业的同时也在选职业。职业价值观是你选择职业的判断标准。
            </div>
            <div className={styles['card-wrapper']}>
              <div className={styles.left}>
                <div>你最看重的职业价值观</div>
                <div className={styles.content}>{valuesRecommend}</div>
              </div>
              <div className={styles.right}>
                <div>符合你职业价值观的职业</div>
                <div className={styles.content}>{occupationals}</div>
                {showOccupationalsMore && (
                  <div
                    className={styles['more']}
                    onClick={() => toBlock(`${BLOCK_F_STRING.professionalValues}${LEVEL_INT.third}`)}
                  >
                    更多
                    <i className="iconfont iconshengxuecebaogao-xialajiantou" />
                  </div>
                )}
              </div>
            </div>

            <div className={styles['tip']}>
              *请尽可能选择与以上职业方向更匹配的专业。
            </div>
          </div>
          <div>
            <Card.SubTitle name={third.label} id={third.value} />
            <div className={styles['sub-desc']}>
              {LEVEL_TO_STRING_MAP[currentTotalLevel!] || ''}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default Overview;
