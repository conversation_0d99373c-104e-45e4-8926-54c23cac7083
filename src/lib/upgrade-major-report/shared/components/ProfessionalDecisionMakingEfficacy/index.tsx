import React from 'react';

import { Table } from 'antd';
import Card from '../UI/Card';
import GreenCard from '../UI/GreenCard';
import { Highlight } from '../UI/List';
import { BLOCK_F_STRING, IComponentProps, EFFICACY, LEVEL_HIG_INT } from '../../types';
import { BLOCK_F_MAP, LEVEL_TEXT_OBJECT_MAP} from '../../logic/constants';  
import { CLIENT_TYPE } from '~/lib/answer/shared/types';
import styles from './index.module.less';


const ProfessionalDecisionMakingEfficacy: React.FC<IComponentProps> = (
  props
) => {
  const { clientType = CLIENT_TYPE.pc, data} = props;
  const { currentTotalLevel }  = data || {};
  const constants =
    BLOCK_F_MAP[BLOCK_F_STRING.professionalDecisionMakingEfficacy];
  const [first, second] = constants.children || [];

  const textObject: any = LEVEL_TEXT_OBJECT_MAP[(currentTotalLevel) as LEVEL_HIG_INT] || {};

  return (
    <Card
      id={BLOCK_F_MAP[BLOCK_F_STRING.professionalDecisionMakingEfficacy].value}
      description={
        BLOCK_F_MAP[BLOCK_F_STRING.professionalDecisionMakingEfficacy]
          .descriptions[0]
      }
    >
      <div className={styles['professional-efficacy-wrapper']}>
        <Card.SubTitle name={first.label} id={first.value} />
        <div>
          <Table
            size="middle"
            rowKey={(record) => record.latitudeId}
            columns={[
              {
                title: '专业决策效能感',
                width: clientType === CLIENT_TYPE.pc ? 140 : 72,
                align: 'center',
                dataIndex: 'latitudeName',
                key: 'latitudeName',
              },
              {
                title: '情况描述',
                align: 'center',
                dataIndex: 'description',
                key: 'description',
                render: (v) => {
                  return (
                    <div
                      dangerouslySetInnerHTML={{ __html: v }}
                      style={{ wordBreak: 'break-all', textAlign: 'left' }}
                    />
                  );
                },
              },
              {
                title: '程度',
                align: 'center',
                width: clientType === CLIENT_TYPE.pc ? 160 : 46,
                dataIndex: 'latitudeLevelName',
                key: 'latitudeLevelName',
                render: (_t, v) => {
                  return (
                    <Highlight
                      value={v}
                      showLine={clientType === CLIENT_TYPE.pc}
                    />
                  );
                },
              },
            ]}
            dataSource={data?.decisionMakingEfficacyList}
            pagination={false}
            bordered={clientType === CLIENT_TYPE.h5}
          />
        </div>
        <Card.SubTitle name={second.label} id={second.value} />
        {clientType === CLIENT_TYPE.h5 ? textObject.desc : ''}
        {clientType === CLIENT_TYPE.pc ? (
          <GreenCard
            clientType={clientType}
            className={styles['second-green-card']}
            description={textObject.desc}
          >
            {textObject.list?.map((v: any, i: number) => {
              const isArray = Array.isArray(v.value);
              const gLevel = data?.currentTotalLevel;
              const cLevel = data?.decisionMakingEfficacyList?.find((lat: { latitudeLabel: any; key: any; })=>lat.latitudeLabel === v.key)?.level;
              if(gLevel !== LEVEL_HIG_INT.high && cLevel === LEVEL_HIG_INT.high){
                return null
              }
              if (isArray) {
                return (
                  <div className={styles.list} key={i}>
                    <div className={styles.title}>{v.title}</div>
                    <ul>
                      {v.titleDesc ? <span>{v.titleDesc}</span> : null}
                      {v.value?.map((val: any, index: number) => (
                        <li key={index}>
                          <span>{val}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              }
              return (
                <div className={styles.list} key={i}>
                  <div className={styles.title}>{v.title}</div>
                  <div>{v.value}</div>
                </div>
              );
            })}
          </GreenCard>
        ) : (
          <>
            {textObject.list?.map((v: any, i: number) => {
              const isArray = Array.isArray(v.value);
              const gLevel = data?.currentTotalLevel;
              const cLevel = data?.decisionMakingEfficacyList?.find((lat: { latitudeLabel: any; key: any; })=>lat.latitudeLabel === v.key)?.level;
              if(gLevel !== LEVEL_HIG_INT.high && cLevel === LEVEL_HIG_INT.high){
                return null
              }
              if (isArray) {
                return (
                  <GreenCard
                    clientType={clientType}
                    key={i}
                    className={styles['second-green-card']}
                    title={v.title}
                  >
                    <div className={styles.list}>
                      <ul>
                      {v.titleDesc ? <span>{v.titleDesc}</span> : null}
                        {v.value?.map((val: any, index: number) => (
                          <li key={index}>
                            <span>{val}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </GreenCard>
                );
              }
              return (
                <GreenCard
                  clientType={clientType}
                  key={i}
                  className={styles['second-green-card']}
                  title={v.title}
                >
                  <span>{v.value}</span>
                </GreenCard>
              );
            })}
          </>
        )}
      </div>
    </Card>
  );
};

export default ProfessionalDecisionMakingEfficacy;
