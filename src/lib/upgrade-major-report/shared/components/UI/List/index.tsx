import React, {
  useContext,
  useRef,
  useState,
  createContext,
  useEffect,
} from 'react';
import styles from './index.module.less';

export const GroupContext = createContext<any>({});

const OFFSET_LENGTH = 5;
const List = (props: {
  dataSource: any;
  name: string | number;
  clientType?: any;
  wrapperClass?: string
}) => {
  const groupCtx = useContext(GroupContext);
  const { dataSource, name, wrapperClass } = props || {};
  const [showMore, setShowMore] = useState(false);

  const listRef = useRef<HTMLDivElement>(null);
  const lastOffsetHight = useRef<number>();

  const viewL = () => {
    if (!showMore) {
      return dataSource?.categoryMajorLatitudeScoreList?.slice(
        0,
        OFFSET_LENGTH
      );
    }
    return dataSource?.categoryMajorLatitudeScoreList;
  };

  const setOtherMoreFalse = () => {
    if (!groupCtx.accordion) return;
    if (groupCtx?.ins && name) {
      const canIns = groupCtx?.ins?.filter(
        (ins: { name: string | number }) => ins.name !== name
      );
      canIns.forEach((ins: { show: (arg0: boolean) => void }) => {
        ins?.show?.(false);
      });
    }
  };

  const onShowMore = (status: boolean) => {
    lastOffsetHight.current = listRef.current?.offsetHeight;
    setShowMore(status);
    setTimeout(() => {
      collapse();
      setOtherMoreFalse();
    }, 0);
  };

  const collapse = () => {
    if (listRef.current) {
      listRef.current.style.height = `auto`;
      listRef.current?.offsetHeight;
      const cOffsetHight = listRef.current?.offsetHeight;
      listRef.current.style.height = `${lastOffsetHight.current}px`;
      listRef.current?.offsetHeight;
      listRef.current.style.transition = '0.3s';
      listRef.current.style.height = `${cOffsetHight}px`;
    }
  };

  useEffect(() => {
    if (groupCtx?.ins && name) {
      groupCtx.mountedIns({
        name,
        ins: listRef.current,
        show: onShowMore,
      });
    }
    return () => {
      return groupCtx.unMountedIns({
        name,
        ins: listRef.current,
        show: onShowMore,
      });
    };
  }, []);


  return (
    <div className={`${styles['list-body-wrapper']} ${wrapperClass ? wrapperClass : ''}`}>
      <div className={styles['list-block']} ref={listRef}>
        <div className={styles['list-title']}>可报专业</div>
        <div className={styles['list-line']}>
          {dataSource?.majorList?.length > 0 ? (
            dataSource?.majorList
              ?.map((m: { majorName: any }) => m.majorName)
              ?.join('、')
          ) : (
            <div className={styles['empty']}>
              该大类下专业与选科冲突，无推荐专业
            </div>
          )}
        </div>
        {
          viewL()?.length > 0 ? (<div className={styles['list-item-line']}>
            <div className={styles['list-title']}>关联高中学科知识</div>
            <div className={styles['list-title']}>自我评价</div>
          </div>) : null
        }
        {viewL()?.map((j: any) => (
          <div key={j.latitudeId} className={styles['list-item-line']}>
            <div>{j.latitudeName}</div>
            <Highlight value={j} />
          </div>
        ))}
      </div>
      {dataSource?.categoryMajorLatitudeScoreList?.length > OFFSET_LENGTH && (
        <div
          className={`${styles['list-item-more']} ${
            showMore ? styles['active'] : ''
          }`}
          onClick={() => onShowMore(!showMore)}
        >
          {showMore ? '点击收起' : '查看更多'}&nbsp;
          <i className="iconfont iconshengxuecebaogao-xialajiantou" />
        </div>
      )}
    </div>
  );
};

const aM = {
  1: 'low',
  2: 'medium',
  3: 'high',
};

export const Group = (props: any) => {
  const { accordion = false } = props;
  const insRef = useRef<{ name: string | number; ins: any }[]>([]);
  const [, forceUpdate] = useState({});

  const mountedIns = (iIns: { name: string | number; ins: any }) => {
    const insC = insRef.current.slice();
    const index = insRef.current.findIndex((v) => v.name === iIns.name);
    if (index !== -1) return;
    insC.push(iIns);
    insRef.current = insC;
    forceUpdate({});
  };

  const unMountedIns = (iIns: { name: string | number; ins: any }) => {
    const insC = insRef.current.filter((v) => v.name !== iIns.name);
    insRef.current = insC;
    forceUpdate({});
  };

  useEffect(() => {
    return () => {
      insRef.current = [];
    };
  }, []);

  return (
    <GroupContext.Provider
      value={{
        ins: insRef.current,
        mountedIns,
        unMountedIns,
        accordion,
      }}
    >
      {props.children}
    </GroupContext.Provider>
  );
};

export const Highlight = (props: { value: any; showLine?: boolean }) => {
  const { value, showLine = true } = props;
  return (
    <div
      key={value.latitudeId}
      // @ts-ignore
      className={`${styles['highlight-wrapper']} ${styles[aM[value.level]]}`}
    >
      {showLine && (
        // @ts-ignore
        <div className={`${styles.highlight} ${styles[aM[value.level]]}`}>
          <div className={styles.before}></div>
          <div className={styles.middle}></div>
          <div className={styles.after}></div>
        </div>
      )}
      <span className={styles.name}>{value.latitudeLevelName || value.levelName}</span>
    </div>
  );
};

List.Group = Group;

export default List;
