@viewportMWidth: 375px;

.list-body-wrapper {
  background: #ffffff;
  border: 1px solid #e1e6f2;
  border-radius: 4px;
  overflow: hidden;
  word-break: break-all;
  .empty {
    color: #fb9300;
  }
  .list-item-more {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #5a8bff;
    border-top: 1px solid #edf0f8;
    padding: 13px 20px;
    font-size: 14px;
    line-height: 14px;
    :global {
      .iconshengxuecebaogao-xialajiantou {
        margin-left: 5px;
        display: block;
        font-size: 12px;
      }
    }
    &.active {
      :global {
        .iconshengxuecebaogao-xialajiantou {
          transform: rotate(180deg);
        }
      }
    }
  }
  .list-block{
    .list-line{
      color: #50596F;
      line-height: 20px;
    }
    .list-title{
      color: #8b97b6;
      background: #f9fafc;
    }
    > div {
      padding: 13px 20px;
      border-bottom: 1px solid #edf0f8;
      font-size: 14px;
      &:nth-child(2n-1) {
        // background: #f9fafc;
      }
      &:last-child{
        border-bottom: none;
      }
    }
    .list-item-line {
      color: #50596F;
      display: flex;
      padding: 0;
      align-items: center;
      justify-content: space-between;
      > div {
        line-height: 1.5;
        padding: 13px 20px;
        &:first-child {
          flex: 1;
        }
        &:last-child {
          text-align: center;
          width: 18%;
          min-width: 80px;
        }
      }
    }
  }
}

.highlight-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  &.high {
    color: #FA4A23;
  }
  &.medium {
    color: #FB9300;
  }
  &.low {
    color: #05867E;
  }
  .name{
    width: 42px;
  }
  .highlight {
    display: flex;
    align-items: center;
    margin-right: 12px;
    
    .middle,
    .before,
    .after {
      display: block;
      width: 28px;
      height: 6px;
      background: #e8eaee;
    }
    .middle,
    .before {
      margin-right: 2px;
    }
    .before {
      border-radius: 6px 0 0 6px;
    }
    .after {
      border-radius: 0 6px 6px 0;
    }
    &.high {
      .before,
      .middle,
      .after {
        background: #fdc8bd;
      }
    }
    &.medium {
      .before,
      .middle {
        background: #fddeb2;
      }
    }
    &.low {
      .before {
        background: #b4dad8;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .list-body-wrapper {
    background: #ffffff;
    border: 1px solid #e1e6f2;
    border-radius: (8px / @viewportMWidth) * 100vw;
    .list-block{
      > div {
        padding: (10px / @viewportMWidth) * 100vw;
        border-bottom: 1px solid #edf0f8;
        font-size: 14px;
        line-height: 14px;
        color: #50596f;
        &:nth-child(2n-1) {
          // background: #f9fafc;
        }
      }
      .list-item-line {
        display: flex;
        padding: 0;
        align-items: center;
        color: #50596F;
        justify-content: space-between;
        > div {
          font-size: 13px;
          padding: (8px / @viewportMWidth) * 100vw (10px / @viewportMWidth) * 100vw;
        }
        > div:first-child {
          display: block;
          flex: 1;
          border-right: 1px solid #edf0f8;
        }
        .highlight-wrapper{
          justify-content: center;
          .name{
            width: auto;
          }
        }
        .highlight {
          display: none !important;
        }
      }
    }
    .list-item-more {
      padding: (8px / @viewportMWidth) * 100vw (10px / @viewportMWidth) * 100vw;
      font-size: 13px;
      text-align: center;
      cursor: pointer;
      color: #5a8bff !important;
    }
  }
}
