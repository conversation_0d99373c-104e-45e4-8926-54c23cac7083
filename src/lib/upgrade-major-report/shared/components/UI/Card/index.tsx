'use client';

import React from 'react';
import styles from './index.module.less';

import { BLOCK_F_STRING } from '../../../types';

import { BLOCK_F_MAP } from '../../../logic/constants';

interface ICardProps {
  id: BLOCK_F_STRING;
  description?: string;
}

const Card: React.FC<ICardProps> = (props) => {
  return (
    <div className={styles['card-wrapper']}>
      <div className={styles['card-title']} id={props.id}>
        {BLOCK_F_MAP[props.id] && (
          <img
            src={BLOCK_F_MAP[props.id].backgroundImage}
            alt={BLOCK_F_MAP[props.id].label}
          />
        )}
      </div>
      <div className={styles['card-body']}>
        <div className={styles['description']}>{props.description}</div>
        <div> {props.children}</div>
      </div>
    </div>
  );
};

const SubTitle = (props: {
  name: string;
  id: string;
  desc?: string;
  className?: string;
}) => {
  return (
    <div
      className={`${styles['card-sub-title']} ${props.className}`}
      id={props.id}
    >
      <div className={`${styles.left} left`}>
        {props.name && (
          <span
            className={`${styles['card-sub-title-inner']} card-sub-title-inner`}
          >
            <span>{props.name}</span>
          </span>
        )}
        {props.desc && (
          <span
            className={`${styles['card-sub-desc-inner']} card-sub-desc-inner`}
          >
            {props.desc}
          </span>
        )}
      </div>
    </div>
  );
};

const CardDefault = Object.assign(Card, { SubTitle });

export default CardDefault;
