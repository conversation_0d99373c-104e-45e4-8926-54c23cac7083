@viewportMWidth: 375px;

.card-wrapper{
  width: 1000px;
  max-width: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px 0 rgba(210, 213, 222, 0.5);
  .card-title{
    overflow: hidden;
    border: 2px solid #FFFFFF;
    border-bottom: none;
    padding: 24px 20px 0;
    border-radius: 12px;
    background-image: linear-gradient(180deg, rgba(255, 192, 85, 0.2) 0%, #ffffff 100%);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    img{
      display: block;
      height: 30px;
    }
  }
  .card-body{
    padding: 0 20px 20px;
    margin-top: 6px;
    .description{
      display: flex;
      justify-content: center;
      font-size: 14px;
      color: #50596F;
    }
  }
}

.card-sub-title{
  margin: 25px 0 13px;
  display: flex;
  height: 22px;
  align-items: center;
  justify-content: space-between;
  .left{
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
  }
  .card-sub-desc-inner{
    margin-left: 8px;
    font-weight: 400;
    font-size: 13px;
    color: #6B7884;
    line-height: 13px;
  }
  .card-sub-title-inner{
    position: relative;
    z-index: 1;
    font-weight: 600;
    font-size: 17px;
    color: #2A333A;
    line-height: 17px;
    > span{
      position: relative;
      z-index: 2;
    }
    &::after{
      position: absolute;
      z-index: 1;
      left: 0;
      display: block;
      content: "";
      height: 12px;
      opacity: 0.4;
      background-image: linear-gradient(270deg, rgba(255, 191, 84, 0) 0%, #FF7609 99%);
      border-radius: 8px;
      width: 100%;
      bottom: 0;
      bottom: -5px;
    }
  }
}

@media screen and (max-width: 768px) {
  .card-wrapper{
    position: relative;
    margin-bottom: (12px / @viewportMWidth) * 100vw;
    
    .card-title{
      padding: (16px / @viewportMWidth) * 100vw (12px / @viewportMWidth) * 100vw 0;
      border: 1px solid #FFFFFF;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      img{
        display: block;
        height: (22px / @viewportMWidth) * 100vw;
      }
    }
    .card-body{
      margin-top: (6px / @viewportMWidth) * 100vw;
      padding: 0 (12px / @viewportMWidth) * 100vw (12px / @viewportMWidth) * 100vw;
      .description{
        font-size: 13px;
      }
    }
  }
  .card-sub-title{
    margin: (20px / @viewportMWidth) * 100vw 0 (10px / @viewportMWidth) * 100vw;
    display: flex;
    height: (18px / @viewportMWidth) * 100vw;
    align-items: center;
    justify-content: space-between;
    .left{
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
    }
    .card-sub-desc-inner{
      margin-left: (8px / @viewportMWidth) * 100vw;
      font-weight: 400;
      font-size: 13px;
      color: #6B7884;
      line-height: 13px;
    }
    .card-sub-title-inner{
      position: relative;
      z-index: 2;
      font-weight: 600;
      font-size: 15px;
      color: #2A333A;
      line-height: 15px;
      &::after{
        position: absolute;
        z-index: 0;
        left: 0;
        display: block;
        content: "";
        height: (8px / @viewportMWidth) * 100vw;
        opacity: 0.4;
        background-image: linear-gradient(270deg, rgba(255, 191, 84, 0) 0%, #FF7609 99%);
        border-radius: 4px;
        width: 100%;
        bottom: 0;
      }
    }
  }
}