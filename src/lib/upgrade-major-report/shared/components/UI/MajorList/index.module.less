@viewportMWidth: 375px;

.major-list-wrapper{
  .major-list-body{
    width: 100%;
    .item-wrapper{
      min-width: 312px;
      margin-bottom: 12px;
      margin-right: 12px;
      &:nth-child(3n) {
        margin-bottom: 0;
        margin-right: 0;
      }
      &.flex-1{
        flex: 1;
        &:last-child{
          margin-right: 0;
        }
       }
    }
    
    display: flex;
    flex-wrap: wrap;
  }
  .major-list-bottom{
    width: 54%;
    margin-left: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .all-button{
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 124px;
      height: 32px;
      border: 1px solid #FB9300;
      border-radius: 32px;
      color: #FB9300;
      font-weight: 500;
      font-size: 14px;
    }
  }
  :global{
    .ant-pagination{
      margin-left: auto;
    }
  }
}



.major-item-wrapper {
  width: 100%;
  min-height: 56px;
  background: #F7F8FA;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .l{
    flex: 1;
    overflow: hidden;
    line-height: 1;
    .name{
      cursor: pointer;
      font-weight: 600;
      font-size: 14px;
      color: #50596F;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 12px;
    }
    .desc{
      margin-top: 4px;
      font-weight: 400;
      font-size: 13px;
      color: #96A2BE;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 12px;
    }
  }
  .r{
    width: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 64px;
    height: 28px;
    background: rgba(90, 139, 255, 0.1);
    border-radius: 14px;
    color: #5A8BFF;
    font-size: 12px;
    border: 1px solid transparent;
    &.active{
      color: #50596F;
      border: 1px solid #E1E6F2;
      background-color: transparent;
    }
    :global{
      .iconshengxuecebaogao-guanzhuzhuanye{
        font-size: 12px;
        margin-right: 4px;
      }
    }
  }
}

.tooltip-wrapper{
  :global{
    .ant-tooltip-inner{
      padding: 10px;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 8px;
    }
  }
}

@media screen and (max-width: 768px) {
  .major-item-wrapper {
    width: 100%;
    height: (56px / @viewportMWidth) * 100vw;
    background: #F7F8FA;
    border-radius: (8px / @viewportMWidth) * 100vw;
    padding: (12px / @viewportMWidth) * 100vw;
    .l{
      .name{
        font-weight: 600;
        font-size: 14px;
        color: #50596F;
      }
      .desc{
        margin-top: 4px;
        font-weight: 400;
        font-size: 12px;
      }
    }
    .r{
      cursor: pointer;
      width: (76px / @viewportMWidth) * 100vw;
      height: (30px / @viewportMWidth) * 100vw;
      border-radius: (14px / @viewportMWidth) * 100vw;
      font-size: 12px;
    }
  }
  .major-list-wrapper{
    margin: 0 (-12px / @viewportMWidth) * 100vw;
    .major-list-bottom{
      width: 100%;
      justify-content: center;
    }
    :global{
      .adm-swiper-slide{
        &:first-child{
          padding-left: (8px / @viewportMWidth) * 100vw;;
        }
        &:last-child{
          padding-right: (8px / @viewportMWidth) * 100vw;;
        }
      }
      .adm-swiper-item{
        > div{
          margin-left: (4px / @viewportMWidth) * 100vw;
          margin-right: (4px / @viewportMWidth) * 100vw;
          margin-bottom: (8px / @viewportMWidth) * 100vw;
          &:last-child{
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
