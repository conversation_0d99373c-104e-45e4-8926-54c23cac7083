import React, { useState, useMemo, useRef } from 'react';
import styles from './index.module.less';

import { useDeepCompareEffect } from 'ahooks';

import MstQtAnalytics from 'mst-analytics';
import { Tooltip, Pagination, Spin, message } from 'antd';
import chunk from 'lodash/chunk';
import { Swiper } from 'antd-mobile';
import { CLIENT_TYPE } from '~/lib/answer/shared/types';
import useCollect from '../../../hooks/useCollect';
import { openWebView } from '@/utils/tools';

const PAGE_SIZE = 15;
const CHUNK_SIZE = 5;
const OFFSET = 5;
const SLIDE_SIZE = 90;

const OFFSET_D_LENGTH = 2;

const DEFAULT_SIZE = 1;

const MajorList = (props: any) => {
  const { dataSource, clientType, showDesc } = props || {};
  const [pageIndex, setPageIndex] = useState(DEFAULT_SIZE);
  const [globalLoading, setGlobalLoading] = useState(false);

  const [mList, setMList] = useState<any[]>([]);

  const list = useMemo(() => {
    return dataSource?.map((v: any) => v.majorId);
  }, [dataSource]);

  const {
    state: { collectList = [], collectTotal },
    run: { runAddCollect, runCancelCollect, runCollectOne, runCollectTotal },
  } = useCollect(list, { clientType });

  useDeepCompareEffect(() => {
    if (dataSource?.length) {
      const mList = dataSource?.map((v: any) => ({
        ...v,
        loading: false,
        isCollect: collectList?.includes(v.majorId),
      }));
      setMList(mList);
    }
  }, [dataSource, collectList]);

  const viewSource = useMemo(() => {
    return mList?.slice((pageIndex - 1) * PAGE_SIZE, pageIndex * PAGE_SIZE);
  }, [mList, pageIndex]);

  const upDateCollect = (id: any, info: any) => {
    const newList = mList.map((v: any) => {
      if (v.majorId === id) {
        return {
          ...v,
          ...info,
        };
      }
      return v;
    });
    setMList(newList);
  };

  const toggleCollect = async (v: any) => {
    const offset = 200;
    const api = v.isCollect ? runCancelCollect : runAddCollect;
    if (v.loading) return;
    if (globalLoading) return;

    if (collectTotal > offset) {
      message.warning(`最多只能关注${offset}个专业`);
      return;
    }
    const eventCode =
      clientType === CLIENT_TYPE.pc
        ? 'ewt_pc_career_evaluation_upgrade_major_report_home_follow_click'
        : 'ewt_h5_career_evaluation_upgrade_major_report_home_follow_click';
    MstQtAnalytics.clickPv({
      eventCode,
      status: v.isCollect ? '取消关注' : '关注',
    });
    setGlobalLoading(true);
    upDateCollect(v.majorId, {
      loading: true,
    });
    try {
      const { data, success } = await api({
        contentType: 4,
        contentId: v.majorId,
      });
      setGlobalLoading(false);
      if (success && data) {
        upDateCollect(v.majorId, {
          isCollect: !v.isCollect,
          loading: false,
        });
      } else {
        upDateCollect(v.majorId, {
          loading: false,
        });
      }
    } catch (error) {
      setGlobalLoading(false);
      upDateCollect(v.majorId, {
        loading: false,
      });
      await runCollectOne({
        contentType: 4,
        contentId: v.majorId,
      });
    }
    runCollectTotal({
      contentType: 4,
    });
  };
  const toProfessional = (majorId: string) => {
    if (clientType === CLIENT_TYPE.pc) {
      const url = `${origin}/career-new-prod/#/major/detail?majorId=${majorId}`;
      window.open(url);
    } else {
      const url = `${origin}/career/careerapp/index/#/professional-details?majorId=${majorId}&showTopBar=false`;
      openWebView(url);
    }
  };

  if (clientType === CLIENT_TYPE.pc) {
    return (
      <div className={styles['major-list-wrapper']}>
        <div className={styles['major-list-body']}>
          {viewSource?.map((v: any, i: any) => (
            <div
              key={i}
              className={`${styles['item-wrapper']} ${
                mList.length <= OFFSET_D_LENGTH ? styles['flex-1'] : ''
              }`}
              style={{
                width: mList.length <= OFFSET_D_LENGTH ? '100%' : 158,
              }}
            >
              <Item
                showDesc={showDesc}
                onClick={() => toProfessional(v.majorId)}
                onFollow={() => toggleCollect(v)}
                key={i}
                value={v}
              />
            </div>
          ))}
        </div>
        <div className={styles['major-list-bottom']}>
          {mList.length > PAGE_SIZE && (
            <Pagination
              onChange={(v) => {
                setPageIndex(v);
              }}
              showSizeChanger={false}
              pageSize={PAGE_SIZE}
              current={pageIndex}
              total={mList.length}
            />
          )}
        </div>
      </div>
    );
  }
  return (
    <div className={styles['major-list-wrapper']}>
      <Swiper
        trackOffset={mList.length > CHUNK_SIZE ? OFFSET : undefined}
        slideSize={mList.length > CHUNK_SIZE ? SLIDE_SIZE : undefined}
        defaultIndex={0}
        indicator={() => null}
      >
        {chunk(mList, CHUNK_SIZE)?.map((j, i) => (
          <Swiper.Item key={i}>
            {j?.map((v, vi) => (
              <div key={vi}>
                <Item
                  showDesc={showDesc}
                  registerMouse={false}
                  value={v}
                  onClick={() => toProfessional(v.majorId)}
                  onFollow={() => toggleCollect(v)}
                />
              </div>
            ))}
          </Swiper.Item>
        ))}
      </Swiper>
      <div className={styles['major-list-bottom']}></div>
    </div>
  );
};

const Item = (props: any) => {
  const {
    value,
    onClick,
    onFollow,
    showDesc = false,
    registerMouse = true,
  } = props || {};
  const [inMouse, setInMouse] = useState(false);

  return (
    <div className={styles['major-item-wrapper']} onClick={onClick}>
      <div className={styles.l}>
        <div className={styles['name']}>
          <Tooltip
            title={value.majorName}
            overlayClassName={styles['tooltip-wrapper']}
          >
            {value.majorName}
          </Tooltip>
        </div>
        {showDesc && <div className={styles['desc']}>{value.categoryName}</div>}
      </div>
      <Spin spinning={value.loading}>
        <div
          className={`${styles.r} ${value.isCollect ? styles.active : ''}`}
          onMouseEnter={() => {
            registerMouse && setInMouse(true);
          }}
          onMouseLeave={() => {
            registerMouse && setInMouse(false);
          }}
          onClick={(e) => {
            e.stopPropagation();
            onFollow();
          }}
        >
          {value.isCollect ? (
            !inMouse ? (
              '已关注'
            ) : (
              '取消关注'
            )
          ) : (
            <>
              <i className="iconfont iconshengxuecebaogao-guanzhuzhuanye" />
              关注
            </>
          )}
        </div>
      </Spin>
    </div>
  );
};

const MajorListDefault = Object.assign(MajorList, { Item });

export default MajorListDefault;
