@viewportMWidth: 375px;

.green-card-wrapper {
  background: #7FAC8D;
  padding: 8px;
  border-radius: 8px;
  overflow: hidden;
  .card-header-wrapper{
    display: flex;
    align-items: center;
    .card-header-left{
      padding-right: 16px;
      flex: 1;
      color: #FFFFFF;
    }
    .card-title {
      font-weight: 600;
      color: #FFFFFF;
      line-height: 24px;
      font-size: 16px;
    }
    :global{
      .iconshengxuecebaogao-youshangjiaotiaowen{
        color: #4f7f66;
      }
    }
  }
  .card-body {
    margin-top: 6px;
    .card-inner-bg {
      display: block;
      width: 100%;
      height: 28px;
      margin-bottom: -1px;
    }
    .card-content {
      background: #FFFFFF;
      padding: 0 20px 20px;
      background: #FFFFFF;
      border-radius: 0 0 8px 8px;
    }
  }
}

@media screen and (max-width: 768px) {
  .green-card-wrapper {
    .card-header-wrapper{
      .card-title {
        font-size: 14px;
        line-height: 14px;
      }
      :global{
        .iconshengxuecebaogao-youshangjiaotiaowen{
          font-size: 12px;
        }
      }
    }
    .card-body {
      margin-top: (6px / @viewportMWidth) * 100vw;
      .card-content {
        padding: 0 (10px / @viewportMWidth) * 100vw (10px / @viewportMWidth) * 100vw;
        font-size: 12px;
      }
    }
  }
}
