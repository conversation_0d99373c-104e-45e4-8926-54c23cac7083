'use client';

import React from 'react';
import styles from './index.module.less';
import greenBg from '@/assets/upgrade-major-report/green-bg.png';
import greenBgH5 from '@/assets/upgrade-major-report/green-bg-h5.png';


import {
  BLOCK_F_STRING,
} from '@/lib/upgrade-major-report/shared/types';

interface ICardProps {
  id?: BLOCK_F_STRING;
  title?: string;
  description?: string;
  className?: string;
  clientType?:string
}

const GreenCard: React.FC<ICardProps> = (props) => {
  return (
    <div className={`${styles['green-card-wrapper']} ${props.className}`}>
      <div className={styles['card-header-wrapper']} id={props.id}>
       <div className={styles['card-header-left']}>
        {props.title && <div className={styles['card-title']} >{props.title}</div>}
        {props.description && <div id={props.id}>{props.description}</div>}
       </div>
       <i className="iconfont iconshengxuecebaogao-youshangjiaotiaowen"/>
      </div>
      <div className={styles['card-body']}>
        <img className={styles['card-inner-bg']} src={props.clientType === '1' ? greenBg : greenBgH5 } />
        <div className={styles['card-content']}>{props.children}</div>
      </div>
    </div>
  );
};

export default GreenCard;
