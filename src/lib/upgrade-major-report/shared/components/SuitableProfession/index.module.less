@viewportMWidth: 375px;

.suitable-profession-tip {
  width: 1000px;
  max-width: 100%;
  height: 52px;
  background: #fef4e5;
  border: 2px solid rgba(251, 147, 0, 0.5);;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 14px;
  color: #944a0f;
  border-radius: 12px;
  .img {
    width: 42px;
    display: block;
    margin-right: 9px;
    margin-top: -12px;
  }
  .text{
    display: flex;
    align-items: center;
  }
}
.suitable-profession-wrapper {
  .g-wrapper-layout {
    margin-top: 16px;
    .g-wrapper {
      width: 100%;
      max-width: 100%;
      background: #fef4e5;
      border: 1px solid #fb930033;
      border-radius: 8px;
      padding: 16px;
      .g-top {
        color: #50596f;
        font-size: 13px;
        font-weight: 400;
        line-height: 18px;
        padding-bottom: 12px;
        border-bottom: 1px dashed #fb9300;
        margin-bottom: 12px;
      }
      .g-bottom {
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          font-weight: 500;
          font-size: 14px;
          color: #50596f;
          line-height: 14px;
          .g-desc{
            color: #50596F;
            font-weight: 400;
            margin-top: 8px;
            font-size: 13px;
          }
        }
        .btn {
          cursor: pointer;
          width: 82px;
          height: 32px;
          background-image: linear-gradient(270deg, #fdac57 0%, #fc8103 99%);
          border-radius: 32px;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  .mixed-card-wrapper{
    margin-top: 20px;
    .tips{
      white-space: pre-wrap;
      margin-top: 12px;
      max-width: 100%;
      min-height: 54px;
      font-weight: 400;
      font-size: 13px;
      color: #96A2BE;
    }
  }
  .double-card-wrapper {
    display: flex;
    > div:first-child {
      margin-right: 20px;
    }
  }
}



@media screen and (max-width: 768px) {
  .suitable-profession-wrapper {
    .double-card-wrapper {
      display: flex;
      flex-direction: column;
      > div:first-child {
        margin-right: 0;
      }
    }
    .g-wrapper-layout{
      margin-top: (8px / @viewportMWidth) * 100vw;
      .g-wrapper {
        border-radius: (8px / @viewportMWidth) * 100vw;
        padding: (10px / @viewportMWidth) * 100vw;
      }
    }
    .mixed-card-wrapper{
      margin-top: (12px / @viewportMWidth) * 100vw;
      .tips{
        white-space: normal;
      }
    }
  }
  .suitable-profession-tip {
    margin-bottom: (12px / @viewportMWidth) * 100vw;
    height: (60px / @viewportMWidth) * 100vw;
    .text{
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      .desc{
        font-weight: 400;
        font-size: 13px;
        color: #944A0F;
      }
    }
  }
}
