import React, { useState } from 'react';
import { Tabs } from 'antd';

import Hello from '@/assets/upgrade-major-report/hello.png';
import { BLOCK_F_STRING, IComponentProps } from '../../types';
import { BLOCK_F_MAP } from '../../logic/constants';
import { CLIENT_TYPE } from '~/lib/answer/shared/types';

import Card from '../UI/Card';
import MajorList from '../UI/MajorList';

import styles from './index.module.less';

const SuitableProfession: React.FC<IComponentProps> = (props) => {
  const { clientType = CLIENT_TYPE.pc, state, methods } = props;

  const {
    academicPotentialRecommendMajorList,
    categoryMajorRecommendMajorList,
    suitableProfessionMajorList,
  } = state || {};

  const { toBlock } = methods || {};

  const renderDesc = () => {
    /** 交集 */
    if (suitableProfessionMajorList?.length > 0) {
      return '综合你的潜能专业和职业兴趣，本测评为你推荐如下专业，这些专业既是你感兴趣的，又与你的能力相匹配，请点击专业名称了解该专业具体情况：';
    }
    /** 双卡片无交集 */
    if (
      academicPotentialRecommendMajorList?.length > 0 &&
      categoryMajorRecommendMajorList?.length > 0
    ) {
      return '根据测评结果，你感兴趣的和有潜能的专业方向目前来看不太匹配——你喜欢的可能目前还不太擅长，你擅长的可能目前不太感兴趣。';
    }
    /** 单卡片 */
    if (academicPotentialRecommendMajorList?.length > 0) {
      return `根据测评结果，你感兴趣的和有潜能的专业方向目前来看不太匹配。${
        clientType === CLIENT_TYPE.h5
          ? '结合你的选考科目，推荐你选择与自身潜能更匹配的专业方向，同时在未来培养相关兴趣，相信潜能里藏着热爱。'
          : ''
      }`;
    }

    if (categoryMajorRecommendMajorList?.length > 0) {
      return `根据测评结果，你感兴趣的和有潜能的专业方向目前来看不太匹配。${
        clientType === CLIENT_TYPE.h5
          ? '结合你的选考科目，推荐你选择自己更喜欢的专业方向，同时在未来更加努力发展能力，相信兴趣是最好的老师。'
          : ''
      }`;
    }
    return '';
  };

  const renderCard = ({
    desc = '',
    linkText = '',
    one = false,
    onClick,
  }: {
    desc?: string;
    linkText?: string;
    one?: boolean;
    onClick?: () => void;
  }) => {
    return (
      <div className={styles['g-wrapper-layout']}>
        <div className={styles['g-wrapper']}>
          {desc && !one && <div className={styles['g-top']}>{desc}</div>}
          <div className={styles['g-bottom']}>
            <div className={styles['left']}>
              {linkText}
              {one && clientType === CLIENT_TYPE.pc && (
                <div className={styles['g-desc']}>{desc}</div>
              )}
            </div>
            <div onClick={onClick} className={styles['btn']}>
              去看看
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderDom = () => {
    const str = `注：1.表中涉及的专业以教育部2024年颁布的《普通高等学校本科专业目录》为依据。\n2.除了以上推荐的专业外，你也可以根据专业详情介绍了解该专业的相近专业。\n3.具体专业开设情况和高校招生情况以当年招生章程为准。`;
    /** 交集 */
    if (suitableProfessionMajorList?.length > 0) {
      return (
        <div className={styles['mixed-card-wrapper']}>
          <MajorList
            showDesc
            dataSource={suitableProfessionMajorList}
            clientType={clientType}
            follow
          />
          <div className={styles.tips}>{str}</div>
        </div>
      );
    }
    /** 双卡片无交集 */
    if (
      academicPotentialRecommendMajorList?.length > 0 &&
      categoryMajorRecommendMajorList?.length > 0
    ) {
      return (
        <div className={styles['double-card-wrapper']}>
          {renderCard({
            desc: '如果你更笃定自己的兴趣方向，建议你可以选择自己喜欢的专业方向，同时在未来更加努力发展能力，相信兴趣是最好的老师。',
            linkText: '查看职业兴趣具体分析。',
            onClick: () => {
              toBlock(BLOCK_F_STRING.vocationalInterest);
            },
          })}
          {renderCard({
            desc: '如果你更希望发挥自身潜能，建议你可以选择自己更擅长的专业方向，同时在未来培养相关兴趣，相信潜能里藏着热爱。',
            linkText: '查看专业潜能具体分析。',
            onClick: () => {
              toBlock(BLOCK_F_STRING.professionalPotential);
            },
          })}
        </div>
      );
    }
    /** 单卡片 */
    if (academicPotentialRecommendMajorList?.length > 0) {
      return renderCard({
        desc: '结合你的选考科目，推荐你选择与自身潜能更匹配的专业方向，同时在未来培养相关兴趣，相信潜能里藏着热爱。',
        linkText: '查看专业潜能具体分析。',
        one: true,
        onClick: () => {
          toBlock(BLOCK_F_STRING.professionalPotential);
        },
      });
    }

    if (categoryMajorRecommendMajorList?.length > 0) {
      return renderCard({
        desc: '结合你的选考科目，推荐你选择自己更喜欢的专业方向，同时在未来更加努力发展能力，相信兴趣是最好的老师。',
        linkText: '查看职业兴趣具体分析。',
        one: true,
        onClick: () => {
          toBlock(BLOCK_F_STRING.vocationalInterest);
        },
      });
    }
    return null;
  };

  return (
    <>
      <Card
        id={BLOCK_F_MAP[BLOCK_F_STRING.suitableProfession].value}
        description={renderDesc()}
      >
        <div className={styles['suitable-profession-wrapper']}>
          {renderDom()}
        </div>
      </Card>
      {/* {suitableProfessionMajorList && (
        <div className={styles['suitable-profession-tip']}>
          <img className={styles['img']} src={Hello} alt="hello" />
          <div className={styles['text']}>
            温馨提示：关注报告中推荐的专业
            <div className={styles['desc']}>
              可在填报系统中快速找到匹配的志愿哦～
            </div>
          </div>
        </div>
      )} */}
    </>
  );
};

export default SuitableProfession;
