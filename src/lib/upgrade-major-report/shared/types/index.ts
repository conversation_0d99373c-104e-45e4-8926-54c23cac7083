/** 一级标题key */
export enum BLOCK_F_STRING {
  /** 报告总览 */
  overview = 'overview',
  /** 适合你的专业 */
  suitableProfession = 'suitableProfession',
  /** 专业潜能 */
  professionalPotential = 'ProfessionalPotential',
  /** 职业兴趣 */
  vocationalInterest = 'vocationalInterest',
  /** 职业价值观 */
  professionalValues = 'professionalValues',
  /** 专业决策效能感 */
  professionalDecisionMakingEfficacy = 'professionalDecisionMakingEfficacy',
  /** 结束语 */
  conclusion = 'conclusion',
}

export enum LEVEL_INT {
  first = 1,
  second = 2,
  third = 3,
}

export interface IComponentProps {
  clientType?: string;
  data?: any;
  basicData?: any;
  state?: {
    [key : string] : any;
    currentTotalLevel?: LEVEL_HIG_INT;
  };
  methods?: any;
}


export enum EFFICACY {
  /** 自我评价 */
  SA = 'SA',
  /** 收集信息 */
  CI = 'CI',
  /** 选择目标 */
  SG = 'SG',
  /** 制定规划 */
  DP = 'DP',
  /** 问题解决 */
  PS = 'PS',
}


export enum LEVEL_HIG_INT {
  low = 1,
  medium = 2,
  high = 3,
}