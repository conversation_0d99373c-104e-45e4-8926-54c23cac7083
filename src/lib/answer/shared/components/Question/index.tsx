'use client';

import React from 'react';
import { <PERSON>lide<PERSON>, Button } from 'antd';
import styles from './index.module.less';
import {
  CLIENT_TYPE,
  IQuestionStateProps,
  TYPE_STRING,
} from '../../types/index';
import cloneDeep from 'lodash/cloneDeep';
import dynamic from 'next/dynamic';

const EwtQuestion = dynamic(
  () =>
    import('paper-common').then((mod) => {
      return mod.EwtQuestion;
    }),
  {
    ssr: false,
  }
);
const EwtQuestionH5 = dynamic(
  () => import('paper-common-h5').then((mod) => mod.Question),
  {
    ssr: false,
  }
);

import 'paper-common/dist/index.css';

interface IQuestionProps {
  value: Partial<IQuestionStateProps>;
  onChange: (q: any, o?: any) => void;
  clientType?: CLIENT_TYPE;
  loading: boolean;
}

const Question: React.FC<IQuestionProps> = (props) => {
  const { value, onChange, clientType = CLIENT_TYPE.pc, loading } = props || {};
  const { sourceType } = value || {};

  const getMarks = (min: number, max: number) => {
    const offset = 10;
    let minN = min;
    const mask: any = {};
    while (minN < max) {
      mask[`${minN}`] = minN;
      minN = minN + offset;
    }
    mask[`${max}`] = max;
    return mask;
  };
  const checkboxOrder = cloneDeep(value?.checkboxOrder || []);
  const typeMapToRender: {
    [key in TYPE_STRING]?: () => JSX.Element;
  } = {
    checkboxOrder: () => {
      return (
        <div className={styles['checkbox-order-block']}>
          {value.element.map((item: any) => {
            const orderKey =
              checkboxOrder?.map((v: { id: string }) => v.id) || [];
            const isActive = orderKey.includes(item.id);
            const current = checkboxOrder.find(
              (v: { id: string }) => v.id === item.id
            );
            const currentOrderN = current?.selectOrder;
            return (
              <div
                className={`${styles['checkbox-order-wrapper']} ${
                  isActive ? styles['active'] : ''
                }`}
                key={item.id}
                onClick={() => {
                  if (isActive) {
                    const currentOrder = checkboxOrder
                      ?.filter((v: { id: string }) => v.id !== item.id)
                      ?.map((v: { selectOrder: number }) => {
                        let selectOrder = v.selectOrder;
                        if (selectOrder > currentOrderN) {
                          selectOrder--;
                        }
                        return {
                          ...v,
                          selectOrder,
                        };
                      });
                    onChange({
                      ...value,
                      checkboxOrder: currentOrder,
                    });
                    return;
                  }
                  const orderList =
                    checkboxOrder?.map(
                      (v: { selectOrder: string }) => v.selectOrder
                    ) || [];
                  let selectOrder = Math.max(0, ...orderList) + 1;
                  const currentO = {
                    id: item.id,
                    selectOrder,
                  };
                  const currentOrder = checkboxOrder.slice();
                  currentOrder.push(currentO);
                  onChange({
                    ...value,
                    checkboxOrder: currentOrder,
                  });
                }}
              >
                <span className={styles['checkbox-order-radio']}>
                  <span className={styles['checkbox-order-radio-inner']}>
                    {currentOrderN > 0 ? currentOrderN : null}
                  </span>
                </span>
                <span>{item.problem}</span>
              </div>
            );
          })}
        </div>
      );
    },
    slider: () => {
      if (clientType === CLIENT_TYPE.h5) {
        return (
          <div className={styles['h5-slider-block']}>
            <div className={styles['mark-tips']}>
              <span>最大值</span>
              <span>最小值</span>
            </div>
            <Slider
              value={value.inputNumber || 0}
              onChange={(e) => {
                onChange({
                  ...value,
                  inputNumber: e,
                });
              }}
              min={+value.element.minNum}
              max={+value.element.maxNum}
              marks={getMarks(value.element.minNum, value.element.maxNum)}
            />
          </div>
        );
      }
      return (
        <div className={styles['slider-block']}>
          <span>最大值</span>
          <Slider
            style={{
              width: 400,
            }}
            value={value.inputNumber || 0}
            onChange={(e) => {
              onChange({
                ...value,
                inputNumber: e,
              });
            }}
            min={+value.element.minNum}
            max={+value.element.maxNum}
            marks={getMarks(value.element.minNum, value.element.maxNum)}
          />
          <span>最小值</span>
        </div>
      );
    },
  };

  return (
    <div
      id="question"
      className={`${styles['question-wrapper']}  ${
        loading ? styles['pointer-events-none'] : ''
      } `}
    >
      {clientType === CLIENT_TYPE.pc && (
        <EwtQuestion
          key={value.id}
          //@ts-ignore
          value={value}
          onChange={(choices, _i, ques) => {
            onChange(ques, {
              choices,
            });
          }}
        />
      )}
      {clientType === CLIENT_TYPE.h5 && (
        <EwtQuestionH5
          key={value.id}
          //@ts-ignore
          value={value}
          onChange={(choices, _i, ques) => {
            onChange(ques, {
              choices,
            });
          }}
        />
      )}
      {sourceType && typeMapToRender[sourceType]?.()}
    </div>
  );
};

export default Question;
