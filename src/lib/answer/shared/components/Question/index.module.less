.question-wrapper {
  &.pointer-events-none{
    pointer-events: none;
  }
  .h5-slider-block,
  .slider-block {
    display: flex;
    height: 76px;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    color: #8a8a8a;
    :global {
      .ant-slider {
        margin: 0 16px;
      }
      .ant-slider-dot {
        display: none;
      }
      .ant-slider-rail {
        background: #eaf3ff;
      }
      .ant-slider-step,
      .ant-slider-track,
      .ant-slider-rail {
        height: 6px;
      }
      .ant-slider:hover .ant-slider-handle {
        border: 2px solid #ffffff;
      }
      .ant-slider-track {
        background-color: #2e86ff;
      }
      .ant-slider-handle {
        top: 1px;
        width: 20px;
        height: 20px;
        background: #2e86ff;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 4px 0 #2f87ff70;
      }
      .ant-slider-mark {
        font-size: 12px;
        margin-top: 6px;
      }
    }
  }
  .h5-slider-block{
    flex-direction: column;
    .mark-tips{
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 7px;
    }
    :global{
      .ant-slider{
        width: 100%;
      }
    }
  }
  .checkbox-order-block {
    .checkbox-order-wrapper {
      padding-left: 20px;
      display: flex;
      align-items: center;
      height: 46px;
      background: #eaf3ff;
      border-radius: 4px;
      font-size: 14px;
      color: #333333;
      margin-bottom: 16px;
      cursor: pointer;
      &.active{
        background: #2E86FF;
        color: #FFFFFF;
      }
      .checkbox-order-radio {
        width: 24px;
        height: 24px;
        background: #ffffff;
        border-radius: 15px;
        margin-right: 20px;
        .checkbox-order-radio-inner{
          display: flex;
          align-items: center;
          justify-content: center;
          color: #2E86FF;
          height: 100%;
        }
      }
    }
  }
}
