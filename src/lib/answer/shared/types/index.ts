
import type {
  GetEvaluationQuestion
} from '~/service/psychology/evaluation/types';

export enum TYPE_STRING {
  radio = 'radio',
  checkbox = 'checkbox',
  scale = 'scale',
  checkboxOrder = 'checkboxOrder',
  slider = 'slider',
  text = 'text',
  compound = 'compound',
}

export enum TYPE_INT {
  radio = 1,
  scale = 1,
  checkbox = 2,
  compound = 21,
}


export enum RULE_TYP {
  /** 最少选 */
  gt = 'gt',
  /** 必须选 */
  eq = 'eq'
}

export enum CLIENT_TYPE {
  pc = '1',
  h5 = '4',
}

export const typeMapToValue = {
  [TYPE_STRING.radio]: 1,
  [TYPE_STRING.scale]: 1,
  [TYPE_STRING.checkbox]: 2,
  [TYPE_STRING.compound]: 21,
};


export const rulesToLabel = {
  [RULE_TYP.gt]: '最少选',
  [RULE_TYP.eq]: '必须选',
};

export type N_KEYS = keyof typeof typeMapToValue;

export interface IQuestionStateProps extends  Omit<GetEvaluationQuestion.Data, 'type'> {
  type: TYPE_INT
  sourceType: TYPE_STRING;
  children: IQuestionStateProps[]
  studentAnswers: any;
  element: any;
  options: any[];
}