import {
  TYPE_STRING,
  TYPE_INT,
  N_KEYS,
  rulesToLabel,
  RULE_TYP,
} from '../types/index';

export const letter26: string[] = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
];

export const typeMapToValue = {
  [TYPE_STRING.radio]: 1,
  [TYPE_STRING.scale]: 1,
  [TYPE_STRING.checkbox]: 2,
  [TYPE_STRING.compound]: 21,
};

export const typeMapToLabel = {
  [TYPE_STRING.radio]: '单选题',
  [TYPE_STRING.checkbox]: '多选题',
  [TYPE_STRING.scale]: '量表题',
  [TYPE_STRING.checkboxOrder]: '多选排序题，按先后顺序点击选择',
  [TYPE_STRING.slider]: '滑块题',
  [TYPE_STRING.text]: '描述题',
  [TYPE_STRING.compound]: '复合题',
};

const _getStudentAnswers = ({
  type,
  options,
  answer,
}: {
  type: TYPE_INT;
  options: any[];
  answer: any;
}) => {
  const selectIds =
    type === TYPE_INT.radio
      ? [answer.selectId]
      : answer.checkboxOrder?.map((v: { id: any }) => v.id) || [];
  return options
    ?.filter((v: { id: any }) => selectIds?.includes(v.id))
    ?.map((v: { choice: any }) => v.choice);
};

export const transformQuestionToValue = (
  data: any,
  option: {
    type?: '_init' | '_change';
    pageIndex?: number;
    is_upgrade_major: boolean;
    choices?: string[];
  }
) => {
  const { pageIndex, type = '_init', choices, is_upgrade_major } = option || {};

  const _init = () => {
    const element = JSON.parse(data.elementContent || '{}');
    const children = data?.childElementDetailList?.map((v: any) =>
      transformQuestionToValue(v, option)
    );

    const options =
      element?.map?.((v: { problem: any }, i: number) => ({
        option: v.problem,
        choice: letter26[i],
        index: i,
        ...v,
      })) || [];

    const studentAnswers = _getStudentAnswers({
      type: typeMapToValue[data.type as N_KEYS],
      options,
      answer: data,
    });

    const _getContent = () => {
      const selectNum =
        data.type === TYPE_STRING.checkbox
          ? `、${
              rulesToLabel[(data.selectNumType || RULE_TYP.gt) as RULE_TYP]
            }${data.selectNum || 1}项`
          : '';

      const index = is_upgrade_major ? data.elementIndex : pageIndex;
      const showIndexAndLabel = is_upgrade_major
        ? ![TYPE_STRING.text, TYPE_STRING.compound].includes(data.type)
        : true;
      const aIndex = showIndexAndLabel ? `${index}、` : '';
      const label = showIndexAndLabel
        ? `（${typeMapToLabel[data.type as N_KEYS]}${selectNum}）`
        : '';
      const title =
        data.type === TYPE_STRING.text ? data.description : data.title;

      return `<div class='title-wrapper' >${aIndex}${label}${title}</div>`;
    };
    const value = {
      ...data,
      element,
      questionId: data.id,
      sourceType: data.type,
      content: _getContent(),
      type: typeMapToValue[data.type as N_KEYS],
      studentAnswers,
      answers: studentAnswers,
      options: [TYPE_INT.radio, TYPE_INT.checkbox].includes(
        typeMapToValue[data.type as N_KEYS]
      )
        ? options
        : [],
      children,
    };
    return value;
  };

  const _change = () => {
    let checkboxOrder =
      data?.type === TYPE_INT.checkbox
        ? data?.options
            ?.map((v: any) => ({ ...v, selectOrder: v.index }))
            ?.filter((v: { choice: string }) => choices?.includes(v.choice))
        : data.checkboxOrder;
    let selectId =
      data?.type === TYPE_INT.radio
        ? data?.options?.find((v: { choice: string }) =>
            choices?.includes(v.choice)
          )?.id
        : undefined;
    let selectOrder =
      data?.type === TYPE_INT.radio
        ? data?.options?.findIndex((v: { choice: string }) =>
            choices?.includes(v.choice)
          )
        : undefined;

    return {
      ...data,
      questionId: data.id,
      selectId,
      selectOrder,
      checkboxOrder,
      sourceType: data?.sourceType || data?.type,
      answers: choices,
      studentAnswers: choices,
    };
  };
  return {
    _init,
    _change,
  }[type]();
};

export const transformQuestionToPayload = (value: any) => {
  const {
    userRecordId,
    templateVersionId,
    questionId,
    elementId,
    selectId,
    selectOrder,
    inputNumber,
    checkboxOrder,
    clientType,
    sourceType,
    elementAnswerDTOList,
  } = value || {};
  const tCheckboxOrder = checkboxOrder?.length
    ? checkboxOrder?.map(
        (v: {
          id: any;
          selectOrder: any;
          elementLatitudeId: any;
          selectScore: any;
        }) => ({
          id: v.id,
          selectOrder: v.selectOrder,
          elementLatitudeId: v.elementLatitudeId,
          selectScore: v.selectScore,
        })
      )
    : undefined;
  const payload = {
    userRecordId,
    templateVersionId,
    questionId,
    elementId,
    selectId,
    selectOrder,
    inputNumber: sourceType === TYPE_STRING.slider ? inputNumber : undefined,
    checkboxOrder: tCheckboxOrder,
    clientType,
    type: sourceType,
    elementAnswerDTOList,
  };
  return payload;
};
