.answer-layout-wrapper {
  overflow: hidden;
  position: relative;
  height: 100vh;
  .title-wrapper {
    display: inline-flex;
    flex-wrap: wrap;
  }
  .pc-layout-block {
    position: relative;
    width: 920px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .global-loading{
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .lottie-loading{
        width: 100px;
        height: 100px;
      }
      .global-loading-text{
        font-size: 14px;
        color: #8A8A8A;
        margin-top: 10px;
      }
    }
    .feign-answer{
      .title{
        padding-top: 24px;
        color: #333;
        font-size: 22px;
        padding-bottom: 24px;
        text-align: center;
      }
      .content{
        border-bottom: 1px dashed #d8d8d8;
        border-top: 1px dashed #d8d8d8;
        padding: 50px !important;
        box-sizing: border-box;
        .desc{
          margin-top: 20px;
          padding-left: 30px;
          .color-r{
            color: #ff4d4f;
          }
        }
      }
    }

    .guide-content{
      padding: 28px;
      font-size: 14px;
    }
    .user {
      height: 21px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 12px;
    }
    .pc-question-block {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      box-sizing: border-box;
      padding-bottom: 28px;
      width: 100%;
      min-height: 492px;
      max-height: 85vh;
      background: #ffffff;
      border: 1px solid #eff0f1;
      box-shadow: 0 2px 6px 0 #e6e6e6;
      border-radius: 4px;
      overflow: hidden;
      .action {
        display: flex;
        justify-content: flex-end;
        padding: 0 28px;
        margin-top: 28px;
        .action-item {
          margin-left: 30px;
          padding: 0 16px;
          height: 40px;
          line-height: 40px;
          background: #2e86ff;
          border-radius: 4px;
        }
        .action-item[disabled],
        .action-item[disabled]:hover,
        .action-item[disabled]:focus,
        .action-item[disabled]:active {
          background-color: #f5f5f5;
        }
      }
      .content{
        height: 100%;
        overflow: auto;
        flex: 1;
        padding: 0 28px;
      }
      .question {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      .alert{
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        height: 60px;
        margin-bottom: 20px;
        background: #FFF8EC;
        border-radius: 4px 4px 0 0;
        font-weight: 400;
        font-size: 14px;
        color: #FF7F21;
      }
      .header {
        position: relative;
        margin: 28px 28px 18px;
        .percent-tips {
          text-align: right;
          font-weight: 500;
          font-size: 16px;
          color: #8a8a8a;
        }
        .percent-index {
          color: #333333;
        }
        .tips {
          min-width: 91px;
          height: 50px;
          display: flex;
          justify-content: center;
          position: absolute;
          padding: 3px;
          padding-left:6px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          font-weight: 500;
          font-size: 12px;
          color: #323767;
        }
      }
    }
    }
    :global {
      .ant-progress{
        margin-top: 10px;
      }
      .pm-qnum-width{
        display: none !important;
      }
      .pm-question-label{
        display: none !important;
      }
      .pm-content {
        font-size: 16px;
        color: #222222;
        margin-bottom: 24px;
        padding-left:0 !important;
        .mst-text-indent{
          display: inline-block;
          width: 2em;
        }
        img{
          max-width: 100% !important;
          height: auto !important;
        }
      }
      .pm-ewt-option-item {
        box-sizing: border-box;
        border: 1px solid transparent !important;
        overflow: hidden;
        border-radius: 4px !important;
      }
      .pm-ewt-option-item:hover {
        border: 1px solid #2E86FF !important;
      }
      .pm-ewt-option-item.correct:hover,
      .pm-ewt-option-item.selected:hover {
        border: 1px solid transparent !important;
      }
      .pm-option-box-for-ui {
        border-radius: 4px !important;
      }
      .pm-ewt-option-item.correct,
      .pm-ewt-option-item.selected {
        border: none;
        .pm-option-box-for-ui {
          background-color: #2e86ff;
        }
        .pm-ewt-option-content {
          color: #fff !important;
        }
      }
      .pm-option-box-for-ui {
        display: flex;
        align-items: center;
        min-height: 46px;
        line-height: 20px;
        background: #eaf3ff;
        border: none;
      }
      .pm-option-tag {
        display: none;
      }
      .pm-ewt-option-content {
        box-sizing: border-box;
        min-height: 100%;
        margin: 0 !important;
        padding: 13px 20px !important;
        color: #333 !important;
        line-height: 20px !important;
      }
    }
  }
}