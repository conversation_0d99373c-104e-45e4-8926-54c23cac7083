import React, { useState } from 'react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';

import { Button, message, Modal, Progress } from 'antd';
import { useSetState } from 'ahooks';
import type {
  GetEvaluationInfo,
  GetEvaluationByType,
} from '~/service/psychology/evaluation/types';
import { UserBaseInfo } from '@/service/common/types';

import { goToPermissionPage } from '@/utils/tools';
import SafeLogger from '~/utils/safe-logger';

import DataJson from '../../lottie/data.json';
import Animation from '../../lottie/animation.json';
import LoadingJson from '../../lottie/loading.json';

import BgTips from '@/assets/psychology/bg-tips.png';

import useAnimation from '../../hooks/useAnimation';
import useQuestion from '../../hooks/useQuestion';

import { CLIENT_TYPE } from '../../shared/types/index';

const Question = dynamic(() => import('../../shared/components/Question'), {
  ssr: false,
});

const Lottie = dynamic(() => import('lottie-react'), { ssr: false });
import styles from './index.module.less';

interface IAnswerProps {
  basic: GetEvaluationInfo.Response['data'] &
    GetEvaluationByType.Response['data'];
  userInfo?: UserBaseInfo.Response['data'];
  onReStart: () => void;
  clientType: CLIENT_TYPE;
}

const AnswerPC: React.FC<IAnswerProps> = (props) => {
  const {
    basic,
    userInfo,
    onReStart,
    clientType = CLIENT_TYPE.pc,
  } = props || {};
  const router = useRouter();
  const { evaluationTemplateType }: any = router?.query || {};
  /** 升学测评 */
  const is_upgrade_major = evaluationTemplateType === 'upgrade_major';
  const { guideContent, answerInfo } = basic || {};

  const [globalLoading, setGlobalLoading] = useState(false);

  const [pageState, setPageState] = useSetState({
    pageIndex: 1,
    pageSize: 1,
  });

  const [feignState, setFeignState] = useSetState({
    feignAnswer: answerInfo?.feignAnswer,
    feignAnswerTimes: answerInfo?.feignAnswerTimes || 0,
  });

  const {
    state: { showTips },
    lottieRef,
    aLottieRef,
    startTipsAm,
    stop,
    play,
    setIsAlerts,
  } = useAnimation();

  const {
    change: { onQuestionChange },
    state: {
      index,
      percent,
      showGuideContent,
      showProgress,
      showHeader,
      totalIndex,
      questionInfo,
      questionDoing,
    },
    actions,
    loading: { questionGlobalLoading, submitAndCheckLoading },
  } = useQuestion({
    basic,
    userInfo,
    pageState,
    feignState,
    setPageState,
    animation: {
      play,
      aLottieRef,
      startTipsAm,
      stop,
      setIsAlerts,
    },
    onReStart,
    onCheck: ({ type, cancelText, content, okText, _reset }) => {
      if (type === 'modal') {
        Modal.confirm({
          centered: true,
          cancelText,
          content,
          okText,
          onCancel: async () => {
            // 重新作答
            try {
              _reset();
              onReStart();
            } catch (e) {}
          },
        });
      }
    },
    onSubmit: async ({ check, payload, completed, _reset}) => {
      const { data } = await check(payload);
      if ((data || []).length > 0) {
        setPageState({
          pageIndex: data[0],
        });
        return;
      }
      const res = await completed({
        userId: userInfo?.userId,
        ...payload,
        clientType,
      });

      const {
        feignAnswer = false,
        feignAnswerTimes = 0,
        personalReportUrl = '',
      } = res?.data || {};

      // 判断是否伪答
      if (feignAnswer) {
        _reset();
        setFeignState({
          feignAnswer,
          feignAnswerTimes,
        });
        return;
      }

      if (!personalReportUrl) {
        Modal.confirm({
          centered: true,
          title: '提示',
          content: '答题开小差了，请稍后重试~',
          okText: '我知道了',
          cancelButtonProps: { style: { display: 'none' } },
        });
        SafeLogger?.baseLogger?.error('personalReportUrl-none', {
          reason: '没有获取到报告地址',
          error: res,
        });
        return;
      }
      if (is_upgrade_major) {
        const offsetTime = 3000;
        setGlobalLoading(true);
        setTimeout(() => {
          location.replace(personalReportUrl);
        }, offsetTime);
        return;
      }
      // 都已经答题完毕 跳转
      if (
        personalReportUrl.includes('?') ||
        personalReportUrl.includes('CompleteAnswer')
      ) {
        location.replace(personalReportUrl);
      } else {
        location.replace(
          `${personalReportUrl}?evaluationRecordId=${payload.userRecordId}`
        );
      }
    },
    onError: (error, _key, _reset) => {
      const { data } = error || {}
      const { success, code, msg } = data || {}
      
      if (code && ['9091401', '9091402', '9091403'].indexOf(code) !== -1) {
        message.error(msg);
        setTimeout(() => {
          goToPermissionPage({
            code,
            msg,
          });
        }, 500);
        return;
      }
      if (code === '019101100') {
        Modal.confirm({
          centered: true,
          title: '提示',
          content: msg,
          okText: '重新测试',
          cancelButtonProps: { style: { display: 'none' } },
          onOk: async () => {
            try {
              _reset?.();
              onReStart();
            } catch (e) {}
          },
        });
        return;
      }
      SafeLogger?.baseLogger?.error(_key, {
        reason: '答题相关请求出现异常了',
        error,
      });
      if (!success) {
        Modal.confirm({
          centered: true,
          title: '提示',
          content: msg || '答题开小差了，请稍后重试~',
          okText: '我知道了',
          cancelButtonProps: { style: { display: 'none' } },
        });
      }
    },
    onActionCheck: ({ type, content }) => {
      if (type === 'toast') {
        message.error(content);
      }
    },
    onLink: (url: string)=>{
      location.replace(url);
    }
  });

  const headerA = () => {
    if (!showHeader) {
      return <div style={{ marginBottom: 28 }}></div>;
    }
    return showProgress ? (
      <div className={styles['header']}>
        {totalIndex > 0 && (
          <div className={styles['percent-tips']}>
            <span className={styles['percent-index']}>{index}</span>/
            {totalIndex}
          </div>
        )}
        <Progress
          percent={percent}
          strokeColor="#2E86FF"
          trailColor="#DFDFDF"
          showInfo={false}
        />
        <Lottie
          style={{
            position: 'absolute',
            width: 26,
            left: `${percent}%`,
            top: 22,
            transition: 'all 0.3s',
            transform: 'translate(-50%, 0)',
          }}
          // @ts-ignore
          lottieRef={lottieRef}
          animationData={DataJson}
          autoplay={false}
          assetsPath={`${process.env.LOTTIEASSETPATH}/images/`}
        />
        <Lottie
          style={{
            position: 'absolute',
            left: `${percent}%`,
            top: 0,
            width: 140,
            transition: 'all 0.3s',
            transform: 'translate(-50%, -40%)',
          }}
          // @ts-ignore
          lottieRef={aLottieRef}
          animationData={Animation}
          loop={false}
          autoplay={false}
        />
        <div
          className={styles['tips']}
          style={{
            backgroundImage: `url(${BgTips})`,
            left: `${percent}%`,
            top: 10,
            transition: 'all 0.3s',
            transform: 'translate(26%, -40%)',
            opacity: showTips ? 1 : 0,
          }}
        >
          前半程已通关，
          <br />
          后半程更精彩！
        </div>
      </div>
    ) : (
      <div className={styles['alert']}>
        请确认你的优势学科再点击下一题，若在答题过程中退回重新选择优势学科，原来的答题记录将不再保留
      </div>
    );
  };

  return (
    <div className={styles['answer-layout-wrapper']}>
      <div className={styles['pc-layout-block']}>
        {userInfo?.realName && (
          <h5 className={styles['user']}>
            你好，<span>{userInfo?.realName}</span>
          </h5>
        )}
        <div className={styles['pc-question-block']}>
          {globalLoading ? (
            <div className={styles['global-loading']}>
              <Lottie
                className={styles['lottie-loading']}
                animationData={LoadingJson}
                autoplay={true}
                loop={true}
                assetsPath={`${process.env.LOTTIEASSETPATH}/loading/`}
              />
              <div className={styles['global-loading-text']}>报告生成中...</div>
            </div>
          ) : (
            <>
              {showGuideContent && !questionDoing && (
                // 首屏引导内容
                <div
                  className={styles['guide-content']}
                  dangerouslySetInnerHTML={{
                    __html: guideContent || '',
                  }}
                />
              )}
              {feignState.feignAnswer && !questionDoing && (
                // 伪答内容
                <div className={styles['feign-answer']}>
                  <h3 className={styles['title']}>
                    规范答题告知书（
                    {feignState.feignAnswerTimes > 1 ? '二' : '一'}）
                  </h3>
                  <div className={styles['content']}>
                    <h5>同学：</h5>
                    {feignState.feignAnswerTimes <= 1 ? (
                      <div className={styles['desc']}>
                        <h5>
                          你好！你本次的作答被系统判定为无效，
                          <b>
                            因为你未经思考匆忙选择，或答题过程中存在不真实作答的情况
                          </b>
                          ，你还有一次重新作答的机会，请仔细阅读下列
                          《测评须知》后，点击下方 蓝色
                          按钮，返回答题首页再次作答。
                        </h5>
                        <ol>
                          <li>测评须知：</li>
                          <li>
                            1、本次测评题目
                            <b>没有对错之分</b>
                            ，请你仔细阅读答题过程中的提示，根据要求并结合你的实际情况真实作答。
                          </li>
                          <li>
                            2、测评有不认真作答的鉴别机制，若未经思考匆忙选择，会被系统判定为
                            <b>无效</b>。
                          </li>
                          <li>3、你还有一次答题机会，请务必审慎作答。</li>
                        </ol>
                      </div>
                    ) : (
                      <div className={styles['desc']}>
                        <h5>
                          你好！经系统鉴别，
                          <b>你两次作答均不符合测评答题的要求和规范，</b>
                          <span className={styles['color-r']}>
                            被判定为 无效问卷
                          </span>
                          。本次答题将关闭 ，请联系负责老师为你重新安排答题。
                        </h5>
                        <ol>
                          <li>再次提醒：</li>
                          <li>
                            1、本次测评题目没有对错之分，请你仔细阅读答题过程中的提示，根据要求并结合你的实际情况真实作答。
                          </li>
                          <li>
                            2、测评有不认真作答的鉴别机制，若未经思考匆忙选择，会被系统判定为
                            <span className={styles['color-r']}>无效</span>。
                          </li>
                        </ol>
                      </div>
                    )}
                  </div>
                </div>
              )}
              {questionDoing && (
                <div className={styles['question']}>
                  {headerA()}
                  <div className={styles['content']}>
                    <Question
                      key={questionInfo.id}
                      clientType={clientType}
                      loading={questionGlobalLoading || submitAndCheckLoading}
                      value={questionInfo}
                      onChange={onQuestionChange}
                    />
                  </div>
                </div>
              )}
              <div className={styles['action']}>
                {actions.map(({ label, visible, ...btnProps }, i) => (
                  // @ts-ignore
                  <Button
                    className={styles['action-item']}
                    key={i}
                    {...btnProps}
                  >
                    {label}
                  </Button>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnswerPC;
