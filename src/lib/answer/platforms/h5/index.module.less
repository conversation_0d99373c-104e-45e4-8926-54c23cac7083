@viewportMWidth: 375px;

.answer-layout-wrapper {
  overflow: hidden;
  position: relative;
  height: 100vh;
  .global-loading{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .lottie-loading{
      width: 100px;
      height: 100px;
    }
    .global-loading-text{
      font-size: 14px;
      color: #8A8A8A;
      margin-top: 10px;
    }
  }
  .title-wrapper {
    display: inline-flex;
    flex-wrap: wrap;
  }
  .h5-layout-block {
    display: flex;
    height: 100vh;
    flex-direction: column;
    overflow: hidden;
    .user {
      min-height: (21px / @viewportMWidth) * 100vw;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      margin-bottom: (12px / @viewportMWidth) * 100vw;
    }
    .h5-question-block {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      box-sizing: border-box;
      width: 100%;
      background: #ffffff;
      .h5-guide-wrapper {
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
        .h5-top-banner {
          padding: (45px / @viewportMWidth) * 100vw (20px / @viewportMWidth) *
            100vw 0;
          height: (200px / @viewportMWidth) * 100vw;
          color: #fff;
          .title {
            font-size: 20px;
            font-weight: 500;
            margin-bottom: (10px / @viewportMWidth) * 100vw;
          }
        }
        .guide-content {
          font-size: 14px;
          height: 100%;
          overflow: auto;
          padding: (20px / @viewportMWidth) * 100vw (15px / @viewportMWidth) *
            100vw (50px / @viewportMWidth) * 100vw;
        }
      }
      .feign-answer{
        padding: 16px;
        font-size: 14px;
        box-sizing: border-box;
        b{
          color: #50596f;
        }
        .title{
          color: #50596f;
          font-size: 16px;
          font-weight: 700;
        }
        .mb-16px{
          margin-bottom: 16px;
        }
        .content{
          margin-top: 16px;
          box-sizing: border-box;
          .desc{
            .color-r{
              color: #ff4d4f;
            }
          }
        }
      }
    }
    .action {
      display: flex;
      width: 100%;
      background: #f4f3f8;
      justify-content: flex-end;
      padding-bottom: calc(constant(safe-area-inset-bottom));
      padding-bottom: calc(env(safe-area-inset-bottom));
      .action-item {
        :global{
          .adm-spin-loading{
            --size: 20px !important;
            --color: #2e86ff !important;
            margin-right: (6px / @viewportMWidth) * 100vw;
          }
        }
        &.start {
          width: 100%;
          font-size: 16px;
          background-color: #2e86ff;
          color: #fff;
        }
        padding: 0 (16px / @viewportMWidth) * 100vw;
        color: #2e86ff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        min-height: 48px;

        &.disabled {
          color: #666;
          cursor: not-allowed;
        }
      }
    }
    .question {
      height: 100%;
      display: flex;
      flex-direction: column;
      .alert{
        height: (60px / @viewportMWidth) * 100vw;
        display: flex;
        align-items: center;
        padding: 0 (16px / @viewportMWidth) * 100vw;
        background: #FFF8EC;
        font-weight: 400;
        font-size: 14px;
        color: #FF7F21;
      }
      .mb-16{
        margin-bottom: (16px / @viewportMWidth) * 100vw;
      }
      .header {
        position: relative;
        margin: 0  (16px / @viewportMWidth) * 100vw;
        padding: (8px / @viewportMWidth) * 100vw 0 (12px / @viewportMWidth) * 100vw;
        min-height: (68px / @viewportMWidth) * 100vw;

        .lottie-ip{
          position: absolute;
          height: (42px / @viewportMWidth) * 100vw;
          top: (30px / @viewportMWidth) * 100vw;
          transition: all 0.3s;
          width: max-content;
          transform: translate(-50%, 0);
        }
        .lottie-ani{
          position: absolute;
          top: 0;
          transition: all 0.3s;
          transform: translate(-50%, -40%);
        }
        .percent-tips {
          text-align: right;
          font-weight: 500;
          font-size: (14px / @viewportMWidth) * 100vw;
          line-height: (20px / @viewportMWidth) * 100vw;
          height: (20px / @viewportMWidth) * 100vw;
          color: #8a8a8a;
          margin-bottom: (4px / @viewportMWidth) * 100vw;
        }
        .percent-index {
          color: #333333;
        }
        .tips {
          min-width: (91px / @viewportMWidth) * 100vw;
          height: (50px / @viewportMWidth) * 100vw;
          display: flex;
          justify-content: center;
          position: absolute;
          padding: 3px  0;
          top: (6px / @viewportMWidth) * 100vw;
          padding-left: (6px / @viewportMWidth) * 100vw;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          font-weight: 500;
          transition: all 0.3s;
          transform: translateX(10%);
          font-size: (12px / @viewportMWidth) * 100vw;
          color: #323767;
        }
      }
      .content {
        padding: 0 (16px / @viewportMWidth) * 100vw (16px / @viewportMWidth) *
          100vw;
        height: 100%;
        overflow: auto;
        flex: 1;
        &.pt-10{
          padding-top: (10px / @viewportMWidth) * 100vw;
        }
      }
    }
    :global {
      .ant-progress{
        margin-top: (10px / @viewportMWidth) * 100vw;
        height: (22px / @viewportMWidth) * 100vw;
        .ant-progress-outer{
          height: (22px / @viewportMWidth) * 100vw;
          .ant-progress-inner{
            height: (8px / @viewportMWidth) * 100vw;
            .ant-progress-bg{
              height: (8px / @viewportMWidth) * 100vw !important;
            }
          }
        }
      }
      .pmm-question-label{
        display: none;
      }
      .pmm-content {
        margin-bottom: 20px;
        font-size: 14px;
        .mst-text-indent{
          display: inline-block;
          width: 2em;
        }
        img{
          max-width: 100% !important;
          height: auto !important;
        }
      }
      .pmm-qo__option.checked,
      .pmm-qo__option.correct {
        background-color: #2e86ff !important;
        .pmm-qo__content {
          padding: 0.8em 1.2em !important;
          color: #fff !important;
          transition: none;
          font-size: 14px !important;
        }
      }
      .pmm-qo__option {
        border-radius: 4px;
        display: flex;
        align-items: center;
        min-height: 46px;
        line-height: 20px;
        background: #eaf3ff;
        border: none;
        margin-bottom: 10px;
      }
      .pmm-qo__tag {
        display: none;
      }
      .pmm-qo__content {
        font-size: 14px !important;
        box-sizing: border-box;
        min-height: 100%;
        padding: 0.8em 1.2em !important;
        color: #333 !important;
        line-height: 20px;
      }
    }
  }
}
.global-modal {
  :global {
    .adm-space {
      flex-direction: row;
      justify-content: space-between;
      .adm-space-item {
        margin-bottom: 0;
      }
    }
  }
}
.h5-popup-wrapper {
  background: #ffffff;
  border-radius: (20px / @viewportMWidth) * 100vw (20px / @viewportMWidth) *
    100vw 0 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  min-height: (262px / @viewportMWidth) * 100vw;
  .h5-popup-block {
    position: relative;
    padding: (16px / @viewportMWidth) * 100vw;
    .h5-popup-close {
      position: absolute;
      font-size: 14px;
      right: (19px / @viewportMWidth) * 100vw;
      top: (16px / @viewportMWidth) * 100vw;
      color: #a7acb9;
    }
    .h5-popup-title {
      font-weight: 600;
      font-size: 18px;
      color: #333333;
      text-align: center;
      margin-top: 8px;
      line-height: 16px;
    }
    .h5-popup-content {
      display: flex;
      align-items: center;
      padding-left: 12px;
      font-weight: 600;
      height: (98px / @viewportMWidth) * 100vw;
      margin-top: (33px / @viewportMWidth) * 100vw;
      background: #eef1f6;
      border-radius: 8px;
    }
    .h5-popup-bg {
      position: absolute;
      right: (19px / @viewportMWidth) * 100vw;
      top: (50px / @viewportMWidth) * 100vw;
      width: (121px / @viewportMWidth) * 100vw;
      height: (129px / @viewportMWidth) * 100vw;
      background-size: contain;
      background-repeat: no-repeat;
    }
    .h5-popup-action {
      display: flex;
      justify-content: space-between;
      div {
        display: flex;
        justify-content: center;
        align-items: center;
        width: (164px / @viewportMWidth) * 100vw;
        height: (44px / @viewportMWidth) * 100vw;
        margin-top: (30px / @viewportMWidth) * 100vw;
        background: #eef1f6;
        border-radius: (22px / @viewportMWidth) * 100vw;
        font-weight: 600;
        font-size: 16px;
        color: #666666;
      }
      .primary {
        background: #2d86fe;
        color: #ffffff;
      }
    }
  }
}
