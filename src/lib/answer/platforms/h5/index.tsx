import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { Progress } from 'antd';
import { useSetState } from 'ahooks';
import { Modal as mModal, Popup, Toast, SpinLoading } from 'antd-mobile';
import type {
  GetEvaluationInfo,
  GetEvaluationByType,
} from '~/service/psychology/evaluation/types';
import { UserBaseInfo } from '@/service/common/types';
import DataJson from '../../lottie/data.json';
import Animation from '../../lottie/animation.json';
import LoadingJson from '../../lottie/loading.json';
import SafeLogger from '~/utils/safe-logger';

import { goToPermissionPage } from '@/utils/tools';

import BgTips from '@/assets/psychology/bg-tips.png';
import PlanBannerImg from '@/assets/psychology/bg.png';
import PopupBg from '@/assets/psychology/popup-bg.png';

import useAnimation from '../../hooks/useAnimation';
import useQuestion from '../..//hooks/useQuestion';

import { closeCurrentAndOpenWebView } from '@/utils/tools';

import { CLIENT_TYPE } from '../../shared/types/index';
const Question = dynamic(() => import('../../shared/components/Question'), {
  ssr: false,
});
const Lottie = dynamic(() => import('lottie-react'), { ssr: false });

import styles from './index.module.less';

interface IAnswerProps {
  basic: GetEvaluationInfo.Response['data'] &
    GetEvaluationByType.Response['data'];
  userInfo?: UserBaseInfo.Response['data'];
  onReStart: () => void;
  clientType: CLIENT_TYPE;
}

const AnswerH5: React.FC<IAnswerProps> = (props) => {
  const router = useRouter();
  const { evaluationTemplateType }: any = router?.query || {};
  /** 升学测评 */
  const is_upgrade_major = evaluationTemplateType === 'upgrade_major';
  const [open, setOpen] = useState(false);
  const [globalLoading, setGlobalLoading] = useState(false);

  const [pageState, setPageState] = useSetState({
    pageIndex: 1,
    pageSize: 1,
  });

  const {
    basic,
    userInfo,
    onReStart,
    clientType = CLIENT_TYPE.h5,
  } = props || {};
  const { guideContent, answerInfo, evaluationTemplateTitle } = basic || {};

  const [feignState, setFeignState] = useSetState({
    feignAnswer: answerInfo?.feignAnswer,
    feignAnswerTimes: answerInfo?.feignAnswerTimes || 0,
  });

  const {
    state: { showTips },
    lottieRef,
    aLottieRef,
    startTipsAm,
    stop,
    play,
    setIsAlerts,
  } = useAnimation();

  const {
    change: { onQuestionChange },
    state: {
      index,
      percent,
      showGuideContent,
      showProgress,
      total,
      showHeader,
      totalIndex,
      questionInfo,
      questionDoing,
    },
    actions,
    loading: { questionGlobalLoading, submitAndCheckLoading },
  } = useQuestion({
    basic,
    userInfo,
    pageState,
    feignState,
    setPageState,
    animation: {
      play,
      aLottieRef,
      startTipsAm,
      setIsAlerts,
      stop,
    },
    onReStart,
    onSubmit: async ({ check, payload, completed, _reset }) => {
      const { data } = await check(payload);
      if ((data || []).length > 0) {
        setPageState({
          pageIndex: data[0],
        });
        return;
      }

      const res = await completed({
        userId: userInfo?.userId,
        ...payload,
        clientType,
      });

      const {
        feignAnswer = false,
        feignAnswerTimes = 0,
        personalReportUrl = '',
      } = res?.data || {};
      // 判断是否伪答
      if (feignAnswer) {
        _reset();
        setFeignState({
          feignAnswer,
          feignAnswerTimes,
        });
        return;
      }
      if (!personalReportUrl) {
        Toast.show({
          content: '答题开小差了，请稍后重试~',
        });
        SafeLogger?.baseLogger?.error('personalReportUrl-none', {
          reason: '没有获取到报告地址',
          error: res,
        });
        return;
      }
      if (is_upgrade_major) {
        const offsetTime = 3000;
        setGlobalLoading(true);
        setTimeout(() => {
          closeCurrentAndOpenWebView(personalReportUrl);
        }, offsetTime);
        return;
      }

      if (
        personalReportUrl.includes('?') ||
        personalReportUrl.includes('CompleteAnswer')
      ) {
        closeCurrentAndOpenWebView(personalReportUrl);
      } else {
        closeCurrentAndOpenWebView(
          `${personalReportUrl}?evaluationRecordId=${payload.userRecordId}`
        );
      }
    },
    onCheck: ({ type, cancelText, content, okText, _reset }) => {
      if (type === 'modal') {
        mModal.show({
          title: '提示',
          bodyClassName: styles['global-modal'],
          content,
          closeOnAction: true,
          actions: [
            {
              key: '1',
              text: cancelText,
              onClick: async () => {
                try {
                  _reset();
                  onReStart();
                } catch (e) {}
              },
            },
            {
              key: '2',
              text: okText,
            },
          ],
        });
      }
    },
    onError: (error, _key, _reset) => {
      const { data } = error || {}
      const { success, code, msg } = data || {}

      if (code && ['9091401', '9091402', '9091403'].indexOf(code) !== -1) {
        Toast.show({
          content: msg,
        });
        setTimeout(() => {
          goToPermissionPage({
            code,
            msg,
          });
        }, 500);
        return;
      }
      if (code === '019101100') {
        mModal.show({
          title: '提示',
          content: msg || '答题出差了',
          closeOnAction: true,
          actions: [
            {
              key: '1',
              text: '重新测试',
              onClick: async () => {
                try {
                  _reset?.();
                  onReStart();
                } catch (e) {}
              },
            },
          ],
        });
        return;
      }
      SafeLogger?.baseLogger?.error(_key, {
        reason: '答题相关请求出现异常了',
        error,
      });
      if (!success) {
        mModal.show({
          title: '提示',
          content: msg || '答题开小差了，请稍后重试~',
          closeOnAction: true,
          actions: [
            {
              key: '1',
              text: '我知道了',
            },
          ],
        });
      }
    },
    onActionCheck: ({ type, content }) => {
      if (type === 'toast') {
        Toast.show({
          content,
        });
      }
    },
    onLink: (url) => {
      closeCurrentAndOpenWebView(url);
    },
  });

  useEffect(() => {
    window.ewt_goBack = () => {
      setOpen(true);
      return false;
    };
    return () => {
      window.ewt_goBack = () => true;
    };
  }, []);

  const headerA = () => {
    if (!showHeader) return <div className={styles['mb-16']}></div>;
    return showProgress ? (
      <div className={styles['header']}>
        {totalIndex > 0 && (
          <div className={styles['percent-tips']}>
            <span className={styles['percent-index']}>{index}</span>/
            {totalIndex}
          </div>
        )}
        <Progress
          percent={percent}
          strokeColor="#2E86FF"
          trailColor="#DFDFDF"
          showInfo={false}
        />
        <Lottie
          className={styles['lottie-ip']}
          style={{
            left: `${percent}%`,
          }}
          // @ts-ignore
          lottieRef={lottieRef}
          animationData={DataJson}
          autoplay={false}
          assetsPath={`${process.env.LOTTIEASSETPATH}/images/`}
        />
        <Lottie
          className={styles['lottie-ani']}
          style={{
            left: `${percent}%`,
          }}
          // @ts-ignore
          lottieRef={aLottieRef}
          animationData={Animation}
          autoplay={false}
          loop={false}
        />
        <div
          className={styles['tips']}
          style={{
            backgroundImage: `url(${BgTips})`,
            left: `${percent + 2}%`,
            top: 6,
            transition: 'all 0.3s',
            transform: 'translateX(10%)',
            opacity: showTips ? 1 : 0,
          }}
        >
          前半程已通关，
          <br />
          后半程更精彩！
        </div>
      </div>
    ) : (
      <div className={styles['alert']}>
        请确认你的优势学科再点击下一题，若在答题过程中退回重新选择优势学科，原来的答题记录将不再保留
      </div>
    );
  };

  return (
    <div className={styles['answer-layout-wrapper']}>
      {globalLoading ? (
        <div className={styles['answer-layout-wrapper']}>
          <div className={styles['global-loading']}>
            <Lottie
              className={styles['lottie-loading']}
              animationData={LoadingJson}
              autoplay={true}
              loop={true}
              assetsPath={`${process.env.LOTTIEASSETPATH}/loading/`}
            />
            <div className={styles['global-loading-text']}>报告生成中...</div>
          </div>
        </div>
      ) : (
        <div className="h5-layout-block">
          <div className={styles['h5-layout-block']}>
            <div className={styles['h5-question-block']}>
              {showGuideContent && !questionDoing && (
                // 首屏引导内容
                <div className={styles['h5-guide-wrapper']}>
                  <div
                    className={styles['h5-top-banner']}
                    style={{ backgroundImage: `url(${PlanBannerImg})` }}
                  >
                    <div>
                      <p className={styles['title']}>
                        {evaluationTemplateTitle}
                      </p>
                      <p>共 {total || 0} 题（约30分钟）</p>
                    </div>
                  </div>
                  <div
                    className={styles['guide-content']}
                    dangerouslySetInnerHTML={{
                      __html: guideContent || '',
                    }}
                  />
                </div>
              )}

              {feignState.feignAnswer && !questionDoing && (
                // 伪答内容
                <div className={styles['feign-answer']}>
                  <h3 className={styles['title']}>
                    规范答题告知书（
                    {feignState.feignAnswerTimes > 1 ? '二' : '一'}）
                  </h3>
                  <div className={styles['content']}>
                    <h5 className={styles['mb-16px']}>同学：</h5>
                    {feignState.feignAnswerTimes <= 1 ? (
                      <div className={styles['desc']}>
                        <h5 className={styles['mb-16px']}>
                          你好！你本次的作答被系统判定为无效，
                          <b>
                            因为你未经思考匆忙选择，或答题过程中存在不真实作答的情况
                          </b>
                          ，你还有一次重新作答的机会，请仔细阅读下列
                          《测评须知》后，点击下方 蓝色
                          按钮，返回答题首页再次作答。
                        </h5>
                        <ol className={styles['mb-16px']}>
                          <li>测评须知：</li>
                          <li>
                            1、本次测评题目
                            <b>没有对错之分</b>
                            ，请你仔细阅读答题过程中的提示，根据要求并结合你的实际情况真实作答。
                          </li>
                          <li>
                            2、测评有不认真作答的鉴别机制，若未经思考匆忙选择，会被系统判定为
                            <b>无效</b>。
                          </li>
                          <li>3、你还有一次答题机会，请务必审慎作答。</li>
                        </ol>
                      </div>
                    ) : (
                      <div className={styles['desc']}>
                        <h5 className={styles['mb-16px']}>
                          你好！经系统鉴别，
                          <b>你两次作答均不符合测评答题的要求和规范，</b>
                          <span className={styles['color-r']}>
                            被判定为 无效问卷
                          </span>
                          。本次答题将关闭 ，请联系负责老师为你重新安排答题。
                        </h5>
                        <ol className={styles['mb-16px']}>
                          <li>再次提醒：</li>
                          <li>
                            1、本次测评题目没有对错之分，请你仔细阅读答题过程中的提示，根据要求并结合你的实际情况真实作答。
                          </li>
                          <li>
                            2、测评有不认真作答的鉴别机制，若未经思考匆忙选择，会被系统判定为
                            <span className={styles['color-r']}>无效</span>。
                          </li>
                        </ol>
                      </div>
                    )}
                  </div>
                </div>
              )}
              {questionDoing && (
                <div className={styles['question']}>
                  {headerA()}
                  <div
                    className={`${styles['content']} ${
                      !showProgress ? styles['pt-10'] : ''
                    }`}
                  >
                    <Question
                      key={questionInfo.id}
                      loading={questionGlobalLoading || submitAndCheckLoading}
                      clientType={clientType}
                      value={questionInfo}
                      onChange={onQuestionChange}
                    />
                  </div>
                </div>
              )}
            </div>
            <div
              className={styles['action']}
              style={{
                justifyContent:
                  actions.length >= 2 ? 'space-between' : 'flex-end',
              }}
            >
              {actions.map(
                (
                  { label, disabled, className, loading, onClick, loadingVisible, ...btnProps },
                  i
                ) => (
                  <div
                    // @ts-ignore
                    className={`${styles['action-item']} ${styles[className]} ${
                      disabled ? styles['disabled'] : ''
                    }`}
                    key={i}
                    onClick={() => {
                      if (disabled) return;
                      if (loading) return;
                      onClick();
                    }}
                    {...btnProps}
                  >
                      {/* 移动端仅在提交展示loading */}
                     {(submitAndCheckLoading && loadingVisible) && <SpinLoading />} {label}
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      )}
      <Popup visible={open} bodyClassName={styles['h5-popup-wrapper']}>
        <div className={styles['h5-popup-block']}>
          <div
            className={styles['h5-popup-close']}
            onClick={() => setOpen(false)}
          >
            <i className="iconfont iconguanbi1"></i>
          </div>
          <div className={styles['h5-popup-title']}> 测评还没做完 </div>
          <div className={styles['h5-popup-content']}>
            <span>
              能不能别走T-T 把测评做完呗~ <br />
              我们将为你<span style={{ color: '#FF7F21' }}>保留3天记录</span>
              <br />
              超过3天需要重新开始测评
            </span>
          </div>
          <div
            className={styles['h5-popup-bg']}
            style={{
              backgroundImage: `url(${PopupBg})`,
            }}
          ></div>
          <div className={styles['h5-popup-action']}>
            <div
              onClick={() => {
                setOpen(false);
                try {
                  console.log('触发残忍离开', window?.mstJsBridge);
                  if (window?.mstJsBridge && window?.mstJsBridge.isInMstApp()) {
                    console.log('触发mstJsBridge');
                    window?.mstJsBridge.closeWebview();
                  } else {
                    history.back();
                  }
                } catch (error) {
                  console.log('触发残忍离开报错了', error);
                }
              }}
            >
              残忍离开
            </div>
            <div className={styles['primary']} onClick={() => setOpen(false)}>
              继续作答
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default AnswerH5;
