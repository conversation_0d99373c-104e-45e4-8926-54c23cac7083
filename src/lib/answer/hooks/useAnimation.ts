'use client';

import { useState, useRef } from 'react';
import type { LottieRefCurrentProps } from 'lottie-react';
import debounce from 'lodash/debounce';

const useAnimation = ()=>{

  const [showTips, setShowTips] = useState(false);
  const [isAlert, setIsAlerts] = useState(false);

  const lottieRef = useRef<LottieRefCurrentProps>();
  const aLottieRef = useRef<LottieRefCurrentProps>();

  const stop = debounce(() => {
    lottieRef?.current?.stop();
  }, 500);
  const play = ()=>{
    lottieRef?.current?.play();
  }

  const startTipsAm = ({ percent, total } : {percent: number, total: number}) => {
    if (showTips) return;
    if (isAlert) return;
    if (total < 10) return;
    const p = percent;
    if (p >= 50) {
      aLottieRef?.current?.play();
      setShowTips(true);
      setIsAlerts(true);
      setTimeout(() => {
        setShowTips(false);
      }, 3000);
    }
  };



  return {
    state: {
      showTips,
      isAlert,
    },
    lottieRef,
    aLottieRef,
    startTipsAm,
    setIsAlerts,
    stop,
    play,
  }
}

export default useAnimation;