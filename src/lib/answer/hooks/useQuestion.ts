'use client';
import { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/router';
import { useRequest, useSetState } from 'ahooks';
import cloneDeep from 'lodash/cloneDeep';

import api from '~/service/psychology/evaluation';
import debounce from 'lodash/debounce';
import type {
  GetEvaluationInfo,
  GetEvaluationByType,
} from '~/service/psychology/evaluation/types';
import { UserBaseInfo } from '@/service/common/types';


import { transformQuestionToValue, transformQuestionToPayload } from '../shared/logic';

import { TYPE_INT, TYPE_STRING, RULE_TYP } from '../shared/types/index';
import { CLIENT_TYPE, IQuestionStateProps } from '../shared/types/index';

interface IQuestionProps {
  basic: GetEvaluationInfo.Response['data'] &
    GetEvaluationByType.Response['data'];
  userInfo?: UserBaseInfo.Response['data'];
  pageState?: any;
  feignState?: any;
  setPageState: (state: object) => void;
  animation?: any;
  onError?: (error: any, key: string, _reset?: () => void) => void;
  onCheck?: (options: any) => void;
  onSubmit?: (options: any) => void;
  onActionCheck?: (options: any) => void;
  onReStart?: () => void;
  onLink?: (url: string) => void;
}

const useQuestion = (props: IQuestionProps) => {
  const router = useRouter();
  const {
    classId,
    gradeId,
    scoolId,
    skipIntro,
    evaluationTemplateType,
    multipleAnswer,
    clientType = CLIENT_TYPE.pc
  }: any = router?.query || {};
  const {
    basic,
    userInfo,
    pageState,
    feignState,
    setPageState,
    animation: { play = () => {}, startTipsAm, stop, setIsAlerts },
    onError = () => {},
    onCheck = () => {},
    onSubmit = () => {},
    onActionCheck = () => {},
    onLink = () => {},
  } = props;

  const { answerInfo } = basic || {};
  const evaluationTaskId =
    basic?.evaluationTaskId || router?.query?.evaluationTaskId;

  const { answerStatus, userRecordId, currentQuestions, totalQuestions } =
    answerInfo || {};

  const templateVersionId = answerInfo?.templateVersionId;
  const [isMounted, setMounted] = useState(false);
  const [questionDoing, setQuestionDoing] = useState(false);
  const [questionInfo, setQuestionInfo] = useSetState<Partial<IQuestionStateProps>>({});
  const [elementIndex, setElementIndex] = useState<any>(1);
  const [questionGlobalLoading, setQuestionGlobalLoading] = useState(false);

  /** 升学测评 */
  const is_upgrade_major = evaluationTemplateType === 'upgrade_major';

  const showProgress = useMemo(() => {
    if (is_upgrade_major) {
      return !questionInfo.reorganizeFlag;
    }
    return true;
  }, [questionInfo, evaluationTemplateType]);

  const scrollIntoViewQuestion = ()=>{
    setTimeout(()=>{
      /** 题目滚到顶部 */
      const dQuestion = document.getElementById('question')
      if(dQuestion){
        dQuestion.scrollIntoView({ behavior: "smooth", block: "start" })
      }
    }, 300)
  }

  /** 获取题详情 */
  const { runAsync: fetchQuestion, loading: questionLoading } = useRequest(
    api.getEvaluationQuestion,
    {
      manual: true,
      onSuccess: (res) => {
        const { data: questionInfo } = res || {};
        const trans = transformQuestionToValue(questionInfo, {
          ...pageState,
          type: '_init',
          is_upgrade_major,
        });
        setQuestionInfo(trans);
        if(showProgress){
          scrollIntoViewQuestion();
        }
        if(questionInfo.type === TYPE_STRING.compound){
          const [firstQ] = questionInfo.childElementDetailList || []
          setElementIndex(firstQ.elementIndex || 1)
          return
        }
        if(questionInfo.elementIndex){
          setElementIndex(questionInfo.elementIndex)
        }
      },
      onError: (error) => onError(error, 'evaluationQuestion', _reset),
      onFinally() {
        stop();
        setQuestionGlobalLoading(false);
        setQuestionDoing(true);
      },
    }
  );

  const _reset = () => {
    setQuestionGlobalLoading(false);
    setQuestionDoing(false);
    setIsAlerts(false);
    stop();
  };

  /** 刷新题重新答题 */
  const {
    runAsync: fetchRefreshUserBeginTime,
    data: { data: refreshInfo = {} } = {},
  } = useRequest(api.refreshUserBeginTime, {
    manual: true,
    onError: (error) => onError(error, 'refreshUserBeginTime', _reset),
  });

  /** 刷新清伪答 */
  const {
    runAsync: fetchCleanUserFeignAnswerDetail,
  } = useRequest(api.cleanUserFeignAnswerDetail, {
    manual: true,
    onError: (error) => onError(error, 'cleanUserFeignAnswerDetail', _reset),
  });

  /** 提交答案 */
  const { runAsync: fetchPostEvaluationAnswer } = useRequest(
    (p, o = {}) => {
      const oApi = o.batch
        ? api.postBatchEvaluationAnswer
        : api.postEvaluationAnswer;
      return oApi(p);
    },
    {
      manual: true,
      onError: (error) => onError(error, 'postEvaluationAnswer', _reset),
      onFinally() {
        stop();
        // setQuestionGlobalLoading(false);
      },
    }
  );

  /** 答题提交校验 */
  const { runAsync: fetchUnAnswerCheck, loading: checkLoading } = useRequest(api.unAnswerCheck, {
    manual: true,
    onError: (error) => onError(error, 'unAnswerCheck', _reset),
  });

  /** 完成答题 */
  const {
    loading: completeLoading,
    runAsync: fetchRefreshEvaluationCompletedTime,
  } = useRequest(api.refreshEvaluationCompletedTime, {
    manual: true,
    onError: (error) =>
      onError(error, 'refreshEvaluationCompletedTime', _reset),
  });

  const showGuideContent = useMemo(
    () => {
      if(feignState.feignAnswer){
        return false
      }
      return answerStatus === 0 || answerStatus === 3
    },
    [answerStatus, feignState]
  );

  // 生涯测评使用场景：当skipIntro为true时，跳过介绍内容，直接作答
  const nextFirst =  (skipIntro && skipIntro === 'true') || !!evaluationTemplateType;

  const total = useMemo(() => {
    if (questionInfo?.generateQuestionGroupFlag) {
      return questionInfo.totalQuestion;
    }
    return refreshInfo.totalQuestions || totalQuestions;
  }, [refreshInfo, totalQuestions, questionInfo]);


  /** 答题and获取详情 */
  const postAndQuestion = async (answer: any) => {
    const payload: any = {
      userRecordId: refreshInfo.userRecordId || userRecordId,
      questionId: questionInfo.id,
      templateVersionId: refreshInfo.templateVersionId || templateVersionId,
      ...answer,
      clientType,
    };
    if (questionInfo.type === TYPE_INT.compound && questionInfo?.children) {
      const elementList =
        questionInfo?.children?.length > 0
          ? questionInfo?.children?.map((v: { sourceType: any }) => (transformQuestionToPayload({
              ...v,
            })))
          : undefined;
      payload.elementAnswerDTOList = elementList;
    }
    await fetchPostEvaluationAnswer(transformQuestionToPayload(payload), {
      batch: questionInfo.type === TYPE_INT.compound,
    });
    stop();
    /** 最后一题 */
    if (pageState.pageIndex === total) {
      setQuestionGlobalLoading(false);
      return;
    }
    const pageIndex =
      questionInfo.type === TYPE_INT.compound
        ? pageState.pageIndex + (questionInfo?.children?.length || 0)
        : pageState.pageIndex + 1;
    setPageState({
      pageIndex,
    });

    const percent = (pageIndex / total) * 100;
    startTipsAm({ percent, total });
    fetchQuestion({
      ...pageState,
      pageIndex,
      userRecordId: refreshInfo.userRecordId || userRecordId,
      templateVersionId: refreshInfo?.templateVersionId || templateVersionId,
    });
  };

  const postAndQuestionDebounce = debounce(postAndQuestion, 1000);

  const getNextDisabled = () => {
    const gType: TYPE_STRING = questionInfo.sourceType!;
    // 根据题型和存在答案就不置灰
    const getM = {
      [TYPE_STRING.text]: () => false,
      [TYPE_STRING.radio]: () => questionInfo.studentAnswers?.length <= 0,
      [TYPE_STRING.checkbox]: () =>
        questionInfo.studentAnswers?.length < (questionInfo.selectNum || 1),
      [TYPE_STRING.scale]: () => questionInfo.studentAnswers?.length <= 0,
      [TYPE_STRING.checkboxOrder]: () =>
        questionInfo.checkboxOrder?.length !== questionInfo.element?.length,
      [TYPE_STRING.slider]: () => questionInfo.inputNumber! <= 0,
      [TYPE_STRING.compound]: () =>
        !questionInfo?.children?.every((v: IQuestionStateProps) => v.selectId),
    };
    return getM[gType] ? getM[gType]() : true;
  };

  const onQuestionChange = async (
    ques: any,
    options?: {
      choices?: string[];
    }
  ) => {
    const { choices = [] } = options || {};
    const currentQuest = cloneDeep(questionInfo);
    /** 复合题 */
    if (currentQuest.type === TYPE_INT.compound) {
      const currentChildrenI = currentQuest?.children?.findIndex(
        (v: { id: any }) => v.id === ques.id
      );
      currentQuest.children[currentChildrenI] = transformQuestionToValue(ques, {
        ...pageState,
        choices,
        type: '_change',
        is_upgrade_major,
      });
      setQuestionInfo(currentQuest);
      return;
    }
    const trans = transformQuestionToValue(ques, {
      ...pageState,
      choices,
      type: '_change',
      is_upgrade_major,
    });
    setQuestionInfo(trans);
    /** 单选题 */
    if (questionInfo.type === TYPE_INT.radio) {
      if (questionGlobalLoading) return;
      setQuestionGlobalLoading(true);
      play?.();
      const [selected] =
        questionInfo.options?.filter((v: { choice: string }) =>
          choices?.includes(v.choice)
        ) || [];
        const selectOrder =
        questionInfo.options?.findIndex((v: { choice: string }) =>
          choices?.includes(v.choice)
        );
      postAndQuestionDebounce({
        ...questionInfo,
        selectId: selected?.id,
        selectOrder,
      });
    }
  };

  const _beginAnswer = async () => {
    setIsAlerts(false);
    setPageState({
      pageIndex: 1,
    });
    setElementIndex(1);
    const res = await fetchRefreshUserBeginTime({
      userId: userInfo?.userId,
      evaluationTaskId,
      classId,
      gradeId,
      scoolId,
    });
    stop();
    fetchQuestion({
      ...pageState,
      pageIndex: 1,
      userRecordId: res?.data?.userRecordId || userRecordId,
      templateVersionId: res?.data?.templateVersionId || templateVersionId,
    });
  };

  const _nextAnswer = async () => {
    if (questionGlobalLoading) return;
    if (TYPE_INT.checkbox === questionInfo.type) {
      const { selectNum, selectNumType } = questionInfo;
      if (
        selectNumType === RULE_TYP.eq &&
        selectNum !== questionInfo.studentAnswers?.length
      ) {
        onActionCheck({
          type: 'toast',
          content: `只能选择${selectNum}个选项哦~`,
        });
        return Promise.reject({
          type: 'toast',
          content: `只能选择${selectNum}个选项哦~`,
        });
      }
    }
    if(getNextDisabled()){
      onActionCheck({
        type: 'toast',
        content: `有题目未作答，请检查~`,
      });
      return Promise.reject({
        type: 'toast',
        content: `有题目未作答，请检查~`,
      });
    }
    setQuestionGlobalLoading(true);
    play?.();
    await postAndQuestion(questionInfo);
  };

  const _submitAnswer = async () => {
    if (questionGlobalLoading) return;
    if(getNextDisabled()){
      onActionCheck({
        type: 'toast',
        content: `有题目未作答，请检查~`,
      });
      return Promise.reject({
        type: 'toast',
        content: `有题目未作答，请检查~`,
      });
    }
    /** 非单选 */
    if (questionInfo.type !== TYPE_INT.radio) {
      await _nextAnswer();
      onSubmit({
        payload: {
          userRecordId: refreshInfo.userRecordId || userRecordId,
        },
        check: fetchUnAnswerCheck,
        completed: fetchRefreshEvaluationCompletedTime,
        _reset,
      });
      return;
    }
    onSubmit({
      payload: {
        userRecordId: refreshInfo.userRecordId || userRecordId,
      },
      check: fetchUnAnswerCheck,
      completed: fetchRefreshEvaluationCompletedTime,
      _reset,
    });
  };

  const actions = [
    {
      visible:
        feignState.feignAnswer &&
        feignState.feignAnswerTimes <= 1 &&
        !questionDoing,
      label: '我已了解，再次答题',
      className: 'start',
      type: 'primary',
      onClick: async ()=>{
        await fetchCleanUserFeignAnswerDetail({
          userRecordId: refreshInfo.userRecordId || userRecordId,
        });
        _beginAnswer();
      },
    },
    {
      visible: showGuideContent && !questionDoing,
      label: '开始答题',
      className: 'start',
      type: 'primary',
      onClick: _beginAnswer,
    },
    {
      visible: questionDoing && pageState.pageIndex > 1,
      label: '上一题',
      loading: questionLoading,
      type: 'primary',
      onClick: async () => {
        const pageIndex =
          pageState.pageIndex - Number(questionInfo.previousQuestionNum || 1);
        setPageState({
          pageIndex: Math.max(pageIndex, 1),
        });
        const res = await fetchQuestion({
          ...pageState,
          pageIndex: Math.max(pageIndex, 1),
          userRecordId: refreshInfo.userRecordId || userRecordId,
          templateVersionId:
            refreshInfo?.templateVersionId || templateVersionId,
        });
        if(res?.data && !res?.data?.elementIndex && res?.data?.type === TYPE_STRING.text){
          const index = Math.max(1, elementIndex - 1)
          setElementIndex(index)
        }
      },
    },
    {
      visible: questionDoing && pageState.pageIndex !== total,
      label: '下一题',
      loading: questionLoading || questionGlobalLoading,
      type: 'primary',
      onClick: _nextAnswer,
    },
    {
      visible: questionDoing && pageState.pageIndex === total,
      label: '提交',
      loading: completeLoading || checkLoading || questionGlobalLoading,
      disabled: false,
      type: 'primary',
      loadingVisible: true,
      onClick: _submitAnswer,
    },
  ].filter((v) => !!v.visible);

  const onMounted = async () => {
    setMounted(true);
    if (feignState?.feignAnswer) return;
    /** 已完成 */
    if (answerStatus === 2) {
      if (+multipleAnswer === 1) {
        _reset();
        if (nextFirst) {
          _beginAnswer();
        } else {
          await fetchRefreshUserBeginTime({
            userId: userInfo?.userId,
            evaluationTaskId,
            classId,
            gradeId,
            scoolId,
          });
        }
      } else {
        // 已经答题完毕 跳转
        const url = answerInfo?.personalReportUrl || '';
        if (url) {
          onLink(url)
        }
      }
      return;
    }
    /** 答题中 */
    if(answerStatus === 1){
      const p = (currentQuestions! / total) * 100;
      if (p >= 50) {
        setIsAlerts(true);
      }
      setQuestionDoing(true);
      setPageState({
        pageIndex: Math.max(+currentQuestions!, 1),
      });
      fetchQuestion({
        ...pageState,
        pageIndex: Math.max(+currentQuestions!, 1),
        userRecordId: refreshInfo.userRecordId || userRecordId,
        templateVersionId: refreshInfo?.templateVersionId || templateVersionId,
      });
      if (currentQuestions! > 1) {
        onCheck({
          type: 'modal',
          cancelText: '重新测试',
          content: '是否继续上次未完成的测试？',
          okText: '继续测试',
          _reset,
        });
      }
      return
    }
    /** 未开始 */
    if (nextFirst) {
      _beginAnswer();
    }
  };

  const percent = useMemo(()=>{
      const default_p = 0
      if(is_upgrade_major){
        if(questionInfo?.totalIndex){
          return (elementIndex / Number(questionInfo?.totalIndex)) * 100
        }else{
          return default_p;
        }
      }
      return (pageState.pageIndex / total) * 100
  }, [is_upgrade_major, elementIndex,questionInfo, pageState, total])


  const totalIndex = useMemo(()=>{
    if(is_upgrade_major){
      return questionInfo?.totalIndex || 0;
    }
    return total
  }, [is_upgrade_major, questionInfo, total])


  const showHeader = useMemo(()=>{
    if(is_upgrade_major && questionInfo.sourceType === TYPE_STRING.text){
      return false
    }
    return true
  }, [questionInfo, is_upgrade_major])

  useEffect(() => {
    onMounted();
  }, [basic, nextFirst, feignState]);

  return {
    run: {
      fetchQuestion,
      fetchRefreshUserBeginTime,
      fetchPostEvaluationAnswer,
      postAndQuestion,
    },
    state: {
      index: is_upgrade_major ? elementIndex : pageState.pageIndex,
      percent,
      isMounted,
      showProgress,
      showHeader,
      refreshInfo,
      showGuideContent,
      pageState,
      questionInfo,
      questionDoing,
      total,
      totalIndex,
    },
    change: {
      onQuestionChange,
    },
    actions,
    loading: {
      submitAndCheckLoading: completeLoading || checkLoading || questionGlobalLoading,
      questionGlobalLoading,
    },
  };
};

export default useQuestion;
