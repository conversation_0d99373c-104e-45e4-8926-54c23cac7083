import {
  BLOCK_F_STRING,
  LatitudePartial,
  LatitudePartialMap,
} from '../types/index';

interface TransformIdentifyItem {
  latitudeId: string | number;
}

export const _transformIdentify = (
  ids: TransformIdentifyItem[],
  {
    overallAnalysis,
    caseAnalysis,
  }: { overallAnalysis: LatitudePartial[]; caseAnalysis: LatitudePartial[] }
): LatitudePartial[] => {
  if (!ids || ids.length <= 0) {
    return [];
  }

  const values: LatitudePartial[] = [];

  ids.forEach((element) => {
    if (!element?.latitudeId) return;

    const item = caseAnalysis.find(
      (v: LatitudePartial) => v.latitudeId === element.latitudeId
    );
    const overall = overallAnalysis.find(
      (v: LatitudePartial) => v.latitudeId === element.latitudeId
    );

    if (item) {
      values.push({
        ...overall,
        ...item,
      });
    }
  });

  return values;
};

interface OverallAnalysisData {
  overallAnalysisList?: LatitudePartial[];
  latitudeWarningList?: LatitudePartial[];
  [key: string]: any;
}

interface LatitudeRelationData {
  latitudeRelationList?: LatitudePartial[];
  noParentLatitude?: TransformIdentifyItem[];
}

interface TransformConfig {
  latitudeRelation: LatitudeRelationData;
  firstLevelKey: string;
}

/**
 * 关系数据过滤转换 配上维度信息，背景颜色等。
 * @param source - 源数据包含案例分析和整体分析
 * @param config - 配置信息包含维度关系和一级维度键名
 */
export const transformLatitudeRelationToView = (
  source: {
    caseAnalysis: LatitudePartial[];
    overallAnalysis: OverallAnalysisData;
  },
  config: TransformConfig
) => {
  const { caseAnalysis, overallAnalysis } = source || {};
  const { latitudeRelation, firstLevelKey } = config;
  const { latitudeRelationList, noParentLatitude = [] } =
    latitudeRelation || {};
  const firstLevel = overallAnalysis[firstLevelKey] || [];
  /**
   * 二级维度 维度得分：overallAnalysis.overallAnalysisList
   * 预警情况：overallAnalysis.latitudeWarningList
   * 具体情况：CaseAnalysis
   * 转 map
   */
  const overallAnalysisMap =
    overallAnalysis?.overallAnalysisList?.reduce(
      (p: LatitudePartialMap, c: LatitudePartial) => {
        p[c.latitudeId!] = c;
        return p;
      },
      {}
    ) || {};
  const latitudeWarningMap =
    overallAnalysis?.latitudeWarningList?.reduce(
      (p: LatitudePartialMap, c: LatitudePartial) => {
        p[c.latitudeId!] = c;
        return p;
      },
      {}
    ) || {};
  const caseAnalysisMap =
    caseAnalysis?.reduce((p: LatitudePartialMap, c) => {
      p[c.latitudeId!] = c;
      return p;
    }, {}) || {};
  /**-----------------------  */
  /** 历史不存在关联关系 */
  if (!latitudeRelationList || latitudeRelationList?.length <= 0) {
    return {
      relation: [
        {
          overallAnalysis: overallAnalysis?.overallAnalysisList,
          latitudeWarning: overallAnalysis?.latitudeWarningList,
          caseAnalysis: caseAnalysis?.map((v: any) => ({
            ...v,
            latitudeLevelId: overallAnalysisMap[v.latitudeId].latitudeLevelId,
          })),
        },
      ].filter(
        (v: any) =>
          v.overallAnalysis?.length > 0 ||
          v.caseAnalysis?.length > 0 ||
          v.latitudeWarning?.length > 0
      ),
      identify: [],
    };
  }

  const wLevelIds = latitudeRelationList?.map(
    (v: LatitudePartial) => v.latitudeId
  );
  const hasFirstL =
    latitudeRelationList?.some(
      (v: LatitudePartial) => v.latitudeTypeLevel === 1
    ) &&
    firstLevel?.some((v: LatitudePartial) => wLevelIds.includes(v.latitudeId));

  /** 不存在一级维度 */
  if (!hasFirstL) {
    /**
     * 1. 存在一级但是没匹配到
     * 2. 不存在一级
     */
    const hasNLevel = latitudeRelationList?.some(
      (v: LatitudePartial) => v.latitudeTypeLevel === 1
    );
    const ids = !hasNLevel
      ? latitudeRelationList?.map((v: LatitudePartial) => v.latitudeId)
      : latitudeRelationList?.reduce((p: string[], c: any) => {
          const childrenIds = c.childLatitudeList?.map(
            (v: LatitudePartial) => v.latitudeId
          );
          return [...p, ...childrenIds];
        }, []);

    const overallA =
      ids
        ?.filter((v) => overallAnalysisMap[v!])
        ?.map((v) => overallAnalysisMap[v!]) || [];
    const latitudeWarning =
      ids
        ?.filter((v) => latitudeWarningMap[v!])
        ?.map((v) => latitudeWarningMap[v!]) || [];
    const caseA =
      ids
        ?.filter((v) => caseAnalysisMap[v!])
        ?.map((v) => ({
          ...caseAnalysisMap[v!],
          latitudeLevelId: overallAnalysisMap[v!].latitudeLevelId,
        })) || [];

    const relation = [
      {
        overallAnalysis: overallA,
        latitudeWarning,
        caseAnalysis: caseA,
      },
    ].filter(
      (v: any) =>
        v.overallAnalysis?.length > 0 ||
        v.caseAnalysis?.length > 0 ||
        v.latitudeWarning?.length > 0
    );
    return {
      relation,
      identify: _transformIdentify(noParentLatitude, {
        overallAnalysis: overallAnalysis.overallAnalysisList || [],
        caseAnalysis,
      }),
    };
  }
  const relation = latitudeRelationList
    .map((v: any) => {
      const firstInfo =
        firstLevel?.find(
          (f: LatitudePartial) => v.latitudeId === f.latitudeId
        ) || {};
      const childrenIds = v.childLatitudeList?.map(
        (v: LatitudePartial) => v.latitudeId
      );
      const overallA =
        childrenIds
          ?.filter((v: string) => overallAnalysisMap[v])
          .map((v: string) => overallAnalysisMap[v]) || [];
      const latitudeWarning =
        childrenIds
          ?.filter((v: string) => latitudeWarningMap[v])
          ?.map((v: string) => latitudeWarningMap[v]) || [];
      const caseA =
        childrenIds
          ?.filter((v: string) => caseAnalysisMap[v])
          ?.map((v: string) => ({
            ...caseAnalysisMap[v],
            latitudeLevelId: overallAnalysisMap[v].latitudeLevelId,
          })) || [];

      return {
        ...firstInfo,
        overallAnalysis: overallA,
        latitudeWarning,
        caseAnalysis: caseA,
      };
    })
    .filter(
      (v: any) =>
        v.overallAnalysis?.length > 0 ||
        v.caseAnalysis?.length > 0 ||
        v.latitudeWarning?.length > 0
    );
  return {
    relation,
    identify: _transformIdentify(noParentLatitude, {
      overallAnalysis: overallAnalysis.overallAnalysisList || [],
      caseAnalysis,
    }),
  };
};

interface BarChartItem {
  name: string;
  value: number;
}

export const renderBarOptions = (items: BarChartItem[]) => {
  if (!Array.isArray(items) || items.length === 0) {
    return undefined;
  }

  const keyData: string[] = [];
  const valueData: number[] = [];

  items.forEach((item) => {
      keyData.push(item.name);
      valueData.push(item.value);
  });
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '25px',
      right: '0%',
      top: '0%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      show: false,
    },
    yAxis: {
      type: 'category',
      data: [...keyData],
      axisLabel: {
        fontSize: 18,
        color: 'rgba(0, 17, 57, 0.85)',
      },
    },
    series: [
      {
        type: 'bar',
        data: [...valueData],
        label: {
          show: true, // 柱上显示value值
          color: '#fff',
          fontSize: 16,
        },
        barMaxWidth: 32,
        itemStyle: {
          color: 'rgba(0, 17, 57, 0.3)', // 柱上文字的颜色
          barBorderRadius: [0, 8, 8, 0], // 顶部圆角
        },
      },
    ],
  };
};

interface PieChartItem {
  name: string;
  num: number;
}

export const renderPieOptions = (items: PieChartItem[]) => {
  if (!Array.isArray(items) || items.length === 0) {
    return undefined;
  }

  const pieList: Array<{ name: string; value: number }> = [];

  items.forEach((item) => {
      pieList.push({
        name: item.name,
        value: item.num,
      });
  });
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: '10%',
      bottom: '20%',
      textStyle: {
        fontSize: 18,
      },
    },
    color: ['#00DFA6', '#4B80FF', '#FFC314', '#FF5858'],
    series: [
      {
        type: 'pie', // 饼图类型
        radius: '70%', // 饼图的缩放比例
        center: ['40%', '50%'], // 饼图的圆心坐标，[x坐标，y坐标]，百分比时相对容器宽高
        data: pieList,
        label: {
          fontSize: 16,
          color: '#000',
          formatter: (params: { name: string; percent: string }) => {
            return params.name + '\n' + params.percent + '%';
          },
        },
      },
    ],
  };
};

export const firstColorOptionsMap = {
  1: { value: 1, color: '橘红色', colorValue: '#FF5858' },
  2: { value: 2, color: '橙色', colorValue: '#FF8831' },
  3: { value: 3, color: '黄色', colorValue: '#FFC314' },
  4: { value: 4, color: '果绿色', colorValue: '#99BA00' },
  5: { value: 5, color: '翠绿色', colorValue: '#00C322' },
  6: { value: 6, color: '浅绿色', colorValue: '#00DFA6' },
  7: { value: 7, color: '浅蓝色', colorValue: '#00C7FF' },
  8: { value: 8, color: '青蓝色', colorValue: '#4B80FF' },
  9: { value: 9, color: '紫色', colorValue: '#9550FF' },
  10: { value: 10, color: '玫红色', colorValue: '#F4309E' },
};

const secondColorOptionsMap = {
  11: { value: 11, color: '橘红色', colorValue: '#FFDEDD' },
  12: { value: 12, color: '橙色', colorValue: '#FFE7D6' },
  13: { value: 13, color: '黄色', colorValue: '#FFF3D0' },
  14: { value: 14, color: '果绿色', colorValue: '#EBF1CC' },
  15: { value: 15, color: '翠绿色', colorValue: '#CDF3D3' },
  16: { value: 16, color: '浅绿色', colorValue: '#00DFA6' },
  17: { value: 17, color: '浅蓝色', colorValue: '#CCF9ED' },
  18: { value: 18, color: '青蓝色', colorValue: '#CDF4FF' },
  19: { value: 19, color: '紫色', colorValue: '#DBE6FF' },
  20: { value: 20, color: '玫红色', colorValue: '#FDD6EC' },
};

const firstColorToSecondColorMap: Record<string, string> = {
  '#FF5858': '#FFDEDD',
  '#FF8831': '#FFE7D6',
  '#FFC314': '#FFF3D0',
  '#99BA00': '#EBF1CC',
  '#00C322': '#CDF3D3',
  '#00DFA6': '#00DFA6',
  '#00C7FF': '#CCF9ED',
  '#4B80FF': '#CDF4FF',
  '#9550FF': '#DBE6FF',
  '#F4309E': '#FDD6EC',
};

export const getColorValue = (item: any, source: any) => {
  const defaultValue = 8;
  const colorValue =
    source?.find(
      (v: { latitudeId: any; latitudeLevelId: any }) =>
        v.latitudeId === item.latitudeId &&
        v.latitudeLevelId === item.latitudeLevelId
    )?.color || defaultValue;
  const tempMap: Record<string, any> = {
    ...firstColorOptionsMap,
    ...secondColorOptionsMap,
  };
  if (colorValue > 10) {
    return {
      color: tempMap[colorValue],
    };
  }
  return {
    color: tempMap[colorValue]?.colorValue,
    secondColor: firstColorToSecondColorMap[tempMap[colorValue]?.colorValue],
  };
};

export const getNavList = (source: any, options: any) => {
  const { overallAnalysis, relation, identify } = source;
  const { type } = options as { type: string };
  const prefix: Record<string, string> = {
    person: '个人',
    class: '班级',
    grade: '年级',
  };
  return [
    {
      label: '报告说明',
      key: BLOCK_F_STRING['Report Description'],
    },
    {
      label: `${prefix[type]}整体概况`,
      key: BLOCK_F_STRING['Overall Overview'],
      hidden: !overallAnalysis?.length,
    },
    {
      label: `${prefix[type]}整体分析`,
      key: BLOCK_F_STRING['Analysis'],
      hidden: !relation?.length,
    },
    {
      label: '测评鉴别分析',
      key: BLOCK_F_STRING.identify,
      hidden: !identify?.length,
    },
    {
      label: '预警名单',
      key: BLOCK_F_STRING['Warning List'],
      hidden: type === 'person',
    },
    {
      label: '附加说明',
      key: BLOCK_F_STRING.additional,
    },
  ].filter((v) => !v.hidden);
};
