/** 一级标题key */
export enum BLOCK_F_STRING {
  "Report Description" = 'Report Description',
  "Overall Overview" = 'Overall Overview',
  "Analysis" = 'Analysis',
  "identify" = 'identify',
  'Warning List' = 'Warning List',
  "additional" = 'additional',
} 


export interface LevelList {
  /** id */
  id: number
  /** 名称,良好，轻微 */
  name: string
  /** 人数 */
  num: number
}

export interface ClassScoreList {
  /** 班级名称 */
  name?: string
  /** 班级分数 */
  score?: number
}


export interface Latitude {
  /** 纬度id	 */
  latitudeId: number | string
  /** 纬度名称	 */
  latitudeTitle: string
  /** 维度分数	 */
  score: number
  /** 程度id	 */
  latitudeLevelId: number
  /** 程度名称	 */
  latitudeLevelTitle: string
  /** 分数对应的级别1，2，3，4	 */
  latitudeScoreLevel: number
  /** 总分	 */
  totalScore: number
  /** 测评结果说明	 */
  result: string
  /** 得分说明	 */
  description: string
  /** 建议说明	 */
  suggestDesc: string
  /** 内容(维度)说明 */
  latitudeDescription: string;
  /** 维度得分说明 */
  latitudeScoreDescription: string;
  /** 你的情况 */
  levelDescription: string;
  /** 心理建议 */
  levelSuggest: string;
  /** 纬度类型 */
  latitudeTypeLevel:  number;
  /** 程度分布 */
  levelList?: LevelList[],
   /** 各班级在该项得分情况 ,ClassScoreVO */
   classScoreList?: ClassScoreList[]
  /** 各个程度的分布数据 */
  degreeDistributionList: DegreeDistribution[];
}

export type LatitudePartial = Partial<Latitude>
export type LatitudePartialMap = Record<string, LatitudePartial>

export interface DegreeDistribution {
  /** 程度id	 */
  degreeId: number
  /** 程度名称	 */
  degreeTitle: string
  /** 分布分数	 */
  degreeScore: number
  /** 分布人数	 */
  degreeNum: number
}
