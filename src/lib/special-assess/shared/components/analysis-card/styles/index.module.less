.caseAnalysisCard {
  margin: 0 20px 40px;
  background-color: #d0defa;
  padding: 20px 10px 10px;
  border-radius: 8px;
  &:last-child{
    margin-bottom: 0;
  }

  .resultRemark {
    margin-top: 20px;
    padding: 20px;
    font-size: 18px;
    min-height: 110px;
    background: #FFFFFF;
    border-radius: 8px;
    .resultLabel {
      font-size: 18px;
      color: #666;
      line-height: 30px;
      }
    }
    .result{
      margin-bottom: 40px;
    }
  }
  .info{
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left{
      width: 520px;
      max-height: 64px;
      font-weight: bold;
      font-size: 20px;
      color: rgba(0, 17, 57, 0.85);
      line-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

.chartCard{
  .resultRemark {
    font-size: 18px;
    min-height: 110px;
    border-radius: 8px;
    .resultLabel {
      font-size: 18px;
      color: #666;
      line-height: 30px;
      & + span {
        color: #666;
      }
    }
    .result{
      margin-bottom: 40px;
    }
  }
  .statusWrapper{
    position: relative;
    margin: 40px 0 20px;
    img{
      display: block;
      margin: 0 auto;
      width: 272px;
    }
    .label{
      position: absolute;
      left: 50%;
      top: 45%;
      transform: translate(-50%, -50%);
      font-weight: bold;
      font-size: 20px;
      color: rgba(0, 17, 57, 0.85);
      line-height: 32px;
    }
  }
  .chartSituation{
    .extra{
      line-height: 40px;
      font-size: 18px;
      padding-left: 25px;
      margin-bottom: 40px;
      ul{
        padding-left: 20px;
        list-style-type: disc;
      }
    }
    .chartWrapper{
      margin: 20px 0 10px;
    }
  }
  .title{
    font-weight: bold;
    font-size: 18px;
    color: #475370;
    margin-bottom: 20px;
    line-height: 38px;
  }
  .content{
    font-family: MicrosoftYaHeiUI;
    font-size: 18px;
    color: #666;
    line-height: 30px;
    margin-bottom: 40px;
    padding-left: 25px;
    &:last-child{
      margin-bottom: 0;
    }
  }
  .render {
    color: #666;
    margin:10px 0;
  }
}

