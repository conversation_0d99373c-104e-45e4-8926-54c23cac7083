import React from 'react';
import classnames from 'classnames';
import styles from './styles/index.module.less';
import { useClient } from '@/hooks';
import ChartForPie from '@/components/ChartForPie';
import ChartForBar from '@/components/ChartForBar';
import PersonBox from '@/assets/special-assess/person-box.png';
// Add more specific prop-type interfaces
interface IChartProps {
  pieProps?: {
    height?: number;
    customOption?: any; // Define specific chart option type
    label?: string;
  },
  barProps?: {
    height?: number;
    customOption?: any; // Define specific chart option type
    label?: string;
  },
}

interface IFieldNames {
  latitudeDescription?: string;
  levelDescription?: string;
  chartSituation?: string;
  waringStudentsList?: string;
  levelSuggest?: string;
  levelList?: string;
  degreeDistributionList?: string;
}

interface ILabelNames {
  [key: string]: string;
}

type Type = 'person' | 'class' | 'grade';

interface IProps {
  className?: string;
  data: Record<string, any>;
  resultLabelAfter?: string;
  children?: React.ReactNode;
  resultAfter?: React.ReactNode;
  fieldNames: IFieldNames;
  labelNames?: ILabelNames;
  chartProps?: IChartProps;
  type: Type;
  color: string;
}

interface IChartCardProps {
  type: Type;
  data: Record<string, any>;
  chartProps?: IChartProps;
  fieldNames?: IFieldNames;
  labelNames?: ILabelNames;
}

export const ChartCard = (props: IChartCardProps) => {
  const {
    type,
    data,
    chartProps,
    fieldNames = {
      latitudeDescription: 'latitudeDescription',
      levelDescription: 'levelDescription',
      chartSituation: 'chartSituation',
      waringStudentsList: 'waringStudentsList',
      levelSuggest: 'levelSuggest',
    },
    labelNames = {},
  } = props;

  const cLabelNames = {
    latitudeDescription: '内容说明：',
    levelDescription: '★ 你的情况',
    waringStudentsList: '★ 预警学生',
    levelSuggest: '★ 心理建议',
    ...labelNames,
  };

  const [render] = useClient();
  if (!type) return <></>;
  const renderM = {
    person: () => (
      <div className={styles.statusWrapper}>
        <img src={PersonBox} />
        <div className={styles.label}>{data.latitudeLevelTitle}</div>
      </div>
    ),
    class: () => (
      <div className={styles.chartSituation}>
        <p className={styles.title}>★ 班级情况</p>
        {chartProps?.pieProps && <ChartForPie {...chartProps?.pieProps} />}
        {fieldNames.degreeDistributionList && (
          <div className={styles.extra}>
            <p>在该班级中：</p>
            <ul>
              {data[fieldNames.degreeDistributionList].map(
                (item: any, i: any) => (
                  <li key={i}>
                    有<span>{item.num}</span>
                    人表现{item.latitudeLevelTitle} 占比
                    <span>{item.percentage}%</span>;
                  </li>
                )
              )}
            </ul>
          </div>
        )}
        {fieldNames.levelList && <div className={styles.extra}>
          <p>在该班级中：</p>
          <ul>
            {data[fieldNames.levelList].map((item: any, i: number) => (
              <li key={i}>
                有<span>{item.num}</span>
                人表现{item.name};
              </li>
            ))}
          </ul>
        </div>}
      </div>
    ),
    grade: () => (
      <div className={styles.chartSituation}>
        <p className={styles.title}>★ 年级情况</p>
        <div className={styles.chartWrapper}>
          {chartProps?.pieProps && <ChartForPie {...chartProps?.pieProps} />}
        </div>
        {fieldNames.levelList && (
          <div className={styles.extra}>
            <p>在该年级中：</p>
            <ul>
              {data[fieldNames.levelList].map((item: any, i: any) => (
                <li key={i}>
                  有<span>{item.num}</span>
                  人表现{item.name};
                </li>
              ))}
            </ul>
          </div>
        )}
        {fieldNames.degreeDistributionList && (
          <div className={styles.extra}>
            <p>在该年级中：</p>
            <ul>
              {data[fieldNames.degreeDistributionList].map(
                (item: any, i: any) => (
                  <li key={i}>
                    有<span>{item.num}</span>
                    人表现{item.latitudeLevelTitle} 占比
                    <span>{item.percentage}%</span>;
                  </li>
                )
              )}
            </ul>
          </div>
        )}
        {chartProps?.barProps && (
          <>
            <p className={styles.title}>{chartProps?.barProps.label}</p>
            <ChartForBar {...chartProps?.barProps} />
          </>
        )}
      </div>
    ),
  };

  return (
    <div className={styles.chartCard}>
      <div className={styles.resultRemark}>
        {/* 说明 */}
        {fieldNames.latitudeDescription && (
          <div className={styles.result}>
            <span className={styles.resultLabel}>内容说明：</span>
            <span
              className={styles.render}
              dangerouslySetInnerHTML={{
                __html: (render && data[fieldNames.latitudeDescription]) || '',
              }}
            />
          </div>
        )}
        {/* 图和文案 */}
        {fieldNames['chartSituation'] && renderM[type as Type]()}
        <div>
          {fieldNames.levelDescription && (
            <>
              <p className={styles.title}>{cLabelNames.levelDescription}</p>
              <p
                className={styles.content}
                dangerouslySetInnerHTML={{
                  __html: (render && data[fieldNames.levelDescription]) || '',
                }}
              />
            </>
          )}

          {fieldNames.waringStudentsList && (
            <>
              <p className={styles.title}>{cLabelNames.waringStudentsList}</p>
              <p className={styles.content}>
                {data[fieldNames.waringStudentsList]?.join('，')}
              </p>
            </>
          )}

          {fieldNames.levelSuggest && (
            <>
              <p className={styles.title}>{cLabelNames.levelSuggest}</p>
              <p
                className={styles.content}
                dangerouslySetInnerHTML={{
                  __html: (render && data[fieldNames.levelSuggest]) || '',
                }}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

const AnalysisCard = (props: IProps) => {
  const { chartProps, type, fieldNames, labelNames, data, color } = props;

  return (
    <div className={classnames(styles.caseAnalysisCard)} style={{ backgroundColor: color }}>
      <div className={styles.info}>
        <div className={styles.left}>{data.latitudeTitle}</div>
      </div>
      <ChartCard
        type={type}
        chartProps={chartProps}
        fieldNames={fieldNames}
        labelNames={labelNames}
        data={data}
      />
    </div>
  );
};

export default AnalysisCard;
