import { ITreeItem } from '~/complexComponents/index.d';

const tree: ITreeItem[] = [
  {
    type: 'ChartForPie',
    props: {
      wrapperStyle: {
        width: '860px',
        height: '400px',
      },
      contentStyle: {
        width: '860px',
        height: '400px',
        border: '1px solid #ccc',
        borderRadius: '4px'
      }
    },
    sortProps: (data: any) => {
      return {
        id: `ChartForPie${data.latitudeId}`,
        data: data?.levelList.map(
          (j: Record<string, string>) => ({
            ...j,
            value: j.num,
          })
        ),
        options: {
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: "vertical",
            right: "10%",
            bottom: "20%",
            textStyle: {
              fontSize: 18,
            },
          },
          color: ["#00DFA6", "#4B80FF", "#FF9631", "#FF5858"],
          series: [
            {
              name: '',
              type: 'pie',
              radius: '50%',
              center: ["40%", "50%"],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              label: {
                fontSize: 16,
                color: "#000",
                formatter: (params: { name: string; percent: string; }) => {
                  return (params.name + "\n" + params.percent + "%");
                },
              },
            },
          ],
        },
      }
    }
  }
]

export default tree;