import React from "react";
import { Form, Button, Input, Popover, message } from "antd";
import styles from "./index.module.less";
import { DownloadOutlined } from '@ant-design/icons';
import MstQtAnalytics from 'mst-analytics';
import moment from "moment";
import { downloadFileStream, filterInvalidValues } from '@/utils/tools';
import Api from '@/service/api/special-report';
import { useSyncState } from "~/utils/hooks";

interface ISearchParams {
  evaluationTaskId: string;
  gradeId?: string;
  classId?: string;
  classGroupNum?: string;
}

interface IClickPvParams {
  eventCode: string;
  [key: string]: string | number;
}

interface ISearchBarProps {
  handleSearch: (values: any) => void;
  searchParams: ISearchParams;
  clickPvParams: IClickPvParams;
  reportType: number;
}

interface IExcelLoadItem {
  loading: boolean;
}

const SearchBar: React.FC<ISearchBarProps> = ({
  searchParams,
  handleSearch,
  clickPvParams,
  reportType
}) => {
  const [form] = Form.useForm();
  const onFinish = async (values: any) => handleSearch(values);
  const [excelLoad, setExcelLoad] = useSyncState<IExcelLoadItem[]>([
    {
      loading: false,
    },
    {
      loading: false,
    }
  ]);

  const handleDownLoadExcel = async (index: number) => {
    try {
      if (excelLoad.current[index].loading) {
        message.warn('正在生成中，无需重复点击');
        return
      }
      excelLoad.current[index].loading = true;
      setExcelLoad([...excelLoad.current]);
      const {
        evaluationTaskId,
        gradeId,
        classId,
        classGroupNum,
      } = searchParams;
      const params = filterInvalidValues({
        evaluationTaskId,
        gradeId,
        classId,
        classGroupNum,
        desensitizationFlag: index,
      })
      const stream = await (reportType === 2 ? Api.exportClassWarningRosterExcel(params) : Api.exportGradeWarningRosterExcel(
        params
      ));
      if (stream) {
        downloadFileStream({
          stream,
          fileName: `${reportType === 2 ? '班级' : '年级'}预警名单${index ? '隐私': '全名'}版本-${moment(Date.now()).format('YYYY-MM-DD HH:mm:ss')}.xlsx`,
        })
        message.success('下载成功！')
      }
      MstQtAnalytics.clickPv({
        download_type: index ? '隐私名单' : '全名名单',
        ...clickPvParams,
      });
    } catch (error: any) {
      if (error) {
        message.error(error.msg || '导出预警名单失败！')
      }
    } finally {
      excelLoad.current[index].loading = false;
      setExcelLoad([...excelLoad.current]);
    }
  };

  const formLayout = {
    labelCol: {
      xs: { span: 7 },
      md: { span: 7 }
    },
    wrapperCol: {
      xs: { span: 17 },
      md: { span: 17 }
    }
  };

  return (
    <div className={styles['search-bar-container']}>
      <Form
        form={form}
        {...formLayout}
        onFinish={onFinish}
        layout="inline"
        initialValues={{
          userName: "",
          orderType: 0
        }}
      >
        <Form.Item label="学生查询" name="userName">
          <Input placeholder="请输入学生名称" style={{ width: 170 }} />
        </Form.Item>

        <Form.Item>
          <Button type="primary" className={styles['search-btn']} htmlType="submit">
            搜索
          </Button>
        </Form.Item>

        <div className={styles['buttons-box']}>
          <Popover content={<p>请注意保护学生隐私安全</p>}>
            <Button
              type='ghost'
              onClick={() => handleDownLoadExcel(0)}
              /* @ts-ignore */
              icon={<DownloadOutlined />}
              loading={excelLoad.current[0].loading}
              className={excelLoad.current[0].loading ? styles['button-loading'] : ''}
            >
              {excelLoad.current[0].loading ? '全名版本生成中...' : '下载全名版本'}
            </Button>
          </Popover>
          <Button
            type='ghost'
            onClick={() => handleDownLoadExcel(1)}
            /* @ts-ignore */
            icon={<DownloadOutlined />}
            loading={excelLoad.current[1].loading}
            className={excelLoad.current[1].loading ? styles['button-loading'] : ''}
          >
            {excelLoad.current[1].loading ? '隐私版本生成中...' : '下载隐私版本'}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default SearchBar;
