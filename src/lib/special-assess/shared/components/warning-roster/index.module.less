

.warning-container {
  background-color: #fff;
  margin-top: 32px;

  .warning-content {
    display: block;
    .warning-table {
      :global {
        .ant-table-thead > tr > th  {
          background: #F0F3FB;
          color: #96A2BE
        }
        .ant-table-tbody > tr > td {
          font-size: 14px;
          color: #4B5466;
        }
      }
    }
  }
  .warning-description {
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
    color: #666;
    .warning-sort-desc {
      line-height: 32px;
      font-size: 14px;
    }
    .pagination {
      :global {
        .ant-pagination-item {
          color: #4B5466;
          font-size: 14px;
        }
        .ant-pagination-item-active {
          background-color: #2F6DFF;
          a {
            color: #fff;
          }
        }
      }
    }
  }

  &>div:nth-child(1) {
    margin-top: 0;
  }

  &.single-print {
    margin-top: 0;
    padding-top: 0;
  }
}