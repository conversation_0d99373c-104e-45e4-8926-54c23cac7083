import React, { useState, useEffect, useRef } from "react";
import { Table, Pagination } from "antd";
import styles from "./index.module.less";
import Api from "~/service/api/special-report";
import SearchBar from "../search-bar";
import { IWarningRosterItem } from "~/service/api/special-report/types";
import SafeLogger from '~/utils/safe-logger';

interface IWarningRosterProps {
  queryParams: any;
  printMode: any;
  textClassName?: string;
  clickPvParams: any
  reportType: number;
  sortDescription: string
}

// 预警名单
const WarningRoster: React.FC<IWarningRosterProps> = ({
  queryParams = {},
  printMode: isPrint,
  clickPvParams,
  reportType,
  sortDescription
}) => {
  const {
    evaluationTaskId,
    gradeId,
    classId,
    classGroupNum,
  } = queryParams;

  const [dataList, setDataList] = useState<IWarningRosterItem[]>([]);
  const dataListRef = useRef<IWarningRosterItem[]>([]);
  const [pageInfo, setPage] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
  });

  const getWarningList = async (params: any) => {
    try {
      const {
        current = params.current || pageInfo.current,
        userName = '',
      } = params;
      const { data: { data = [], totalRecords = 0, pageIndex = 1 } } = reportType === 2 ? await Api.getClassWarningRosterList({
        userName,
        evaluationTaskId,
        gradeId,
        classId,
        pageIndex: current,
        pageSize: 10,
      }) : await Api.getGradeWarningRosterList({
        userName,
        evaluationTaskId,
        gradeId,
        classGroupNum,
        pageIndex: current,
        pageSize: 10,
      });
      const dataSourceList: any[] = [];
      if (data && data.length) {
        dataListRef.current = data;
        data.forEach((item, index) => {
          const { classId, className, userId, userName, warningUserLatitudeList } = item;
          const obj = Object.fromEntries(warningUserLatitudeList.sort(function (a, b) {
            return a.latitudeLevelType - b.latitudeLevelType;
          }).map(v => [v.latitudeId, v]));
          dataSourceList.push({
            classId,
            className,
            userId,
            userName,
            ...obj
          })
        })
      }
      setDataList(dataSourceList || []);
      setPage((pre) => ({
        ...pre,
        total: totalRecords,
        current: pageIndex,
      }));
    } catch (error) {
      SafeLogger?.baseLogger?.error('special-assess-warning-list', {
        reason: '获取预警名单失败',
        error,
      });
    }
  };


  const getColumns = (list: IWarningRosterItem[]) => {
    if (list && list.length) {
      const titleList: any[] = [];
      const keyMap: Record<string, string> = {};
      list.forEach((item: { warningUserLatitudeList: any[]; }) => {
        if (item.warningUserLatitudeList) {
          item.warningUserLatitudeList.forEach(v => {
            if (!keyMap[v.latitudeId]) {
              keyMap[v.latitudeId] = v.latitudeId;
              titleList.push({
                dataIndex: 'latitudeLevelType',
                title: '维度',
                width: 200,
                render: (_: any, record: any) => {
                  const current = record[v.latitudeId] || {};
                  return current.latitudeTitle
                }
              }, {
                title: '得分',
                dataIndex: 'score',
                width: 70,
                render: (_: any, record: any) => {
                  const current = record[v.latitudeId] || {};
                  return current.score
                }
              }, {
                title: '等级',
                dataIndex: 'levelName',
                width: 160,
                render: (_:any, record: any) => {
                  const current = record[v.latitudeId] || {};
                  return current.levelName
                }
              })
            }
         })
        }
      })
      return titleList;
    }
    return [{
      title: '维度',
      dataIndex: 'latitudeLevelType',
    }, {
      title: '得分',
      dataIndex: 'score',
    }, {
      title: '等级',
      dataIndex: 'levelName',
    }];
  }

  const columns: any[] = [
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 100,
    },
    {
      title: '班级',
      dataIndex: 'className',
      width: 120,
    },
    ...getColumns(dataListRef.current),
  ]

  useEffect(() => {
    getWarningList({});
  }, []);

  const onChangePage = (page: any) => getWarningList({ current: page });

  const handleSearch = (values: any) => getWarningList({
    current: 1,
    ...values,
  });

  return (
    <div className={
      styles['warning-container']
    }>
      {!isPrint && (
        <SearchBar
          handleSearch={handleSearch}
          searchParams={queryParams}
          clickPvParams={clickPvParams}
          reportType={reportType}
        />
      )}
      <div className={styles['warning-content']}>
        <Table
          dataSource={dataList}
          columns={columns}
          rowKey='userId'
          size="small"
          bordered
          className={styles['warning-table']}
          pagination={false}
          scroll={{x: 100, y: 800}}
        />
      </div>
      <div className={styles['warning-description']}>
        <div>{sortDescription}</div>
        <div className={styles.pagination}>
          { pageInfo.total > 10 ?
            <Pagination
              {...pageInfo}
              onChange={onChangePage}
              showSizeChanger={false}
            /> : null
          }
        </div>
      </div>
    </div>
  );
};

export default WarningRoster;
