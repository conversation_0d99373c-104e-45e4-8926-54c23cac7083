import React from 'react';
import styles from './styles/index.module.less';
import classnames from 'classnames';
import LogoPng from '@/assets/special-assess/logo.png';
import HeaderBg from '@/assets/special-assess/header.png';
import moment from 'moment';
import chunk from 'lodash/chunk';

interface IUserBaseInfo {
  userName?: string;
  className?: string;
  schoolName?: string;
  gradeName?: string;
  reportCode?: string;
  reportTime?: string;
}

interface IProps {
  height?: number;
  userBaseInfo: IUserBaseInfo;
  reportTypeTitle: string;
  reportTitle: string;
  isPrint: boolean;
}

const ReportCover = (props: IProps) => {
  const { userBaseInfo, reportTitle, reportTypeTitle } = props;

  const renderUserInfoList = () => {
    const GROUP_SIZE = 3;
    const mappingObj: IUserBaseInfo = {
      userName: '姓名',
      className: '班级',
      schoolName: '学校',
      gradeName: '毕业年份',
      reportCode: '报告编号',
      reportTime: '报告时间',
    };
    const mapValue = Object.keys(mappingObj)
      .filter((i: string) => userBaseInfo?.[i as keyof IUserBaseInfo])
      ?.map((j: string) => ({
        label: mappingObj[j as keyof IUserBaseInfo],
        value:
          j === 'reportTime'
            ? moment(userBaseInfo?.[j as keyof IUserBaseInfo]).format('YYYY-MM-DD')
            : userBaseInfo?.[j as keyof IUserBaseInfo],
      }));
    return chunk(mapValue, GROUP_SIZE);
  };

  return (
    <div className={classnames(styles.bannerWrapper)}>
      <div
        className={styles.bannerImg}
        style={{
          backgroundImage: `url(${HeaderBg})`,
        }}
      />
      <div className={styles.infoWrap}>
        <img className={styles.logo} src={LogoPng} alt="log" />
        <p>高中生</p>
        <p className={styles.title}>
          {reportTitle && reportTitle.length > 50
            ? `${reportTitle.substring(0, 50)}`
            : reportTitle}
        </p>
        <div className={styles.tag}>{reportTypeTitle}</div>
        <div className={styles.user}>
          {userBaseInfo &&
            renderUserInfoList().map((i, ind) => {
              return (
                <div key={ind} className={styles.line}>
                  {i.map((item, index) => (
                    <div key={index}>
                      <span className={styles.label}>{item.label}：</span>
                      <span>{item.value}</span>
                    </div>
                  ))}
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default ReportCover;
