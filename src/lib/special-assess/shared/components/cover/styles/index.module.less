.bannerWrapper {
  position: relative;
  .bannerImg{
    position: absolute;
    width: 100%;
    min-width: 1100px;
    height: 420px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top;
    z-index: 1;
  }
}
.infoWrap {
  position: relative;
  z-index: 5;
  margin: 0 auto;
  min-height: 475px;
  width: 1000px;
  font-size: 38px;
  color: #fff;
  padding-top: 23px;
  .logo{
    width: 90px;
    margin-bottom: 32px;
  }
  p{
    line-height: 50px;
  }
  .title{
    font-weight: bold;
    font-size: 60px;
    line-height: 76px;
    max-width: 660px;
    max-height: 152px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .tag{
    margin-top: 24px;
    width: 128px;
    height: 32px;
    background: #FF9320;
    line-height: 32px;
    font-size: 16px;
    color: #fff;
    text-align: center;
  }
  .user{
    color: #3c4963;
    margin-top: 84px;
    height: 110px;
    background: #FFFFFF;
    box-shadow: 0 0 20px 0 #E6ECE9;
    border-radius: 8px;
    font-size: 16px;
    flex-wrap: wrap;
    .line{
      display: flex;
      align-items: center;
      height: 55px;
      border-bottom: 1px dashed #ccc;
      &:last-child{
        border-bottom: none;
      }
      > div{
        flex: 1;
        padding-left: 20px;
        display: flex;
        align-items: center;
        height: 55px;
        border-right: 1px dashed #ccc;
        &:last-child{
          border-right: none;
        }
      }
      .label{
        font-weight: bold; 
      }
    }
  }
}
