import Image from 'next/image';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import moment from 'moment';
import ReadGuide from '@/components/ReadGuide';
import CustomTable from '@/components/CustomTable';
import { descriptions } from '@/constants/psychology';
import type { ITreeList } from '@/complexComponents/index.d';
import type { classReportDataProps, latitudeItem } from '@/pages/psychology/classReport/types';
import API, { IGetClassWarningStudentList }  from '@/service/psychology/gradeReport';
import { ColumnsType } from 'antd/es/table/interface';
import { objectToUrlParams } from '~/utils/tools';
import IconDownload from '@/assets/psychology/iconDownload.png';
import styles from '@/pages/psychology/classReport/index.module.less';

const sidePageNavList: any[] = [
  { label: "1、报告导读", point: "reportReading" },
  {
    label: "2、结果分析", point: "resultAnalyse",
    children: [
      { label: "2.1 主要发现", point: "mainFindings" },
      { label: "2.2 心理风险指数", point: "riskIndex" },
      { label: "2.3 心理状态分布", point: "stateDistribution" },
      { label: "2.4 具体因子分析", point: "factorAnalysis" }
    ]
  },
  { label: "3、预警名单", point: "warningList" }
]

// 表格通用props
const columnJson: Record<
  string,
  { title: string; noLevel?: boolean; width?: number }
> = {
  // className: { title: '班级', width: 80, noLevel: true },
  userName: { title: '姓名', noLevel: true },
  anxietyTendency: { title: '焦虑倾向', width: 70 },
  depressionTendency: { title: '抑郁倾向', width: 70  },
  paranoidTendency: { title: '偏执倾向', width: 70  },
  hostileTendency: { title: '敌对倾向', width: 70  },
  interpersonalSensitivity: { title: '人际敏感', width: 70  },
  obsessive: { title: '强迫倾向', width: 70  },
  fearTendency: { title: '恐惧倾向', width: 70  },
  physicalSymptoms: { title: '身体症状', width: 70  },
  psychotic: { title: '精神病性', width: 70  },
  riskIndex: { title: '总体风险', width: 70, noLevel: true },
  riskLevelName: { title: '风险等级', width: 70, noLevel: true },
  options: { title: '详情', },
};
export const riskColorList = [
  { name: '高风险', color: '#ff5858' },
  { name: '较高风险', color: '#ff9630' },
  { name: '轻微风险', color: '#4b80ff' },
  { name: '无风险', color: '#263456' },
];
export const getTopicBoxProps = (): { columns: ColumnsType } => {
  return {
    columns: [
      ...Object.entries(columnJson).map(
        ([dataIndex, { title, noLevel, width }]) => ({
          dataIndex,
          title,
          width,
          ...(noLevel
            ? {}
            : {
                render: (v: number, record: any) => {
                  const {
                    [dataIndex]: value,
                    [`${dataIndex}LevelName`]: name,
                    userRecordId,
                  } = record;
                  const color = riskColorList.find(
                    (item) => item.name === name
                  )?.color;

                  return dataIndex !== 'options' ? (
                    <span {...{ style: { color } }}>{value}</span>
                  ) : (
                    <a
                      href={`${location.origin}/psychology-service/psychology/personReport?evaluationRecordId=${userRecordId}`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      个人报告
                    </a>
                  );
                },
              }),
        })
      ),
    ],
  };
};

// 健康测-班级报告
const tree: ITreeList = [
  {
    type: 'page',
    list: [
      {
        isRealNode: true,
        props: {
          style: {
            width: '1000px',
            margin: '0 auto',
          },
        },
        list: [
          {
            type: 'THeaderControl',
            props: {
              title: '报告导读',
              subTitle: 'Introduction',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[0].point
            },
            sortProps: (data: classReportDataProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperStyle: printMode ? { margin: '0px 0px 0px' } : { margin: '60px 0px 30px' },
              }
            }
          },
          {
            type: 'Card',
            props: {
              title: '尊敬的老师',
              wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
            },
            sortProps: (data: classReportDataProps) => {
              const { startTime, endTime, evaluatedPeople } = data;
              return {
                content: (
                  <ReadGuide>
                    <p>
                      ★ &nbsp;<b>测评时间</b>
                    </p>
                    <p>
                      ● &nbsp;&nbsp;
                      {`${moment(startTime).format('LL')} ~ ${moment(
                        endTime
                      ).format('LL')}`}
                    </p>
                    <p>
                      ★ &nbsp;<b>参与报告人数</b>
                    </p>
                    <p>● &nbsp;&nbsp;{evaluatedPeople}</p>
                  </ReadGuide>
                ),
              };
            },
          },
          {
            type: 'THeaderControl',
            props: {
              title: '结果分析',
              subTitle: 'Analysis',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[1].point
            },
            sortProps: (data: classReportDataProps) => {
              const { queryParams: { printMode } } = data;
              return {
                className: printMode && 'typePrint',
              }
            }
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                padding: '30px',
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '01 主要发现',
                  themeColor: '#00C865',
                  titleStyle: {
                    marginBottom: 20,
                  },
                  id: sidePageNavList[1].children[0].point,
                },
                sortProps: (data: classReportDataProps) => {
                  const { mainFindings } = data;
                  return {
                    content: (
                      <div className={styles.textBox}>
                        {mainFindings?.map((html: string, key: number) => {
                          return (
                            <p
                              key={key}
                              dangerouslySetInnerHTML={{
                                __html: `${key + 1}. ${html}`,
                              }}
                            />
                          );
                        })}
                      </div>
                    ),
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                padding: '30px',
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '02 心理风险指数',
                  themeColor: '#00C865',
                  titleStyle: {
                    marginBottom: 20,
                  },
                  id: sidePageNavList[1].children[1].point,
                },
              },
              {
                type: 'ChartForPie',
                props: {
                  id: 'ChartForPie-riskIndex',
                  wrapperClass: styles.textBox,
                  contentStyle: { width: '100%', margin: '0 auto' },
                  height: '360px',
                },
                sortProps: (data: classReportDataProps) => {
                  const { riskIndex } = data;
                  const colors = [
                    '#00DFA6',
                    '#4B80FF',
                    '#FF9631',
                    '#FF5858',
                  ];
                  return {
                    before: <p>{riskIndex?.inConclusion}</p>,
                    data: [
                      { name: riskIndex?.riskLevelName, value: riskIndex?.riskIndexScore },
                      { value: 100 - riskIndex?.riskIndexScore, name: '其他' },
                    ],
                    color: [colors[(riskIndex?.riskLevel || 1) - 1], '#eef1f0'],
                    options: {
                      series: [
                        {
                          name: '风险等级',
                          type: 'pie',
                          radius: ['67%', '80%'],
                          avoidLabelOverlap: false,
                          label: {
                            show: true,
                            position: 'center',
                            formatter: ['{e|风险等级}', `{d|${riskIndex?.riskLevelName}}`].join('\n'),
                            rich: {
                              a: {
                                color: '#7D869A',
                                lineHeight: 24,
                                fontSize: 18,
                                fontFamily: 'MicrosoftYaHeiUI',
                                height: 24,
                              },
                              b: {
                                color: colors[(riskIndex?.riskLevel || 1) - 1],
                                lineHeight: 74,
                                fontSize: 53,
                                fontWeight: 600,
                                fontFamily:
                                  'DINAlternate, DINAlternate- Bold',
                                height: 74,
                              },
                              c: {
                                color: colors[(riskIndex?.riskLevel || 1) - 1],
                                lineHeight: 24,
                                fontSize: 20,
                                fontFamily: 'MicrosoftYaHeiUI',
                                height: 24,
                              },
                              d: {
                                color: colors[(riskIndex?.riskLevel || 1) - 1],
                                lineHeight: 74,
                                fontSize: 36,
                                fontWeight: 600,
                                fontFamily:
                                  'PingFangSC, PingFangSC-Semibold',
                                height: 74,
                              },
                              e: {
                                color: '#7D869A',
                                lineHeight: 24,
                                fontSize: 18,
                                fontFamily: 'MicrosoftYaHeiUI',
                                height: 24,
                              },
                            },
                          },
                          emphasis: {
                            label: {
                              show: true,
                              fontSize: '40',
                              fontWeight: 'bold',
                            },
                          },
                          labelLine: {
                            show: false,
                          },
                        },
                      ],
                    },
                    content: (
                      <>
                      <p>注：</p>
                        {riskIndex?.remarks.map((html: string, key: number) => {
                          return (
                            <p
                              key={key}
                              dangerouslySetInnerHTML={{
                                __html: html,
                              }}
                            />
                          );
                        })}
                      </>
                    ),
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '03 心理情况分析',
                  themeColor: '#00C865',
                  titleStyle: {
                    marginBottom: 20,
                  },
                  id: sidePageNavList[1].children[2].point,
                }
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '班级心理健康状态分布',
                  icon: true,
                  iconColor: '#00C865',
                  iconType: 'half',
                  wrapperStyle: {
                    marginLeft: -20,
                  },
                },
                sortProps: (data: classReportDataProps) => {
                  const { stateDistribution } = data;
                  return {
                    content: (
                      <div className={styles.textBox}>
                        <div className={styles.riskDistribution}>
                          <p>
                            <b>在本班级学生中：</b>
                          </p>
                          {stateDistribution?.map((item: any) => {
                            return (
                              <p key={item.riskId}>
                                ● &nbsp;{item.riskName}
                                的学生人数为
                                <b>{item.number}</b>
                                人,占比
                                <b>{item.percentage}%</b>;
                              </p>
                            );
                          })}
                        </div>
                      </div>
                    ),
                  }
                }
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '各因子预警统计',
                  icon: true,
                  iconColor: '#00C865',
                  iconType: 'half',
                  wrapperStyle: {
                    margin: '40px 0 0 -20px',
                  },
                },
              },
              {
                type: 'SearchTable',
                props: {
                  hideSearch: true,
                  wrapperStyle: {
                    margin: '20px 10px',
                  },
                  wrapperClass: styles.customTable,
                },
                sortProps: (data: classReportDataProps) => {
                  const {  factorWarning } = data;
                  const columns = [
                    {
                      title: `总人数=${factorWarning?.totalPeople}`,
                      dataIndex: 'latitudeName',
                      align: 'center',
                    },
                    {
                      title: '预警人数',
                      dataIndex: 'warningPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'percentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '高风险人数',
                      dataIndex: 'highestRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'highestRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '较高风险人数',
                      dataIndex: 'higherRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'higherRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '轻微风险人数',
                      dataIndex: 'minorRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'minorRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                  ];
                  return {
                    tableProps: {
                      columns,
                      dataSource: factorWarning?.latitudeList || [],
                      pagination: false,
                      border: true,
                      rowKey: 'latitudeId',
                      hidePagination: true,
                      info: <p className="mt20">注：饮食与睡眠因子无需预警</p>,
                    },
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '04 具体因子分析',
                  themeColor: '#00C865',
                  id: sidePageNavList[1].children[3].point,
                },
                sortProps: (data: classReportDataProps) => {
                  const {
                    queryParams: { printMode, detailReportMode },
                  } = data;
                  return {
                    subTitle: detailReportMode ? '' : '(不存在风险的因子不在此展示，如需了解无风险因子的具体情况请查看详细报告)',
                    wrapperClass: printMode ? 'typePrint' : '',
                  }
                }
              },
              {
                type: 'ChartForRadar',
                props: {
                  wrapperStyle: { lineHeight: '32px', fontSize: 18, marginTop: 20, padding: 20 },
                  contentStyle: { width: '100%', margin: '20px auto 40px' },
                  height: '400px',
                },
                sortProps: (data: classReportDataProps) => {
                  const { factorAnalysis } = data;
                  const columns: any = [
                    {
                      title: '',
                      dataIndex: 'name',
                      align: 'center',
                      width: 80,
                    },
                  ];
                  const keyName: string[] = [
                    'depressionScore',
                    'anxietyScore',
                    'bigotryScore',
                    'forcedScore',
                    'interpersonalSensitivityScore',
                    'hostileScore',
                    'fearScore',
                    'physicalSymptomsScore',
                    'psychoticScore',
                    'dietSleepScore',
                  ];
                  const propsName: { [propName: string]: any } = {};
                  keyName.forEach((v) => {
                    propsName[`${v}`] = 0;
                  });
                  const dataList: any = [
                    { name: '得分', id: 0, ...propsName },
                    { name: '参考分', id: 1, ...propsName },
                    { name: '预警分', id: 2, ...propsName },
                  ];
                  const dataName: any[] = [];
                  const dataVaule: number[] = [];
                  const dataMax: number[] = [];
                  const dataWarnnig: number[] = [];
                  factorAnalysis?.latitudeList.forEach((v: latitudeItem, key: number) => {
                    const name = `${keyName[key]}`;
                    columns.push({
                      title: (<div><div>{v.latitudeName.slice(0, 2)}</div><div>{v.latitudeName.slice(2)}</div></div>),
                      dataIndex: name,
                      align: 'center',
                    });
                    dataName.unshift({
                      name: v.latitudeName,
                      max: 5,
                    });
                    dataVaule.unshift(v.score);
                    dataMax.unshift(v.referenceScore);
                    dataWarnnig.unshift(v.warnningScore);
                    dataList[0][`${keyName[key]}`] = v.score;
                    dataList[1][`${keyName[key]}`] = v.referenceScore;
                    dataList[2][`${keyName[key]}`] = v.warnningScore;
                  });
                  return factorAnalysis?.latitudeList && {
                    before: (
                      <p dangerouslySetInnerHTML={{ __html: factorAnalysis?.inConclusion }} />
                    ),
                    legend: {
                      data: ['得分', '参考分', '预警分'],
                      fontSize: 14,
                      verticalAlign: 'bottom',
                      bottom: 0,
                    },
                    options: {
                      radar: [
                        {},
                        {
                          indicator: dataName,
                          center: ['50%', '46%'],
                          radius: 120,
                          startAngle: 125,
                          axisName: {
                            color: '#001139',
                            fontSize: 18,
                          },
                          splitArea: {
                            areaStyle: {
                              color: ['#fff', '#F2F6FF'].reverse(),
                            },
                          },
                          axisLine: {
                            lineStyle: {
                              color: '#E0ECFF',
                            },
                          },
                          splitLine: {
                            show: true,
                            lineStyle: {
                              color: '#E0ECFF',
                            },
                          },
                        },
                      ],
                      series: [
                        {
                          name: '成绩单',
                          type: 'radar',
                          radarIndex: 1,
                          symbol: 'none',
                          data: [
                            {
                              type: 'radar',
                              value: dataVaule,
                              name: '得分',
                              itemStyle: {
                                color: '#00C865',
                                fontSize: 18,
                              },
                              areaStyle: {
                                color: '#00C865',
                              },
                            },
                            {
                              value: dataMax,
                              type: 'radar',
                              name: '参考分',
                              itemStyle: {
                                color: '#FF9631',
                                borderColor: '#FF9631',
                                borderType: 'solid',
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                            {
                              value: dataWarnnig,
                              type: 'radar',
                              name: '预警分',
                              itemStyle: {
                                color: '#FF3C19',
                                borderColor: '#FF3C19',
                                borderType: 'solid',
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                          ],
                        },
                      ],
                    },
                    content: (
                      <div>
                        <CustomTable
                          wrapperClass={styles.factorTable}
                          propsData={{
                            columns,
                            dataSource: dataList,
                            pagination: false,
                            border: true,
                            rowKey: 'id',
                          }}
                        />
                        <p>注：</p>
                        <p>
                        参考分为全国常模的平均分，得分高于某个因子所对应的参考分，表明本次参评学生群体在该因子上的表现要差于全国平均水平，得分低于某个因子所对应的参考分，表明本次参评学生群体在该因子上的表现要好于全国平均水平。
                        </p>
                        <p>
                        预警分值为学生心理健康与否的分界线，若在某因子上得分超过预警分值，则表明参评群体内大部分学生在该因子上存在不同程度的问题，需要引起高度关注。
                        </p>
                      </div>
                    ),
                  };
                },
              },
              {
                sortList: (data: classReportDataProps) => {
                  const { factorAnalysis, queryParams: { detailReportMode } } = data;
                  const factorAnalysisDetailsList = detailReportMode ? factorAnalysis?.factorAnalysisDetails : factorAnalysis?.factorAnalysisDetails.filter(v => v.riskLevel !== 1)
                  const { classData = [] } = descriptions;
                  const renderResult: any[] = [];
                  const themeColorList = ["#00c865", "#E1D815", "#FF9631", "#FF5858"];

                  if (factorAnalysisDetailsList && factorAnalysisDetailsList.length > 0) {
                    factorAnalysisDetailsList.forEach((v, index: number) => {
                      const { latitudeId } = v;
                      const targetItem: any = classData.find(
                        (values: { latitudeId: number }) =>
                          values.latitudeId === latitudeId
                      );
                      renderResult.push({
                        type: 'Card',
                        props: {
                          wrapperStyle: {
                            margin: '30px 15px 15px',
                            boxShadow: '0px 0px 20px 0px #e6ece9',
                          },
                        },
                        list: [
                          {
                            type: 'StatusBar',
                            props: {
                              titleStyle: {
                                color: v.latitudeName.includes("睡眠") ? '#475370' : 'fff',
                              },
                              title: v.latitudeName,
                              themeColor: v.latitudeName.includes("睡眠") ? '#C7D3F1' : themeColorList[(v.riskLevel || 1) - 1],
                              right: (
                                <div className={styles.rightTitle}>
                                  <span
                                    style={{
                                      color: v.latitudeName.includes("睡眠") ? '#475370' : themeColorList[(v.riskLevel || 1) - 1],
                                    }}
                                  >
                                    {v.score}
                                  </span>
                                  / {v.totalScore}
                                </div>
                              ),
                            },
                          },
                          {
                            type: 'ChartForPie',
                            props: {
                              id: `ChartForPie-${index}-factorAnalysis`,
                              height: '400px',
                              contentStyle: v.riskStatusDistribution ? { width: '836px', height: '400px' } : {},
                              before: (
                                <>
                                  {targetItem.factorDescription && (
                                    <>
                                      <p>
                                        <b>★ 因子说明</b>
                                      </p>
                                      <p>{targetItem.factorDescription}</p>
                                    </>
                                  )}
                                  {/* {targetItem.highScore && (
                                    <>
                                      <p>
                                        <b>★ 高分表现</b>
                                      </p>
                                      <p>{targetItem.highScore}</p>
                                    </>
                                  )}
                                  {targetItem.lowScore && (
                                    <>
                                      <p>
                                        <b>★ 低分表现</b>
                                      </p>
                                      <p>{targetItem.lowScore}</p>
                                    </>
                                  )} */}
                                  <p>
                                    <b>★ 风险状态分布</b>
                                  </p>
                                </>
                              ),
                              color: [
                                '#FF5858',
                                '#FF9631',
                                '#4B80FF',
                                '#00DFA6',
                              ],
                              data: detailReportMode && v.riskStatusDistribution?.map(
                                ({ riskName: name, percentage: value }) => ({
                                  name,
                                  value,
                                })
                              ),
                              options: detailReportMode && {
                                title: {
                                  text: '班级学生的心理健康状况分布',
                                  left: 'center',
                                  bottom: 0,
                                  color: '#001139',
                                },
                                tooltip: {
                                  trigger: 'axis',
                                  formatter: '{a} : {c}({d}%)',
                                },
                                legend: {
                                  orient: 'vertical',
                                  right: '5%',
                                  bottom: '20%',
                                  itemHeight: 14,
                                  itemWidth: 14,
                                  icon: 'rect',
                                  fontSize: 18,
                                  data: v.riskStatusDistribution?.map(
                                    ({ riskName: name }) => name
                                  ),
                                },
                                series: [
                                  {
                                    name: '心理状态分布',
                                    type: 'pie',
                                    radius: '65%',
                                    center: ['50%', '50%'],
                                    selectedMode: 'single',
                                    data: v.riskStatusDistribution?.map(
                                      ({
                                        riskName: name,
                                        number: value,
                                        percentage: percent,
                                      }) => {
                                        return {
                                          name,
                                          value,
                                          percent,
                                        };
                                      }
                                    ),
                                    label: {
                                      fontSize: 18,
                                      position: 'outside',
                                      color: '#001139',
                                      formatter: (params: {
                                        name: string;
                                        percent: string;
                                      }) => {
                                        return (
                                          params.name +
                                          '\n' +
                                          params.percent +
                                          '%'
                                        );
                                      },
                                    },
                                    itemStyle: {
                                      borderRadius: 3,
                                      borderColor: '#fff',
                                      borderWidth: 1,
                                    },
                                  },
                                ],
                              },
                              wrapperClass: styles.textBox,
                              content: (
                                <div>
                                  <div className={styles.riskDistribution}>
                                    <p>
                                      <b>
                                        在该因子上，本班学生的风险状态分布：
                                      </b>
                                    </p>
                                    {v.riskStatusDistribution?.map(
                                      (item: any) => {
                                        return (
                                          <p key={item.riskId}>
                                            ● &nbsp;{item.riskName}
                                            的学生人数为
                                            <b>{item.number}</b>
                                            人,占比
                                            <b>{item.percentage}%</b>;
                                          </p>
                                        );
                                      }
                                    )}
                                  </div>

                                  {v.highestRiskStudents && (
                                    <>
                                      <p>
                                        <b>★ 高风险学生</b>
                                      </p>
                                      <p>{v.highestRiskStudents}</p>
                                    </>
                                  )}
                                  {v.higherRiskStudents && (
                                    <>
                                      <p>
                                        <b>★ 较高风险学生</b>
                                      </p>
                                      <p>{v.higherRiskStudents}</p>
                                    </>
                                  )}
                                  {v.minorRiskStudents && (
                                    <>
                                      <p>
                                        <b>★ 轻微风险学生</b>
                                      </p>
                                      <p>{v.minorRiskStudents}</p>
                                    </>
                                  )}
                                  {targetItem.counselingAdvice && (
                                    <>
                                      <p>
                                        <b>★ 辅导建议</b>
                                      </p>
                                      {targetItem.counselingAdvice.length > 0 &&
                                        targetItem.counselingAdvice.map(
                                          (text: string, key: number) => {
                                            return <p key={key}>{text}</p>;
                                          }
                                        )}
                                    </>
                                  )}
                                </div>
                              ),
                            },
                          },
                        ],
                      });
                    });
                  }
                  return renderResult;
                },
              },
            ],
          },

          {
            type: 'THeaderControl',
            props: {
              title: '预警名单',
              subTitle: 'Potential Risk Individuals',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[2].point,
            },
            sortProps: (data: classReportDataProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperClass: printMode && 'typePrint',
              }
            }
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                marginBottom: 50,
                boxShadow: '0px 0px 20px 0px #e6ece9',
              }
            },
            list: [
              {
                type: 'SearchTable',
                props: {
                  wrapperClass: styles.earlyWarningTable,
                },
                sortProps: (data) => {
                  const {
                    queryParams: { evaluationTaskId, classId, gradeId },
                  } = data;
                  const handleDonwload = (params: any) => {
                    const {
                      evaluationTaskId,
                      gradeId,
                      classId,
                      hide,
                      sort,
                      type,
                    } = params;
                    const urlParams = {
                      evaluationTaskId,
                      gradeId,
                      classId,
                      hide,
                      sort,
                      type,
                      pageIndex: 1,
                      pageSize: 99999,
                    };
                    window.open(
                      `${
                        location.origin
                      }/api/psychology/mentalHealthReport/exportPageClassWarning?${objectToUrlParams(
                        urlParams
                      )}`
                    );
                  };
                  const riskGradeList = [
                    { name: '高风险', color: '#ff5858' },
                    { name: '较高风险', color: '#ff9630' },
                    { name: '轻微风险', color: '#4b80ff' },
                    { name: '无风险', color: '#263456' },
                  ];
                  return {
                    searchForm: {
                      layout: 'inline',
                      wrapperClass: styles.searchBox,
                      optionBoxStyle: styles.buttonStyle,
                      columns: [
                        {
                          type: 'input',
                          key: 'userName',
                          formItemProps: {
                            label: '学生查询',
                          },
                          props: {
                            style: {
                              width: 230,
                            },
                            placeholder: '输入 学生姓名查询',
                          },
                        },
                        {
                          type: 'select',
                          key: 'sort',
                          formItemProps: {
                            label: '排序',
                          },
                          props: {
                            style: {
                              width: 230,
                            },
                            options: [
                              {
                                label: '按照姓名排序',
                                value: 1,
                              },
                              {
                                label: '按总体风险高低排序',
                                value: 2,
                              },
                            ],
                          },
                        },
                      ],
                    },
                    tableProps: {
                      beforeRender: (paramsData: any) => {
                        const { params } = paramsData;
                        return (
                          <div className={styles.downloadBox}>
                            <Tooltip
                              placement="top"
                              color="#fff"
                              overlayInnerStyle={{
                                color: '#333',
                                padding: '10px',
                              }}
                              title="请注意保护学生隐私安全"
                            >
                              <Button
                                onClick={() =>
                                  handleDonwload({ ...params, hide: false })
                                }
                              >
                                <Image
                                  src={IconDownload}
                                  width="14"
                                  height="14"
                                  alt="Download"
                                  className={styles.iconDownload}
                                />
                                &nbsp;下载全名称版本名单
                              </Button>
                            </Tooltip>
                            <Button
                              onClick={() =>
                                handleDonwload({ ...params, hide: true })
                              }
                            >
                              <Image
                                src={IconDownload}
                                width="14"
                                height="14"
                                alt="Download"
                                className={styles.iconDownload}
                              />
                              &nbsp;下载隐私保护名单
                            </Button>
                          </div>
                        );
                      },
                      rowKey: 'userId',
                      request: API.getClassWarningStudentList,
                      pagination: {
                        showTotal: (total: number, range: number[]) =>
                          `共 ${total}条,每页${range[1]}条`,
                      },
                      params: {
                        evaluationTaskId,
                        classId,
                        gradeId,
                        type: 1,
                        userName: '',
                        sort: 2,
                        pageIndex: 1,
                        pageSize: 10
                      },
                      sortParams: ({ current: pageIndex, ...rest} : { current: number; [key: string]: any;}): Partial<IGetClassWarningStudentList['params']> => ({
                        ...rest,
                        pageIndex,
                      }),
                      sortData: ({data: { data: dataSource, totalRecords: total }}: IGetClassWarningStudentList['data']) => ({
                        dataSource,
                        total,
                      }),
                      ...getTopicBoxProps(),
                      info: (
                        <div className={styles.explain}>
                          <p>
                            {riskGradeList.map((v, index) => (
                              <span key={index} className={styles.explainItem}>
                                <i
                                  className={styles.colorItem}
                                  style={{
                                    background: v.color,
                                  }}
                                />
                                {v.name}
                              </span>
                            ))}
                          </p>
                          <p>
                            注：分数越高，表示学生在这方面的风险越高
                          </p>
                        </div>
                      ),
                    },
                  };
                },
              },
            ],
          },
          {
            type: 'PageNavigation',
            props: {
              list: sidePageNavList,
              theme: 'green',
              timeout: 180000
            },
            sortProps: (data) => {
              const { queryParams: { printMode, detailReportMode }} = data;
              return {
                printMode,
                detailReportMode,
                sendParams: {
                  analysis_dimension: '班级',
                  bxl_evaluation_name: '高中生心理健康诊断测验',
                  bxl_evaluation_type: '心理综合筛查',
                }
              }
            }
          }
        ],
      },
    ],
  },
];

export default tree;
