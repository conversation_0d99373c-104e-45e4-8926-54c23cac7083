import Image from 'next/image';
import { Button } from "antd";
import moment from "moment";
import API, { IGetAreaCitySchoolEntranceListInfo } from '@/service/psychology/countyCover'
import { getSourceType, getToken, objectToUrlParams } from "~/utils/tools";
import styles from "@/pages/psychology/countyCover/index.module.less";
import { schoolStaticsItem } from "@/pages/psychology/countyCover/types";
import IconDownload from '@/assets/psychology/iconDownload.png'

// 心理健康测-区县入口页面报告
const tree = [
  {
    type: 'page',
    list: [{
      isRealNode: true,
      props: {
        style: {
          width: '1000px',
          margin: '0 auto',
        },
      },
      list: [
        {
          type: 'SearchTable',
          props: {
            wrapperStyle: { margin: '60px 0px 30px' },
            hideSearch: true,
          },
          sortProps: (data: any) => {
            const { currentData, onSearch } = data;
            const { cityCode, areaCode, graduationYear, reportDateStr } = currentData;
            // 跳转到年级
            const JumpToGrade = (record: schoolStaticsItem) => {
              if (!record.gradeFlag) return;
              const params: any = {
                type: 2,
                cityCode,
                schoolCode: record.schoolCode,
                areaCode: record.areaCode,
                gradeId: graduationYear,
                reportDateStr
              }

              if (record.gradeFlag === 1) {
                params.evaluationTaskId = record.answerTaskId;
                const link = `${location.origin}/psychology-service/psychology/gradeReport?${objectToUrlParams(params)}`
                window.open(link)
              } else {
                params.graduationYear = graduationYear;
                onSearch(params, record)
              }
            }

            const columnsList = [
              { title: '区县', dataIndex: 'areaName', align: 'center' },
              { title: '学校', dataIndex: 'schoolName', align: 'center' },
              { title: '年级', dataIndex: 'gradeName', align: 'center' },
              { title: '布置人数', dataIndex: 'totalNum', align: 'center' },
              { title: '完成测评人数', dataIndex: 'finishedNum', align: 'center' },
              { title: '完成率', dataIndex: 'finishedPercentage', align: 'center', render: (value: number) => <span>{value}%</span> },
              { title: '布置日期', dataIndex: 'assignDate', align: 'center', render: (value: number) => <span>{value ? `${moment(value).format('YYYY-MM-DD')}` : ''}</span> },
              { title: '截止日期', dataIndex: 'endDate', align: 'center', render: (value: number) => <span>{value ? `${moment(value).format('YYYY-MM-DD')}` : ''}</span> },
              // {
              //   title: '集体报告', dataIndex: 'operation', align: 'center', render: (_: any, record: schoolStaticsItem, key: any) => {
              //     return (
              //       <span key={record.areaCode}>
              //         {key ?
              //           <a
              //             onClick={() => {
              //               JumpToGrade(record);
              //             }}
              //             style={{
              //               textDecoration: record.gradeFlag ? 'underline' : '',
              //               color: record.gradeFlag ? 'rgba(250,140,22,1)' : 'rgba(0,17,57,0.85)',
              //               cursor: record.gradeFlag ? 'pointer' : 'text',
              //             }}
              //           >
              //             查看集体报告
              //           </a> : ''
              //         }
              //       </span>)
              //   }
              // }
            ]
            // 下载
            const handleDonwload = (params: any) => {
              const { type, cityCode, areaCode, graduationYear, reportDateStr } = params;
              const urlParams = {
                type,
                graduationYear,
                areaCode,
                cityCode,
                reportDateStr,
                sourceType: getSourceType(),
                token: getToken(),
              }
              const link = `${location.origin}/api/psychology/mentalHealthReport/exportAreaCitySchoolEntranceListInfo?${objectToUrlParams(urlParams)}`
              window.open(link)
            }
            return {
              tableProps: {
                wrapperClass: styles.customTable,
                beforeRender: (paramsData: any) => {
                  const { params } = paramsData;
                  return (
                    <div className={styles.downloadBox}>
                      <Button onClick={() => handleDonwload({ ...params })}><Image src={IconDownload} width="14" height="14" alt="Download" className={styles.iconDownload} /> &nbsp;Excel下载</Button>
                    </div>
                  )
                },
                params: {
                  areaCode,
                  cityCode,
                  type: 2,
                  graduationYear,
                  reportDateStr,
                },
                request: API.getAreaCitySchoolEntranceListInfo,
                sortParams: ({
                  ...rest
                } : {
                  [key: string]: any;
                }): Partial<IGetAreaCitySchoolEntranceListInfo['params']> => ({
                  ...rest,
                }),
                sortData: ({
                  data: { schoolList: dataSource },
                }: IGetAreaCitySchoolEntranceListInfo['data']) => ({
                  dataSource,
                }),
                rowKey: 'schoolCode',
                columns: columnsList,
                hidePagination: true,
              }
            }
          }
        },
      ]
    }]
  }];
export default tree;