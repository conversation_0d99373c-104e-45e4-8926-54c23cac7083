import { Button } from "antd";
import ReadGuide from "~/components/ReadGuide";
import { descriptions } from "~/constants/psychology";
import API, { IGetPageAreaWarningDetail } from '~/service/psychology/countyReport'
import { getSourceType, getToken, objectToUrlParams } from "~/utils/tools";
import styles from "@/pages/psychology/countyReport/index.module.less";
import { areaReportDataProps, areaReportProps, factorAnalysisDetailsItem } from "@/pages/psychology/countyReport/types";
import { sortBarYCategoryStack, riskColorList, sortRadarTable } from "~/utils/complex";
import IconDownload from '@/assets/psychology/iconDownload.png'

const sidePageNavList: any[] = [
  { label: "1、报告导读", point: "reportReading" },
  {
    label: "2、结果分析", point: "resultAnalyse",
    children: [
      { label: "2.1 主要发现", point: "mainFindings" },
      { label: "2.2 心理风险指数", point: "riskIndex" },
      { label: "2.3 心理状态分布", point: "stateDistribution" },
      { label: "2.4 具体因子分析", point: "factorAnalysis" }
    ]
  },
  { label: "3、预警汇总表", point: "earlyWarningSummaryTable" }
]

// 心理健康测-区县报告页面报告
const tree = [
  {
    type: 'page',
    isRealNode: true,
    props: {
      style: {
        width: '1000px',
        margin: '0 auto',
      },
    },
    list: [
      {
        type: 'THeaderControl',
        props: {
          title: '报告导读',
          subTitle: 'Introduction',
          wrapperStyle: { margin: '60px 0px 30px' },
          id: sidePageNavList[0].point
        },
      },
      {
        type: 'Card',
        props: {
          title: '尊敬的老师',
          wrapperStyle: { margin: 10, boxShadow: 'none' },
        },
        sortProps: () => {
          return {
            content: (
              <ReadGuide />
            ),
          };
        },
      },
      {
        type: 'THeaderControl',
        props: {
          title: '结果分析',
          subTitle: 'Analysis',
          wrapperStyle: { margin: '60px 0px 30px' },
          id: sidePageNavList[1].point
        },
        sortProps: (data: areaReportDataProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : '',
          }
        }
      },
      {
        type: 'CardTitleBar',
        props: {
          title: '01 主要发现',
          themeColor: '#00C865',
          titleStyle: {
            margin: 20,
            width: 'auto',
          },
        },
        sortProps: (data: areaReportDataProps) => {
          const { mainFindings } = data;
          return {
            id: sidePageNavList[1].children[0].point,
            content: (
              <div className={styles.textBox} style={{margin: 30}}>
                {mainFindings?.map((html: string, key: number) => (
                  <p
                    key={key}
                    dangerouslySetInnerHTML={{
                      __html: html,
                    }}
                  />
                ))}
              </div>
            ),
          };
        },
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '重点关注学校',
          themeColor: '#00C865',
          titleStyle: {
            margin: 20,
            width: 'auto',
          },
          icon: true,
          iconColor: '#00C865',
        },
        sortProps: (data: areaReportDataProps) => {
          const { focusOnSchools } = data;
          return {
            content: (
              <div className={styles.focusOnSchoolsBox}>
                {
                  focusOnSchools?.map((item, index) => (
                    <div key={index} className={styles.itemCard}>
                      <div className={styles.circleSerial}>{index + 1}</div>
                      <div className={styles.itemContent}>
                        <p className={styles.schoolName}>{item.schoolName}</p>
                        <p>高风险检出{item.highRiskPeople},占比{item.percentage}%</p>
                      </div>
                    </div>
                  ))
                }
                <p className={styles.explain}>{`${focusOnSchools && focusOnSchools.length > 0 ? '*重点关注学校为高风险检出人数最高的五所学校' : '暂无需要重点关注的学校哦~'}`}</p>
              </div>
            ),
          }
        }
      },
      {
        type: 'CardTitleBar',
        props: {
          title: '02 心理风险等级',
          themeColor: '#00C865',
          titleStyle: {
            margin: 20,
            width: 'auto',
          },
        },
        sortProps: (data: areaReportDataProps) => {
          const { riskIndex, queryParams: { printMode } } = data;
          return {
            id: sidePageNavList[1].children[1].point,
            content: <div className={styles.textBox} style={{margin: 30}}><p>{riskIndex?.conclusion}</p></div>,
            wrapperClass: printMode ? 'typePrint' : '',
          }
        },
      },
      {
        type: 'ChartForPie',
        props: {
          id: `cityReprot-ChartForRing`,
          wrapperStyle: { padding: '0px 20px' },
          contentStyle: { width: '100%', margin: '0 auto' },
          height: '320px',
        },
        sortProps: (data: areaReportDataProps) => {
          const { riskIndex } = data;
          const colors = ["#00DFA6", "#4B80FF", "#FF9631", "#FF5858"];
          return {
            data: [
              { name: riskIndex?.riskLevelName, value: riskIndex?.riskIndexScore },
              { value: 100 - riskIndex?.riskIndexScore, name: '其他' },
            ],
            options: {
              color: [colors[(riskIndex?.riskLevel || 1) - 1], "#eef1f0"],
              series: [
                {
                  name: '风险等级',
                  type: 'pie',
                  radius: ['67%', '80%'],
                  avoidLabelOverlap: false,
                  label: {
                    show: true,
                    position: 'center',
                    formatter: ['{e|风险等级}', `{d|${riskIndex?.riskLevelName}}`].join('\n'),
                    rich: {
                      a: {
                        color: '#7D869A',
                        lineHeight: 24,
                        fontSize: 18,
                        fontFamily: 'MicrosoftYaHeiUI',
                        height: 24,
                      },
                      b: {
                        color: colors[(riskIndex?.riskLevel || 1) - 1],
                        lineHeight: 74,
                        fontSize: 53,
                        fontWeight: 600,
                        fontFamily: 'DINAlternate, DINAlternate- Bold',
                        height: 74,
                      },
                      c: {
                        color: colors[(riskIndex?.riskLevel || 1) - 1],
                        lineHeight: 24,
                        fontSize: 20,
                        fontFamily: 'MicrosoftYaHeiUI',
                        height: 24,
                      },
                      d: {
                        color: colors[(riskIndex?.riskLevel || 1) - 1],
                        lineHeight: 74,
                        fontSize: 36,
                        fontWeight: 600,
                        fontFamily: 'PingFangSC, PingFangSC-Semibold',
                        height: 74,
                      },
                      e: {
                        color: '#7D869A',
                        lineHeight: 24,
                        fontSize: 18,
                        fontFamily: 'MicrosoftYaHeiUI',
                        height: 24,
                      },
                    },
                  },
                  emphasis: {
                    label: {
                      show: true,
                      fontSize: '40',
                      fontWeight: 'bold',
                    },
                  },
                  labelLine: {
                    show: false,
                  },
                },
              ],
            },
            content: (
              <div className={styles.textBox} style={{margin:20}}>
                <p>注：</p>
                <p
                  {...{
                    dangerouslySetInnerHTML: {
                      __html: riskIndex?.remarks
                        ? riskIndex?.remarks.join('\n')
                        : '',
                    },
                  }}
                />
              </div>
            ),
          }
        }
      },
      {
        type: 'CardTitleBar',
        props: {
          title: '03 心理状态分布',
          themeColor: '#00C865',
          titleStyle: {
            margin: 20,
            width: 'auto',
          },
          id: sidePageNavList[1].children[2].point,
        },
        sortProps: (data: areaReportDataProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : '',
          }
        }
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '区/县心理健康状态分布',
          icon: true,
          iconColor: '#00C865',
          iconType: 'half',
          wrapperStyle: {
            marginLeft: 0,
          },
        },
        sortProps: (data: areaReportDataProps) => {
          const { stateDistribution } = data;
          return {
            content: (
              <div className={styles.stateDistributionBox}>
                <div className={styles.stateTitle}>在本区/县学生中：</div>
                {stateDistribution?.map((item) => (
                  <li key={item.riskId}>{item.riskName}的学生人数<b>{item.number}</b>人，占比<b>{item.percentage}%</b>;</li>
                ))
                }
              </div>
            )
          }
        },
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '各校心理健康状态对比',
          icon: true,
          iconColor: '#00C865',
          iconType: 'half',
          wrapperStyle: {
            marginLeft: 0,
          },
        },
      },
      {
        type: 'ChartForBar',
        props: {
          id: 'ChartForBar-schoolHealthWarning',
        },
        sortProps: (data: areaReportDataProps) => {
          const { schoolHealthWarning } = data;
          const nameDataList: string[] = [];
          const scoreList: number[] = [];
          schoolHealthWarning?.schoolWarningBean?.forEach((item) => {
            nameDataList.unshift(item.schoolName);
            scoreList.unshift(item.score);
          });
          const maxValue = Math.max(...scoreList, schoolHealthWarning?.warningPoints);
          return {
            before: <div className={styles.textBox} style={{margin:'0 30px'}}><p>{schoolHealthWarning?.conclusion}</p></div>,
            content: <div className={styles.textBox} style={{margin:'0 30px'}}><p className={styles.center}>各校高中学生心理健康状态对比分析（注：分数越高，表示该群体心理健康风险越大）</p></div>,
            height: `${schoolHealthWarning?.schoolWarningBean?.length * 40 + 160}px`,
            options: {
              grid: {
                bottom: '20px',
                right: '10%',
                left: '20%',
              },
              yAxis: {
                type: "category",
                data: nameDataList,
                show: true,
                offset: 0,
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  color: "rgba(0,17,57,0.85)",
                  fontSize: 14,
                  interval: 0,
                  height: 30,
                  formatter: (params: any) => {
                    if (params.length >= 18) {
                      params = params.slice(0, 18) + "...";
                    }
                    let newParamsName = "";
                    const paramsNameNumber = params.length;
                    const provideNumber = 9;
                    const rowNumber = Math.ceil(paramsNameNumber / provideNumber);
                    if (paramsNameNumber > provideNumber) {
                      for (let p = 0; p < rowNumber; p++) {
                        let tempStr = "";
                        const start = p * provideNumber;
                        const end = start + provideNumber;
                        if (p === rowNumber - 1) {
                          tempStr = params.substring(
                            start,
                            paramsNameNumber
                          );
                        } else {
                          tempStr = params.substring(start, end) + "\n";
                        }
                        newParamsName += tempStr;
                      }
                    } else {
                      newParamsName = params;
                    }
                    return newParamsName;
                  },
                },
              },
              xAxis: {
                type: "value",
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  color: "rgba(71,83,112,0.4)",
                },
                max: Math.ceil(maxValue / 10) * 10,
              },
              series: [
                {
                  type: 'bar',
                  data: scoreList,
                  stack: 'total',
                  label: {
                    show: true,
                    color: '#fff',
                  },
                  barMaxWidth: 50,
                  barCategoryGap: "50%",
                  color: ["#00DFA6", "#4B80FF", "#FF9631", "#FF5858"],
                  emphasis: {
                    focus: 'series',
                  },
                  ...(schoolHealthWarning?.warningPoints
                    ? {
                      markLine: {
                        data: [
                          {
                            name: `参考分`,
                            xAxis: schoolHealthWarning?.warningPoints,
                          },
                        ],
                        symbol: ['none', 'none'],
                        lineStyle: {
                          color: '#59647e',
                          width: 2,
                          type: 'solid',
                        },
                        label: {
                          show: true,
                          color: '#59647e',
                          formatter: () => {
                            return `参考分（${schoolHealthWarning?.warningPoints}分）`;
                          },
                        },
                      },
                    }
                    : {}),
                }
              ],
            }
          }
        }
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '各学校心理健康状态分布',
          icon: true,
          iconColor: '#00C865',
          iconType: 'half',
          wrapperStyle: {
            marginLeft: 0,
          },
        },
        sortProps: (data: areaReportDataProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : '',
          }
        },
        sortList: (data: areaReportDataProps) => {
          const { schoolPercentage } = data;
          let categoryList: any[] = [];
          const schoolPercentageList = [...riskColorList].reverse().map((item) => ({
            ...item,
            data: schoolPercentage?.reverse().map((v) => {
              categoryList.push(v.schoolName)
              // @ts-ignore
              return v[`${item.percentageKey}`]
            }),
          }))
          categoryList = categoryList.filter((v,index) => categoryList.indexOf(v, 0) === index)
          return [
            sortBarYCategoryStack({
              // @ts-ignore
              series: schoolPercentageList,
              categoryList,
              before: <div style={{
                fontSize: 18,
                marginTop: 30,
                marginLeft: 10,
                color: "rgba(0,17,57,0.85)",
              }}>（单位:百分比%）</div>,
            })
          ]
        }
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '各学校风险人数统计',
          icon: true,
          iconColor: '#00C865',
          iconType: 'half',
          wrapperStyle: {
            marginLeft: 0,
          },
        },
        sortProps: (data: areaReportDataProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : '',
          }
        }
      },
      {
        type: 'SearchTable',
        props: {
          wrapperStyle: {
            margin: '30px 30px 60px',
          },
          hideSearch: true,
        },
        sortProps: (data: areaReportDataProps) => {
          const { schoolIndexAnalysis, queryParams } = data;
          // 下载
          const handleDonwload = () => {
            const { cityCode, areaCode, gradeId, reportDateStr } = queryParams;
            const urlParams = {
              cityCode,
              areaCode,
              graduationYear: gradeId,
              reportDateStr,
              sourceType: getSourceType(),
              token: getToken(),
            }
            window.open(`${location.origin}/api/psychology/mentalHealthReport/exportAreaSchoolsRiskStatistics?${objectToUrlParams(urlParams)}`, '_blank')
          }

          return {
            tableProps: {
              wrapperClass: styles.customTable,
              beforeRender: () => {
                return (
                  <div className={styles.downloadBox}>
                    <Button onClick={() => handleDonwload()}>
                      <img src={IconDownload} width="14" height="14" alt="Download" className={styles.iconDownload} /> &nbsp;Excel下载</Button>
                  </div>
                )
              },
              columns: [
                { title: '单位', dataIndex: 'schoolName', align: "center", width: 170 },
                { title: '预警人数(去重)', dataIndex: 'warningPeople', align: "center" },
                { title: '占比', dataIndex: 'warningPeoplePercentage', align: "center",
                  render: (value: number) => (value ? `${value.toFixed(2)}%`: "0.00%")
                },
                { title: '高风险人数', dataIndex: 'highestRiskPeople', align: "center" },
                { title: '占比', dataIndex: 'highestRiskPercentage', align: "center",
                  render: (value: number) => (value ? `${value.toFixed(2)}%` : "0.00%")
                },
                { title: '较高风险人数', dataIndex: 'higherRiskPeople', align: "center" },
                { title: '占比', dataIndex: 'higherRiskPercentage', align: "center",
                  render: (value: number) => (value ? `${value.toFixed(2)}%` : "0.00%")
                },
                { title: '轻微风险人数', dataIndex: 'minorRiskPeople', align: "center" },
                { title: '占比', dataIndex: 'minorRiskPercentage', align: "center",
                  render: (value: number) => (value ? `${value.toFixed(2)}%` : "0.00%")
                },
              ],
              dataSource: schoolIndexAnalysis || [],
              rowKey: 'schoolCode',
              hidePagination: true,
            },
          }
        }
      },
      {
        type: 'CardTitleBar',
        props: {
          title: '04 具体因子分析',
          themeColor: '#00C865',
          titleStyle: {
            margin: 20,
            width: 'auto',
          },
          id: sidePageNavList[1].children[3].point,
        },
        sortProps: (data: areaReportDataProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : '',
          }
        }
      },
      {
        sortList: (data: areaReportDataProps) => {
          const { factorAnalysis } = data;
          const sortRadarTableProps = factorAnalysis && sortRadarTable({ list: factorAnalysis?.latitudeList });
          sortRadarTableProps?.forEach((v) => {
            if (v.type === 'ChartForRadar') {
              v.props.before = (
                <div className={styles.textBox}>
                  <p>{factorAnalysis?.inConclusion}</p>
                </div>
              )
              v.props.wrapperStyle = {
                padding: '10px 20px'
              }
              v.props.contentStyle = {
                width: '100%',
                margin: '0'
              }
            }
            if (v.type === 'SearchTable') {
              v.props.wrapperClass = styles.customTable;
              v.props.wrapperStyle = {
                padding: '0 20px'
              }
            }
          })
          return sortRadarTableProps;
        },
      },
      {
        sortList: (data: areaReportDataProps) => {
          const { factorAnalysisDetails } = data;
          const { countyData = [] } = descriptions;
          const renderResult: any[] = [];
          factorAnalysisDetails?.forEach((v: factorAnalysisDetailsItem, index: number) => {
            const targetItem: any = countyData.find((values: { latitudeId: number }) => values.latitudeId === v.latitudeId);
            if (v.latitudeName?.includes('饮食与睡眠')) {
              v.inConclusion = ''
              v.schoolFactorComparison = []
              v.schoolPercentageBeans = []
            }
            renderResult.push({
              type: 'Card',
              props: {
                wrapperStyle: {
                  margin: '30px 30px 15px',
                  boxShadow: '0px 0px 20px 0px #e6ece9',
                },
              },
              list: [
                {
                  type: 'StatusBar',
                  props: {
                    titleStyle: {
                      color: '#475370'
                    },
                    title: v.latitudeName,
                    themeColor: '#c7d3f1',
                  },
                },
                {
                  props: {
                    content: (
                      <div className={styles.textBox}>
                        {targetItem.factorDescription && (
                          <>
                            <p>
                              <b>★ 因子说明</b>
                            </p>
                            <p>{targetItem.factorDescription}</p>
                          </>
                        )}
                        {targetItem.highScore && (
                          <>
                            <p>
                              <b>★ 高分表现</b>
                            </p>
                            <p>{targetItem.highScore}</p>
                          </>
                        )}
                        {targetItem.lowScore && (
                          <>
                            <p>
                              <b>★ 低分表现</b>
                            </p>
                            <p>{targetItem.lowScore}</p>
                          </>
                        )}
                        {v.inConclusion && (
                          <>
                            <p>
                              <b>★ 各校在该因子得分对比</b>
                            </p>
                            <p>{v.inConclusion}</p>
                          </>
                        )}
                      </div>
                    ),
                  },
                },
                !!v.schoolFactorComparison?.length && {
                  type: 'ChartForBar',
                  props: {
                    id: `ChartForBar-schoolFactorComparison-${index}`,
                    height: `${v.schoolFactorComparison?.length * 40 + 160}px`,
                    content: (
                      <div className={styles.textBox}><p className={styles.center}>注：分数越低，表示心理健康状况越好）</p></div>
                    ),
                    options: {
                      grid: {
                        bottom: '20px',
                        right: '10%',
                        left: '20%',
                      },
                      xAxis: {
                        type: "value",
                        axisLine: {
                          show: false,
                        },
                        axisTick: {
                          show: false,
                        },
                        axisLabel: {
                          color: "rgba(71,83,112,0.4)",
                        },
                        max: 5,
                      },
                      yAxis: {
                        type: "category",
                        data: v.schoolFactorComparison?.map((item) => item.schoolName).reverse() || [],
                        show: true,
                        offset: 0,
                        axisLine: {
                          show: false,
                        },
                        axisTick: {
                          show: false,
                        },
                        axisLabel: {
                          color: "rgba(0,17,57,0.85)",
                          fontSize: 14,
                          interval: 0,
                          height: 30,
                          formatter: (params: any) => {
                            if (params.length >= 18) {
                              params = params.slice(0, 18) + "...";
                            }
                            let newParamsName = "";
                            const paramsNameNumber = params.length;
                            const provideNumber = 9;
                            const rowNumber = Math.ceil(paramsNameNumber / provideNumber);
                            if (paramsNameNumber > provideNumber) {
                              for (let p = 0; p < rowNumber; p++) {
                                let tempStr = "";
                                const start = p * provideNumber;
                                const end = start + provideNumber;
                                if (p === rowNumber - 1) {
                                  tempStr = params.substring(
                                    start,
                                    paramsNameNumber
                                  );
                                } else {
                                  tempStr = params.substring(start, end) + "\n";
                                }
                                newParamsName += tempStr;
                              }
                            } else {
                              newParamsName = params;
                            }
                            return newParamsName;
                          },
                        },
                      },
                      series: [
                        {
                          type: 'bar',
                          data: v.schoolFactorComparison?.map((item) => item.score).reverse() || [],
                          label: {
                            show: true,
                            color: '#fff',
                            fontSize: 14,
                          },
                          barMaxWidth: 50,
                          barCategoryGap: "70%",
                          color: ["#00DFA6", "#4B80FF", "#FF9631", "#FF5858"],
                          ...(v?.referenceScore
                            ? {
                              markLine: {
                                data: [
                                  {
                                    name: `参考分`,
                                    xAxis: v?.referenceScore,
                                  },
                                ],
                                symbol: ['none', 'none'],
                                lineStyle: {
                                  color: '#59647e',
                                  width: 2,
                                  type: 'solid',
                                },
                                label: {
                                  show: true,
                                  color: '#59647e',
                                  formatter: () => {
                                    return `参考分（${v?.referenceScore}分）`;
                                  },
                                },
                              },
                            }
                            : {}),
                        }
                      ],
                    }
                  },
                },
                !!v.schoolPercentageBeans?.length && {
                  type: 'SearchTable',
                  props: {
                    hideSearch: true,
                    before: (
                      <div {...{ className: styles.textBox }}>
                        <p>
                          <b>★ 各校在该因子表现</b>
                        </p>
                      </div>
                    ),
                  },
                  sortProps: () => {
                    return {
                      tableProps: {
                        wrapperClass: styles.customTable,
                        columns: [
                          {
                            title: '学校',
                            dataIndex: 'schoolName',
                            align: 'center',
                          },
                          {
                            title: '高风险人数',
                            dataIndex: 'highestRiskPeople',
                            align: 'center',
                          },
                          {
                            title: '占比',
                            dataIndex: 'highestRiskPercentage',
                            align: 'center',
                            render: (value: number) => `${value}%`,
                          },
                          {
                            title: '较高风险人数',
                            dataIndex: 'higherRiskPeople',
                            align: 'center',
                          },
                          {
                            title: '占比',
                            dataIndex: 'higherRiskPercentage',
                            align: 'center',
                            render: (value: number) => `${value}%`,
                          },
                          {
                            title: '轻微风险人数',
                            dataIndex: 'minorRiskPeople',
                            align: 'center',
                          },
                          {
                            title: '占比',
                            dataIndex: 'minorRiskPercentage',
                            align: 'center',
                            render: (value: number) => `${value}%`,
                          },
                          {
                            title: '无风险人数',
                            dataIndex: 'healthPeople',
                            align: 'center',
                          },
                          {
                            title: '占比',
                            dataIndex: 'healthPercentage',
                            align: 'center',
                            render: (value: number) => `${value}%`,
                          },
                        ],
                        dataSource: v.schoolPercentageBeans,
                        pagination: false,
                        border: true,
                        rowKey: 'schoolCode',
                        hidePagination: true,
                      },
                    };
                  },
                },
              ].filter((item) => !!item),
            });
          }
          );
          return renderResult;
        },
      },
      {
        type: 'THeaderControl',
        props: {
          title: '预警汇总表',
          subTitle: 'Potential Risk Individuals (Summary Sheet)',
          wrapperStyle: { margin: '60px 0px 30px' },
          id: sidePageNavList[2].point
        },
        sortProps: (data: areaReportDataProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : '',
          }
        }
      },
      {
        type: 'SearchTable',
        props: {
          wrapperStyle: { margin: 30 },
        },
        sortProps: (data: areaReportProps) => {
          const { queryParams: { cityCode, areaCode, gradeId, reportDateStr } } = data;

          // 下载
          const handleDonwload = () => {
            const urlParams = {
              cityCode,
              areaCode,
              gradeId,
              graduationYear: gradeId,
              reportDateStr,
              sourceType: getSourceType(),
              token: getToken(),
            }
            window.open(`${location.origin}/api/psychology/mentalHealthReport/exportAreaWarningDetail?${objectToUrlParams(urlParams)}`, '_blank')
          }


          return {
            searchForm: {
              layout: 'inline',
              columns: [{
                type: 'input',
                key: 'schoolName',
                props: {
                  style: {
                    width: 230
                  },
                  placeholder: '请输入学校名称搜索'
                },
              }],
              showReset: false,
              wrapperClass: styles.searchForm,
              optionBoxStyle: styles.buttonStyle
            },
            tableProps: {
              wrapperClass: styles.earlyWarningTable,
              beforeRender: () => {
                return (
                  <div className={styles.downloadBox}>
                    <Button onClick={() => handleDonwload()}>
                      <img src={IconDownload} width="14" height="14" alt="Download" className={styles.iconDownload} /> &nbsp;下载全区预警详情表表格</Button>
                  </div>
                )
              },
              params: {
                schoolName: '',
                cityCode,
                areaCode,
                graduationYear: gradeId,
                reportDateStr,
                pageIndex: 1,
                pageSize: 20
              },
              request: API.getPageAreaWarningDetail,
              pagination: {
                showSizeChanger: false,
                showTotal: (total: number, range: number[]) =>
                  `共 ${total}条,每页${range[1]}条`,
              },
              sortParams: ({
                current: pageIndex,
                ...rest
              }: {
                [key: string]: any;
              }): Partial<IGetPageAreaWarningDetail['params']> => ({
                ...rest,
                pageIndex
              }),
              sortData: ({
                data: { data: dataSource, totalRecords: total },
              }: IGetPageAreaWarningDetail['data']) => ({
                dataSource,
                total,
              }),
              rowKey: 'classId',
              columns: [
                { title: '学校名称', dataIndex: 'schoolName', align: 'center' },
                { title: '班级', dataIndex: 'className', align: 'center' },
                { title: '参与人数', dataIndex: 'totalCount', align: 'center' },
                { title: '高风险人数', dataIndex: 'highRiskCount', align: 'center' },
                {
                  title: '占比', dataIndex: 'highRiskPercentage', align: 'center',
                  render: (value: number) => (value ? `${value.toFixed(2)}%`: "0.00%")
                },
                { title: '较高风险人数', dataIndex: 'higherRiskCount', align: 'center' },
                {
                  title: '占比', dataIndex: 'higherRiskPercentage', align: 'center',
                  render: (value: number) => (value ? `${value.toFixed(2)}%` : "0.00%")
                },
                { title: '轻微风险人数', dataIndex: 'minorRiskCount', align: 'center' },
                {
                  title: '占比', dataIndex: 'minorRiskPercentage', align: 'center',
                  render: (value: number) => (value ? `${value.toFixed(2)}%` : "0.00%")
                },
                { title: '无风险人数', dataIndex: 'noRiskCount', align: 'center' },
                {
                  title: '占比', dataIndex: 'noRiskPercentage', align: 'center',
                  render: (value: number) => (value ? `${value.toFixed(2)}%` : "0.00%")
                },
              ],
              children: (
                <div className={styles.textBox} style={{ paddingBottom: '20px' }}>
                  <p>
                    <b>★ 风险程度说明</b>
                  </p>
                  <p>
                    高风险：表示该生的因子得分高于绝大部分高中生，在对应因子上有极大的可能性存在异常风险，建议老师优先关注并及时沟通干预。
                  </p>
                  <p>
                    较高风险：表示该生的因子得分高于大部分高中生，在对应因子上有较大可能性存在异常风险，建议老师保持密切关注。
                  </p>
                  <p>
                    轻微风险：表示该生的因子得分高于一般高中生，在对应因子上有一定可能性存在异常风险，建议老师保持密切关注。
                  </p>
                  <p>
                    无风险：表示该生的因子得分与大部分高中生一致，不存在明显的异常风险。
                  </p>
                </div>
              ),
            }
          }
        }
      },
      {
        type: 'PageNavigation',
        props: {
          list: sidePageNavList,
          theme: 'green',
          timeout: 180000
        },
        sortProps: (data: areaReportProps) => {
          const { queryParams: { printMode, detailReportMode }} = data;
          return {
            printMode,
            detailReportMode
          }
        }
      }
    ]
  }
];
export default tree;
