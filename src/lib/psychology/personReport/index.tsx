import ReadGuide from '@/components/ReadGuide';
import CustomTable from '@/components/CustomTable';
import type { ITreeList } from '@/complexComponents/index.d';
import { latitudeItem, personReportDataProps } from '@/pages/psychology/personReport/types';
import styles from '@/pages/psychology/personReport/index.module.less';

const sidePageNavList: any[] = [
  { label: "1、报告导读", point: "reportReading" },
  {
    label: "2、结果分析", point: "resultAnalyse",
    children: [
      { label: "2.1 心理风险指数", point: "riskIndex" },
      { label: "2.2 心理因子剖析", point: "stateDistribution" },
      { label: "2.3 具体因子分析", point: "factorAnalysis" }
    ]
  },
  { label: "3、附加说明", point: "additionalInformation" }
]

const pStyle = {
  lineHeight: '32px',
  fontSize: 18,
  marginTop: 20,
};

// 入学测-个人报告
const tree: ITreeList = [
  {
    type: 'page',
    list: [
      {
        isRealNode: true,
        props: {
          style: {
            width: '1000px',
            margin: '0 auto',
          },
        },
        list: [
          {
            type: 'THeaderControl',
            props: {
              title: '报告导读',
              subTitle: 'Introduction',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[0].point
            },
            sortProps: (data: personReportDataProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperStyle: printMode ? { margin: '0px 0px 0px' } : { margin: '60px 0px 30px' },
              }
            }
          },
          {
            type: 'Card',
            props: {
              title: '尊敬的老师',
              wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
              content: <ReadGuide />,
            },
          },
          {
            type: 'THeaderControl',
            props: {
              title: '结果分析',
              subTitle: 'Analysis',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[1].point
            },
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                padding: '30px',
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '01 心理风险指数',
                  themeColor: '#00C865',
                  titleStyle: {
                    marginBottom: 20,
                  },
                  id: sidePageNavList[1].children[0].point
                },
              },
              {
                type: 'ChartForPie',
                props: {
                  id: 'ChartForPie-userReport',
                  wrapperStyle: pStyle,
                  contentStyle: {
                    width: '100%',
                    margin: '0 auto',
                  },
                  height: '350px',
                },
                sortProps: (data: personReportDataProps) => {
                  const {
                    riskIndex: {
                      inConclusion,
                      remarks,
                      riskLevel,
                      riskIndexScore,
                      riskLevelName,
                    },
                  } = data;
                  const colors = ["#00DFA6", "#4B80FF", "#FF9631", "#FF5858"];
                  return {
                    before: inConclusion || '',
                    data: [
                      { value: riskIndexScore, name: riskLevelName },
                      { value: (100 - riskIndexScore), name: "其他" },
                    ],
                    color: [colors[(riskLevel || 1) - 1], "#eef1f0"],
                    options: {
                      series: [
                        {
                          name: "风险等级",
                          type: "pie",
                          radius: ["67%", "80%"],
                          avoidLabelOverlap: false,
                          label: {
                            show: true,
                            position: "center",
                            formatter: [
                                "{a|风险等级}",
                                `{b|${riskIndexScore}}`,
                                `{c|${riskLevelName}}`,
                              ].join("\n"),
                            rich: {
                              a: {
                                color: "#7D869A",
                                lineHeight: 24,
                                fontSize: 18,
                                fontFamily: "MicrosoftYaHeiUI",
                                height: 24,
                              },
                              b: {
                                color: colors[(riskLevel || 1) - 1],
                                lineHeight: 74,
                                fontSize: 53,
                                fontWeight: 600,
                                fontFamily: "DINAlternate, DINAlternate- Bold",
                                height: 74,
                              },
                              c: {
                                color: colors[(riskLevel || 1) - 1],
                                lineHeight: 24,
                                fontSize: 20,
                                fontFamily: "MicrosoftYaHeiUI",
                                height: 24,
                              },
                              d: {
                                color: colors[(riskLevel || 1) - 1],
                                lineHeight: 74,
                                fontSize: 36,
                                fontWeight: 600,
                                fontFamily: "PingFangSC, PingFangSC-Semibold",
                                height: 74
                                ,
                              },
                              e: {
                                color: "#7D869A",
                                lineHeight: 24,
                                fontSize: 18,
                                fontFamily: "MicrosoftYaHeiUI",
                                height: 24,
                              },
                            },
                          },
                          emphasis: {
                            label: {
                              show: true,
                              fontSize: "40",
                              fontWeight: "bold",
                            },
                          },
                          labelLine: {
                            show: false,
                          },
                        },
                      ],
                    },
                    content: (
                      <div>
                        <p>注：</p>
                        {remarks &&
                          remarks.length > 0 &&
                          remarks.map((html: string, index: number) => {
                            return (
                              <p
                                key={index}
                                dangerouslySetInnerHTML={{ __html: html }}
                              />
                            );
                          })}
                      </div>
                    ),
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '02 心理因子剖析',
                  themeColor: '#00C865',
                  titleStyle: {
                    marginBottom: 20,
                  },
                  id: sidePageNavList[1].children[1].point
                },
                sortProps: (data: personReportDataProps) => {
                  const { queryParams: { printMode } } = data;
                  return {
                    wrapperClass: printMode && 'typePrint',
                  }
                }
              },
              {
                type: 'ChartForRadar',
                props: {
                  id: `ChartForRadar-userReprot`,
                  wrapperStyle: pStyle,
                  contentStyle: { width: '100%', margin: '20px auto 40px' },
                  height: '400px',
                },
                sortProps: (data: personReportDataProps) => {
                  const { factorAnalysis: { inConclusion, latitudeList = [] }} = data;
                  const columns: any = [
                    {
                      title: '',
                      dataIndex: 'name',
                      key: 'name',
                      width: 60,
                      align: 'center',
                    },
                  ];
                  const keyName: string[] = [
                    'depressionScore',
                    'anxietyScore',
                    'bigotryScore',
                    'forcedScore',
                    'interpersonalSensitivityScore',
                    'hostileScore',
                    'fearScore',
                    'physicalSymptomsScore',
                    'psychoticScore',
                    'dietSleepScore',
                  ];
                  const propsName: { [propName: string]: any } = {};
                  keyName.forEach((v) => {
                    propsName[`${v}`] = 0;
                  });
                  const dataList: any = [
                    { name: '得分', id: 0, ...propsName },
                    { name: '参考分', id: 1, ...propsName },
                    { name: '预警分', id: 2, ...propsName },
                  ];
                  const dataName: any[] = [];
                  const dataVaule: number[] = [];
                  const dataMax: number[] = [];
                  const dataWarnnig: number[] = [];
                  latitudeList.forEach((v: latitudeItem, key: number) => {
                      const name = `${keyName[key]}`;
                      columns.push({
                        title: (<div><div>{v.latitudeName.slice(0, 2)}</div><div>{v.latitudeName.slice(2)}</div></div>),
                        dataIndex: name,
                        align: 'center',
                        width: 36,
                      });
                      dataName.unshift({
                        name: v.latitudeName,
                        max: 5
                      });
                      dataVaule.unshift(v.score);
                      dataMax.unshift(v.referenceScore);
                      dataWarnnig.unshift(v.warnningScore);
                      dataList[0][`${keyName[key]}`] = v.score;
                      dataList[1][`${keyName[key]}`] = v.referenceScore;
                      dataList[2][`${keyName[key]}`] = v.warnningScore;
                    }
                  );
                  return {
                    before: <p>{inConclusion}</p>,
                    legend: {
                      data: ['得分', '参考分', '预警分'],
                      fontSize: 14,
                      verticalAlign: 'bottom',
                      bottom: 0
                    },
                    options: {
                      radar: [
                        {},
                        {
                          indicator: dataName,
                          center: ["50%", "46%"],
                          radius: 120,
                          startAngle: 125,
                          axisName: {
                            color: "#001139",
                            fontSize: 18,
                          },
                          splitArea: {
                            areaStyle: {
                              color: ["#fff", "#F2F6FF"].reverse(),
                            },
                          },
                          axisLine: {
                            lineStyle: {
                              color: "#E0ECFF",
                            },
                          },
                          splitLine: {
                            show: true,
                            lineStyle: {
                              color: "#E0ECFF",
                            },
                          },
                        },
                      ],
                      series: [
                        {
                          name: "成绩单",
                          type: "radar",
                          radarIndex: 1,
                          symbol: "none",
                          data: [
                            {
                              type: "radar",
                              value: dataVaule,
                              name: '得分',
                              itemStyle: {
                                color: '#00C865',
                                fontSize: 18,
                              },
                              areaStyle: {
                                color: '#00C865',
                              },
                            },
                            {
                              value: dataMax,
                              type: "radar",
                              name: '参考分',
                              itemStyle: {
                                color: '#FF9631',
                                borderColor: '#FF9631',
                                borderType: "solid",
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                            {
                              value: dataWarnnig,
                              type: "radar",
                              name: '预警分',
                              itemStyle: {
                                color: '#FF3C19',
                                borderColor: '#FF3C19',
                                borderType: "solid",
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            }
                          ]
                        }
                      ]
                    },
                    content: (
                      <div>
                        <CustomTable
                          wrapperClass={styles.customTable}
                          propsData={{
                            columns,
                            dataSource: dataList,
                            pagination: false,
                            border: true,
                            rowKey: 'id',
                          }}
                        />
                        <p>注：</p>
                        <p>
                          参考分为全国常模的平均分，得分高于某个因子所对应的参考分，表明学生在该因子上的表现要差于全国平均水平，得分低于某个因子所对应的参考分，表明学生在该因子上的表现要好于全国平均水平。
                        </p>
                        <p>
                          预警分值为学生心理健康与否的分界线，若在某因子上得分超过预警分值，则表明学生在该因子上存在一定程度的心理问题，需要引起重视，得分低于预警分值则表明学生在该因子上不存在问题。
                        </p>
                      </div>
                    ),
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '03 具体因子分析',
                  themeColor: '#00C865',
                  id: sidePageNavList[1].children[2].point,
                },
                sortProps: (data) => {
                  const { queryParams: { printMode, detailReportMode } } = data;
                  return {
                    subTitle: detailReportMode ? '' : '(不存在风险的因子不在此展示，如需了解无风险因子的具体情况请查看详细报告)',
                    wrapperClass: printMode && 'typePrint',
                  }
                },
                sortList: (data: personReportDataProps) => {
                  const { factorAnalysisDetails = [], queryParams: { printMode, detailReportMode } } = data;
                  const renderResult: any[] = [];
                  const titleStyle = {
                    fontWeight: 700,
                    fontSize: 18,
                    color: '#475370',
                    margin: '20px 0',
                  };
                  const pStyle = {
                    lineHeight: '22px',
                    fontSize: 18,
                  };
                  const rightTitleStyle = {
                    fontSize: 20,
                    fontWeight: 700,
                    color: '#475370',
                  };
                  const themeColorList = ["#00c865", "#E1D815", "#FF9631", "#FF5858"];
                  if (factorAnalysisDetails.length > 0) {
                    const factorAnalysisDetailsList = detailReportMode ? factorAnalysisDetails : factorAnalysisDetails.filter(v => v.riskLevel !== 1)
                    factorAnalysisDetailsList.forEach((v: any, index: number) => {
                      renderResult.push({
                        type: 'Card',
                        props: {
                          wrapperStyle: {
                            margin: '30px 15px 15px',
                            boxShadow: '0px 0px 20px 0px #e6ece9',
                          },
                        },
                        list: [
                          {
                            type: 'StatusBar',
                            props: {
                              titleStyle: {
                                color: v.latitudeName.includes("睡眠") ? '#475370' : '#fff',
                              },
                              title: v.latitudeName,
                              themeColor: v.latitudeName.includes("睡眠") ? '#C7D3F1' : themeColorList[(v.riskLevel || 1) - 1],
                              wrapperClass: printMode && (index % 2 === 0 && index > 1) ? 'typePrint' : '',
                              right: (
                                <div style={{ ...rightTitleStyle }}>
                                  <span
                                    style={{
                                      color: v.latitudeName.includes("睡眠") ? '#475370' : themeColorList[(v.riskLevel || 1) - 1],
                                    }}
                                  >
                                    {v.score}
                                  </span>
                                  / {v.totalScore}
                                </div>
                              ),
                              content: (
                                <div>
                                  <p style={{ ...titleStyle }}>★ 因子说明</p>
                                  <p style={{ ...pStyle }}>
                                    {v.factorDescription}
                                  </p>
                                  <p style={{ ...titleStyle }}>★ 学生情况</p>
                                  <p style={{ ...pStyle }}>
                                    {v.studentSituation}
                                  </p>
                                  <p style={{ ...titleStyle }}>★ 辅导建议</p>
                                  <p style={{ ...pStyle }}>
                                    {v.counselingAdvice}
                                  </p>
                                </div>
                              ),
                            },
                          },
                        ],
                      });
                    });
                  }
                  return renderResult;
                },
              },
            ],
          },
          {
            type: 'THeaderControl',
            props: {
              title: '附加说明',
              subTitle: 'Additional Information',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[2].point,
              wrapperClass: 'typePrint'
            },
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                marginBottom: 50,
                boxShadow: '0px 0px 20px 0px #e6ece9',
                padding: '30px 30px 0px',
              },
            },
            sortProps: () => {
              return {
                content: (
                  <div className={styles.reportDescription}>
                    <h3>
                      1、 任何心理测评都存在一定误差
                    </h3>
                    <p>
                      学生在测评时的情绪状态、对测评的态度等主观因素，以及测评过程中的环境干扰等客观因素都会影响测评结果。因此在阅读本测评报告时，请参照学生在日常生活的实际表现来理解。
                    </p>
                    <h3>
                      2、请勿将测评结果当作永久“标签”
                    </h3>
                    <p>
                      请不要把本测评结果看成是对学生的“标签”或最终“宣判”，而应视为学生的阶段性状态。对于处在青春期的高中学生来说，其大多数心理和性格特征都是可变的，因此本测评结果仅为了解学生近期状态提供参考。
                    </p>
                  </div>
                ),
              };
            },
          },
          {
            type: 'PageNavigation',
            props: {
              list: sidePageNavList,
              theme: 'green',
              timeout: 180000
            },
            sortProps: (data) => {
              const { queryParams: { printMode, detailReportMode,  }} = data;
              return {
                printMode,
                detailReportMode,
                sendParams: {
                  analysis_dimension: '个人',
                  bxl_evaluation_name: '高中生心理健康诊断测验',
                  bxl_evaluation_type: '心理综合筛查',
                }
              }
            }
          }
        ],
      },
    ],
  },
];

export default tree;
