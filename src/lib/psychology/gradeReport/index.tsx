import moment from 'moment';
import Image from 'next/image'
import ReadGuide from '@/components/ReadGuide';
import CustomTable from '@/components/CustomTable';
import { descriptions } from '@/constants/psychology';
import IconDownload from '@/assets/psychology/iconDownload.png'
import styles from '@/pages/psychology/gradeReport/index.module.less';
import {
  classFactorAnalysisDetailsItem,
  factorAnalysisDetailsItem,
  gradeReportDataProps,
  latitudeItem,
} from '@/pages/psychology/gradeReport/types';
import API, { IGetClassWarningStudentList } from '@/service/psychology/gradeReport'
import { objectToUrlParams } from '~/utils/tools';
import { Button, Popover } from 'antd';

const sidePageNavList: any[] = [
  { label: "1、报告导读", point: "reportReading" },
  {
    label: "2、结果分析", point: "resultAnalyse",
    children: [
      { label: "2.1 主要发现", point: "mainFindings" },
      { label: "2.2 心理风险指数", point: "riskIndex" },
      { label: "2.3 心理状态分布", point: "stateDistribution" },
      { label: "2.4 具体因子分析", point: "factorAnalysis" }
    ]
  },
  { label: "3、预警名单", point: "warningList" }
]

const riskGradeList: { name: string; color: string; isLine?: boolean }[] = [
  { name: '高风险', color: '#ff5858' },
  { name: '较高风险', color: '#ff9630' },
  { name: '轻微风险', color: '#4b80ff' },
  { name: '无风险', color: '#00DFA6' },
];

export const sortBarWithMarkLineOptions = (
  classComparisonAnalysis: {
    className: string;
    score: number;
    riskLevel: number;
  }[],
  warningReferenceScore: number
) => {
  const riskColor = ["#00DFA6", "#4B80FF", "#FF9631", "#FF5858"];
  return {
    wrapperStyle: {
      position: 'relative',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    contentStyle: {
      flex: '0 0 704px',
    },
    height: (classComparisonAnalysis.length * 30) + 80,
    content: (
      <div {...{ style: { position: 'absolute', right: 40, bottom:0 } }}>
        {[
          ...riskGradeList,
          { name: '参考线', color: '#59647e', isLine: true },
        ].map((item, i) => (
          <p
            key={i}
            {...{
              style: {
                display: 'flex',
                alignItems: 'center',
                marginBottom: '7px',
              },
            }}
          >
            <span
              {...{
                style: {
                  background: item.color,
                  width: '12px',
                  height: item.isLine ? '2px' : '12px',
                  marginRight: '10px',
                },
              }}
            ></span>
            {item.name}
          </p>
        ))}
      </div>
    ),
    options: {
      xAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: { show: false },
        splitLine: { show: false },
        max: (value: { max: number; min: number }) => {
          return Math.max(value.max, warningReferenceScore) + 1;
        },
      },
      yAxis: {
        type: 'category',
        data: classComparisonAnalysis.map((item) => item.className),
        offset: 5,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: 'rgba(0,17,57,0.65)',
        },
      },
      grid: {
        top: '40px',
        left: '3%',
        // right: 0,
        bottom: '10px',
        containLabel: true,
      },
      color: riskColor,
      barWidth: 15,
      itemStyle: {
        borderRadius: [8, 8, 8, 8],
      },
      series: [
        {
          name: "各班级情况对比分析",
          type: 'bar',
          stack: 'total',
          data: classComparisonAnalysis.map((item) => ({
            value: item.score,
            itemStyle: {
              color: riskColor[item.riskLevel - 1],
            },
          })),
          itemStyle: {
            borderRadius: [8, 8, 8, 8],
          },
          label: {
            show: true,
            position: 'right',
            color: 'rgba(0,17,57,0.65)',
          },
          emphasis: {
            focus: 'series',
          },
          markLine: {
            data: [
              {
                name: `参考分`,
                xAxis: warningReferenceScore,
              },
            ],
            symbol: ['none', 'none'],
            lineStyle: {
              color: '#59647e',
              width: 2,
              type: 'dashed',
            },
            label: {
              show: true,
              color: '#59647e',
              formatter: () => {
                return `参考分（${warningReferenceScore}分）`;
              },
            },
          },
        },
      ],
    },
  };
};

// 健康测-年级报告
const tree = [
  {
    type: 'page',
    list: [
      {
        isRealNode: true,
        props: {
          style: {
            width: '1000px',
            margin: '0 auto',
          },
        },
        list: [
          {
            type: 'THeaderControl',
            props: {
              title: '报告导读',
              subTitle: 'Introduction',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[0].point
            },
            sortProps: (data: gradeReportDataProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperStyle: printMode ? { margin: '0px 0px 0px' } : { margin: '60px 0px 30px' },
              }
            }
          },
          {
            type: 'Card',
            props: {
              title: '尊敬的老师',
              wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
            },
            sortProps: (data: gradeReportDataProps) => {
              const { startTime, endTime, evaluatedPeople } = data;
              return {
                content: (
                  <ReadGuide>
                    <p>
                      ★ &nbsp;<b>测评时间</b>
                    </p>
                    <p>
                      ● &nbsp;&nbsp;
                      {`${moment(startTime).format('LL')} ~ ${moment(
                        endTime
                      ).format('LL')}`}
                    </p>
                    <p>
                      ★ &nbsp;<b>参与报告人数</b>
                    </p>
                    <p>● &nbsp;&nbsp;{evaluatedPeople}</p>
                  </ReadGuide>
                ),
              };
            },
          },
          {
            type: 'THeaderControl',
            props: {
              title: '结果分析',
              subTitle: 'Analysis',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[1].point,
            },
            sortProps: (data: gradeReportDataProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperStyle: printMode ? { margin: '20px 0px 20px' } : { margin: '60px 0px 30px' },
              }
            }
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                padding: '30px',
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '40px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '01 主要发现',
                  themeColor: '#00C865',
                  titleStyle: {
                    marginBottom: 20,
                  },
                },
                sortProps: (data: gradeReportDataProps) => {
                  const { mainFindings } = data;
                  return {
                    id: sidePageNavList[1].children[0].point,
                    content: (
                      <div className={styles.textBox}>
                        { mainFindings && mainFindings?.map((html: string, key: number) => {
                            return (
                              <p
                                key={key}
                                dangerouslySetInnerHTML={{
                                  __html: `${key + 1}. ${html}`,
                                }}
                              />
                            );
                          })}
                      </div>
                    ),
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                padding: '30px',
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '02 心理风险指数',
                  themeColor: '#00C865',
                  titleStyle: {
                    marginBottom: 20,
                  },
                  id: sidePageNavList[1].children[1].point,
                },
              },
              {
                type: 'ChartForPie',
                props: {
                  id: `ChartForPie-1`,
                  wrapperClass: styles.textBox,
                  contentStyle: { width: '100%', margin: '0 auto' },
                  height: '300px',
                },
                sortProps: (data: gradeReportDataProps) => {
                  const {
                    inConclusion,
                    remarks,
                    riskLevel,
                    riskLevelName: name,
                    riskIndexScore: value,
                  } = data.riskIndex;

                  const colors = ['#00DFA6', '#4B80FF', '#FF9631', '#FF5858'];
                  return {
                    before: <p>{inConclusion}</p>,
                    data: [
                      { name, value },
                      // { value: 100 - value, name: '其他' },
                    ],
                    color: [colors[(riskLevel || 1) - 1], '#eef1f0'],
                    options: {
                      series: [
                        {
                          name: '风险等级',
                          type: 'pie',
                          radius: ['67%', '80%'],
                          avoidLabelOverlap: false,
                          label: {
                            show: true,
                            position: 'center',
                            formatter: ['{e|风险等级}', `{d|${name}}`].join('\n'),
                            rich: {
                              a: {
                                color: '#7D869A',
                                lineHeight: 24,
                                fontSize: 18,
                                fontFamily: 'MicrosoftYaHeiUI',
                                height: 24,
                              },
                              b: {
                                color: colors[(riskLevel || 1) - 1],
                                lineHeight: 74,
                                fontSize: 53,
                                fontWeight: 600,
                                fontFamily: 'DINAlternate, DINAlternate- Bold',
                                height: 74,
                              },
                              c: {
                                color: colors[(riskLevel || 1) - 1],
                                lineHeight: 24,
                                fontSize: 20,
                                fontFamily: 'MicrosoftYaHeiUI',
                                height: 24,
                              },
                              d: {
                                color: colors[(riskLevel || 1) - 1],
                                lineHeight: 74,
                                fontSize: 36,
                                fontWeight: 600,
                                fontFamily: 'PingFangSC, PingFangSC-Semibold',
                                height: 74,
                              },
                              e: {
                                color: '#7D869A',
                                lineHeight: 24,
                                fontSize: 18,
                                fontFamily: 'MicrosoftYaHeiUI',
                                height: 24,
                              },
                            },
                          },
                          emphasis: {
                            label: {
                              show: true,
                              fontSize: '40',
                              fontWeight: 'bold',
                            },
                          },
                          labelLine: {
                            show: false,
                          },
                        },
                      ],
                    },
                    content: (
                      <>
                        {remarks.map((html: string, key: number) => {
                          return (
                            <p
                              key={key}
                              dangerouslySetInnerHTML={{
                                __html: html,
                              }}
                            />
                          );
                        })}
                      </>
                    ),
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '03 心理情况分析',
                  themeColor: '#00C865',
                  titleStyle: {
                    marginBottom: 20,
                  },
                  id: sidePageNavList[1].children[2].point,
                },
                sortProps: (data: gradeReportDataProps) => {
                  const { queryParams: { printMode }} = data;
                  return {
                    wrapperClass: printMode ? 'typePrint' : ''
                  }
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '全年级心理健康状态分布',
                  icon: true,
                  iconColor: '#00C865',
                  iconType: 'half',
                  wrapperStyle: {
                    marginLeft: -20,
                  },
                },
                sortProps: (data: gradeReportDataProps) => {
                  const { stateDistribution } = data;
                  return {
                    content: (
                      <div className={styles.textBox}>
                        <div className={styles.riskDistribution}>
                          <p>
                            <b>在本年级高中学生：</b>
                          </p>
                          {stateDistribution?.map((item: any) => {
                            return (
                              <p key={item.riskId}>
                                ● &nbsp;{item.riskName}
                                的学生人数为
                                <b>{item.number}</b>
                                人,占比
                                <b>{item.percentage}%</b>;
                              </p>
                            );
                          })}
                        </div>
                      </div>
                    )
                  }
                }
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '各因子预警统计',
                  icon: true,
                  iconColor: '#00C865',
                  iconType: 'half',
                  wrapperStyle: {
                    margin: '40px 0 0 -20px',
                  },
                },
              },
              {
                type: 'SearchTable',
                props: {
                  hideSearch: true,
                  wrapperStyle: {
                    margin: '20px 10px',
                  },
                  wrapperClass: styles.customTable,
                },
                sortProps: (data: gradeReportDataProps) => {
                  const {
                    factorWarning: { totalPeople, latitudeList },
                  } = data;
                  const columns = [
                    {
                      title: `总人数=${totalPeople}`,
                      dataIndex: 'latitudeName',
                      align: 'center',
                    },
                    {
                      title: '预警人数',
                      dataIndex: 'warningPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'percentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '高风险人数',
                      dataIndex: 'highestRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'highestRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '较高风险人数',
                      dataIndex: 'higherRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'higherRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '轻微风险人数',
                      dataIndex: 'minorRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'minorRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                  ];
                  return {
                    tableProps: {
                      columns,
                      dataSource: latitudeList,
                      pagination: false,
                      border: true,
                      rowKey: 'latitudeId',
                      hidePagination: true,
                      info: <p className="mt20">注：饮食与睡眠因子无需预警</p>,
                    },
                  };
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '各班预警统计',
                  icon: true,
                  iconColor: '#00C865',
                  iconType: 'half',
                  wrapperStyle: {
                    margin: '40px 0 0 -20px',
                  },
                },
                sortProps: (data: gradeReportDataProps) => {
                  const { queryParams: { printMode }} = data;
                  return {
                    wrapperClass: printMode ? 'typePrint' : ''
                  }
                },
              },
              {
                type: 'SearchTable',
                props: {
                  hideSearch: true,
                  wrapperStyle: {
                    margin: '20px 10px',
                  },
                  wrapperClass: styles.customTable,
                },
                sortProps: (data: gradeReportDataProps) => {
                  const { classWarning } = data;
                  const columns = [
                    { title: '班级', dataIndex: 'className', align: 'center' },
                    {
                      title: '预警人数',
                      dataIndex: 'warningPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'percentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '高风险人数',
                      dataIndex: 'highestRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'highestRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '较高风险人数',
                      dataIndex: 'higherRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'higherRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '轻微风险人数',
                      dataIndex: 'minorRiskPeople',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'minorRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                  ];
                  return {
                    tableProps: {
                      columns,
                      dataSource: classWarning,
                      pagination: false,
                      border: true,
                      rowKey: 'classId',
                      hidePagination: true,
                      info: <p className="mt20">注：饮食与睡眠因子无需预警</p>,
                    },
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '04 具体因子分析',
                  themeColor: '#00C865',
                  id: sidePageNavList[1].children[3].point,
                },
                sortProps: (data: gradeReportDataProps) => {
                  const {
                    queryParams: { printMode, detailReportMode },
                  } = data;
                  return {
                    subTitle: detailReportMode ? '' : '(不存在风险的因子不在此展示，如需了解无风险因子的具体情况请查看详细报告)',
                    wrapperClass: printMode ? 'typePrint' : ''
                  }
                }
              },
              {
                type: 'ChartForRadar',
                props: {
                  id: `ChartForRadar-1}`,
                  wrapperStyle: { lineHeight: '32px', fontSize: 18, marginTop: 20, padding: 20 },
                  contentStyle: { width: '100%', margin: '20px auto 40px' },
                  height: '400px',
                },
                sortProps: (data: gradeReportDataProps) => {
                  const {
                    factorAnalysis: { inConclusion, latitudeList = [] },
                  } = data;
                  const columns: any = [
                    {
                      title: '',
                      dataIndex: 'name',
                      key: 'name',
                      width: 60,
                      align: 'center',
                    },
                  ];
                  const keyName: string[] = [
                    'depressionScore',
                    'anxietyScore',
                    'bigotryScore',
                    'forcedScore',
                    'interpersonalSensitivityScore',
                    'hostileScore',
                    'fearScore',
                    'physicalSymptomsScore',
                    'psychoticScore',
                    'dietSleepScore',
                  ];
                  const propsName: { [propName: string]: any } = {};
                  keyName.forEach((v) => {
                    propsName[`${v}`] = 0;
                  });
                  const dataList: any = [
                    { name: '得分', id: 0, ...propsName },
                    { name: '参考分', id: 1, ...propsName },
                    { name: '预警分', id: 2, ...propsName },
                  ];
                  const dataName: any[] = [];
                  const dataVaule: number[] = [];
                  const dataMax: number[] = [];
                  const dataWarnnig: number[] = [];
                  latitudeList.forEach((v: latitudeItem, key: number) => {
                    const name = `${keyName[key]}`;
                    columns.push({
                      title: (<div><div>{v.latitudeName.slice(0, 2)}</div><div>{v.latitudeName.slice(2)}</div></div>),
                      dataIndex: name,
                      key: name,
                      align: 'center',
                      width: 36,
                    });
                    dataName.unshift({
                      name: v.latitudeName,
                      max: 5,
                    });
                    dataVaule.unshift(v.score);
                    dataMax.unshift(v.referenceScore);
                    dataWarnnig.unshift(v.warnningScore);
                    dataList[0][`${keyName[key]}`] = v.score;
                    dataList[1][`${keyName[key]}`] = v.referenceScore;
                    dataList[2][`${keyName[key]}`] = v.warnningScore;
                  });
                  return {
                    before: (
                      <p dangerouslySetInnerHTML={{ __html: inConclusion }} />
                    ),
                    legend: {
                      data: ['得分', '参考分', '预警分'],
                      fontSize: 14,
                      verticalAlign: 'bottom',
                      top: "bottom",
                    },
                    options: {
                      radar: [
                        {},
                        {
                          indicator: dataName,
                          center: ['50%', '46%'],
                          radius: 120,
                          startAngle: 125,
                          axisName: {
                            color: '#001139',
                            fontSize: 18,
                          },
                          splitArea: {
                            areaStyle: {
                              color: ['#fff', '#F2F6FF'].reverse(),
                            },
                          },
                          axisLine: {
                            lineStyle: {
                              color: '#E0ECFF',
                            },
                          },
                          splitLine: {
                            show: true,
                            lineStyle: {
                              color: '#E0ECFF',
                            },
                          },
                        },
                      ],
                      series: [
                        {
                          name: '成绩单',
                          type: 'radar',
                          radarIndex: 1,
                          symbol: 'none',
                          data: [
                            {
                              type: 'radar',
                              value: dataVaule,
                              name: '得分',
                              itemStyle: {
                                color: '#00C865',
                                fontSize: 18,
                              },
                              areaStyle: {
                                color: '#00C865',
                              },
                            },
                            {
                              value: dataMax,
                              type: 'radar',
                              name: '参考分',
                              itemStyle: {
                                color: '#FF9631',
                                borderColor: '#FF9631',
                                borderType: 'solid',
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                            {
                              value: dataWarnnig,
                              type: 'radar',
                              name: '预警分',
                              itemStyle: {
                                color: '#FF3C19',
                                borderColor: '#FF3C19',
                                borderType: 'solid',
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                          ],
                        },
                      ],
                    },
                    content: (
                      <div>
                        <CustomTable
                          wrapperClass={styles.factorTable}
                          propsData={{
                            columns,
                            dataSource: dataList,
                            pagination: false,
                            border: true,
                            rowKey: 'id',
                          }}
                        />
                        <p>注：</p>
                        <p>
                          参考分为全国常模的平均分，得分高于某个因子所对应的参考分，表明本次参评学生群体在该因子上的表现要差于全国平均水平，得分低于某个因子所对应的参考分，表明本次参评学生群体在该因子上的表现要好于全国平均水平。
                        </p>
                        <p>
                          预警分值为学生心理健康与否的分界线，若在某因子上得分超过预警分值，则表明参评群体内大部分学生在该因子上存在不同程度的问题，需要引起高度关注。
                        </p>
                      </div>
                    ),
                  };
                },
              },
              {
                sortList: (data: gradeReportDataProps) => {
                  const {
                    queryParams: { detailReportMode, printMode },
                    factorAnalysis: { factorAnalysisDetails = [] },
                  } = data;
                  const factorAnalysisDetailsList = detailReportMode ? factorAnalysisDetails : factorAnalysisDetails.filter(v => v.riskLevel !== 1);
                  const { grade = [] } = descriptions;
                  const renderResult: any[] = [];
                  const themeColorList = ["#00c865", "#E1D815", "#FF9631", "#FF5858"];
                  if (factorAnalysisDetailsList.length > 0) {
                    factorAnalysisDetailsList.forEach(
                      (v: classFactorAnalysisDetailsItem, index: number) => {
                        const {
                          latitudeId,
                          riskStatusDistribution,
                          classComparisonAnalysis,
                          warningReferenceScore,
                        } = v;
                        const targetItem: any = grade.find(
                          (values: { latitudeId: number }) =>
                            values.latitudeId === latitudeId
                        );
                        renderResult.push({
                          type: 'Card',
                          props: {
                            wrapperStyle: {
                              margin: '30px 15px 15px',
                              boxShadow: '0px 0px 20px 0px #e6ece9',
                            },
                            wrapperClass: printMode ? 'typePrint': null
                          },
                          list: [
                            {
                              type: 'StatusBar',
                              props: {
                                titleStyle: {
                                  color: v.latitudeName.includes("睡眠") ? '#475370' : '#fff',
                                },
                                title: v.latitudeName,
                                themeColor: v.latitudeName.includes("睡眠") ? '#c7d3f1' : themeColorList[(v.riskLevel || 1) - 1],
                                right: (
                                  <div className={styles.rightTitle}>
                                    <span
                                      style={{
                                        color: v.latitudeName.includes("睡眠") ? '#475370' : themeColorList[(v.riskLevel || 1) - 1],
                                      }}
                                    >
                                      {v.score}
                                    </span>
                                    / {v.totalScore}
                                  </div>
                                ),
                              },
                            },
                            {
                              props: {
                                content: (
                                  <div className={styles.textBox}>
                                    {targetItem.factorDescription && (
                                      <>
                                        <p>
                                          <b>★ 因子说明</b>
                                        </p>
                                        <p>{targetItem.factorDescription}</p>
                                      </>
                                    )}
                                    {/* {targetItem.highScore && (
                                      <>
                                        <p>
                                          <b>★ 高分表现</b>
                                        </p>
                                        <p>{targetItem.highScore}</p>
                                      </>
                                    )} */}
                                    {/* {targetItem.lowScore && (
                                      <>
                                        <p>
                                          <b>★ 低分表现</b>
                                        </p>
                                        <p>{targetItem.lowScore}</p>
                                      </>
                                    )} */}
                                    {targetItem.counselingAdvice && (
                                      <>
                                        <p>
                                          <b>★ 辅导建议</b>
                                        </p>
                                        <p>{targetItem.counselingAdvice}</p>
                                      </>
                                    )}
                                    {factorAnalysisDetails.length - 1 !== index && (
                                      <>
                                        <p>
                                          <b>★ 风险状态分布</b>
                                        </p>
                                        <div className={styles.riskDistribution}>
                                          <p>
                                            <b>
                                              在该因子上，全年级学生的风险状态分布：
                                            </b>
                                          </p>
                                          {riskStatusDistribution &&
                                            riskStatusDistribution.length > 0 &&
                                            riskStatusDistribution.map(
                                              (item: any) => {
                                                return (
                                                  <p key={item.riskId}>
                                                    ● &nbsp;{item.riskName}
                                                    的学生人数为<b>{item.number}</b>
                                                    人,占比<b>{item.percentage}%</b>;
                                                  </p>
                                                );
                                              }
                                            )}
                                        </div>
                                      </>
                                    )}
                                  </div>
                                ),
                              },
                            },
                            !!classComparisonAnalysis?.length && {
                              type: 'ChartForBar',
                              props: {
                                id: `ChartForBar-grader-class-${index}`,
                                height: '140px',
                              },
                              sortProps: () => {
                                return {
                                  outBefore: (
                                    <div {...{ className: styles.textBox }}>
                                      <p>
                                        <b>★ 各班级情况对比分析：</b>
                                      </p>
                                    </div>
                                  ),
                                  // 打印为A4大小 29.7*28.3（默认分辨率为72ppi,高度842）
                                  wrapperClass: (printMode && classComparisonAnalysis.length * 30 > 600) ? 'typePrint' : null,
                                  ...sortBarWithMarkLineOptions(
                                    classComparisonAnalysis,
                                    warningReferenceScore,
                                  ),
                                };
                              },
                            },
                          ].filter((item) => !!item),
                        });
                      }
                    );
                  }
                  return renderResult;
                },
              },
            ],
          },
          {
            type: 'THeaderControl',
            props: {
              title: '预警名单',
              subTitle: 'Potential Risk Individuals',
              wrapperStyle: { margin: '60px 0px 30px' },
              id: sidePageNavList[2].point,
            },
            sortProps: (data: gradeReportDataProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperClass: printMode ? 'typePrint' : '',
              }
            }
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                marginBottom: 50,
                boxShadow: '0px 0px 20px 0px #e6ece9',
              },
            },
            list: [
              {
                type: 'SearchTable',
                props: {
                  wrapperStyle: { margin: '10px 0px 30px' },
                },
                sortProps: (data: gradeReportDataProps) => {
                  const colorList = ["#373A44", "#4B80FF", "#FF9631", "#FF5858"];
                  const columnsList: any[] = [
                    { title: '班级', dataIndex: 'className', align: 'center' },
                    { title: '姓名', dataIndex: 'userName', align: 'center' },
                    {
                      title: '焦虑倾向',
                      dataIndex: 'anxietyTendency',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.anxietyTendencyLevel - 1]
                              }`,
                          }}
                        >
                          {record.anxietyTendency}
                        </span>
                      )
                    },
                    {
                      title: '抑郁倾向',
                      dataIndex: 'depressionTendency',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.depressionTendencyLevel - 1]
                              }`,
                          }}
                        >
                          {record.depressionTendency}
                        </span>
                      ),
                    },
                    {
                      title: '偏执倾向',
                      dataIndex: 'paranoidTendency',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.paranoidTendencyLevel - 1]
                              }`,
                          }}
                        >
                          {record.paranoidTendency}
                        </span>
                      ),
                    },
                    {
                      title: '敌对倾向',
                      dataIndex: 'hostileTendency',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.hostileTendencyLevel - 1]
                              }`,
                          }}
                        >
                          {record.hostileTendency}
                        </span>
                      ),
                    },
                    {
                      title: '人际敏感',
                      dataIndex: 'interpersonalSensitivity',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.interpersonalSensitivityLevel - 1]
                              }`,
                          }}
                        >
                          {record.interpersonalSensitivity}
                        </span>
                      ),
                    },
                    {
                      title: '强迫倾向',
                      dataIndex: 'obsessive',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.obsessiveLevel - 1]
                              }`,
                          }}
                        >
                          {record.obsessive}
                        </span>
                      ),
                    },
                    {
                      title: '恐惧倾向',
                      dataIndex: 'fearTendency',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.fearTendencyLevel - 1]
                              }`,
                          }}
                        >
                          {record.fearTendency}
                        </span>
                      ),
                    },
                    {
                      title: '身体症状',
                      dataIndex: 'physicalSymptoms',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.physicalSymptomsLevel - 1]
                              }`,
                          }}
                        >
                          {record.physicalSymptoms}
                        </span>
                      ),
                    },
                    {
                      title: '精神病性',
                      dataIndex: 'psychotic',
                      align: 'center',
                      width: 60,
                      render: (index: number, record: any) => (
                        <span
                          key={index}
                          style={{
                            color: `${colorList[record.psychoticLevel - 1]
                              }`,
                          }}
                        >
                          {record.psychotic}
                        </span>
                      ),
                    },
                    { title: '总体风险', dataIndex: 'riskIndex', align: 'center', width: 60, },
                    {
                      title: '风险等级',
                      dataIndex: 'riskLevelName',
                      align: 'center',
                      width: 60,
                    },
                    {
                      title: '详情',
                      dataIndex: 'userRecordId',
                      align: 'center',
                      render: (value: string) => {
                        return (
                          <a
                            key={value}
                            target="_blank"
                            href={`${location.origin}/psychology-service/psychology/personReport?evaluationRecordId=${value}`}
                            rel="noreferrer"
                          >
                            个人报告
                          </a>
                        )
                      },
                    },
                  ];
                  const riskGradeList = [
                    { name: '高风险', color: '#ff5858' },
                    { name: '较高风险', color: '#ff9630' },
                    { name: '轻微风险', color: '#4b80ff' },
                    { name: '无风险', color: '#263456' },
                  ];
                  const riskOptions = [
                    { label: '按照姓名排序', value: '1' },
                    { label: '按总体风险高低排序', value: '2' }
                  ]

                  // 下载
                  const handleDonwload = (params: any) => {
                    const { type, evaluationTaskId, gradeId, sort, hide, reportDateStr, classGroupNum } = params;
                    const urlParams = {
                      evaluationTaskId,
                      type,
                      hide,
                      sort,
                      gradeId,
                      reportDateStr,
                      classGroupNum,
                      pageIndex: 1,
                      pageSize: 99999
                    }
                    window.open(`${location.origin}/api/psychology/mentalHealthReport/exportPageClassWarning?${objectToUrlParams(urlParams)}`, '_blank')
                  }
                  const { queryParams: { evaluationTaskId, reportDateStr, gradeId, graduationYear, classGroupNum } } = data;
                  return {
                    searchForm: {
                      layout: 'inline',
                      columns: [{
                        type: 'input',
                        key: 'userName',
                        formItemProps: {
                          label: '学生查询',
                        },
                        props: {
                          style: {
                            width: 230
                          },
                          placeholder: '输入学生姓名搜索'
                        },
                      },
                      {
                        type: 'select',
                        key: 'sort',
                        formItemProps: {
                          label: '选择排序',
                        },
                        props: {
                          style: {
                            width: 230
                          },
                          options: riskOptions,
                        },
                      },
                      ],
                      showReset: false,
                      wrapperClass: styles.searchForm,
                      optionBoxStyle: styles.buttonStyle
                    },
                    tableProps: {
                      wrapperClass: styles.customTable,
                      beforeRender: (paramsData: any) => {
                        const { params } = paramsData;
                        return (
                          <div className={styles.optionBox}>
                            <Popover content={<p>请注意保护学生隐私安全</p>}>
                              <Button onClick={() => handleDonwload({ ...params, hide: false })}><Image src={IconDownload} width="14" height="14" alt="Download" className={styles.iconDownload} /> &nbsp;下载全名版本名单</Button>
                            </Popover>
                            <Button onClick={() => handleDonwload({ ...params, hide: true })}><Image src={IconDownload} width="14" height="14" alt="Download" className={styles.iconDownload} /> &nbsp;下载隐私保护名单</Button>
                          </div>
                        )
                      },
                      params: {
                        userName: '',
                        sort: '2',
                        type: 2,
                        evaluationTaskId,
                        reportDateStr,
                        classGroupNum,
                        gradeId: gradeId || graduationYear,
                        pageIndex: 1
                      },
                      request: API.getClassWarningStudentList,
                      pagination: {
                        showSizeChanger: false,
                        showTotal: (total: number, range: number[]) =>
                          `共 ${total}条,每页${range[1]}条`,
                      },
                      sortParams: ({
                        current: pageIndex,
                        ...rest
                      }: {
                        [key: string]: any;
                      }): Partial<IGetClassWarningStudentList['params']> => ({
                        ...rest,
                        pageIndex
                      }),
                      sortData: ({
                        data: { data: dataSource, totalRecords: total },
                      }: IGetClassWarningStudentList['data']) => ({
                        dataSource,
                        total,
                      }),
                      rowKey: 'userRecordId',
                      columns: columnsList,
                      info: (
                        <div className={styles.explain}>
                          <p>
                            {riskGradeList.map((v, index) => (
                              <span key={index} className={styles.explainItem}>
                                <i
                                  className={styles.colorItem}
                                  style={{
                                    background: v.color,
                                  }}
                                />
                                {v.name} &nbsp;&nbsp;
                              </span>
                            ))}
                          </p>
                          <p>注：分数越高，表示学生在这方面的风险越高</p>
                        </div>
                      )
                    }
                  };
                },
              }
            ]
          },
          {
            type: 'PageNavigation',
            props: {
              list: sidePageNavList,
              theme: 'green',
              timeout: 180000
            },
            sortProps: (data: gradeReportDataProps) => {
              const { queryParams: { printMode, detailReportMode}} = data;
              return {
                printMode,
                detailReportMode,
                sendParams: {
                  analysis_dimension: '年级',
                  bxl_evaluation_name: '高中生心理健康诊断测验',
                  bxl_evaluation_type: '心理综合筛查',
                }
              }
            }
          }
        ],
      },
    ],
  },
];

export default tree;
