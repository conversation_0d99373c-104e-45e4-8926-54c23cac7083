import { Fragment } from 'react';
import { Button } from 'antd';
import ReadGuide from '~/components/ReadGuide';
import { descriptions } from '~/constants/psychology';
import {
  cityReportProps,
  factorAnalysisDetailsItem,
  areaPercentageItem,
} from '@/pages/psychology/cityReport/types';
import {
  sortRadarTable,
  sortBarYCategoryStack,
  riskColorList,
  sortFactorBarChart,
  sortRiskPieChart,
} from '~/utils/complex';
import IconDownload from '@/assets/psychology/iconDownload.png'
import styles from '@/pages/psychology/cityReport/index.module.less';
import { ITreeItem } from '~/complexComponents/index.d';
import { getSourceType, getToken, objectToUrlParams } from '~/utils/tools';

const sidePageNavList: any[] = [
  { label: "1、报告导读", point: "reportReading" },
  {
    label: "2、结果分析", point: "resultAnalyse",
    children: [
      { label: "2.1 主要发现", point: "mainFindings" },
      { label: "2.2 心理风险指数", point: "riskIndex" },
      { label: "2.3 心理状态分布", point: "stateDistribution" },
      { label: "2.4 具体因子分析", point: "factorAnalysis" }
    ]
  },
  { label: "3、预警汇总表", point: "earlyWarningSummaryTable" }
]

// 心理健康测-区县报告页面报告
const tree: ITreeItem[] = [
  {
    type: 'page',
    isRealNode: true,
    props: {
      style: {
        width: '1000px',
        margin: '0 auto',
        padding: 30,
      },
    },
    list: [
      {
        type: 'THeaderControl',
        props: {
          title: '报告导读',
          subTitle: 'Introduction',
          wrapperStyle: { margin: '60px 0px 10px' },
          id: sidePageNavList[0].point
        },
      },
      {
        type: 'Card',
        props: {
          title: '尊敬的老师',
          wrapperStyle: { margin: 10, boxShadow: 'none' },
        },
        sortProps: () => {
          return {
            content: <ReadGuide />,
          };
        },
      },
      {
        type: 'THeaderControl',
        props: {
          title: '结果分析',
          subTitle: 'Analysis',
          wrapperStyle: { margin: '30px 0px 30px' },
          id: sidePageNavList[1].point
        },
        sortProps: (data: cityReportProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : '',
          }
        }
      },
      {
        type: 'CardTitleBar',
        props: {
          title: '01 主要发现',
          themeColor: '#00C865',
          titleStyle: {
            width: 'auto',
            marginBottom: 40,
          },
          wrapperStyle: {
            margin: 20,
          },
        },
        sortProps: (data: cityReportProps) => {
          const { mainFindings } = data;
          return {
            id: sidePageNavList[1].children[0].point,
            content: (
              <div className={styles.textBox}>
                {mainFindings?.map((html: string, key: number) => (
                  <p
                    key={key}
                    dangerouslySetInnerHTML={{
                      __html: `${key + 1}. ${html}`,
                    }}
                  />
                ))}
              </div>
            ),
          };
        },
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '重点关注学校',
          themeColor: '#00C865',
          titleStyle: {
            margin: 20,
            width: 'auto',
          },
          icon: true,
          iconColor: '#00C865',
        },
        sortProps: (data: cityReportProps) => {
          const { focusOnSchools } = data;
          return {
            content: (
              <div className={styles.focusOnSchoolsBox}>
                {
                  focusOnSchools?.map((item, index) => (
                    <div key={index} className={styles.itemCard}>
                      <div className={styles.circleSerial}>{index + 1}</div>
                      <div className={styles.itemContent}>
                        <p className={styles.schoolName}>{item.schoolName}</p>
                        <p>高风险检出{item.highRiskPeople},占比{item.percentage}%</p>
                      </div>
                    </div>
                  ))
                }
                <p className={styles.explain}>{`${focusOnSchools && focusOnSchools.length > 0 ? '*重点关注学校为高风险检出人数最高的五所学校' : '暂无需要重点关注的学校哦~'}`}</p>
              </div>
            ),
          }
        }
      },
      {
        type: 'CardTitleBar',
        props: {
          title: '02 心理风险指数',
          themeColor: '#00C865',
          titleStyle: {
            margin: '20px 20px 0',
            width: 'auto',
          },
          id: sidePageNavList[1].children[1].point,
        },
        sortProps: (data: cityReportProps) => {
          const { riskIndex, queryParams: { printMode } } = data;
          return {
            content: <div className={styles.textBox}><p>{riskIndex?.conclusion}</p></div>,
            wrapperClass: printMode ? 'typePrint' : '',
          }
        },
      },
      {
        type: 'ChartForPie',
        props: {
          id: `cityReprot-ChartForRing`,
          wrapperStyle: { padding: '0px 20px' },
          contentStyle: { width: '100%', margin: '0 auto' },
          height: '320px',
        },
        sortProps: (data: cityReportProps) => {
          const { riskIndex } = data;
          const colors = ["#00DFA6", "#4B80FF", "#FF9631", "#FF5858"];
          return {
            data: [
              { name: riskIndex?.riskLevelName, value:riskIndex?.riskIndexScore },
              { value: 100 - riskIndex?.riskIndexScore, name: '其他' },
            ],
            options: {
              color: [colors[(riskIndex?.riskLevel || 1) - 1], "#eef1f0"],
              series: [
                {
                  name: '风险等级',
                  type: 'pie',
                  radius: ['67%', '80%'],
                  avoidLabelOverlap: false,
                  label: {
                    show: true,
                    position: 'center',
                    formatter: ['{e|风险等级}', `{d|${riskIndex?.riskLevelName}}`].join('\n'),
                    rich: {
                      a: {
                        color: '#7D869A',
                        lineHeight: 24,
                        fontSize: 18,
                        fontFamily: 'MicrosoftYaHeiUI',
                        height: 24,
                      },
                      b: {
                        color: colors[(riskIndex?.riskLevel || 1) - 1],
                        lineHeight: 74,
                        fontSize: 53,
                        fontWeight: 600,
                        fontFamily: 'DINAlternate, DINAlternate- Bold',
                        height: 74,
                      },
                      c: {
                        color: colors[(riskIndex?.riskLevel || 1) - 1],
                        lineHeight: 24,
                        fontSize: 20,
                        fontFamily: 'MicrosoftYaHeiUI',
                        height: 24,
                      },
                      d: {
                        color: colors[(riskIndex?.riskLevel || 1) - 1],
                        lineHeight: 74,
                        fontSize: 36,
                        fontWeight: 600,
                        fontFamily: 'PingFangSC, PingFangSC-Semibold',
                        height: 74,
                      },
                      e: {
                        color: '#7D869A',
                        lineHeight: 24,
                        fontSize: 18,
                        fontFamily: 'MicrosoftYaHeiUI',
                        height: 24,
                      },
                    },
                  },
                  emphasis: {
                    label: {
                      show: true,
                      fontSize: '40',
                      fontWeight: 'bold',
                    },
                  },
                  labelLine: {
                    show: false,
                  },
                },
              ],
            },
            content: (
              <div className={styles.textBox}>
                <p>注：</p>
                <p
                  {...{
                    dangerouslySetInnerHTML: {
                      __html: riskIndex?.remarks
                        ? riskIndex?.remarks.join('\n')
                        : '',
                    },
                  }}
                />
              </div>
            ),
          }
        }
      },
      {
        type: 'CardTitleBar',
        props: {
          title: '03 心理状态分布',
          themeColor: '#00C865',
          titleStyle: {
            margin: 20,
            width: 'auto',
          },
          id: sidePageNavList[1].children[2].point,
        },
        sortProps: (data: cityReportProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : '',
          }
        }
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '市级心理健康状态分布',
          icon: true,
          iconColor: '#00C865',
          iconType: 'half',
          wrapperStyle: {
            marginLeft: 0,
          },
        },
        sortProps: (data: cityReportProps) => {
          const { stateDistribution } = data;
          return {
            content: (
              <div className={styles.stateDistributionBox}>
                <div className={styles.stateTitle}>在本市级学生中：</div>
                {stateDistribution?.map((item) => (
                  <li key={item.riskId}>{item.riskName}的学生人数<b>{item.number}</b>人，占比<b>{item.percentage}%</b>;</li>
                ))
                }
              </div>
            )
          }
        },
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '区域心理健康状态对比',
          icon: true,
          iconColor: '#00C865',
          iconType: 'half',
          wrapperStyle: {
            marginLeft: 0,
          },
        },
        sortList: (data: cityReportProps) => {
          const { areaHealthWarning } = data;
          return [
            sortFactorBarChart({
              id: `areaHealthWarning`,
              list: areaHealthWarning?.areaWarning?.map((item) => ({
                value: item.score,
              })) || [],
              categoryList:
                areaHealthWarning?.areaWarning?.map((item) => item.areaName) ||
                [],
              markLineScore: areaHealthWarning?.areaWarningPoints,
              before: <div className={styles.textBox}><p>{areaHealthWarning?.conclusion}</p></div>,
              content: <div className={styles.textBox}><p className={styles.center}>各行政区高中学生心理健康状态对比分析（注：分数越高，表示该群体心理健康风险越大）</p></div>,
            }),
          ];
        },
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '区域心理健康状态分布',
          icon: true,
          iconColor: '#00C865',
          iconType: 'half',
          wrapperStyle: {
            marginLeft: 0,
          },
        },
        sortList: (data: cityReportProps) => {
          const { areaPercentage } = data;
          let categoryList: any[] = [];
          const seriesAreaPercentageList = [...riskColorList].reverse().map((item) => ({
            ...item,
            data: areaPercentage?.reverse().map((v) => {
              categoryList.push(v.areaName)
              // @ts-ignore
              return v[`${item.percentageKey}`]
            }),
          }))
          // 去重
          categoryList = categoryList.filter((v,index) => categoryList.indexOf(v, 0) === index)
          return [
            sortBarYCategoryStack({
              series: seriesAreaPercentageList,
              categoryList,
              before: <div style={{
                fontSize: 18,
                marginTop: 30,
                marginLeft: 10,
                color: "rgba(0,17,57,0.85)",
              }}>（单位:百分比%）</div>,
            }),
            ...areaPercentage.map((item) => {
              return {
                type: 'Card',
                props: {
                  wrapperStyle: {
                    display: 'inline-block',
                    boxShadow: '0px 0px 20px 0px rgb(32 39 54 / 15%)',
                    width: '47%',
                    margin: '0 1.5% 30px'
                  },
                },
                list: [
                  {
                    type: 'StatusBar',
                    props: {
                      titleStyle: {
                        color: '#fff',
                      },
                      title: item.areaName,
                      themeColor: '#00c865',
                    },
                  },
                  {
                    props: {
                      content: (
                        <div className={styles.riskDistribution}>
                          <li>高心理风险的学生人数为{item.highestRiskPeople}人,占比{item.highestRiskPercentage}%;</li>
                          <li>较高心理风险的学生人数为{item.higherRiskPeople}人,占比{item.higherRiskPercentage}%;</li>
                          <li>轻微心理风险的学生人数为{item.minorRiskPeople}人,占比{item.minorRiskPercentage}%;</li>
                          <li>无心理风险的学生人数为{item.healthPeople}人,占比{item.healthPercentage}%;</li>
                        </div>)
                    }
                  }
                ]
              }
            })
          ];
        },
      },
      {
        type: 'SmallIconTitle',
        props: {
          title: '各行政区各学校心理健康指数分析',
          icon: true,
          iconColor: '#00C865',
          iconType: 'half',
          wrapperStyle: {
            margin: '0 0 40px'
          },
        },
        sortProps: (data: cityReportProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : ''
          }
        },
        sortList: (data: cityReportProps) => {
          const { areaIndexAnalysis, queryParams: { printMode } } = data;
          return (
            areaIndexAnalysis?.map(({ areaName, schoolIndexAnalysis }, i) =>
              sortFactorBarChart({
                id: `areaIndexAnalysis-${i}`,
                list: schoolIndexAnalysis?.map((item) => ({
                  value: item.index,
                })),
                categoryList: schoolIndexAnalysis?.map(
                  (item) => item.schoolName
                ),
                wrapperClass: (printMode && i % 2 === 0 && i !== 0) ? 'typePrint' : '',
                before: (
                  <p {...{ style: { textAlign: 'center' } }}>{areaName}</p>
                ),
                content: (<p {...{ style: {marginBottom: 40, textAlign: 'center',fontSize: 16,color: 'rgba(0, 17, 57, 0.65)'} }}>（注：分数越低，表示心理健康状况越好）</p>)
              })
            ) || []
          );
        },
      },
      {
        type: 'CardTitleBar',
        props: {
          title: '04 具体因子分析',
          themeColor: '#00C865',
          titleStyle: {
            margin: 20,
            width: 'auto',
          },
          id: sidePageNavList[1].children[3].point,
        },
        sortProps: (data: cityReportProps ) => {
          const { factorAnalysis } = data;
          return {
            content: (<div className={styles.textBox}><p>{factorAnalysis?.inConclusion}</p></div>)
          }
        }
      },
      {
        sortList: (data: cityReportProps) => {
          const { factorAnalysis } = data;
          const sortRadarTableProps = factorAnalysis && sortRadarTable({ list: factorAnalysis?.latitudeList });
          sortRadarTableProps.forEach((v) => {
            if (v.type === 'SearchTable') {
              v.props.wrapperClass = styles.customTable;
              v.props.wrapperStyle = {
                margin: '0 20px'
              }
            }
          })
          return sortRadarTableProps;
        },
      },
      {
        // @ts-ignore
        sortList: (data: cityReportProps) => {
          const { factorAnalysisDetails } = data;
          const { cityData = [] } = descriptions;
          return factorAnalysisDetails?.map(
            (v: factorAnalysisDetailsItem, index: number) => {
              const {
                latitudeId,
                areaFactorComparison,
                areaStateDistribution,
                focusOnSchools,
                referenceScore,
              } = v;

              const targetItem: any = cityData.find(
                (values: { latitudeId: number }) =>
                  values.latitudeId === latitudeId
              );
              return {
                type: 'Card',
                props: {
                  wrapperStyle: {
                    margin: '30px 30px 15px',
                    boxShadow: '0px 0px 20px 0px #e6ece9',
                  },
                },
                list: [
                  {
                    type: 'StatusBar',
                    props: {
                      titleStyle: {
                        color:  factorAnalysisDetails.length - 1 === index
                        ? 'rgb(71, 83, 112)'
                        : '#fff',
                      },
                      title: v.latitudeName,
                      themeColor: `${factorAnalysisDetails.length - 1 === index
                        ? '#c7d3f1': '#00c865'}`,
                    },
                  },
                  {
                    props: {
                      content: (
                        <div className={styles.textBox}>
                          {[
                            {
                              name: '因子说明',
                              value: targetItem.factorDescription,
                            },
                            { name: '高分表现', value: targetItem.highScore },
                            { name: '低分表现', value: targetItem.lowScore },
                          ].map(({ name, value }, ii) => (
                            <Fragment key={ii}>
                              {value && (
                                <>
                                  <p>
                                    <b>★ {name}</b>
                                  </p>
                                  <p>{value}</p>
                                </>
                              )}
                            </Fragment>
                          ))}
                        </div>
                      ),
                    },
                  },
                  {
                    props: {
                      content: (
                        <div {...{ className: styles.textBox }}>
                          <p>
                            <b>★ 各行政区在该因子得分对比</b>
                          </p>
                        </div>
                      )
                    },
                  },
                  !!areaFactorComparison?.length &&
                  sortFactorBarChart({
                    id: `${index}`,
                    list: areaFactorComparison.map((item) => ({
                      value: item.score,
                    })),
                    categoryList: areaFactorComparison.map(
                      (item) => item.areaName
                    ),
                    markLineScore: referenceScore,
                    content: (<p {...{ style: {marginBottom: 40, textAlign: 'center',fontSize: 16,color: 'rgba(0, 17, 57, 0.65)'} }}>（注：分数越低，表示心理健康状况越好）</p>)
                  }),
                  ...[
                    {
                      value: areaStateDistribution,
                      name: '各行政区在该因子表现',
                      columns: [
                        {
                          title: '行政区',
                          dataIndex: 'areaName',
                          key: 'areaName',
                          align: 'center',
                        },
                        ...riskColorList.map((item) => ({
                          title: (
                            <>
                              {item.name}
                              <br />
                              (人数/占比)
                            </>
                          ),
                          dataIndex: item.peopleKey,
                          key: item.peopleKey,
                          align: 'center',
                          render: (_: number, record: areaPercentageItem) =>
                            // @ts-ignore
                            `${record[item.peopleKey]}(${
                            // @ts-ignore
                            record[item.percentageKey]
                            }%)`,
                        })),
                      ],
                      rowKey: 'areaCode',
                    }].filter(({ value }) => !!value?.length && latitudeId !== 10).map(({ value, name, columns, rowKey }) => ({
                      type: 'SearchTable',
                      props: {
                        hideSearch: true,
                        before: (
                          <div {...{ className: styles.textBox }}>
                            <p>
                              <b>★ {name}</b>
                            </p>
                          </div>
                        ),
                        tableProps: {
                          wrapperClass: styles.customTable,
                          columns,
                          dataSource: value,
                          pagination: false,
                          border: true,
                          rowKey,
                          hidePagination: true,
                        },
                      },
                  })),
                  ...[
                    {
                      value: focusOnSchools,
                      name: '重点关注的学校',
                      columns: [
                        {
                          title: '学校名称',
                          dataIndex: 'schoolName',
                          key: 'schoolName',
                          align: 'center',
                        },
                        {
                          title: '该因子高风险预警人数',
                          dataIndex: 'highRiskPeople',
                          key: 'highRiskPeople',
                          align: 'center',
                        },
                        {
                          title: '占比',
                          dataIndex: 'percentage',
                          key: 'percentage',
                          align: 'center',
                          render: (t: number) => `${t}%`,
                        },
                      ],
                      rowKey: 'schoolName',
                    },
                  ]
                    .filter(({ value }) => !!value?.length && latitudeId !== 10)
                    .map(({ value, name, columns, rowKey }) => ({
                      type: 'SearchTable',
                      props: {
                        hideSearch: true,
                        before: (
                          <div {...{ className: styles.textBox }}>
                            <p>
                              <b>★ {name}</b>
                            </p>
                          </div>
                        ),
                        tableProps: {
                          wrapperClass: styles.customTable,
                          columns,
                          dataSource: value,
                          pagination: false,
                          border: true,
                          rowKey,
                          hidePagination: true,
                          info: (
                            <p style={{marginTop: 15, textAlign: 'center', fontSize: 18,color:'rgba(0, 17, 57, 0.65)' }}>* 重点关注学校为高风险检出人数最高的十所学校</p>
                          )
                        },
                      },
                    })),
                ].filter((item) => !!item),
              };
            }
          );
        },
      },
      {
        type: 'THeaderControl',
        props: {
          title: '预警汇总表',
          subTitle: 'Potential Risk Individuals (Summary Sheet)',
          wrapperStyle: { margin: '60px 0px 30px' },
          id: sidePageNavList[2].point,
        },
      },
      {
        type: 'SearchTable',
        props: {
          wrapperStyle: { margin: 30 },
          hideSearch: true,
        },
        sortProps: (data: cityReportProps) => {
          const { queryParams, warningSummary = [] } = data;
          const dataList = warningSummary.map((v, index: number) => {
            return { ...v, area: index ? '行政区' : '全市' };
          });
          const columns = [
            {
              title: '范围',
              dataIndex: 'area',
              align: 'center',
              onCell: (_: any, index: number) => {
                if (index === 0) {
                  return { rowSpan: 1 };
                }
                if (index === 1) {
                  return { rowSpan: dataList.length - index };
                }
                return { rowSpan: 0 };
              },
            },
            { title: '单位', dataIndex: 'areaName', align: 'center' },
            { title: '参与人数', dataIndex: 'totlePeople', align: 'center' },
            ...riskColorList
              .map((item) => [
                {
                  title: (
                    <>
                      {item.name}
                      <br />
                      因子人数
                    </>
                  ),
                  dataIndex: item.peopleKey,
                  align: 'center',
                },
                {
                  title: '占比',
                  dataIndex: item.percentageKey,
                  align: 'center',
                  render: (value: number) => `${value}%`,
                },
              ])
              .flat(),
          ];

          // 下载
          const handleDonwload = () => {
            const { type, cityCode, areaCode, gradeId, reportDateStr } = queryParams;
            const urlParams = {
              cityCode,
              areaCode,
              gradeId,
              graduationYear: gradeId,
              reportDateStr,
              sourceType: getSourceType(),
              token: getToken(),
            }
            window.open(`${location.origin}/api/psychology/mentalHealthReport/exportWarningSummary?${objectToUrlParams(urlParams)}`, '_blank')
          }

          return {
            tableProps: {
              wrapperClass: styles.earlyWarningTable,
              beforeRender: () => {
                return (
                  <div className={styles.downloadBox}>
                    <Button onClick={() => handleDonwload()}>
                      <img src={IconDownload} width="14" height="14" alt="Download" className={styles.iconDownload} /> &nbsp;下载全市预警详情表Excel表格</Button>
                  </div>
                )
              },
              columns,
              dataSource: dataList,
              rowKey: 'areaCode',
              hidePagination: true,
              children: (
                <div className={styles.textBox} style={{marginTop: 20}}>
                  <p>
                    <b>★ 风险程度说明</b>
                  </p>
                  <p>
                    高风险：表示该生的因子得分高于绝大部分高中生，在对应因子上有极大的可能性存在异常风险，建议老师优先关注并及时沟通干预。
                  </p>
                  <p>
                    较高风险：表示该生的因子得分高于大部分高中生，在对应因子上有较大可能性存在异常风险，建议老师保持密切关注。
                  </p>
                  <p>
                    轻微风险：表示该生的因子得分高于一般高中生，在对应因子上有一定可能性存在异常风险，建议老师保持密切关注。
                  </p>
                  <p>
                    无风险：表示该生的因子得分与大部分高中生一致，不存在明显的异常风险。
                  </p>
                </div>
              ),
            },
          };
        },
      },
      {
        type: 'PageNavigation',
        props: {
          list: sidePageNavList,
          theme: 'green',
          timeout: 180000
        },
        sortProps: (data) => {
          const { queryParams: { printMode, detailReportMode }} = data;
          return {
            printMode,
            detailReportMode
          }
        }
      }
    ],
  },
];
export default tree;
