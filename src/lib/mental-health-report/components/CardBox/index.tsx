import React from 'react';
import styles from './index.module.less';
import IconRightArrow from '@/assets/mental-health-report/iconRightArrow.png'

interface IProps {
  id?: string;
  title?: string;
  className?: string;
  children?: React.ReactNode;
  titleStyle?: React.CSSProperties;
}

const CardBox = (props: IProps) => {
  const {
    children,
    className,
    id,
    title,
    titleStyle
  } = props;

  return (
    <div
      id={id}
      {...{
        className: `${styles.cardBox} ${className || ''}`,
        style: { ...titleStyle }
      }}
    >
      <div
        {...{
          className: `${styles.cardTitle}`,
          style: { ...titleStyle },
        }}
        >
        <i
        style={{
          backgroundImage: `url(${IconRightArrow})`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }} />
        { title }
      </div>
      { children }
    </div>
  )
}

export default CardBox;
