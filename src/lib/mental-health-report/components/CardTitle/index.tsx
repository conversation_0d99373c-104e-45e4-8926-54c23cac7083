import React from 'react';
import styles from './index.module.less';
import IconRightArrow from '@/assets/mental-health-report/iconRightArrow.png'

interface IProps {
  title?: string;
  titleStyle?: React.CSSProperties;
}

const CardTitle = (props: IProps) => {
  const {
    title,
    titleStyle
  } = props;

  return (
    <div
      {...{
        className: `${styles.cardTitle}`,
        style: { ...titleStyle },
      }}
      >
      <i
      style={{
        backgroundImage: `url(${IconRightArrow})`,
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
      }} />
      { title }
    </div>
  )
}

export default CardTitle;
