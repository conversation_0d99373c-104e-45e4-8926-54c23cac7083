import moment from 'moment';
import { Modal, Table } from 'antd';
import { areaSelectItem } from '~/service/mental-health-report/report-entrance/types';
import { objectToUrlParams } from '~/utils/tools';
import styles from '@/pages/mental-health-report/county-report-entrance/index.module.less'
import CardTitle from '../components/CardTitle';
import API, { IGetAreaCitySchoolEntranceListInfo } from '@/service/mental-health-report/report-entrance'

const columnsList = [
  {
    title: "学校",
    dataIndex: "schoolName",
    align: "center",
    width: 150
  },
  {
    title: "布置人数",
    dataIndex: "allTotalCnt",
    align: "center",
    width: 110
  },
  {
    title: "完成人数",
    dataIndex: "allFinishCnt",
    align: "center",
    width: 110
  },
  {
    title: "完成率",
    dataIndex: "allFinishRate",
    align: "center",
    width: 110,
    render: (index: number, record: { allFinishRate: number }) => (
      <span key={index}>{`${record.allFinishRate}%`}</span>
    )
  },
  {
    title: "布置日期",
    dataIndex: "startDate",
    align: "center",
    width: 120,
    render: (index: number, record: { startDate: number}) => (
      <span key={index}>{`${moment(record.startDate).format('YYYY-MM-DD')}`}</span>
    )
  },
  {
    title: "截止日期",
    dataIndex: "endDate",
    align: "center",
    width: 120,
    render: (index: number, record: { endDate: number }) => (
      <span key={index}>{`${moment(record.endDate).format('YYYY-MM-DD')}`}</span>
    )
  },
]

// 查看年级报告
const handleGradeReport = (data: any) => {
  const { cityCode, areaCode, graduationYear, gradeCode, semester, answerTaskId} = data;
  const exportUrl = objectToUrlParams({
    evaluationTaskId: answerTaskId,
    cityCode,
    areaCode,
    gradeId: graduationYear,
    gradeCode,
    semester,
  })
  window.open(`${location.origin}/psychology-service/mental-health-report/grade-report?`+exportUrl);
}

// 查看同校多分报告
export const handleViewReport = async (record: any) => {
  const {
    cityCode,
    graduationYear,
    gradeCode,
    areaCode,
    semester,
    reportFlag,
    schoolCode,
    schoolName,
    gradeOptions
  } = record;

  if (reportFlag === 1) {
    handleGradeReport(record)
    return
  }

  const currentTitle = `${schoolName} ${gradeOptions[gradeCode]} ${semester === 1 ? '上': '下'} （${graduationYear - 3}入学）`
  const params = {
    schoolId: schoolCode,
    cityCode,
    areaCode,
    gradeId: graduationYear,
    gradeCode,
    semester,
    clientType: 1
  };

  const currentSchoolColums: any[] = [
    ...columnsList,
    {
      title: "报告",
      dataIndex: 'answerTaskId',
      align: 'left',
      width: 200,
      render: (index: number, record: any) => {
        if (record.answerTaskId) {
          return <a key={index} onClick={() => handleGradeReport(record)}>查看集体报告</a>
        }
        return <span key={index} style={{color:'#ccc'}}>暂无</span>
      }
    }
  ]

  try {
    const { data } = await API.getMultiAnswerTaskInfoBySchoolId(params)
    Modal.confirm({
      width: 800,
      icon: '',
      content: (
        <div className={styles.modalSchoolBox}>
          <p className={styles.schoolTitle}>{currentTitle}</p>
          <p>1. 该学校被布置多次测评，且生成多份可查看报告；</p>
          <p>2. 该校多份测评中课程存在重复作答数据，所以进入区县报告的学校数据会有去重处理。</p>
          <Table
            rowKey="answerTaskId"
            className={styles.customTable}
            columns={currentSchoolColums}
            dataSource={data}
            pagination={false}
          />
        </div>
      ),
      centered: true,
      closable: true,
    });
  } catch (e) {
    console.error(e)
  }
}

const nodeTree = [
  {
    type: 'page',
    list: [{
      isRealNode: true,
      props: {
        style: {
          width: '1000px',
          margin: '0 auto',
        },
      },
      list: [
        {
          type: 'Card',
          props: {
            wrapperStyle: {
              margin: '20px 0px 30px',
              boxShadow: '0 4px 15px 0 rgb(0 0 0 / 10%)',
              borderRadius: '8px',
              padding: '32px'
            },
          },
          list: [
            {
              type: 'SearchTable',
              props: {
                wrapperStyle: { margin: '0px 0px 5px' },
              },
              sortProps: (data: any) => {
                const {
                  currentData: {
                    cityCode,
                    graduationYear,
                    gradeCode,
                    semester
                  },
                  gradeOptions,
                  areaOptions
                } = data;

                const schoolColumns = [
                  ...columnsList,
                  {
                    title: "报告",
                    dataIndex: "reportFlag",
                    align: "left",
                    width: 200,
                    render: (index: number, record: any) => {
                      if (record.reportFlag) {
                        return <a key={index} onClick={() => handleViewReport({
                          cityCode,
                          graduationYear,
                          gradeCode,
                          semester,
                          gradeOptions,
                          ...record })}>查看集体报告</a>
                      }
                      return <span key={index} style={{color: '#ccc'}}>暂无</span>
                    }
                  }
                ]

                return {
                  before: (
                    <CardTitle title="校级报告" />
                  ),
                  searchForm: {
                    wrapperStyle: {
                      display: 'inline-block',
                      width: '400px',
                      marginLeft: '30px '
                    },
                    layout: 'inline',
                    columns: [
                      {
                        type: 'select',
                        key: 'areaCode',
                        isImmediate: true,
                        formItemProps: {
                          label: '选择地区',
                        },
                        props: {
                          style: {
                            width: 230
                          },
                          options: [
                            ...areaOptions.map((v: areaSelectItem) =>({
                              label: v.areaName,
                              value: v.areaId
                            }))
                          ],
                        },
                      },
                    ],
                    hideSubmit: true,
                    showReset: false,
                  },
                  tableProps: {
                    wrapperClass: styles.customTable,
                    rowKey: 'schoolCode',
                    columns: schoolColumns,
                    params: {
                      cityCode,
                      gradeId: graduationYear,
                      gradeCode,
                      type: 1,
                      semester,
                      areaCode: 0
                    },
                    request: API.getEntrySchoolInfoList,
                    sortParams: ({
                      ...rest
                    }: {
                      [key: string]: any;
                    }): Partial<IGetAreaCitySchoolEntranceListInfo['params']> => ({
                      ...rest,
                    }),
                    sortData: ({
                      data: dataSource,
                    } : IGetAreaCitySchoolEntranceListInfo['data']) => ({
                      dataSource,
                    }),
                    // info: renderSchoolTable,
                    hidePagination: true,
                  }
                }
              }
            }
          ]
        }
      ]
    }]
  }];
export default nodeTree;
