import moment from "moment";
import { Button } from "antd";
import Image from 'next/image'
import { schoolItem, schoolStaticsItem } from "~/pages/entrance/cityCover2/types";
import { objectToUrlParams } from "~/utils/tools";
import API, { IGetAreaCitySchoolEntranceListInfo } from '~/service/entrance/cityCover2'
import IconDownload from '@/assets/psychology/iconDownload.png'
import styles from '~/pages/entrance/cityCover2/index.module.less';

// 入学测-市级入口页面报告
const tree = [
  { type: 'page',
    isRealNode: true,
    props: {
      style: { width: '1000px', margin: '0 auto' },
    },
    list: [
      {
        type: 'SearchTable',
        props: {
          wrapperStyle: { margin: '60px 0px 30px' },
          hideSearch: true,
          hidePagination: false,
        },
        sortProps: (data: any) => {
          const { currentData, areaCityEntrance: { schoolStaticsList }} = data;
          const JumpToCounty = (record: schoolStaticsItem ) => {
            const { hasAreaReport, areaCode } = record;
            if (!hasAreaReport) return;
            const { cityCode, graduationYear, reportDateStr } = currentData;
            const params = {
              cityCode,
              areaCode,
              gradeId: graduationYear,
              reportDateStr,
              version: 2
            }
            window.open(`${location.origin}/psychology-service/entrance/countyReport?${objectToUrlParams(params)}`,'_blank')
          }

          const columnsList = [
            { title: '区县', dataIndex: 'areaName', align: 'center' },
            { title: '学校数', dataIndex: 'schoolNum', align: 'center' },
            { title: '年级', dataIndex: 'gradeName', align: 'center' },
            { title: '布置人数', dataIndex: 'totalNum', align: 'center' },
            { title: '完成测评人数', dataIndex: 'finishedNum', align: 'center' },
            { title: '完成率', dataIndex: 'finishedPercentage', align: 'center', render:(value: number) => `${value}%`},
            { title: '报告', dataIndex: 'operation', align: 'center', render: (_:any, record: schoolStaticsItem, key:any) => {
              return (
                <span key={record.areaCode}>
                  { key ?
                    <a
                      onClick={() => {
                        JumpToCounty(record);
                      }}
                      style={{
                        textDecoration: record.hasAreaReport ? 'underline' : '',
                        color: record.hasAreaReport ? 'rgb(75, 128, 255)' : 'rgba(0,17,57,0.85)',
                        cursor: record.hasAreaReport ? 'pointer' : 'text',
                      }}
                    >
                      查看区县报告
                    </a> : ''
                  }
                </span>)
            }
          }]
          return {
            tableProps: {
              wrapperClass: styles.customTable,
              rowKey: 'areaCode',
              columns: columnsList,
              dataSource: schoolStaticsList,
              hidePagination: true,
            }
          }
        }
      },
      {
        type: 'SearchTable',
        props: {
          wrapperStyle: { margin: '60px 0px 30px' },
        },
        sortProps: (data: any) => {
          const { currentData, areaOptions } = data;
          const { cityCode, graduationYear, reportDateStr } = currentData;
          // 跳转到年级
          const JumpToGrade = (record: schoolItem ) => {
            if (!record.gradeFlag) return;
            const { answerTaskId, areaCode, schoolCode } = record;
            const params = {
              evaluationTaskId: answerTaskId,
              cityCode,
              areaCode,
              schoolCode,
              gradeId: graduationYear,
              graduationYear,
              reportDateStr,
              type: 1,
              version: 2
            }
            window.open(`${location.origin}/psychology-service/entrance/gradeReport?${objectToUrlParams(params)}`,'_blank')
          }

          const columnsList = [
            { title: '区县', dataIndex: 'areaName', align: 'center' },
            { title: '学校', dataIndex: 'schoolName', align: 'center' },
            { title: '年级', dataIndex: 'gradeName', align: 'center' },
            { title: '布置人数', dataIndex: 'totalNum', align: 'center' },
            { title: '完成测评人数', dataIndex: 'finishedNum', align: 'center' },
            { title: '完成率', dataIndex: 'finishedPercentage', align: 'center', render:(value: number) => <span>{value}%</span>},
            { title: '布置日期', dataIndex: 'assignDate', align: 'center', render:(value: number) => <span>{ value ? `${moment(value).format('YYYY-MM-DD')}`: ''}</span>},
            { title: '截止日期', dataIndex: 'endDate', align: 'center', render:(value: number) => <span>{ value ? `${moment(value).format('YYYY-MM-DD')}`: ''}</span>},
            { title: '集体报告', dataIndex: 'operation', align: 'center', render: (_:any, record: schoolItem, key:any) =>
                <span key={record.areaCode}>
                  { key ?
                    <a
                      onClick={() => {
                        JumpToGrade(record);
                      }}
                      style={record.gradeFlag ? {...{
                        textDecoration: 'underline',
                        color: 'rgb(75, 128, 255)',
                        cursor: 'pointer'
                      }} : {...{
                        color: 'rgba(0,17,57,0.85)',
                        cursor: 'text'
                      }}}
                    >
                      查看集体报告
                    </a> : ''
                  }
                </span>
          }]

          // 下载
          const handleDonwload = (params: any) => {
            const { type, cityCode, graduationYear, reportDateStr } = params;
            const urlParams = {
              type,
              graduationYear,
              areaCode: 0,
              cityCode,
              reportDateStr
            }
            window.open(`${location.origin}/api/psychology/mentalAdmissionAreaReport/v2/exportCityEntranceAreaDetailInfo?${objectToUrlParams(urlParams)}`,'_blank')
          }

          return {
            searchForm: {
              layout: 'inline',
              columns: [
                {
                  type: 'select',
                  key: 'areaCode',
                  isImmediate: true,
                  formItemProps: {
                    label: '选择地区',
                  },
                  props: {
                    style: {
                      width: 230
                    },
                    options: [
                      { label: '全部区县', value: 0 },
                      ...areaOptions.filter((v: { areaCode: any; }) => v.areaCode > 0).
                      map((v: { areaName: string; areaCode: number; }) =>({
                        label: v.areaName,
                        value: v.areaCode
                      }))
                    ],
                  },
                },
              ],
              hideSubmit: true,
              showReset: false,
            },
            tableProps: {
              wrapperClass: styles.customTable,
              beforeRender: (paramsData: any) => {
                const { params } = paramsData;
                return (
                  <div className={styles.downloadBox}>
                    <Button onClick={() => handleDonwload({...params})}><Image src={IconDownload} width="14" height="14" alt="Download" className={styles.iconDownload} /> &nbsp;Excel下载</Button>
                  </div>
                )
              },
              params: {
                cityCode,
                graduationYear,
                type: 1,
                reportDateStr,
                areaCode: 0
              },
              request: API.getAreaCitySchoolEntranceListInfo,
              sortParams: ({
                ...rest
              }: {
                [key: string]: any;
              }): Partial<IGetAreaCitySchoolEntranceListInfo['params']> => ({
                ...rest,
              }),
              sortData: ({
                data: { schoolList: dataSource },
              } : IGetAreaCitySchoolEntranceListInfo['data']) => ({
                dataSource,
              }),
              rowKey: 'schoolCode',
              columns: columnsList,
              hidePagination: true,
            }
          }
        }
      },
  ]
  }
];
export default tree;
