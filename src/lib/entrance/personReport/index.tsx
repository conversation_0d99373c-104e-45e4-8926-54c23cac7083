
import dynamic from 'next/dynamic';
import MentalCover from '~/businessComponents/MentalAdmission/MentalCover';
import EntranceReportInfo from '~/businessComponents/MentalAdmission/MentalHeader';
import Guide from '~/businessComponents/MentalAdmission/Guide';
import MainFinding from '@/pages/entrance/component/MainFindings/mainFindings';
import PieRemarks from '~/businessComponents/MentalAdmission/PieRemarks';
import EntranceUpbringing from '~/businessComponents/MentalAdmission/EntranceUpbringing';
import { userReportProps } from '~/service/entrance/person/types';
import allFoot from "@/assets/MentalAdmission/entrance-banner-all-footer.png"
const EntranceFactorTable = dynamic(() => import('~/businessComponents/MentalAdmission/EntranceFactorTable'), { ssr: false });
const EntranceFactorColors = ['#00DFA6', '#4B80FF', '#FFC314', '#FF5858'];
const EntranceHandstandFactorColors = [
  '#FF5858',
  '#FFC314',
  '#4B80FF',
  '#00DFA6',
];

const sidePageNavList: any[] = [
  { label: "1、报告导读", point: "Introduction" },
  {
    label: "2、结果分析", point: "resultAnalyse",
    children: [
      { label: "2.1 主要发现", point: "majorDiscovery" },
      { label: "2.2 心理健康状态分析", point: "stateAnalysis" },
      { label: "2.3 学习准备度分析", point: "readinessAnalysis" },
      { label: "2.4 性格类型分析", point: "personalityTypeAnalysis" },
      { label: "2.5 家庭教养风格分析", point: "styleAnalysis" },
    ],
  },
  { label: "3、附加说明", point: "additionalInformation" },
]

const tree = [
  {
    type: 'page',
    isRealNode: true,
    props: {
      style: { width: '1000px', margin: '0 auto' },
    },
    list: [
      {
        type: 'THeaderControl',
        props: {
          title: '报告导读',
          subTitle: 'Introduction',
          wrapperStyle: { margin: '60px 0px 30px' },
          id: sidePageNavList[0].point
        }
      },
      {
        type: 'Card',
        props: {
          title: '尊敬的老师:',
          wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
          content: <Guide />,
        },
      },
      {
        type: 'THeaderControl',
        props: {
          title: '结果分析',
          subTitle: 'Analysis',
          wrapperStyle: { margin: '60px 0px 30px' },
          id: sidePageNavList[1].point
        },
      },
      {
        type: 'Card',
        props: {
          wrapperStyle: {
            boxShadow: '0px 0px 20px 0px #e6ece9',
            marginBottom: '60px',
          },
        },
        list: [
          {
            type: 'CardTitleBar',
            props: {
              title: '01 主要发现',
              themeColor: 'rgba(75, 128, 255)',
              id: sidePageNavList[1].children[0].point
            },
            sortProps: (data: userReportProps) => {
              return {
                content: <MainFinding mainFindings={data.mainFindings} />,
              };
            },
          },
        ],
      },
      {
        type: 'Card',
        props: {
          wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
        },
        list: [
          {
            type: 'CardTitleBar',
            props: {
              title: '02 心理健康状态分析',
              themeColor: 'rgba(75, 128, 255)',
              id: sidePageNavList[1].children[1].point
            },
            sortProps: (data: userReportProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperClass: printMode ? 'typePrint' : ''
              }
            }
          },
          {
            type: 'SmallIconTitle',
            props: {
              title: '心理风险程度',
              themeColor: 'rgba(75, 128, 255)',
              icon: true,
              iconType: 'half',
              wrapperStyle: { margin: '40px 0 0 -20px', },
            },
            sortProps: (data: userReportProps) => {
              return {
                content: (
                  <div
                    style={{
                      fontSize: '18px',
                      padding: '30px 0 0 20px',
                      lineHeight: 1.5,
                    }}
                  >
                    {data?.riskAnalysis?.inConclusion}
                  </div>
                ),
              };
            },
          },
          {
            type: 'ChartForPie',
            props: {
              id: 'ChartForPie1',
              contentStyle: {
                width: '100%',
                margin: '0 auto',
              },
              height: '320px',
            },
            sortProps: (data: userReportProps) => {
              const { level, score, name } = data?.riskAnalysis;
              return {
                data: [
                  { value: score, name },
                ],
                color: [EntranceFactorColors[(level || 1) - 1], '#eef1f0'],
                options: {
                  series: [
                    {
                      name: '风险等级',
                      type: 'pie',
                      radius: ['50%', '60%'],
                      avoidLabelOverlap: false,
                      label: {
                        show: true,
                        position: 'center',
                        formatter: ['{e|风险等级}', `{d|${name}}`].join('\n'),
                        rich: {
                          d: {
                            color: EntranceFactorColors[(level || 1) - 1],
                            lineHeight: 74,
                            fontSize: 36,
                            fontWeight: 600,
                            height: 74,
                          },
                          e: {
                            color: '#7D869A',
                            lineHeight: 24,
                            fontSize: 18,
                            height: 24,
                          },
                        },
                      },
                      emphasis: {
                        label: {
                          show: true,
                          fontSize: '40',
                          fontWeight: 'bold',
                        },
                      },
                      labelLine: {
                        show: false,
                      },
                    },
                  ],
                },
              };
            },
          },
          {
            props: {
              content: <PieRemarks type={1} version={2} />,
            },
          },
          {
            type: 'SmallIconTitle',
            props: {
              title: '心理健康因子分析',
              themeColor: 'rgba(75, 128, 255)',
              icon: true,
              wrapperStyle: { margin: '40px 0 0 -20px'},
            },
            sortProps: (data: userReportProps) => {
              const { factorAnalysis, queryParams: { printMode, detailReportMode } } = data;
              return {
                subTitle: detailReportMode ? '' : '(不存在风险的因子不在此展示，如需了解无风险因子的具体情况请查看详细报告)',
                wrapperClass: printMode ? 'typePrint' : '',
                content: (
                  <div
                    style={{
                      fontSize: '18px',
                      padding: '30px 0 0 20px',
                      lineHeight: 1.5,
                    }}
                  >
                    {factorAnalysis?.inConclusion}
                  </div>
                ),
              };
            },
          },
          {
            type: 'ChartForRadar',
            props: {
              id: `ChartForRadar1`,
              contentStyle: { width: '100%', margin: '20px auto 40px' },
              height: '400px',
            },
            sortProps: (data: userReportProps) => {
              const {
                factorAnalysis: { latitudeList = [] },
              } = data;
              const dataName: any[] = [];
              const dataVaule: number[] = [];
              const dataMax: number[] = [];
              const dataWarnnig: number[] = [];
              latitudeList?.forEach((v: any, key: number) => {
                dataName.unshift({
                  name: v.name,
                  max: 5,
                });
                dataVaule.unshift(v.score);
                dataMax.unshift(v.referenceScore);
                dataWarnnig.unshift(v.warnningScore);
              });
              return {
                legend: {
                  data: ['得分', '参考分', '预警分'],
                  fontSize: 14,
                  verticalAlign: 'bottom',
                  bottom: 0,
                },
                options: {
                  radar: [
                    {},
                    {
                      indicator: dataName,
                      center: ['50%', '46%'],
                      radius: 120,
                      startAngle: 125,
                      axisName: {
                        color: '#001139',
                        fontSize: 18,
                      },
                      splitArea: {
                        areaStyle: {
                          color: ['#fff', '#F2F6FF'].reverse(),
                        },
                      },
                      axisLine: {
                        lineStyle: {
                          color: '#E0ECFF',
                        },
                      },
                      splitLine: {
                        show: true,
                        lineStyle: {
                          color: '#E0ECFF',
                        },
                      },
                    },
                  ],
                  series: [
                    {
                      name: '成绩单',
                      type: 'radar',
                      radarIndex: 1,
                      symbol: 'none',
                      data: [
                        {
                          type: 'radar',
                          value: dataVaule,
                          name: '得分',
                          itemStyle: {
                            color: '#4B80FF',
                            fontSize: 18,
                          },
                          areaStyle: {
                            color: '#4B80FF',
                          },
                        },
                        {
                          value: dataMax,
                          type: 'radar',
                          name: '参考分',
                          itemStyle: {
                            color: '#FFA119',
                            borderColor: '#FFA119',
                            borderType: 'solid',
                            borderWidth: '3px',
                            backGroundColor: '#fff',
                            fontSize: 18,
                          },
                          lineStyle: {
                            width: 3,
                          },
                        },
                        {
                          value: dataWarnnig,
                          type: 'radar',
                          name: '预警分',
                          itemStyle: {
                            color: '#FF3C19',
                            borderColor: '#FF3C19',
                            borderType: 'solid',
                            fontSize: 18,
                          },
                          lineStyle: {
                            width: 3,
                          },
                        },
                      ],
                    },
                  ],
                },
              };
            },
          },
          // {
          //   sortProps: (data: userReportProps) => {
          //     return {
          //       content: (
          //         <EntranceFactorTable
          //           type={false}
          //           version={2}
          //           factorAnalysis={data.factorAnalysis || {}}
          //         />
          //       ),
          //     };
          //   },
          // },
          {
            sortList: (data: userReportProps) => {
              const { factorAnalysisDetails = [], queryParams: { detailReportMode } } = data;
              const renderResult: any[] = [];
              const titleStyle = {
                fontWeight: 700,
                fontSize: 18,
                color: '#475370',
                margin: '20px 0',
              };
              const factorAnalysisDetailsList = detailReportMode ? factorAnalysisDetails : factorAnalysisDetails.filter(v => v.level !== 1)
              if (factorAnalysisDetailsList && factorAnalysisDetailsList?.length > 0) {
                factorAnalysisDetailsList?.forEach((v: any) => {
                  const mentalColor = EntranceFactorColors[(v?.level || 1) - 1];
                  renderResult?.push({
                    type: 'Card',
                    props: {
                      wrapperStyle: {
                        marginTop: 30,
                        boxShadow: '0px 0px 20px 0px #e6ece9',
                      },
                    },
                    list: [
                      {
                        type: 'StatusBar',
                        props: {
                          themeColor: mentalColor,
                          title: v.name,
                          right: (
                            <div
                              style={{
                                fontSize: '22px',
                                fontWeight: 700,
                                color: '#475370',
                                display: 'flex',
                              }}
                            >
                              <p
                                style={{
                                  color: v.latitudeName?.includes(
                                    '睡眠'
                                  )
                                    ? '#475370'
                                    : EntranceFactorColors[
                                    (v.level || 1) - 1
                                    ],
                                }}
                              >
                                {v.score}
                              </p>
                              /{v.totalScore}
                              <span style={{ marginLeft: '40px' }}>
                                {v.levelName}
                              </span>
                            </div>
                          ),
                          content: (
                            <>
                              <p style={{ ...titleStyle }}>
                                ★ 因子说明
                              </p>
                              <p
                                style={{
                                  fontSize: '18px',
                                  lineHeight: 1.5,
                                  color: 'rgba(0, 17, 57, 0.85)',
                                }}
                              >
                                {v.factorDescription}
                              </p>
                              <p style={{ ...titleStyle }}>
                                ★ 学生情况
                              </p>
                              <p
                                style={{
                                  fontSize: '18px',
                                  lineHeight: 1.5,
                                  color: 'rgba(0, 17, 57, 0.85)',
                                }}
                              >
                                {v.studentSituation}
                              </p>
                              <p style={{ ...titleStyle }}>
                                ★ 辅导建议
                              </p>
                              <p
                                style={{
                                  fontSize: '18px',
                                  lineHeight: 1.5,
                                  color: 'rgba(0, 17, 57, 0.85)',
                                }}
                              >
                                {v.counselingAdvice}
                              </p>
                            </>
                          ),
                        },
                      },
                    ],
                  });
                });
              }
              return renderResult;
            },
          },
        ],
      },
      // 03学习准备度分析
      {
        type: 'Card',
        props: {
          wrapperStyle: {
            boxShadow: '0px 0px 20px 0px #e6ece9',
            padding: '30px 20px 40px 20px',
            marginTop: '60px',
          }
        },
        list: [
          {
            type: 'CardTitleBar',
            props: {
              title: '03 学习准备度分析',
              themeColor: 'rgba(75, 128, 255)',
              id: sidePageNavList[1].children[2].point
            },
            sortProps: (data: userReportProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperClass: printMode ? 'typePrint' : ''
              }
            },
            list: [
              {
                type: 'SmallIconTitle',
                props: {
                  title: '学习准备度指数',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '40px 0 0 -20px', },
                },
                sortProps: (data: userReportProps) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 0',
                          lineHeight: 1.5,
                          display: 'flex',
                        }}
                      >
                        从测评结果来看，该生的学习准备状态评级为：
                        <p style={{ fontWeight: 'bold', color: '#46526f' }}>
                          {data?.learningReadiness?.name}
                        </p>
                      </div>
                    ),
                  };
                },
              },
              {
                type: 'ChartForPie',
                props: {
                  content: <PieRemarks type={21} version={2} />,
                  id: 'ChartForPie3',
                  height: '260px',
                },
                sortProps: (data: userReportProps) => {
                  const {
                    learningReadiness: { level, name, score },
                  } = data;
                  return {
                    data: [
                      { value: score, name },
                      { value: 100 - score, name: '其他' },
                    ],
                    color: [
                      EntranceHandstandFactorColors[(level || 1) - 1],
                      '#eef1f0',
                    ],
                    options: {
                      series: [
                        {
                          name: '风险等级',
                          type: 'pie',
                          radius: ['70%', '80%'],
                          avoidLabelOverlap: false,
                          label: {
                            show: true,
                            position: 'center',
                            formatter: [
                              '{a|风险等级}',
                              `{c|${name}}`,
                              `{b|${score}}`,
                            ].join('\n'),
                            rich: {
                              a: {
                                color: '#7D869A',
                                lineHeight: 24,
                                fontSize: 18,
                                height: 24,
                              },
                              b: {
                                color:
                                  EntranceHandstandFactorColors[
                                  (level || 1) - 1
                                  ],
                                lineHeight: 24,
                                fontSize: 30,
                                height: 24,
                              },
                              c: {
                                color:
                                  EntranceHandstandFactorColors[
                                  (level || 1) - 1
                                  ],
                                lineHeight: 74,
                                fontSize: 30,
                                fontWeight: 600,
                                height: 74,
                              },
                              d: {
                                color:
                                  EntranceHandstandFactorColors[
                                  (level || 1) - 1
                                  ],
                                lineHeight: 74,
                                fontSize: 36,
                                fontWeight: 600,
                                height: 74,
                              },
                              e: {
                                color: '#7D869A',
                                lineHeight: 24,
                                fontSize: 18,
                                height: 24,
                              },
                            },
                          },
                          emphasis: {
                            label: {
                              show: true,
                              fontSize: '40',
                              fontWeight: 'bold',
                            },
                          },
                          labelLine: {
                            show: false,
                          },
                        },
                      ],
                    },
                  };
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '学习准备度因子分析',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '40px 0 0 -20px', },
                },
                sortProps: (data: userReportProps) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {data?.studyReadinessAnalysis?.inConclusion}
                      </div>
                    ),
                  };
                },
              },
              {
                type: 'ChartForRadar',
                props: {
                  id: `ChartForRadar2`,
                  contentStyle: { width: '100%', margin: '20px auto 40px' },
                  height: '340px',
                },
                sortProps: (data: userReportProps) => {
                  const { studyReadinessAnalysis: { latitudeList = [] },
                  } = data;
                  const dataName: any[] = [];
                  const dataVaule: number[] = [];
                  const dataMax: number[] = [];
                  const dataWarnnig: number[] = [];
                  latitudeList?.forEach((v: any, key: number) => {
                    dataName.unshift({
                      name: v.name,
                      max: 25,
                    });
                    dataVaule.unshift(v.score);
                    dataMax.unshift(v.referenceScore);
                    dataWarnnig.unshift(v.warnningScore);
                  });
                  return {
                    legend: {
                      data: ['得分', '参考分', '预警分'],
                      fontSize: 14,
                      verticalAlign: 'bottom',
                      bottom: 0,
                    },
                    options: {
                      radar: [
                        {},
                        {
                          indicator: dataName,
                          center: ['50%', '46%'],
                          radius: 120,
                          startAngle: 125,
                          axisName: {
                            color: '#001139',
                            fontSize: 18,
                          },
                          splitArea: {
                            areaStyle: {
                              color: ['#fff', '#F2F6FF'].reverse(),
                            },
                          },
                          axisLine: {
                            lineStyle: {
                              color: '#E0ECFF',
                            },
                          },
                          splitLine: {
                            show: true,
                            lineStyle: {
                              color: '#E0ECFF',
                            },
                          },
                        },
                      ],
                      series: [
                        {
                          name: '成绩单',
                          type: 'radar',
                          radarIndex: 1,
                          symbol: 'none',
                          data: [
                            {
                              type: 'radar',
                              value: dataVaule,
                              name: '得分',
                              itemStyle: {
                                color: '#4B80FF',
                                fontSize: 18,
                              },
                              areaStyle: {
                                color: '#4B80FF',
                              },
                            },
                            {
                              value: dataMax,
                              type: 'radar',
                              name: '参考分',
                              itemStyle: {
                                color: '#FFA119',
                                borderColor: '#FFA119',
                                borderType: 'solid',
                                borderWidth: '3px',
                                backGroundColor: '#fff',
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                            {
                              value: dataWarnnig,
                              type: 'radar',
                              name: '预警分',
                              itemStyle: {
                                color: '#FF3C19',
                                borderColor: '#FF3C19',
                                borderType: 'solid',
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                          ],
                        },
                      ],
                    },
                  };
                },
              },
              {
                keyList: ['studyReadinessAnalysis'],
                sortProps: (data: userReportProps) => {
                  return {
                    content: (
                      <EntranceFactorTable
                        type={true}
                        version={1}
                        factorAnalysis={data || {}}
                      />
                    ),
                  };
                },
              },
            ],
            sortList: (data: userReportProps) => {
              const { studyReadinessDetails = [], queryParams: { detailReportMode}} = data;
              if (!detailReportMode) return [];
              const renderResult: any[] = [];
              const titleStyle = {
                fontWeight: 700,
                fontSize: 18,
                color: '#475370',
                margin: '20px 0',
              };
              if (studyReadinessDetails?.length > 0) {
                studyReadinessDetails.forEach((v: any) => {
                  const readyColor = EntranceFactorColors[(v?.level || 1) - 1];
                  renderResult.push({
                    type: 'Card',
                    props: {
                      wrapperStyle: {
                        marginTop: 30,
                        boxShadow: '0px 0px 20px 0px #e6ece9',
                      },
                    },
                    list: [
                      {
                        type: 'StatusBar',
                        props: {
                          themeColor: readyColor,
                          title: v.name,
                          right: (
                            <div
                              style={{
                                fontSize: '22px',
                                fontWeight: 700,
                                color: '#475370',
                                display: 'flex',
                              }}
                            >
                              <p
                                style={{
                                  color: v.latitudeName?.includes('睡眠')
                                    ? '#475370'
                                    : EntranceFactorColors[
                                    (v.level || 1) - 1
                                    ],
                                }}
                              >
                                {v.score}
                              </p>
                              /{v.totalScore}
                              <span style={{ marginLeft: '40px' }}>
                                {v.levelName}
                              </span>
                            </div>
                          ),
                          content: (
                            <>
                              <p style={{ ...titleStyle }}>★ 因子说明</p>
                              <p
                                style={{
                                  fontSize: '18px',
                                  lineHeight: 1.5,
                                  color: 'rgba(0, 17, 57, 0.85)',
                                }}
                              >
                                {v.factorDescription}
                              </p>
                              <p style={{ ...titleStyle }}>★ 学生情况</p>
                              <p
                                style={{
                                  fontSize: '18px',
                                  lineHeight: 1.5,
                                  color: 'rgba(0, 17, 57, 0.85)',
                                }}
                              >
                                {v.studentSituation}
                              </p>
                              <p style={{ ...titleStyle }}>★ 辅导建议</p>
                              <p
                                style={{
                                  fontSize: '18px',
                                  lineHeight: 1.5,
                                  color: 'rgba(0, 17, 57, 0.85)',
                                }}
                              >
                                {v.counselingAdvice}
                              </p>
                            </>
                          ),
                        },
                      },
                    ],
                  });
                });
              }
              return renderResult;
            },
          },
        ],
      },
      // 性格类型分析
      {
        type: 'Card',
        props: {
          wrapperStyle: {
            boxShadow: '0px 0px 20px 0px #e6ece9',
            padding: '30px 20px 40px 20px',
            marginTop: '60px',
          },
        },
        list: [
          {
            type: 'CardTitleBar',
            props: {
              title: '04 性格类型分析',
              themeColor: 'rgba(75, 128, 255)',
              id: sidePageNavList[1].children[3].point,
            },
            sortProps: (data: userReportProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperClass: printMode ? 'typePrint' : '',
                content: (
                  <div
                    style={{
                      fontSize: '18px',
                      padding: '30px 0 0 20px',
                      lineHeight: 1.5,
                      display: 'flex',
                    }}
                  >
                    从测评结果来看，该生的学习准备状态评级为：
                    <p style={{ fontWeight: 'bold', color: '#46526f' }}>
                      {data.personalityTypeAnalysis.name}
                    </p>
                  </div>
                ),
              };
            },
            list: [
              {
                type: 'ChartForBar',
                props: {
                  id: 'ChartForBar',
                  height: '350px',
                },
                sortProps: (data: userReportProps) => {
                  const { personalityTypeAnalysis } = data;
                  const scoreList = personalityTypeAnalysis?.analysisList.map((v: { score: number; }) =>
                    ({value: v.score, label: Math.abs(v.score) >= 80 ? '' : {position: 'right', fontSize: 18 }})
                  )
                  return {
                    options: {
                      tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                          type: 'shadow'
                        }
                      },
                      grid: {
                        left: '3%',
                        right: '4%',
                        top: '30px',
                        containLabel: true
                      },
                      xAxis: {
                        type: 'value',
                        show: false,
                        max: 27, // 最大值
                      },
                      yAxis: {
                        type: 'category',
                        data: personalityTypeAnalysis?.analysisList.map((item: { name: string; }) => item.name) || [],
                        axisLabel: {
                          fontSize: 18,
                        }
                      },
                      series: [
                        {
                          name: '得分',
                          barGap: '-100%',
                          barCategoryGap: '40%',
                          type: 'bar',
                          data: scoreList,
                          label: {
                            show: true,
                          },
                          itemStyle: {
                            color: '#4B80FF'
                          },
                        },
                      ]
                    },
                  };
                },
              },
              {
                props: {
                  content: (
                    <div
                      style={{
                        padding: '0 0 0 20px',
                        fontSize: '18px',
                        color: ' #001139',
                        marginTop: '-40px',
                      }}
                    >
                      <h5 style={{ paddingBottom: '10px' }}>注：</h5>
                      <div>
                        简单来看，相应柱子的分值越高，你的气质类型越接近该类型。但最终的类型则需要综合考虑整个气质维度的关系。
                      </div>
                    </div>
                  ),
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '特点分析',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '40px 0 0 -20px', },
                },
                sortProps: (data: userReportProps) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '20px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {
                          data.personalityTypeAnalysis
                            .characteristicAnalysis
                        }
                      </div>
                    ),
                  };
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '辅导建议',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '40px 0 0 -20px', },
                },
                sortProps: (data: userReportProps) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '20px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {data.personalityTypeAnalysis.counselingAdvice}
                      </div>
                    ),
                  };
                },
              },
            ],
          },
        ],
      },
      // 05家庭教养风格分析
      {
        type: 'Card',
        props: {
          wrapperStyle: {
            boxShadow: '0px 0px 20px 0px #e6ece9',
            padding: '30px 20px 40px 20px',
            marginTop: '60px',
          },
        },
        list: [
          {
            type: 'CardTitleBar',
            props: {
              title: '05 家庭教养风格分析',
              themeColor: 'rgba(75, 128, 255)',
              id: sidePageNavList[1].children[4].point
            },
            sortProps: (data: userReportProps) => {
              const { queryParams: { printMode } } = data;
              return {
                wrapperClass: printMode ? 'typePrint' : '',
                content: (
                  <>
                    <EntranceUpbringing {...data?.familyEducation} />
                  </>
                ),
              };
            },
            list: [
              {
                type: 'SmallIconTitle',
                props: {
                  title: '特点分析',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '40px 0 0 -20px', },
                },
                sortProps: (data: userReportProps) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '20px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {
                          data.personalityTypeAnalysis
                            .characteristicAnalysis
                        }
                      </div>
                    ),
                  };
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '辅导建议',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '40px 0 0 -20px', },
                },
                sortProps: (data: userReportProps) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '20px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {data.personalityTypeAnalysis.counselingAdvice}
                      </div>
                    ),
                  };
                },
              },
            ],
          },
        ],
      },
      // 附加说明
      {
        type: 'THeaderControl',
        props: {
          title: '附加说明',
          subTitle: 'Additional Information',
          wrapperStyle: { margin: '60px 0px 30px' },
          id: sidePageNavList[2].point
        },
        sortProps: (data: userReportProps) => {
          const { queryParams: { printMode } } = data;
          return {
            wrapperClass: printMode ? 'typePrint' : ''
          }
        }
      },
      {
        type: 'Card',
        props: {
          wrapperStyle: {
            marginBottom: 50,
            boxShadow: '0px 0px 20px 0px #e6ece9',
          },
        },
        sortProps: () => {
          const titleStyle = {
            lineHeight: '44px',
            fontWeight: 700,
            color: 'rgba(0, 17, 57, 0.85)',
            fontSize: 18,
          };
          const contentStyle = {
            lineHeight: '22px',
            color: 'rgba(0, 17, 57, 0.85)',
            fontSize: 18,
          };
          return {
            content: (
              <div>
                <h3 style={{ ...titleStyle }}>
                  1、 任何心理测评都存在一定误差
                </h3>
                <p style={{ ...contentStyle, marginBottom: 20 }}>
                  学生在测评时的情绪状态、对测评的态度等主观因素，以及测评过程中的环境干扰等客观因素都会影响测评结果。因此在阅读本测评报告时，请参照学生在日常生活的实际表现来理解。
                </p>
                <h3 style={{ ...titleStyle }}>
                  2、请勿将测评结果当作永久“标签”
                </h3>
                <p style={{ ...contentStyle }}>
                  请不要把本测评结果看成是对学生的“标签”或最终“宣判”，而应视为学生的阶段性状态。对于处在青春期的高中学生来说，其大多数心理和性格特征都是可变的，因此本测评结果仅为了解学生近期状态提供参考。
                </p>
              </div>
            ),
          };
        },
      },
      {
        type: 'PageNavigation',
        props: {
          list: sidePageNavList,
          timeout: 180000
        },
        sortProps: (data: userReportProps) => {
          const { queryParams: { printMode, detailReportMode } } = data;
          return {
            printMode,
            detailReportMode
          }
        }
      }
    ],
  },
];
export default tree;
