// 入学测-区县报告页面报告

import dynamic from 'next/dynamic';
import MentalCover from '~/businessComponents/MentalAdmission/MentalCover';
import Guide from '~/businessComponents/MentalAdmission/Guide';
import MainFinding from '@/pages/entrance/component/MainFindings/mainFindings';
import PieRemarks from '~/businessComponents/MentalAdmission/PieRemarks';
import DotBar from '~/businessComponents/MentalAdmission/DotBar';
import countyHeader from "@/assets/MentalAdmission/entrance-banner-region.png"
import EntranceReportDetails from "~/businessComponents/MentalAdmission/EntranceReportDetails"
// 入学测-个人报告
import SchoolTable from '~/businessComponents/MentalAdmission/SchoolTable';
import styles from '@/pages/entrance/gradeReport/index.module.less';

const EntranceFactorTable = dynamic(
  () => import('~/businessComponents/MentalAdmission/EntranceFactorTable'),
  { ssr: false }
);
const EntranceFactorColors = ['#00DFA6', '#4B80FF', '#FFC314', '#FF5858'];
const EntranceStyleColors = ['#4B80FF', '#47BFFF', '#78F0E2', '#9DD3FF'];
const EntranceHandstandFactorColors = [
  '#FF5858',
  '#FFC314',
  '#4B80FF',
  '#00DFA6',
];

const tree = [
  {
    type: 'page',
    isRealNode: true,
    props: {
      WrapperStyle: {
        width: '1000px',
        margin: '0 auto',
        padding: 30,
      }
    },
    list: [
      {
        type: '',
        props: { content: <MentalCover src={countyHeader} islonger={true} /> },
        list: [
          {
            type: '',
            props: {},
            sortProps: (data: any) => {
              return {
                content: <EntranceReportDetails infoData={data} type={2} version={2} />
                ,
              };
            },
          },
        ],
      },
      {
        sortProps: (data: any) => {
          return {
            content:
              <SchoolTable evaluationSchoolVOS={data?.evaluationSchoolVOS} />

          }
        }
      },
      {
        isRealNode: true,
        props: {
          style: { width: '1000px', margin: '0 auto' },
        },
        list: [
          {
            type: 'THeaderControl',
            props: {
              title: '报告导读',
              subTitle: 'Introduction',
              wrapperStyle: { margin: '60px 0px 30px' },
            },
          },
          {
            type: 'Card',
            props: {
              title: '尊敬的老师:',
              wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
              content: <Guide />,
            },
          },
          {
            type: 'THeaderControl',
            props: {
              title: '结果分析',
              subTitle: 'Analysis',
              wrapperStyle: { margin: '60px 0px 30px' },
            },
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                boxShadow: '0px 0px 20px 0px #e6ece9',
                marginBottom: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '01 主要发现',
                  themeColor: 'rgba(75, 128, 255)',
                },

                sortProps: (data: any) => {
                  return {
                    content: <MainFinding mainFindings={data.mainFindings} />,
                  };
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '心理健康状态',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '30px 0px 0px -20px' },
                },
                sortProps: (data: any) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {' '}
                        {data.mainFindings.mentalHealth}
                      </div>
                    ),
                  };
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '学习准备状态',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '30px 0px 0px -20px' },
                },
                sortProps: (data: any) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {' '}
                        {data.mainFindings.studyReadiness}
                      </div>
                    ),
                  };
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '学生性格类型',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '30px 0px 0px -20px' },
                },
                sortProps: (data: any) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {' '}
                        {data.mainFindings.characterTraits}
                      </div>
                    ),
                  };
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '家庭教养风格',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '30px 0px 0px -20px' },
                },
                sortProps: (data: any) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {' '}
                        {data.mainFindings.growthEnvironment}
                      </div>
                    ),
                  };
                },
              },
            ],
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: { boxShadow: '0px 0px 20px 0px #e6ece9' },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '02 心理健康状态分析',
                  themeColor: 'rgba(75, 128, 255)',
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '心理风险程度',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '30px 0px 0px -20px' },
                },
                sortProps: (data: any) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {' '}
                        {data.statusAnalysis.riskIndex.inConclusion}
                      </div>
                    ),
                  };
                },
              },
              {
                type: 'ChartForPie',
                props: {
                  id: 'ChartForPie1',
                  contentStyle: {
                    width: '100%',
                    margin: '0 auto',
                  },
                  height: '350px',
                },
                sortProps: (data: any) => {
                  const { level, score, name } =
                    data?.statusAnalysis?.riskIndex;
                  return {
                    // before: inConclusion,
                    data: [
                      { value: score, name },
                      { value: 100 - score, name: '其他' },
                    ],
                    color: [EntranceFactorColors[(level || 1) - 1], '#eef1f0'],
                    options: {
                      series: [
                        {
                          name: '风险等级',
                          type: 'pie',
                          radius: ['67%', '80%'],
                          avoidLabelOverlap: false,
                          label: {
                            show: true,
                            position: 'center',
                            formatter: ['{e|风险等级}', `{d|${name}}`].join(
                              '\n'
                            ),

                            rich: {
                              d: {
                                color: EntranceFactorColors[(level || 1) - 1],
                                lineHeight: 74,
                                fontSize: 36,
                                fontWeight: 600,
                                fontFamily: 'PingFangSC, PingFangSC-Semibold',
                                height: 74,
                              },
                              e: {
                                color: '#7D869A',
                                lineHeight: 24,
                                fontSize: 18,
                                fontFamily: 'MicrosoftYaHeiUI',
                                height: 24,
                              },
                            },
                          },
                          emphasis: {
                            label: {
                              show: true,
                              fontSize: '40',
                              fontWeight: 'bold',
                            },
                          },
                          labelLine: {
                            show: false,
                          },
                        },
                      ],
                    },
                  };
                },
              },
              {
                props: {
                  content: <PieRemarks type={3} version={2} />,
                },
              },
              {
                type: 'SmallIconTitle',
                props: {
                  title: '心理健康风险分布',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '30px 0px 0px -20px' },
                },
              },
              {
                type: '',
                sortProps: (data: any) => {
                  return {
                    content: (
                      <DotBar
                        style={{ fontWeight: 'bold' }}
                        title={`学生心理风险群体分布图`}
                      />
                    ),
                  };
                },
              },
              {
                type: 'ChartForPie',
                props: {
                  id: 'ChartForPie2',
                  contentStyle: { width: '100%', margin: '15px auto' },
                  height: '400px',
                  color: ['#00DFA6', '#4B80FF', '#FFC314', '#FF5858'].reverse(),
                },
                sortProps: (data: any) => {
                  const { latitudeListBeans } =
                    data?.statusAnalysis?.stateDistribution;
                  return {
                    data: latitudeListBeans?.map(
                      ({ name, percentage: value }: any) => ({
                        name,
                        value,
                      })
                    ),
                    options: {
                      tooltip: {
                        trigger: 'axis',
                        formatter: '{a} : {c}({d}%)',
                      },
                      legend: {
                        orient: 'vertical',
                        right: '5%',
                        bottom: '20%',
                        itemHeight: 14,
                        itemWidth: 14,
                        icon: 'rect',
                        textStyle: {
                          fontSize: 18,
                        },
                        data: latitudeListBeans?.map(({ name }: any) => name),
                      },
                      series: [
                        {
                          type: 'pie',
                          radius: '60%',
                          center: ['50%', '50%'],
                          selectedMode: 'single',
                          data: latitudeListBeans?.map(
                            ({ name, num: value, percentage: percent }: any) => {
                              return {
                                name,
                                value,
                                percent,
                              };
                            }
                          ),
                          label: {
                            fontSize: 18,
                            position: 'outside',
                            color: '#001139',
                            formatter: (params: {
                              name: string,
                              percent: string,
                            }) => {
                              return params.name + '\n' + params.percent + '%';
                            },
                          },
                          itemStyle: {
                            borderRadius: 3,
                            borderColor: '#fff',
                            borderWidth: 1,
                          },
                        },
                      ],
                    },
                  };
                },
              },
              {
                sortProps: (data: any) => {
                  console.log({ data });
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        <ol style={{ marginBottom: ' 32px' }}>
                          <li>
                            从结果来看，本区县学生在心理健康风险上的状态分布：
                          </li>
                          {data?.statusAnalysis?.stateDistribution?.latitudeListBeans.map(
                            (item: any) => (
                              <li
                                key={Math.random()}
                                style={{ display: 'flex' }}
                              >
                                <i />
                                {item.name}心理健康风险学生人数为
                                <span>{item.num}</span>
                                ,占比
                                <p style={{ fontWeight: 'bold' }}>
                                  {item.percentage}
                                </p>
                                %
                              </li>
                            )
                          )}
                        </ol>
                      </div>
                    ),
                  };
                },
              },
              {
                type: '',
                props: {
                  content: <DotBar title={`各学校风险人数占比对比`} />
                }
              },
              {
                sortProps: (data: any) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        <ol style={{ marginBottom: ' 32px' }}>
                          <li>
                            从结果来看，本区县学生在心理健康风险上的状态分布：
                          </li>
                          <li dangerouslySetInnerHTML={{
                            __html: data?.statusAnalysis?.stateDistribution?.inConclusion
                          }}></li>

                        </ol>
                      </div>
                    ),
                  };
                },
              },
              {
                type: '',
                props: {
                  content: <DotBar title={`各学校风险人数统计`} />
                }
              },
              {
                type: 'SearchTable',
                props: {
                  hideSearch: true,
                  wrapperStyle: {
                    margin: '20px 10px',
                  },
                  wrapperClass: styles.customTable,
                },
                sortProps: (data: any) => {
                  const {
                    areaFactorWarningVOS
                  } = data?.statusAnalysis;
                  const columns = [
                    {
                      title: '学校',
                      dataIndex: 'schoolName',
                      align: 'center',
                    },
                    {
                      title: '预警人数(去重)',
                      dataIndex: 'num',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'percentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '高风险',
                      dataIndex: 'highRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'highRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '较高风险',
                      dataIndex: 'higherRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'higherRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '轻微风险',
                      dataIndex: 'minorRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'minorRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                  ];
                  return {
                    tableProps: {
                      columns,
                      dataSource: areaFactorWarningVOS,
                      pagination: false,
                      border: true,
                      rowKey: 'latitudeId',
                      hidePagination: true,
                    },
                  };
                },
              },
              // 各学校预警统计
              {
                type: '',
                props: {
                  content: <DotBar title={`预警学生名单`} />
                }
              },
              {
                type: 'SearchTable',
                props: {
                  hideSearch: true,
                  wrapperStyle: {
                    margin: '20px 10px',
                  },
                  wrapperClass: styles.customTable,
                },
                sortProps: (data: any) => {

                  const {
                    factorWarningVOS
                  } = data?.statusAnalysis;
                  const columns = [
                    {
                      title: '学校级',
                      dataIndex: 'name',
                      align: 'center',
                    },
                    {
                      title: '预警人数',
                      dataIndex: 'warningNum',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'percentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '高风险',
                      dataIndex: 'highRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'highRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '较高风险',
                      dataIndex: 'higherRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'higherRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '轻微风险',
                      dataIndex: 'minorRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'minorRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                  ];
                  return {
                    tableProps: {
                      columns,
                      dataSource: factorWarningVOS,
                      pagination: false,
                      border: true,
                      rowKey: 'latitudeId',
                      hidePagination: true,
                    },
                  };
                },
              },
              // 预警学生名单
              {
                type: '',
                props: {
                  content: <DotBar title={`预警学生名单`} />
                }
              },


              // 心理健康因子分析雷达
              {
                type: 'SmallIconTitle',
                props: {
                  title: '心理健康因子分析',
                  themeColor: 'rgba(75, 128, 255)',
                  icon: true,
                  wrapperStyle: { margin: '30px 0px 0px -20px' },
                },
              },
              {
                type: 'ChartForRadar',
                props: {
                  id: `ChartForRadar1`,
                  contentStyle: { width: '100%', margin: '20px auto 40px' },
                  height: '400px',
                },
                sortProps: (data: any) => {
                  const {
                    resultAnalysis: { latitudeList = [] },
                  } = data?.statusAnalysis;
                  const dataName: any[] = [];
                  const dataVaule: number[] = [];
                  const dataMax: number[] = [];
                  const dataWarnnig: number[] = [];
                  latitudeList?.forEach((v: any, key: number) => {
                    dataName.unshift({
                      name: v.name,
                      max: 2,
                    });
                    dataVaule.unshift(v.score);
                    dataMax.unshift(v.referenceScore);
                    dataWarnnig.unshift(v.warnningScore);
                  });
                  return {
                    legend: {
                      data: ['得分', '参考分', '预警分'],
                      textStyle: {
                        fontSize: 14,
                        verticalAlign: 'bottom',
                      },
                      bottom: 0,
                    },
                    options: {
                      radar: [
                        {},
                        {
                          indicator: dataName,
                          center: ['50%', '46%'],
                          radius: 120,
                          startAngle: 125,
                          name: {
                            textStyle: {
                              color: '#001139',
                              fontSize: 18,
                            },
                          },
                          splitArea: {
                            areaStyle: {
                              color: ['#fff', '#F2F6FF'].reverse(),
                            },
                          },
                          axisLine: {
                            lineStyle: {
                              color: '#E0ECFF',
                            },
                          },
                          splitLine: {
                            show: true,
                            lineStyle: {
                              color: '#E0ECFF',
                            },
                          },
                        },
                      ],
                      series: [
                        {
                          name: '成绩单',
                          type: 'radar',
                          radarIndex: 1,
                          symbol: 'none',
                          data: [
                            {
                              type: 'radar',
                              value: dataVaule,
                              name: '得分',
                              itemStyle: {
                                color: '#4B80FF',
                                fontSize: 18,
                              },
                              areaStyle: {
                                normal: {
                                  color: '#4B80FF',
                                },
                              },
                            },
                            {
                              value: dataMax,
                              type: 'radar',
                              name: '参考分',
                              itemStyle: {
                                color: '#FFA119',
                                borderColor: '#FFA119',
                                borderType: 'solid',
                                borderWidth: '3px',
                                backGroundColor: '#fff',
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                            {
                              value: dataWarnnig,
                              type: 'radar',
                              name: '预警分',
                              itemStyle: {
                                color: '#FF3C19',
                                borderColor: '#FF3C19',
                                borderType: 'solid',
                                fontSize: 18,
                              },
                              lineStyle: {
                                width: 3,
                              },
                            },
                          ],
                        },
                      ],
                    },
                  };
                },
              },
              {
                sortProps: (data: any) => {
                  return {
                    content: (
                      <div
                        style={{
                          fontSize: '18px',
                          padding: '30px 0 0 20px',
                          lineHeight: 1.5,
                        }}
                      >
                        {' '}
                        {data?.statusAnalysis?.resultAnalysis?.inConclusion}
                        <EntranceFactorTable
                          type={false}
                          version={2}
                          factorAnalysis={
                            data?.statusAnalysis?.resultAnalysis || {}
                          }
                        />
                      </div>
                    ),
                  };
                },

              },
              {
                type: '',
                props: {
                  content: <DotBar title={`各因子预警统计`} />
                }
              },
              {
                type: 'SearchTable',
                props: {
                  hideSearch: true,
                  wrapperStyle: {
                    margin: '20px 10px',
                  },
                  wrapperClass: styles.customTable,
                },
                sortProps: (data: any) => {

                  const {
                    factorWarning: { latitudeList }
                  } = data?.statusAnalysis;
                  const columns = [
                    {
                      title: ``,
                      dataIndex: 'name',
                      align: 'center',
                    },
                    {
                      title: '预警人数',
                      dataIndex: 'num',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'percentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '高风险',
                      dataIndex: 'highRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'highRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '较高风险',
                      dataIndex: 'higherRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'higherRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                    {
                      title: '轻微风险',
                      dataIndex: 'minorRisk',
                      align: 'center',
                    },
                    {
                      title: '占比',
                      dataIndex: 'minorRiskPercentage',
                      align: 'center',
                      render: (value: number) => <span>{value}%</span>,
                    },
                  ];
                  return {
                    tableProps: {
                      columns,
                      dataSource: latitudeList,
                      pagination: false,
                      border: true,
                      rowKey: 'latitudeId',
                      hidePagination: true,
                    },
                  };
                },
              },
              {
                sortList: (data: any) => {
                  const { factorAnalysisDetails = [] } = data?.statusAnalysis;
                  const renderResult: any[] = []
                  const titleStyle = {
                    fontWeight: 700,
                    fontSize: 18,
                    color: '#475370',
                    margin: '20px 0'
                  }
                  if (factorAnalysisDetails?.length > 0) {
                    factorAnalysisDetails?.forEach((v: any) => {
                      const mentalColor = EntranceFactorColors[(v?.level || 1) - 1]
                      const { latitudeListBeans } = v?.pieChartVOS;
                      renderResult.push({
                        type: 'Card',
                        props: {
                          wrapperStyle: {
                            marginTop: 30,
                            boxShadow: "0px 0px 20px 0px #e6ece9"
                          },
                        },
                        list: [
                          {
                            type: 'StatusBar',
                            props: {
                              themeColor: mentalColor,
                              title: <div style={{
                                color: "#fff"
                              }}>{v.name}</div>,
                              right: (
                                <div
                                  style={{
                                    fontSize: '22px',
                                    fontWeight: 700,
                                    color: '#475370',
                                    display: 'flex',
                                  }}
                                >
                                  <p
                                    style={{
                                      color: v.latitudeName?.includes(
                                        '睡眠'
                                      )
                                        ? '#475370'
                                        : EntranceFactorColors[
                                        (v.level || 1) - 1
                                        ],
                                    }}
                                  >
                                    {v.score}
                                  </p>
                                  /{v.totalScore}
                                  <span style={{ marginLeft: '40px' }}>
                                    {v.levelName}
                                  </span>
                                </div>
                              ),
                              content: (
                                <>
                                  <p style={{ ...titleStyle }}>★ 因子说明</p>
                                  <p style={{ fontSize: "18px", lineHeight: 1.5, color: "rgba(0, 17, 57, 0.85)" }}>{v.factorDescription}</p>
                                  <p style={{ ...titleStyle }}>★ 学生情况</p>
                                  <p style={{ fontSize: "18px", lineHeight: 1.5, color: "rgba(0, 17, 57, 0.85)" }}>{v.studentPerformed}</p>
                                  <p style={{ ...titleStyle }}>★ 区县风险状态分布</p>
                                </>
                              )
                            },
                          },
                          {
                            type: 'ChartForPie',
                            id: 'ChartForPie3',
                            props: {
                              contentStyle: latitudeListBeans?.length
                                ? { width: '836px', height: '400px' }
                                : {},

                              color: ["#FF5858", "#FF9631", "#4B80FF", "#00DFA6"],
                              data: latitudeListBeans?.map(
                                ({ name, percentage: value }: any) => ({
                                  name,
                                  value,
                                })
                              ),
                              options: {
                                tooltip: {
                                  trigger: 'axis',
                                  formatter: '{a} : {c}({d}%)'
                                },
                                legend: {
                                  orient: 'vertical',
                                  right: '5%',
                                  bottom: '20%',
                                  itemHeight: 14,
                                  itemWidth: 14,
                                  icon: 'rect',
                                  textStyle: {
                                    fontSize: 18
                                  },
                                  data: latitudeListBeans?.map(
                                    ({ name }: any) => name
                                  ),
                                },
                                series: [
                                  {
                                    name: '心理状态分布',
                                    type: 'pie',
                                    radius: '65%',
                                    center: ['50%', '50%'],
                                    selectedMode: 'single',
                                    data: latitudeListBeans?.map(
                                      ({ name, num: value, percentage: percent }: any) => {
                                        return {
                                          name,
                                          value,
                                          percent
                                        }
                                      }
                                    ),
                                    label: {
                                      fontSize: 18,
                                      position: 'outside',
                                      color: '#001139',
                                      formatter: (params: { name: string; percent: string; }) => {
                                        return params.name + '\n' + params.percent + '%';
                                      }
                                    },
                                    itemStyle: {
                                      borderRadius: 3,
                                      borderColor: '#fff',
                                      borderWidth: 1
                                    },
                                  }
                                ]
                              },
                              content: (
                                <div>
                                  <div style={{ fontSize: "18px", fontWeight: 'normal', color: " rgba(0, 17, 57, 0.85)", lineHeight: '2' }}>
                                    <p >
                                      <b>
                                        在该因子上，本区县学生的风险状态分布：
                                      </b>
                                    </p>
                                    {latitudeListBeans?.map(
                                      (item: any) => {
                                        return (
                                          <p key={item.id} >
                                            ● &nbsp;{item.name}
                                            的学生人数为
                                            <b>{item.num}</b>
                                            人,占比
                                            <b>{item.percentage}%</b>;
                                          </p>

                                        );
                                      }
                                    )}
                                  </div>
                                </div>
                              ),
                            },
                          },
                          {
                            type: '',
                            props: {
                              content: (
                                <>
                                  <p style={{ ...titleStyle }}>★ 因子说明</p>
                                  <p style={{ fontSize: "18px", lineHeight: 1.5, color: "rgba(0, 17, 57, 0.85)" }}>{v.factorDescription}</p>
                                  <p style={{ ...titleStyle }}>★ 学生情况</p>
                                  <p style={{ fontSize: "18px", lineHeight: 1.5, color: "rgba(0, 17, 57, 0.85)" }}>{v.studentPerformed}</p>
                                  <p style={{ ...titleStyle }}>★ 区县风险状态分布</p>
                                </>
                              )
                            }
                          },
                          
                        ]
                      })
                    })
                  }
                  return renderResult
                }
              },

            ],
          },
          // 03学习准备度分析
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                boxShadow: '0px 0px 20px 0px #e6ece9',
                padding: '30px 20px 40px 20px',
                marginTop: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '03 学习准备度分析',
                  themeColor: 'rgba(75, 128, 255)',
                },

                list: [
                  {
                    type: 'SmallIconTitle',
                    props: {
                      title: '学习准备度指数',
                      themeColor: 'rgba(75, 128, 255)',
                      icon: true,
                      wrapperStyle: { margin: '30px 0px 0px -20px' },
                    },
                    sortProps: (data: any) => {
                      console.log({ data });
                      return {
                        content: (
                          <div
                            style={{
                              fontSize: '18px',
                              padding: '30px 0 0 20px',
                              lineHeight: 1.5,
                              display: 'flex',
                            }}
                          >
                            {' '}
                            <p style={{ fontWeight: 'bold', color: '#46526f' }}>
                              {data?.learningStatus?.riskIndex?.inConclusion}
                            </p>
                          </div>
                        ),
                      };
                    },
                  },
                  {
                    type: 'ChartForPie',
                    id: 'ChartForPie4',
                    props: {
                      content: <PieRemarks type={21} version={2} />,
                      id: 'ChartForPie4',
                      height: '350px',
                    },
                    sortProps: (data: any) => {
                      const { level, score, name } =
                        data?.learningStatus?.riskIndex;
                      return {
                        data: [
                          { value: score, name },
                          { value: 100 - score, name: '其他' },
                        ],
                        color: [
                          EntranceHandstandFactorColors[(level || 1) - 1],
                          '#eef1f0',
                        ],

                        options: {
                          series: [
                            {
                              name: '风险等级',
                              type: 'pie',
                              radius: ['67%', '80%'],
                              avoidLabelOverlap: false,
                              label: {
                                show: true,
                                position: 'center',
                                formatter: [
                                  '{a|学习准备度}',
                                  `{b|${name}}`,
                                  `{c|${score}}`,
                                ].join('\n'),
                                rich: {
                                  a: {
                                    color: '#7D869A',
                                    lineHeight: 24,
                                    fontSize: 18,
                                    fontFamily: 'MicrosoftYaHeiUI',
                                    height: 24,
                                  },
                                  b: {
                                    color:
                                      EntranceHandstandFactorColors[
                                      (level || 1) - 1
                                      ],
                                    lineHeight: 74,
                                    fontSize: 40,
                                    fontWeight: 600,
                                    fontFamily:
                                      'DINAlternate, DINAlternate- Bold',
                                    height: 74,
                                  },
                                  c: {
                                    color:
                                      EntranceHandstandFactorColors[
                                      (level || 1) - 1
                                      ],
                                    lineHeight: 24,
                                    fontSize: 24,
                                    fontFamily: 'MicrosoftYaHeiUI',
                                    height: 24,
                                  },
                                },
                              },
                              emphasis: {
                                label: {
                                  show: true,
                                  fontSize: '40',
                                  fontWeight: 'bold',
                                },
                              },
                              labelLine: {
                                show: false,
                              },
                            },
                          ],
                        },
                      };
                    },
                  },
                  {
                    type: 'SmallIconTitle',
                    props: {
                      title: '学习准备状态分布',
                      themeColor: 'rgba(75, 128, 255)',
                      icon: true,
                      wrapperStyle: { margin: '30px 0px 0px -20px' },
                    },
                    sortProps: (data: any) => {
                      return {
                        content: (
                          <div
                            style={{
                              fontSize: '18px',
                              padding: '30px 0 0 20px',
                              lineHeight: 1.5,
                            }}
                          >
                            {' '}
                            {data?.studyReadinessAnalysis?.inConclusion}
                          </div>
                        ),
                      };
                    },
                  },
                  {
                    type: 'ChartForPie',
                    props: {
                      id: 'ChartForPie5',
                      height: '400px',
                      color: [
                        '#00DFA6',
                        '#4B80FF',
                        '#FFC314',
                        '#FF5858',
                      ].reverse(),
                    },
                    sortProps: (data: any) => {
                      const { latitudeListBeans } =
                        data?.learningStatus?.pieResultAnalysis;
                      return {
                        data: latitudeListBeans?.map(
                          ({ name, percentage: value }: any) => ({
                            name,
                            value,
                          })
                        ),
                        options: {
                          tooltip: {
                            trigger: 'axis',
                            formatter: '{a} : {c}({d}%)',
                          },
                          legend: {
                            orient: 'vertical',
                            right: '5%',
                            bottom: '20%',
                            itemHeight: 14,
                            itemWidth: 14,
                            icon: 'rect',
                            textStyle: {
                              fontSize: 18,
                            },
                            data: latitudeListBeans?.map(({ name }: any) => name),
                          },
                          series: [
                            {
                              type: 'pie',
                              radius: '60%',
                              center: ['50%', '50%'],
                              selectedMode: 'single',
                              data: latitudeListBeans?.map(
                                ({ name, num: value, percentage: percent }: any) => {
                                  return {
                                    name,
                                    value,
                                    percent,
                                  };
                                }
                              ),
                              label: {
                                fontSize: 18,
                                position: 'outside',
                                color: '#001139',
                                formatter: (params: {
                                  name: string,
                                  percent: string,
                                }) => {
                                  return (
                                    params.name + '\n' + params.percent + '%'
                                  );
                                },
                              },
                              itemStyle: {
                                borderRadius: 3,
                                borderColor: '#fff',
                                borderWidth: 1,
                              },
                            },
                          ],
                        },
                      };
                    },
                  },
                  {
                    keyList: ['studyReadinessAnalysis'],
                    sortProps: (data: any) => {
                      const { latitudeListBeans } =
                        data?.learningStatus?.pieResultAnalysis;
                      return {
                        content: (
                          <div
                            style={{
                              fontSize: '18px',
                              padding: '30px 0 0 20px',
                              lineHeight: 1.5,
                            }}
                          >
                            <DotBar
                              title={`完全没做好学习准备的学生人数为<span class='bolder'>${latitudeListBeans[0] && latitudeListBeans[0].num
                                ? latitudeListBeans[0].num
                                : 0
                                }</span>人,占比<span class='bolder'>${latitudeListBeans[0] && latitudeListBeans[0]
                                  ? latitudeListBeans[0].percentage
                                  : 0
                                }%</span>;`}
                            />
                            <DotBar
                              title={`只为学习做了一点点准备的学生占比为<span class='bolder'>${latitudeListBeans[1] && latitudeListBeans[1].num
                                ? latitudeListBeans[1].num
                                : 0
                                }</span>人,占比<span class='bolder'>${latitudeListBeans[1] && latitudeListBeans[1]
                                  ? latitudeListBeans[1].percentage
                                  : 0
                                }%</span>;`}
                            />
                            <DotBar
                              title={`已经为高中学习做了较多准备的学生占比为<span class='bolder'>${latitudeListBeans[2] && latitudeListBeans[2].num
                                ? latitudeListBeans[2].num
                                : 0
                                }</span>人,占比<span class='bolder'>${latitudeListBeans[2] && latitudeListBeans[2]
                                  ? latitudeListBeans[2].percentage
                                  : 0
                                }%</span>;`}
                            />
                            <DotBar
                              title={`已经为高中学习做了较为充分准备的学生占比为<span class='bolder'>${latitudeListBeans[3] && latitudeListBeans[3].num
                                ? latitudeListBeans[3].num
                                : 0
                                }</span>人,占比<span class='bolder'>${latitudeListBeans[3] && latitudeListBeans[3]
                                  ? latitudeListBeans[3].percentage
                                  : 0
                                }%</span>;`}
                            />
                          </div>
                        ),
                      };
                    },
                  },
                  {
                    type: 'SmallIconTitle',
                    props: {
                      title: '学习准备度情况分析',
                      themeColor: 'rgba(75, 128, 255)',
                      icon: true,
                      wrapperStyle: { margin: '30px 0px 0px -20px' },
                    },
                    sortProps: (data: any) => {
                      return {
                        content: <DotBar title={`各学校级待提升人数`} />,
                      };
                    },
                  },
                  {
                    type: 'SearchTable',
                    props: {
                      hideSearch: true,
                      wrapperStyle: {
                        margin: '20px 10px',
                      },
                      wrapperClass: styles.customTable,
                    },
                    sortProps: (data: any) => {
                      const {
                        classStudyReadiness
                      } = data?.learningStatus;
                      const columns = [
                        {
                          title: '学校级',
                          dataIndex: 'name',
                          align: 'center',
                        },
                        {
                          title: '待提升人数(去重)',
                          dataIndex: 'warningNum',
                          align: 'center',
                        },
                        {
                          title: '占比',
                          dataIndex: 'percentage',
                          align: 'center',
                          render: (value: number) => <span>{value}%</span>,
                        },
                        {
                          title: '低准备度',
                          dataIndex: 'highRisk',
                          align: 'center',
                        },
                        {
                          title: '占比',
                          dataIndex: 'highRiskPercentage',
                          align: 'center',
                          render: (value: number) => <span>{value}%</span>,
                        },
                        {
                          title: '较低准备度',
                          dataIndex: 'higherRisk',
                          align: 'center',
                        },
                        {
                          title: '占比',
                          dataIndex: 'higherRiskPercentage',
                          align: 'center',
                          render: (value: number) => <span>{value}%</span>,
                        },
                      ];
                      return {
                        tableProps: {
                          columns,
                          dataSource: classStudyReadiness,
                          pagination: false,
                          border: true,
                          rowKey: 'latitudeId',
                          hidePagination: true,

                        },
                      };
                    },
                  },
                  {
                    sortProps: (data: any) => {
                      return {
                        content: <DotBar title={`待提升学生名单`} />,
                      };
                    },
                  },
                  {
                    type: 'SmallIconTitle',
                    props: {
                      title: '学习准备度因子分析',
                      themeColor: 'rgba(75, 128, 255)',
                      icon: true,
                      wrapperStyle: { margin: '30px 0px 0px -20px' },
                    },
                    sortProps: (data: any) => {
                      return {
                        content: (
                          <div
                            style={{
                              fontSize: '18px',
                              padding: '30px 0 0 20px',
                              lineHeight: 1.5,
                            }}
                          >
                            {' '}
                            {data?.learningStatus?.resultAnalysis?.inConclusion}
                          </div>
                        ),
                      };
                    },
                  },
                  {
                    type: 'ChartForRadar',
                    props: {
                      id: 'ChartForRadar2',
                      height: '350px',
                    },
                    sortProps: (data: any) => {
                      const {
                        resultAnalysis: { latitudeList = [] },
                      } = data?.learningStatus;
                      const dataName: any[] = [];
                      const dataVaule: number[] = [];
                      const dataMax: number[] = [];
                      const dataWarnnig: number[] = [];
                      latitudeList?.forEach((v: any, key: number) => {
                        dataName.unshift({
                          name: v.name,
                          max: 20,
                        });
                        dataVaule.unshift(v.score);
                        dataMax.unshift(v.referenceScore);
                        dataWarnnig.unshift(v.warnningScore);
                      });
                      return {
                        legend: {
                          data: ['得分', '参考分', '预警分'],
                          textStyle: {
                            fontSize: 14,
                            verticalAlign: 'bottom',
                          },
                          bottom: 0,
                        },
                        options: {
                          radar: [
                            {},
                            {
                              indicator: dataName,
                              center: ['50%', '46%'],
                              radius: 120,
                              startAngle: 125,
                              name: {
                                textStyle: {
                                  color: '#001139',
                                  fontSize: 18,
                                },
                              },
                              splitArea: {
                                areaStyle: {
                                  color: ['#fff', '#F2F6FF'].reverse(),
                                },
                              },
                              axisLine: {
                                lineStyle: {
                                  color: '#E0ECFF',
                                },
                              },
                              splitLine: {
                                show: true,
                                lineStyle: {
                                  color: '#E0ECFF',
                                },
                              },
                            },
                          ],
                          series: [
                            {
                              name: '成绩单',
                              type: 'radar',
                              radarIndex: 1,
                              symbol: 'none',
                              data: [
                                {
                                  type: 'radar',
                                  value: dataVaule,
                                  name: '得分',
                                  itemStyle: {
                                    color: '#4B80FF',
                                    fontSize: 18,
                                  },
                                  areaStyle: {
                                    normal: {
                                      color: '#4B80FF',
                                    },
                                  },
                                },
                                {
                                  value: dataMax,
                                  type: 'radar',
                                  name: '参考分',
                                  itemStyle: {
                                    color: '#FFA119',
                                    borderColor: '#FFA119',
                                    borderType: 'solid',
                                    borderWidth: '3px',
                                    backGroundColor: '#fff',
                                    fontSize: 18,
                                  },
                                  lineStyle: {
                                    width: 3,
                                  },
                                },
                                {
                                  value: dataWarnnig,
                                  type: 'radar',
                                  name: '预警分',
                                  itemStyle: {
                                    color: '#FF3C19',
                                    borderColor: '#FF3C19',
                                    borderType: 'solid',
                                    fontSize: 18,
                                  },
                                  lineStyle: {
                                    width: 3,
                                  },
                                },
                              ],
                            },
                          ],
                        },
                      };
                    },
                  },
                  {
                    sortList: (data: any) => {
                      const { factorAnalysisDetails = [] } = data?.learningStatus;
                      const renderResult: any[] = []
                      const titleStyle = {
                        fontWeight: 700,
                        fontSize: 18,
                        color: '#475370',
                        margin: '20px 0'
                      }
                      if (factorAnalysisDetails?.length > 0) {
                        factorAnalysisDetails?.forEach((v: any) => {
                          const readyColor = EntranceHandstandFactorColors[(v?.level || 1) - 1]
                          // const { latitudeListBeans } = v?.pieChartVOS;
                          renderResult.push({
                            type: 'Card',
                            props: {
                              wrapperStyle: {
                                marginTop: 30,
                                boxShadow: "0px 0px 20px 0px #e6ece9"
                              },
                            },
                            list: [
                              {
                                type: 'StatusBar',
                                props: {
                                  themeColor: readyColor,
                                  title: <div style={{
                                    color: "#fff"
                                  }}>{v.name}</div>,
                                  right: (
                                    <div
                                      style={{
                                        fontSize: '22px',
                                        fontWeight: 700,
                                        color: '#475370',
                                        display: 'flex',
                                      }}
                                    >
                                      <p
                                        style={{
                                          color: v.latitudeName?.includes(
                                            '睡眠'
                                          )
                                            ? '#475370'
                                            : EntranceHandstandFactorColors[
                                            (v?.level || 1) - 1
                                            ],
                                        }}
                                      >
                                        {v.score}
                                      </p>
                                      /{v.totalScore}
                                      <span style={{ marginLeft: '40px' }}>
                                        {v.levelName}
                                      </span>
                                    </div>
                                  ),
                                  content: (
                                    <>
                                      <p style={{ ...titleStyle }}>★ 因子说明</p>
                                      <p style={{ fontSize: "18px", lineHeight: 1.5, color: "rgba(0, 17, 57, 0.85)" }}>{v.factorDescription}</p>
                                      <p style={{ ...titleStyle }}>★ 学生情况</p>
                                      <p style={{ fontSize: "18px", lineHeight: 1.5, color: "rgba(0, 17, 57, 0.85)" }}>{v.studentPerformed}</p>
                                      <p style={{ ...titleStyle }}>★ 辅导建议</p>
                                      <p style={{ fontSize: "18px", lineHeight: 1.5, color: "rgba(0, 17, 57, 0.85)" }}>{v.counselingAdvice
                                      }</p>

                                    </>
                                  )
                                },
                              },

                            ]
                          })
                        })
                      }
                      return renderResult
                    }
                  },
                ],
                sortList: (data: any) => {
                  const { readinessDetailsBeans = [] } = data?.learningStatus;
                  const renderResult: any[] = [];
                  const titleStyle = {
                    fontWeight: 700,
                    fontSize: 18,
                    color: '#475370',
                    margin: '20px 0',
                  };
                  if (readinessDetailsBeans?.length > 0) {
                    readinessDetailsBeans.forEach((v: any) => {
                      const readyColor =
                        EntranceHandstandFactorColors[(v?.level || 1) - 1];
                      renderResult.push({
                        type: 'Card',
                        props: {
                          wrapperStyle: {
                            marginTop: 30,
                            boxShadow: '0px 0px 20px 0px #e6ece9',
                          },
                          list: []
                        },
                        list: [
                          {
                            type: 'StatusBar',
                            props: {
                              themeColor: readyColor,
                              title: v.name,
                              right: (
                                <div
                                  style={{
                                    fontSize: '22px',
                                    fontWeight: 700,
                                    color: '#475370',
                                    display: 'flex',
                                  }}
                                >
                                  <p
                                    style={{
                                      color: v.latitudeName?.includes('睡眠')
                                        ? '#475370'
                                        : EntranceHandstandFactorColors[
                                        (v.level || 1) - 1
                                        ],
                                    }}
                                  >
                                    {v.score}
                                  </p>
                                  /{v.totalScore}
                                  <span style={{ marginLeft: '40px' }}>
                                    {v.levelName}
                                  </span>
                                </div>
                              ),
                              content: (
                                <>
                                  <p style={{ ...titleStyle }}>★ 因子说明</p>
                                  <p
                                    style={{
                                      fontSize: '18px',
                                      lineHeight: 1.5,
                                      color: 'rgba(0, 17, 57, 0.85)',
                                    }}
                                  >
                                    {v.factorDesc}
                                  </p>
                                  <p style={{ ...titleStyle }}>★ 学生情况</p>
                                  <p
                                    style={{
                                      fontSize: '18px',
                                      lineHeight: 1.5,
                                      color: 'rgba(0, 17, 57, 0.85)',
                                    }}
                                  >
                                    {v.studentPerformed}
                                  </p>
                                  <p style={{ ...titleStyle }}>★ 辅导建议</p>
                                  <p
                                    style={{
                                      fontSize: '18px',
                                      lineHeight: 1.5,
                                      color: 'rgba(0, 17, 57, 0.85)',
                                    }}
                                  >
                                    {v.counselingAdvice}
                                  </p>
                                </>
                              ),
                            },
                          },
                        ],
                      });
                    });
                  }
                  // console.log(renderResult)
                  return renderResult;
                },
              },
            ],
          },
          // 04性格类型分析
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                boxShadow: '0px 0px 20px 0px #e6ece9',
                padding: '30px 20px 40px 20px',
                marginTop: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '04 性格类型分析',
                  themeColor: 'rgba(75, 128, 255)',
                },
                sortProps: (data: any) => {
                  // return {
                  //     content:
                  //         <div style={{ fontSize: "18px", padding: "30px 0 0 20px", lineHeight: 1.5, display: "flex" }}> 从测评结果来看，该生的学习准备状态评级为：<p style={{ fontWeight: "bold", color: "#46526f", }}>{data.personalityTypeAnalysis.name}</p></div>,
                  // }
                },

                list: [
                  {
                    type: 'ChartForPie',
                    props: {
                      id: 'ChartForPie6',
                      height: '400px',
                      color: [
                        '#42DBB4',
                        '#779FFF',
                        '#FFA9A9',
                        '#F66D6E',
                        '#69D6FB',
                        '#FB8CFF',
                        '#FFD14B',
                        '#A86FFF',
                        '#906066',
                      ],
                    },
                    sortProps: (data: any) => {
                      const { latitudeListBeans } =
                        data?.personalityAnalysis?.pieChartVO;
                      return {
                        data: latitudeListBeans?.map(
                          ({ name, percentage: value }: any) => ({
                            name,
                            value,
                          })
                        ),
                        options: {
                          tooltip: {
                            trigger: 'axis',
                            formatter: '{a} : {c}({d}%)',
                          },
                          legend: {
                            orient: 'vertical',
                            right: '5%',
                            bottom: '20%',
                            itemHeight: 14,
                            itemWidth: 14,
                            icon: 'rect',
                            textStyle: {
                              fontSize: 18,
                            },
                            data: latitudeListBeans?.map(({ name }: any) => name),
                          },
                          series: [
                            {
                              type: 'pie',
                              radius: '60%',
                              center: ['50%', '50%'],
                              selectedMode: 'single',
                              data: latitudeListBeans?.map(
                                ({ name, num: value, percentage: percent }: any) => {
                                  return {
                                    name,
                                    value,
                                    percent,
                                  };
                                }
                              ),
                              label: {
                                fontSize: 18,
                                position: 'outside',
                                color: '#001139',
                                formatter: (params: {
                                  name: string,
                                  percent: string,
                                }) => {
                                  return (
                                    params.name + '\n' + params.percent + '%'
                                  );
                                },
                              },
                              itemStyle: {
                                borderRadius: 3,
                                borderColor: '#fff',
                                borderWidth: 1,
                              },
                            },
                          ],
                        },
                      };
                    },
                  },
                  {
                    type: '',
                    sortList: (data: any) => {
                      const { analysisDetails = [] } =
                        data?.personalityAnalysis;
                      const renderResult: any[] = [];
                      const titleStyle = {
                        fontWeight: 700,
                        fontSize: 18,
                        color: '#475370',
                        margin: '20px 0',
                      };
                      if (analysisDetails?.length > 0) {
                        analysisDetails.forEach((v: any) => {
                          renderResult.push({
                            type: 'Card',
                            props: {
                              wrapperStyle: {
                                marginTop: 30,
                                boxShadow: '0px 0px 20px 0px #e6ece9',
                              },
                            },
                            list: [
                              {
                                type: 'StatusBar',
                                props: {
                                  themeColor: 'rgb(75, 128, 255)',
                                  title: v.name,
                                  right: (
                                    <div
                                      style={{
                                        fontSize: '22px',
                                        fontWeight: 700,
                                        color: '#475370',
                                        display: 'flex',
                                      }}
                                    >
                                      <p
                                        style={{
                                          color: v.latitudeName?.includes(
                                            '睡眠'
                                          )
                                            ? '#475370'
                                            : 'rgb(75, 128, 255)',
                                        }}
                                      ></p>
                                      学生占比
                                      <span style={{ marginLeft: '40px' }}>
                                        {v.percentage}%
                                      </span>
                                    </div>
                                  ),
                                  content: (
                                    <>
                                      <p style={{ ...titleStyle }}>
                                        ★ 性格特征
                                      </p>
                                      <p
                                        style={{
                                          fontSize: '18px',
                                          lineHeight: 1.5,
                                          color: 'rgba(0, 17, 57, 0.85)',
                                        }}
                                      >
                                        {v.characterTraits}
                                      </p>
                                      <p style={{ ...titleStyle }}>
                                        ★ 辅导建议
                                      </p>
                                      <p
                                        style={{
                                          fontSize: '18px',
                                          lineHeight: 1.5,
                                          color: 'rgba(0, 17, 57, 0.85)',
                                        }}
                                      >
                                        {v.counselingAdvice}
                                      </p>
                                    </>
                                  ),
                                },
                              },
                            ],
                          });
                        });
                      }
                      return renderResult;
                    },
                  },
                ],
              },
            ],
          },
          // 05家庭教养风格分析
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                boxShadow: '0px 0px 20px 0px #e6ece9',
                padding: '30px 20px 40px 20px',
                marginTop: '60px',
              },
            },
            list: [
              {
                type: 'CardTitleBar',
                props: {
                  title: '05 家庭教养风格分析',
                  themeColor: 'rgba(75, 128, 255)',
                },
                sortProps: (data: any) => { },
                list: [
                  {
                    type: 'SmallIconTitle',
                    props: {
                      title: '家庭基本信息统计',
                      themeColor: 'rgba(75, 128, 255)',
                      icon: true,
                      wrapperStyle: { marginTop: '30px' },
                    },
                    sortProps: (data: any) => {
                      // return {
                      //     content:
                      //         <div style={{ fontSize: "18px", padding: "20px 0 0 20px", lineHeight: 1.5, }}>{data.personalityTypeAnalysis.characteristicAnalysis}</div>,
                      // }
                    },
                  },
                  // 独生子女
                  {
                    type: 'ChartForPie',
                    props: {
                      id: `ChartForPie7`,
                      height: '500px',
                      color: EntranceStyleColors,
                      before: <DotBar title={`学生的独生子女情况分布`} />,
                    },
                    sortProps: (data: any) => {
                      const { onlyChild } = data?.familyUpbringingAnalysis;
                      return {
                        data: onlyChild?.map(({ name, num: value }: any) => ({
                          name,
                          value,
                        })),
                        options: {
                          tooltip: {
                            trigger: 'axis',
                            formatter: '{a} : {c}({d}%)',
                          },
                          legend: {
                            orient: 'vertical',
                            right: '5%',
                            bottom: '20%',
                            itemHeight: 14,
                            itemWidth: 14,
                            icon: 'rect',
                            textStyle: {
                              fontSize: 18,
                            },
                            data: onlyChild?.map(({ name }: any) => name),
                          },
                          series: [
                            {
                              type: 'pie',
                              radius: '60%',
                              center: ['50%', '50%'],
                              selectedMode: 'single',
                              data: onlyChild?.map(({ name, num: value }: any) => {
                                return {
                                  name,
                                  value,
                                };
                              }),
                              label: {
                                fontSize: 18,
                                position: 'outside',
                                color: '#001139',
                                formatter: (params: {
                                  name: string,
                                  percent: string,
                                }) => {
                                  return (
                                    params.name + '\n' + params.percent + '%'
                                  );
                                },
                              },
                              itemStyle: {
                                borderRadius: 3,
                                borderColor: '#fff',
                                borderWidth: 1,
                              },
                            },
                          ],
                        },
                      };
                    },
                  },
                  // 学生的日常生活环境分布
                  {
                    type: 'ChartForPie',
                    props: {
                      id: `ChartForPie8`,
                      height: '500px',
                      color: EntranceStyleColors,
                      before: <DotBar title={`学生的日常生活环境分布`} />,
                    },
                    sortProps: (data: any) => {
                      const { life } = data?.familyUpbringingAnalysis;
                      return {
                        data: life?.map(({ name, num: value }: any) => ({
                          name,
                          value,
                        })),
                        options: {
                          tooltip: {
                            trigger: 'axis',
                            formatter: '{a} : {c}({d}%)',
                          },
                          legend: {
                            orient: 'vertical',
                            right: '5%',
                            bottom: '20%',
                            itemHeight: 14,
                            itemWidth: 14,
                            icon: 'rect',
                            textStyle: {
                              fontSize: 18,
                            },
                            data: life?.map(({ name }: any) => name),
                          },
                          series: [
                            {
                              type: 'pie',
                              radius: '60%',
                              center: ['50%', '50%'],
                              selectedMode: 'single',
                              data: life?.map(({ name, num: value }: any) => {
                                return {
                                  name,
                                  value,
                                };
                              }),
                              label: {
                                fontSize: 18,
                                position: 'outside',
                                color: '#001139',
                                formatter: (params: {
                                  name: string,
                                  percent: string,
                                }) => {
                                  return (
                                    params.name + '\n' + params.percent + '%'
                                  );
                                },
                              },
                              itemStyle: {
                                borderRadius: 3,
                                borderColor: '#fff',
                                borderWidth: 1,
                              },
                            },
                          ],
                        },
                      };
                    },
                  },
                  // 父亲学历分布
                  {
                    type: 'ChartForPie',
                    props: {
                      id: `ChartForPie9`,
                      height: '500px',
                      color: EntranceStyleColors,
                      before: <DotBar title={`父亲学历分布`} />,
                    },
                    sortProps: (data: any) => {
                      const { fatherEducation } =
                        data?.familyUpbringingAnalysis;
                      return {
                        data: fatherEducation?.map(({ name, num: value }: any) => ({
                          name,
                          value,
                        })),
                        options: {
                          tooltip: {
                            trigger: 'axis',
                            formatter: '{a} : {c}({d}%)',
                          },
                          legend: {
                            orient: 'vertical',
                            right: '5%',
                            bottom: '20%',
                            itemHeight: 14,
                            itemWidth: 14,
                            icon: 'rect',
                            textStyle: {
                              fontSize: 18,
                            },
                            data: fatherEducation?.map(({ name }: any) => name),
                          },
                          series: [
                            {
                              type: 'pie',
                              radius: '60%',
                              center: ['50%', '50%'],
                              selectedMode: 'single',
                              data: fatherEducation?.map(
                                ({ name, num: value }: any) => {
                                  return {
                                    name,
                                    value,
                                  };
                                }
                              ),
                              label: {
                                fontSize: 18,
                                position: 'outside',
                                color: '#001139',
                                formatter: (params: {
                                  name: string,
                                  percent: string,
                                }) => {
                                  return (
                                    params.name + '\n' + params.percent + '%'
                                  );
                                },
                              },
                              itemStyle: {
                                borderRadius: 3,
                                borderColor: '#fff',
                                borderWidth: 1,
                              },
                            },
                          ],
                        },
                      };
                    },
                  },
                  // 母亲学历分布
                  {
                    type: 'ChartForPie',
                    props: {
                      id: `ChartForPie10`,
                      height: '500px',
                      color: EntranceStyleColors,
                      before: <DotBar title={`母亲学历分布`} />,
                    },
                    sortProps: (data: any) => {
                      const { motherEducation } =
                        data?.familyUpbringingAnalysis;
                      return {
                        data: motherEducation?.map(({ name, num: value }: any) => ({
                          name,
                          value,
                        })),
                        options: {
                          tooltip: {
                            trigger: 'axis',
                            formatter: '{a} : {c}({d}%)',
                          },
                          legend: {
                            orient: 'vertical',
                            right: '5%',
                            bottom: '20%',
                            itemHeight: 14,
                            itemWidth: 14,
                            icon: 'rect',
                            textStyle: {
                              fontSize: 18,
                            },
                            data: motherEducation?.map(({ name }: any) => name),
                          },
                          series: [
                            {
                              type: 'pie',
                              radius: '60%',
                              center: ['50%', '50%'],
                              selectedMode: 'single',
                              data: motherEducation?.map(
                                ({ name, num: value }: any) => {
                                  return {
                                    name,
                                    value,
                                  };
                                }
                              ),
                              label: {
                                fontSize: 18,
                                position: 'outside',
                                color: '#001139',
                                formatter: (params: {
                                  name: string,
                                  percent: string,
                                }) => {
                                  return (
                                    params.name + '\n' + params.percent + '%'
                                  );
                                },
                              },
                              itemStyle: {
                                borderRadius: 3,
                                borderColor: '#fff',
                                borderWidth: 1,
                              },
                            },
                          ],
                        },
                      };
                    },
                  },
                  // 家庭教养风格分布
                  {
                    type: 'ChartForPie',
                    props: {
                      id: `ChartForPie11`,
                      height: '500px',
                      color: EntranceStyleColors,
                      before: <DotBar title={`家庭教养风格分布`} />,
                    },
                    sortProps: (data: any) => {
                      const { parentingStyle } = data?.familyUpbringingAnalysis;
                      return {
                        data: parentingStyle?.map(({ name, num: value }: any) => ({
                          name,
                          value,
                        })),
                        options: {
                          tooltip: {
                            trigger: 'axis',
                            formatter: '{a} : {c}({d}%)',
                          },
                          legend: {
                            orient: 'vertical',
                            right: '5%',
                            bottom: '20%',
                            itemHeight: 14,
                            itemWidth: 14,
                            icon: 'rect',
                            textStyle: {
                              fontSize: 18,
                            },
                            data: parentingStyle?.map(({ name }: any) => name),
                          },
                          series: [
                            {
                              type: 'pie',
                              radius: '60%',
                              center: ['50%', '50%'],
                              selectedMode: 'single',
                              data: parentingStyle?.map(
                                ({ name, num: value }: any) => {
                                  return {
                                    name,
                                    value,
                                  };
                                }
                              ),
                              label: {
                                fontSize: 18,
                                position: 'outside',
                                color: '#001139',
                                formatter: (params: {
                                  name: string,
                                  percent: string,
                                }) => {
                                  return (
                                    params.name + '\n' + params.percent + '%'
                                  );
                                },
                              },
                              itemStyle: {
                                borderRadius: 3,
                                borderColor: '#fff',
                                borderWidth: 1,
                              },
                            },
                          ],
                        },
                      };
                    },
                  },
                  // 具体因子分析
                  {
                    type: '',
                    sortList: (data: any) => {
                      const { parentingStyle = [] } =
                        data?.familyUpbringingAnalysis;
                      const renderResult: any[] = [];
                      if (parentingStyle?.length > 0) {
                        parentingStyle.forEach((v: any) => {
                          renderResult.push({
                            type: 'Card',
                            props: {
                              wrapperStyle: {
                                marginTop: 30,
                                boxShadow: '0px 0px 20px 0px #e6ece9',
                              },
                            },
                            list: [
                              {
                                type: 'StatusBar',
                                props: {
                                  themeColor: 'rgb(75, 128, 255)',
                                  title: v.name,
                                  right: (
                                    <div
                                      style={{
                                        fontSize: '22px',
                                        fontWeight: 700,
                                        color: '#475370',
                                        display: 'flex',
                                      }}
                                    >
                                      <p
                                        style={{
                                          color: v.name?.includes('睡眠')
                                            ? '#475370'
                                            : 'rgb(75, 128, 255)',
                                        }}
                                      ></p>
                                      学生占比
                                      <span style={{ marginLeft: '40px' }}>
                                        {v.percentage}%
                                      </span>
                                    </div>
                                  ),
                                  content: (
                                    <>
                                      <p
                                        style={{
                                          fontSize: '18px',
                                          lineHeight: 1.5,
                                          color: 'rgba(0, 17, 57, 0.85)',
                                          paddingTop: '20px',
                                        }}
                                      >
                                        {v.description}
                                      </p>
                                    </>
                                  ),
                                },
                              },
                            ],
                          });
                        });
                      }
                      return renderResult;
                    },
                  },
                ],
              },
            ],
          },
          // 附加说明
          {
            type: 'THeaderControl',
            props: {
              title: '附加说明',
              subTitle: 'Additional Information',
              wrapperStyle: { margin: '60px 0px 30px' },
            },
          },
          {
            type: 'Card',
            props: {
              wrapperStyle: {
                marginBottom: 50,
                boxShadow: '0px 0px 20px 0px #e6ece9',
              },
            },
            sortProps: () => {
              const titleStyle = {
                lineHeight: '44px',
                fontWeight: 700,
                color: 'rgba(0, 17, 57, 0.85)',
                fontSize: 18,
              };
              const contentStyle = {
                lineHeight: '22px',
                color: 'rgba(0, 17, 57, 0.85)',
                fontSize: 18,
              };

              return {
                content: (
                  <div>
                    <h3 style={{ ...titleStyle }}>
                      1、 任何心理测评都存在一定误差
                    </h3>
                    <p style={{ ...contentStyle, marginBottom: 20 }}>
                      学生在测评时的情绪状态、对测评的态度等主观因素，以及测评过程中的环境干扰等客观因素都会影响测评结果。因此在阅读本测评报告时，请参照学生在日常生活的实际表现来理解。
                    </p>
                    <h3 style={{ ...titleStyle }}>
                      2、请勿将测评结果当作永久“标签”
                    </h3>
                    <p style={{ ...contentStyle }}>
                      请不要把本测评结果看成是对学生的“标签”或最终“宣判”，而应视为学生的阶段性状态。对于处在青春期的高中学生来说，其大多数心理和性格特征都是可变的，因此本测评结果仅为了解学生近期状态提供参考。
                    </p>
                  </div>
                ),
              };
            },
          },
        ],
      },
    ],
  },
];

export default tree;

