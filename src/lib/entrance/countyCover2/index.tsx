import { Button } from "antd";
import Image from 'next/image';
import moment from "moment";
import { schoolItem } from "~/pages/entrance/countyCover2/types";
import API, { IGetAreaCitySchoolEntranceListInfo } from '~/service/entrance/countyCover2';
import { objectToUrlParams } from "~/utils/tools";
import IconDownload from '@/assets/psychology/iconDownload.png';
import styles from '~/pages/entrance/countyCover2/index.module.less';

// 入学测-区县入口页面报告
const tree = [
  {
  type: 'page',
  isRealNode: true,
  props: {
    style: { width: '1000px', margin: '0 auto' },
  },
  list: [
    {
      type: 'SearchTable',
      props: {
        wrapperStyle: { margin: '60px 0px 30px' },
      },
      sortProps: (data: any) => {
        const { currentData, gradeTermList } = data;
        const { cityCode, areaCode, graduationYear, reportDateStr } = currentData;

        // 跳转到年级
        const JumpToCounty = (record: schoolItem) => {
          if (!record.gradeFlag) return;
          const params = {
            type: 2,
            cityCode,
            schoolCode: record.schoolCode,
            evaluationTaskId: record.answerTaskId,
            areaCode: record.areaCode,
            gradeId: graduationYear,
            reportDateStr
          }
          const link = `${location.origin}/psychology-service/psychology/gradeReport?${objectToUrlParams(params)}`
          window.open(link)
        }
        const columnsList = [
          { title: '区县', dataIndex: 'areaName', align: 'center' },
          { title: '学校', dataIndex: 'schoolName', align: 'center' },
          { title: '年级', dataIndex: 'gradeName', align: 'center' },
          { title: '布置人数', dataIndex: 'totalNum', align: 'center' },
          { title: '完成测评人数', dataIndex: 'finishedNum', align: 'center' },
          { title: '完成率', dataIndex: 'finishedPercentage', align: 'center', render: (value: number) => <span>{value}%</span> },
          { title: '布置日期', dataIndex: 'assignDate', align: 'center', render: (value: number) => <span>{value ? `${moment(value).format('YYYY-MM-DD')}` : ''}</span> },
          { title: '截止日期', dataIndex: 'endDate', align: 'center', render: (value: number) => <span>{value ? `${moment(value).format('YYYY-MM-DD')}` : ''}</span> },
          {
            title: '集体报告', dataIndex: 'operation', align: 'center', render: (_: any, record: schoolItem, key: any) => {
              return (
                <span key={record.areaCode}>
                  {key ?
                    <a
                      onClick={() => {
                        JumpToCounty(record);
                      }}
                      style={{
                        textDecoration: record.gradeFlag ? 'underline' : '',
                        color: record.gradeFlag ? 'rgba(250,140,22,1)' : 'rgba(0,17,57,0.85)',
                        cursor: record.gradeFlag ? 'pointer' : 'text',
                      }}
                    >
                      查看集体报告
                    </a> : ''
                  }
                </span>)
            }
          }]
        return {
          searchForm: {
            layout: 'inline',
            columns: [
              {
                type: 'cascader',
                key: 'graduationYear,reportDateStr',
                isImmediate: true,
                formItemProps: {
                  label: '报告覆盖人群',
                },
                props: {
                  style: {
                    width: 300
                  },
                  options: gradeTermList,
                },
              },
            ],
            initialValues: {'graduationYear,reportDateStr':[gradeTermList[0].value, gradeTermList[0].children[0].value]},
            hideSubmit: true,
            showReset: false,
          },
          tableProps: {
            wrapperClass: styles.customTable,
            params: {
              areaCode,
              cityCode,
              type: 2,
              clientType: 1,
              graduationYear,
              reportDateStr,
            },
            request: API.getAreaCitySchoolEntranceListInfo,
            sortParams: ({
              ...rest
            } : {
              [key: string]: any;
            }): Partial<IGetAreaCitySchoolEntranceListInfo['params']> => ({
              ...rest,
            }),
            sortData: ({
              data: { schoolList: dataSource },
            }: IGetAreaCitySchoolEntranceListInfo['data']) => ({
              dataSource,
            }),
            rowKey: 'schoolCode',
            columns: columnsList,
            hidePagination: true,
          }
        }
      }
    },
  ]
}];
export default tree;
