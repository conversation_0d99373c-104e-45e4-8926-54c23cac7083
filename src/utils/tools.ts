import Cookie from "cookie";
import isMobile from 'is-mobile';
import { web, IS_SIT } from './hosts';
import { IAddressItem } from "~/@types";

/** 过滤掉 为 false 的 */
export const cls = (names: Array<string | false | undefined>) =>
  names.filter((v) => v).join(' ');

/** 获取hash后面的url参数  */
export function getUrlParams<T extends object>(
  str: string,
  key: string[] | string | Array<keyof T>
): any {
  const searchObj = new URLSearchParams(str);
  if (typeof key === 'string') {
    return searchObj.get(key);
  } else {
    const res: { [key: string]: string | null } = {};
    key.forEach((v) => {
      res[v as string] = searchObj.get(v as string);
    });
    return res;
  }
}

// 对象转URL参数
export const objectToUrlParams = (param: { [x: string]: any; }) => {
  return Object.keys(param).map(key => `${key}=${param[key] != null || param[key] != undefined ? param[key] : ''}`).join('&');
};

// Url参数转对象
export const UrlParamsToObject = (url: string) => {
  if (!url) return {};
  let obj:any = {};
  url.substring(url.indexOf('?') + 1).trim().split('&').forEach(item => obj[item.split('=')[0]] = item.split('=')[1]);
  return obj;
}

// 和web项目不一样，后端重定向次数过多浏览器会报错，需要直接到登陆页
export const reLaunchToLogin = (url: string) => {
  // 重新登录之前需删除url上token,否则会循环登录
  let currentUrl = url;
  const urlParams = UrlParamsToObject(url);
  if (url.includes('token')) {
    delete urlParams.token;
    currentUrl = `${url.split("?")[0]}?${objectToUrlParams(urlParams)}`
  }
  // 特殊场景跳转特殊页面进行额外登录逻辑处理
  if (urlParams.sourceType) {
    return `${web}/psychology-service/common/wait-login?sourceType=${urlParams.sourceType}&fromurl=${encodeURIComponent(currentUrl)}`;
  }
  return `${web}/register/#/login?sid=${IS_SIT ? 14 : 16}&fromurl=${encodeURIComponent(currentUrl)}`;;
};


export const subscribe = () => {
  let topics: {
    [msg: string]: { token: string; func: (...arg: any) => any }[];
  } = {};
  let subUid = -1;
  const q = {
    publish: (topic: string, args: any) => {
      if (!topics[topic]) return;
      const subs = topics[topic];
      let len = subs.length;
      while (len--) {
        subs[len].func(topic, args);
      }
      return this;
    },
    subscribe: (topic: string, func: (...arg: any) => any) => {
      topics[topic] = topics[topic] ? topics[topic] : [];
      const token = (++subUid).toString();
      topics[topic].push({ token, func });
      return token;
    },
    clear: () => {
      topics = {};
    },
  };
  return q;
};

// GUID
export const guid = (len?: number, radix?: number) => {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  const uuid = [];
  let i;
  const cardinality = radix || chars.length;

  if (len) {
    for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * cardinality)];
  } else {
    let r;
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (Math.random() * 16);
        uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
      }
    }
  }
  return uuid.join('');
};

/**
 * description: 对象转URL参数
 * @param {object} params 待转对象
 * @returns {string} 转化后结果
 */
export const queryParams = (params: {
  [key: string]: string | number | boolean | null | undefined;
}): string => {
  const arr: string[] = [];
  const keys = Object.keys(params);
  keys.forEach((item) => {
    if (
      params[item] !== null &&
      params[item] !== '' &&
      params[item] !== undefined
    ) {
      arr.push(
        `${item}=${encodeURIComponent(
          params[item] as string | number | boolean
        )}`
      );
    }
  });
  return arr.join('&');
};

// 获取token信息
export const getToken = () => {
  const authToken = getUrlParams(location.search, 'token');
  if (authToken) return authToken;

  const { token, ewt_user, user } = Cookie.parse(document.cookie);
  if (token) return token;

  const str = 'tk'
  const reg = new RegExp("(^|&)" + str + "=([^&]*)(&|$)", "i");
  const r = (ewt_user || user || '').substr(1).match(reg);
  if (r != null) {
    return r[2]
  }
  return token;
}

// 获取登陆者的userId
export const getUserId = (tk: string) => {
  if (tk) {
    const tokenKeys = tk.split('-');
    return tokenKeys[0] || '';
  }
  return '';
};

/**
 * 调用pst打印程序
 * @param { object } 延迟多长时间执行（默认毫秒）
 */
export const mountPSTScreenshot = ({ currentUrl, delay } : {currentUrl: string; delay: number}) => {
  console.log(`等待${delay}毫秒，执行打印函数`);
  setTimeout(() => {
    try {
      // 注意： PSTScreenshotHook 方法与 打印请求 activeTriggerFuncName 传参值必须与之相同，才可调用
      if (window.PSTScreenshotHook) {
         window.PSTScreenshotHook({
          status: true
        });
        console.log('调用PSTScreenshotHook函数成功！');
      } else {
        console.log('未找到PSTScreenshotHook！执行PSTScreenshot函数失败');
        window.PSTScreenshot();  // 使用PSTScreenshotHook该方法, 无回调
      }
    } catch (err) {
      console.error(`该页面${currentUrl}调用打印函数出错`, err);
    }
  }, delay);
};

// 用于暂停pst打印服务
export const stopPSTScreenshotHook = (data: { errorCode: string | number, errorMessage: string }) => {
  try {
    // PSTScreenshotHook 方法为PST云函数，自动注入到window下
    if (window.PSTScreenshotHook) {
      window.PSTScreenshotHook({
        status: false,
        ...data
      })
    } else {
      console.error('PSTScreenshotHook不存在, 终止打印服务失败')
    }
  } catch (err) {
    console.error('终止打印服务异常', err)
  }
}


// 跳转到权限页面
export const goToPermissionPage = ({ code, msg } : { code: string, msg: string }) => {
  const isM = isMobile();
  const path = isM? 'nativeNoAuth' : 'webNoAuth';
  try {
    window.location.href = `${web}/single-point-page/#/unifyAuthentication/${path}?authCode=${code}&authMessage=${msg}`
  } catch (err) {
    console.error(err)
  }
};

// 加载公共头部
export const eHeader = () => {
  try {
    const eCommonScript = document.createElement('script');
    eCommonScript.setAttribute('type', 'text/javascript');
    eCommonScript.src = '//web.ewt360.com/common/eHeader.min.js';
    eCommonScript.async = true;
    eCommonScript.onload = () => {
      if (window.eHeader) {
        const header = window.eHeader(null, true);
        header.BindLogoutEvent(() => {
          window.location.reload();
        });
      }
    };
    document.body.appendChild(eCommonScript);
  } catch (e) {
    console.log('加载公共头部异常', e);
  }
};

// 判断是否在iframe中
export const isInIframe = () => {
  try {
    if (self !== top) {
      return true;
    }
  } catch (e) {
    // 不做处理
  }
  return false;
}

// 获取链接上的sourceType
export const getSourceType = () => {
  return getUrlParams(location.search, 'sourceType');
}

/**
 * 过滤无效的省市权限
 * @param addressItems 后端返回的省市区
 * @param checkDistrict 是否检查区
 * @returns 
 */
export const filterAddressItem = (addressItems: IAddressItem[] = [], checkDistrict = false) => {
  return addressItems.filter(province => {
    let citys = province.children || [];
    if (checkDistrict) {
      citys = citys.filter(city => city.children?.length);
    }
    return citys.length;
  });
}

/**
 * @description 过滤数组或对象无效值
 * @param input 入参
 * @returns 过滤后结果
 */
export const filterInvalidValues = (input: any) => {
  const unwantedValues = new Set([null, undefined, '', 'null', 'undefined']);
  if (Array.isArray(input)) {
    return input.filter((item: any) => !unwantedValues.has(item));
  } else if (typeof input === 'object' && input !== null) {
    return Object.fromEntries(
      Object.entries(input).filter(([key, value]: any) => !unwantedValues.has(value))
    );
  } else {
    return input;
  }
}


/**
 * @description 下载文件流
 * @param { Blob } stream 文件数据流;
 * @param { String } fileName 文件名称;
 * @param { String } type 文件类型;
 */
export const downloadFileStream = ({
  stream,
  fileName = `${new Date().getTime()}`,
  type = 'application/vnd.ms-excel;charset=UTF-8',
}: any) => {
  const blob = new Blob([stream], { type });
  const url = window.URL.createObjectURL(blob);
  const aEl = document.createElement('a');
  aEl.style.display = 'none';
  aEl.href = url;
  aEl.download = fileName;
  document.body.appendChild(aEl);
  aEl.click();
  document.body.removeChild(aEl);
};

/**
 * @description 获取纯文本
 * @param html 富文本内容
 * @returns 返回纯文本
 */
export const getPlainText = (html: String) => {
  // 去除标签
  let text = html.replace(/<[^<>]+>/g, "").replace(/&nbsp;/gi, "");

  // 去除空格
  return text.replace(/\s/g, "")
}

/**
 * 将File对象转为json对象
 * @param {File} file file对象
 */
export const fileToJson = (file: any) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (res: any) => {
      const { result } = res.target;
      const data = JSON.parse(result);
      resolve(data)
    }
    reader.onerror = err => {
      reject(err)
    }
    reader.readAsText(new Blob([file]), 'utf-8');
  })
}

/**
 * 根据code，判断错误是否在忽略列表中
 * @param {Error} error 错误
 * @param {array} codeList 错误码列表，支持数字和字符串
 * @returns {boolean}
 */
export function isIgnoreError(error: any, codeList: any) {
  try {
    if (error && error.code && codeList && codeList.length && codeList.some((code: string | number) => `${error.code}` === `${code}`)) {
      return true;
    }
  } catch (e) {
    // do nothing
  }
  return false;
}

/**
 * @description 查询接口状态
 * @param { function } fn 接口函数
 * @returns { object } 返回查询结果
 */

export async function checkTaskStatus (fn: Function, checkTaskParams: { ids: string[]}) {
  try {
    const { code, data } = await fn(checkTaskParams);
    if (code && Number(code) === 200 && data && data.length) {
      return data[0];
    } else {
      throw new Error('网络响应不正常');
    }
  } catch (error) {
    return {
      status: 2,
      error
    };
  }
};

/**
 * @description 轮询查询状态
 * @param { function } fn 函数方法
 * @param { number } interval 轮询间隔时间（毫秒）
 * @param { number } timeout 最大轮询时长（毫秒）
 * @returns { string } 地址
 */
// 轮询任务状态的函数
export async function pollTaskStatus(
  checkTaskStatusFn: any,
  checkTaskParams: { ids: string[]},
  timeout = 180000,
  interval = 5000
) {
  const startTime = Date.now();
  return new Promise(async(resolve, reject) => {
    const poll = setInterval(async () => {
      try {
        // 检查是否超过3分钟
        if (Date.now() - startTime > timeout) {
          clearInterval(poll);
          reject({
            status: 2,
            error: '生成文件超时',
          });
        }
        // 调用查询任务状态的接口
        const res = await checkTaskStatus(checkTaskStatusFn, checkTaskParams);
        // 判断任务状态
        if (res && res.status === 1 && res.filePath) { // 成功
          console.log('生成文件成功，轮询结束');
          clearInterval(poll);
          resolve(res);
        } else if (res && res.status === 2) { // 失败
          console.log('生成文件失败，轮询终止');
          clearInterval(poll);
          reject({
            status: 2,
            ...res,
            error: '生成文件失败'
          });
        }
      } catch (error) {
        clearInterval(poll);
        console.log('生成文件失败，轮询终止');
        reject({
          status: 2,
          error,
        }); // 错误处理：请求失败或其他错误
      }
    }, interval); // 每隔 `interval` 毫秒执行一次
  });
}


/**
 * 通过Native来新开webview
 * @param {string} url 链接
 * @param {object} config 更多参数
 * - title 页面标题
 * - removeCurrentPage 关闭当前页面: 1 表示关闭
 * - style 页头样式
 * 0 : 导航栏是蓝底、白字的样式、标题居中
 * 1 : 导航栏是白底、黑字、标题左对齐
 * 2 : 导航栏是白底、黑字、标题居中对齐
 * 3 : 导航栏是白底、黑字、标题居中对齐 返回按钮是关闭
 */
export function openWebView(
  url: string,
  config: { title?: string; removeCurrentPage?: number; style?: number } = {},
) {
  const params = { url, ...config };
  openRoute({
    domain: "web",
    action: "open_webview",
    params,
  });
}

// 调起app
export const openRoute = (route: any) => {
  try {
    window?.mstJsBridge.openNative(route);
  } catch (e) {
    console.log(e);
  }
};



/**
 * 同步接口，判断是否在App中
 * 参照mst-bridge代码处理
 * @returns {boolean}
 */
export function getIsInMSTApp(): boolean {
  const {
    navigator: { userAgent },
  } = window;
  return userAgent.indexOf("EWT/") > -1;
}



/**
 * 同步接口，返回App的版本号，如果不在App中，返回空字符串
 * 参照mst-bridge代码处理
 */
export function getAppVersion() {
  const {
    navigator: { userAgent },
  } = window;

  const ewtFlagIndex = userAgent.indexOf("EWT/");
  const ewtFlagEndIndex = userAgent.indexOf("/com.mistong.ewt");
  if (ewtFlagIndex > -1 && ewtFlagEndIndex > -1) {
    return userAgent.substring(ewtFlagIndex + 4, ewtFlagEndIndex);
  }
  return "";
}


/**
 * 获取 App 所属平台：ios、android、harmonyos、others
 */
export function getAppAgentName() {
  try {
    if (window.mstJsBridge) {
      return window.mstJsBridge.getAgentName().toLowerCase();
    }
  } catch (e) {
    console.log(e);
  }
  return "others";
}

/**
 * 版本比较 versionCmp
 * @param {String} s1 当前版本.
 * @param {String} s2 比较版本.
 * @return {Boolean} 当前版本大于或等于返回true, 否则false
 * versionCompare('6.3', '5.2.5'); // true.
 * versionCompare('6.1', '6.1'); // true.
 * versionCompare('6.1.5', '6.2'); // false.
 */
export const versionCompare = (s1: string, s2: string) => {
  const s2i = (s: string) => {
    let num = 0;
    let factor = 1;
    for (let i = s.length - 1; i >= 0; i--) {
      const code = s.charCodeAt(i);
      // eslint-disable-next-line yoda
      if (48 <= code && code < 58) {
        num += (code - 48) * factor;
        factor *= 10;
      }
    }
    return num;
  };
  const a = s1.split(".").map(s2i);
  const b = s2.split(".").map(s2i);
  const n = Math.min(a.length, b.length);
  for (let i = 0; i < n; i++) {
    if (a[i] < b[i]) {
      return false;
      // eslint-disable-next-line no-else-return
    } else if (a[i] > b[i]) {
      return true;
    }
  }
  if (a.length < b.length) return false;
  if (a.length > b.length) return true;
  // eslint-disable-next-line no-bitwise
  const last1 = s1.charCodeAt(s1.length - 1) | 0x20;
  // eslint-disable-next-line no-bitwise
  const last2 = s2.charCodeAt(s2.length - 1) | 0x20;
  // eslint-disable-next-line no-unneeded-ternary
  return last1 > last2 ? true : last1 < last2 ? false : true;
};



/**
 * 获取App支持情况
 */
export function getAppSupportInfo() {
  const info = {
    // 是否支持新开 Webview 时关闭当前 Webview，iOS 都可以，Android 需要 10.8.5
    isSupportRemoveCurrentPage: false,
    // 是否支持设备安全区域
    isSupportDeviceSafeArea: false,
    // 是否支持修改状态栏颜色
    isSupportChangeStatusBarStyle: false,
    // 是否支持 通知App 事件
    isSupportNotifyAppEvent: false,
  };
  const version = getAppVersion();
  const agentName = getAppAgentName();
  if (version) {
    info.isSupportRemoveCurrentPage =
      agentName === "ios" ||
      (agentName === "android" && versionCompare(version, "10.8.5"));
    info.isSupportDeviceSafeArea = versionCompare(version, "9.9.7");
    info.isSupportChangeStatusBarStyle = versionCompare(version, "10.4.0");
    info.isSupportNotifyAppEvent = versionCompare(version, "10.8.5");
  }
  return info;
}


/**
 * 关闭webview
 */
export function closeWebview() {
  try {
    window.mstJsBridge && window.mstJsBridge.closeWebview();
  } catch (e) {
    console.log(e);
  }
}

/**
 * 通过Native来新开webview，并尝试关闭当前 webview
 * @param {string} url 链接
 * @param {object} config 更多参数
 * - title 页面标题
 * - style 页头样式
 * 0 : 导航栏是蓝底、白字的样式、标题居中
 * 1 : 导航栏是白底、黑字、标题左对齐
 * 2 : 导航栏是白底、黑字、标题居中对齐
 * 3 : 导航栏是白底、黑字、标题居中对齐 返回按钮是关闭
 */
export function closeCurrentAndOpenWebView(
  url: string,
  config: { title?: string; style?: number } = {},
) {
  if (getIsInMSTApp()) {
    const { isSupportRemoveCurrentPage } = getAppSupportInfo();
    const agentName = getAppAgentName();
    if (isSupportRemoveCurrentPage) {
      // removeCurrentPage 不能传 bool，Android 不认
      openWebView(url, { ...config, removeCurrentPage: 1 });
    } else if (agentName === "android") {
      // 历史经验上，在低版本 Android App 上这样的做法没什么问题
      closeWebview();
      openWebView(url, config);
    } else {
      // 鸿蒙之类的需要延时
      closeWebview();
      setTimeout(() => {
        openWebView(url, config);
      }, 300);
    }
  } else {
    window.location.replace(url);
  }
}

