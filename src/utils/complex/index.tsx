import React from 'react';
import type { ITreeItem } from '~/complexComponents/index.d';
import type { ILatitudeItem } from '~/@types/index.d';

export const sortLatitudeData = (list?: ILatitudeItem[]) => {
  const dataSource = [
    {
      name: '得分',
      key: 'score',
    },
    {
      name: '参考分',
      key: 'referenceScore',
    },
    {
      name: '预警分',
      key: 'warnningScore',
    },
  ];
  const columns: any[] = [];
  list?.forEach(({ latitudeId, latitudeName, ...rest }, i) => {
    const title = (<div><div>{latitudeName.slice(0,2)}</div><div>{latitudeName.slice(2)}</div></div>)
    columns.push({
      dataIndex: `${latitudeId}-${i}`,
      title,
      name: latitudeName
    });
    dataSource.forEach((item) => {
      // @ts-ignore
      item[`${latitudeId}-${i}`] = rest[item.key];
    });
  });
  return {
    columns,
    dataSource,
  };
};

/**
 * @description 具体因子分析雷达图和表格
 */
export const sortRadarTable = ({
  list,
}: {
  list?: ILatitudeItem[];
}): ITreeItem[] => {
  const { dataSource, columns } = sortLatitudeData(list);
  return [
    {
      type: 'ChartForRadar',
      props: {
        id: `cityReprot-ChartForRadar`,
        wrapperStyle: { padding: 20 },
        contentStyle: { width: '100%', margin: '20px auto 40px' },
        height: '400px',
        before: <p></p>,
        options: {
          legend: {
            data: ['得分', '参考分', '预警分'],
            textStyle: {
              fontSize: 14,
              verticalAlign: 'bottom',
            },
            bottom: 0,
          },
          radar: [
            {},
            {
              indicator: columns.map(({ name }) => ({
                name,
                max: 5,
              })),
              center: ['50%', '46%'],
              radius: 120,
              startAngle: 90,
              axisName: {
                color: '#001139',
                fontSize: 18,
              },
              splitArea: {
                areaStyle: {
                  color: ['#fff', '#F2F6FF'].reverse(),
                },
              },
              axisLine: {
                lineStyle: {
                  color: '#E0ECFF',
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#E0ECFF',
                },
              },
            },
          ],
          series: [
            {
              name: '',
              type: 'radar',
              radarIndex: 1,
              symbol: 'none',
              data: [
                {
                  type: 'radar',
                  value: list?.map((item) => item.score),
                  name: '得分',
                  itemStyle: {
                    color: '#00C865',
                    fontSize: 18,
                  },
                  areaStyle: {
                    color: '#00C865',
                  },
                },
                {
                  value: list?.map((item) => item.referenceScore),
                  type: 'radar',
                  name: '参考分',
                  itemStyle: {
                    color: '#FF9631',
                    borderColor: '#FF9631',
                    borderType: 'solid',
                    fontSize: 18,
                  },
                  lineStyle: {
                    width: 3,
                  },
                },
                {
                  value: list?.map((item) => item.warnningScore),
                  type: 'radar',
                  name: '预警分',
                  itemStyle: {
                    color: '#FF3C19',
                    borderColor: '#FF3C19',
                    borderType: 'solid',
                    fontSize: 18,
                  },
                  lineStyle: {
                    width: 3,
                  },
                },
              ],
            },
          ],
        },
      },
    },
    {
      type: 'SearchTable',
      props: {
        hideSearch: true,
        tableProps: {
          hidePagination: true,
          dataSource,
          columns: [{ title: '', dataIndex: 'name' }, ...columns],
          wrapperStyle: {
            margin: '20px'
          },
          info: (
            <p style={{ margin: '20px 0', lineHeight: 1.5, fontSize: 16 }}>
              注：
              <br />
              参考分为全国常模的平均分，得分高于某个因子所对应的参考分，表明本次参评学生群体在该因子上的表现要差于全国平均水平，得分低于某个因子所对应的参考分，表明本次参评学生群体在该因子上的表现要好于全国平均水平。
              <br />
              预警分值为学生心理健康与否的分界线，若在某因子上得分超过预警分值，则表明参评群体内大部分学生在该因子上存在不同程度的问题，需要引起高度关注。
            </p>
          ),
        },
      },
    },
  ];
};

export const riskColorList = [
  { name: '高风险', color: '#FF5858', key: 'highest' },
  { name: '较高风险', color: '#FF9631', key: 'higher' },
  { name: '轻微风险', color: '#4B80FF', key: 'minor' },
  { name: '无风险', color: '#00DFA6', key: 'health' },
].map((item) => ({
  ...item,
  peopleKey: `${item.key}${item.key === 'health' ? '' : 'Risk'}People`,
  percentageKey: `${item.key}${item.key === 'health' ? '' : 'Risk'}Percentage`,
}));

/**
 * @description 堆叠条形柱状图
 * @params object
 * @type bar-y-category-stack
 */
export const sortBarYCategoryStack = ({
  series,
  categoryList,
  before
}: {
  series: { color?: string; name?: string; key: string; data: number[] }[];
  categoryList: string[];
  before: React.ReactNode;
}): ITreeItem => {
  const height = ((categoryList.length * 40) + 160);
  return {
    type: 'ChartForBar',
    props: {
      id: 'ChartForBar-bar-y-category-stack',
      height: `${height}px`,
      before,
      options: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          bottom: '40px',
          itemHeight: 14,
          itemWidth: 14,
          icon: "rect",
          textStyle: {
            borderRadius: 0,
            fontSize: 18,
          },
        },
        grid: {
          top: '5%',
          left: '5%',
          right: '6%',
          bottom: '20%',
          containLabel: true,
        },
        xAxis: {
          type: "value",
          max: 100,
          splitNumber: 4,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
            formatter: "{value}%",
            interval: "auto",
          },
          splitLine: { show: false },
        },
        yAxis: {
          type: "category",
          data: categoryList,
          axisLabel: {
            fontSize: 14,
            color: "rgba(0, 17, 57, 0.85)",
            height: 5,
          },
          axisTick: {
            show: false,
          },
          offset: 0,
        },
        series: series.map((item, index) => ({
          ...item,
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            color: "#fff",
            formatter: (params: { value: any; }) => {
              return index ? "" : (params.value || "")
            },
          },
          barWidth: 15,
          emphasis: {
            focus: 'series',
          },
        })),
      },
    },
  };
};

/**
 * 绿色的因子柱状图
 *
 */
export const sortFactorBarChart = ({
  id,
  categoryList,
  list,
  markLineScore,
  before,
  wrapperClass,
  content
}: {
  id: string;
  categoryList: string[];
  list: { value: number | string }[];
  markLineScore?: number;
  before?: React.ReactNode;
  wrapperClass?: string | React.CSSProperties;
  content?: React.ReactNode;
}): ITreeItem => {
  return {
    type: 'ChartForBar',
    props: {
      id: `ChartForBar-${id}`,
      height: '360px',
      wrapperStyle: {
        paddingBottom: 0
      },
      wrapperClass,
      content,
      before,
      options: {
        xAxis: {
          type: 'category',
          data: categoryList,
          offset: 5,
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            rotate: list.length > 1 ? 45 : 0,
            color: 'rgba(0,17,57,0.65)',
            formatter: (value: string) => value.length < 10 ? value : value.slice(0, 10) + '...'
          },
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false,
          },
          // max: 5,
        },
        grid: {
          left: '10%',
          bottom: '40px',
          top: '40px',
          right: '15%',
          containLabel: true,
        },
        barWidth: 40,
        series: [
          {
            type: 'bar',
            data: list,
            stack: 'total',
            label: {
              show: true,
              color: '#fff',
            },
            color: ["#00DFA6", "#4B80FF", "#FF9631", "#FF5858"],
            emphasis: {
              focus: 'series',
            },
            ...(markLineScore
              ? {
                markLine: {
                  data: [
                    {
                      name: `参考分`,
                      yAxis: markLineScore,
                    },
                  ],
                  symbol: ['none', 'none'],
                  lineStyle: {
                    color: '#59647e',
                    width: 2,
                    type: 'solid',
                  },
                  label: {
                    show: true,
                    color: '#59647e',
                    formatter: () => `参考分（${markLineScore}分）`,
                  },
                },
              }
              : {}),
          },
        ],
      },
    },
  };
};

export const sortRiskPieChart = ({
  id,
  categoryList,
  list,
}: {
  id: string;
  categoryList?: string[];
  list?: { name: string; value: number;[key: string]: string | number }[];
}): ITreeItem => {
  return {
    type: 'ChartForPie',
    props: {
      height: '400px',
      id: `ChartForPie-${id}`,
      options: {
        title: {
          text: '',
          left: 'center',
          bottom: 0,
          textStyle: {
            color: '#001139',
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{a} : {c}({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          bottom: '20%',
          itemHeight: 14,
          itemWidth: 14,
          icon: 'rect',
          textStyle: {
            fontSize: 18,
          },
          data: categoryList || [],
        },
        color: riskColorList.map((item) => item.color),
        series: [
          {
            name: '',
            type: 'pie',
            radius: '65%',
            center: ['50%', '50%'],
            selectedMode: 'single',
            data: list || [],
            label: {
              fontSize: 18,
              position: 'outside',
              color: '#001139',
              formatter: (params: { name: string; percent: string }) =>
                `${params.name}\n${params.percent}%`,
            },
            itemStyle: {
              borderRadius: 3,
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
        ],
      },
    },
  };
};

export const sortRiskLevelPieChart = () => {
  return {};
};
