export const env = process.env.DEPLOYMENT_ENV || 'prod';
export const IS_DEV = process.env.DEPLOYMENT_ENV === 'dev' || (typeof window !== 'undefined' && location.hostname.includes('dev'));
export const IS_SIT =
  process.env.DEPLOYMENT_ENV === 'atest' ||
  (typeof window !== 'undefined' && location.hostname.includes('test'));
export const IS_MOCK = process.env.DEPLOYMENT_ENV === 'mock';
export const IS_PRE =
  process.env.DEPLOYMENT_ENV?.includes('pre') ||
  process.env.DEPLOYMENT_ENV === 'pre' ||
  (typeof window !== 'undefined' && location.hostname.includes('staging'));
export const IS_PROD =
  process.env.DEPLOYMENT_ENV?.includes('prod') ||
  process.env.DEPLOYMENT_ENV === 'prod';

export const isServer = typeof window === 'undefined';

// 转换预发域名，观察所有的域名都是前缀加了staging-，因此只传递关键域名host，其余host可定制
export const translateStagingDomain = (originDomain = '', originHost = '') => {
  return `//staging-${originDomain}${originHost || '.ewt360.com'}`;
};

// 根据指定的域名前缀生成通用的完整域名
export const makeUnifyDomain = (originDomain: string, stagingDomain?: string) => {
  let urlHost = `//${originDomain}.ewt360.com`;
  if (IS_SIT) urlHost = `//${originDomain}.test.ewt360.com`;
  if (IS_DEV) urlHost = `//${originDomain}.dev.ewt360.com`;
  if (IS_PRE) urlHost = translateStagingDomain(originDomain, stagingDomain);
  return urlHost;
};

export const www = makeUnifyDomain('www');
export const web = makeUnifyDomain('web');
export const teacher = makeUnifyDomain('teacher');
export const pst = 'https://pst.ewt360.com'; // 与基建组确认，统一使用线上地址
export const gateway = makeUnifyDomain('gateway');
export const edu = IS_SIT
  ? 'http://test-edu.ewt360.com'
  : makeUnifyDomain('edu');
export const file = makeUnifyDomain('file');
export const live = makeUnifyDomain('live');
