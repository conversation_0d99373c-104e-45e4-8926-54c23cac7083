import CommonStyles from '~/styles/common.module.less';

// 风险等级color数组，对应枚举1-4
export const riskLevelColors = ["#5CDBD3", "#40A9FF", "#FFA940", "#FF4D4F"];
// 风险等级背景色样式类数组，对应枚举1-4
export const riskLevelClassName = [CommonStyles.riskLevelBg1, CommonStyles.riskLevelBg2, CommonStyles.riskLevelBg3, CommonStyles.riskLevelBg4];

// 风险等级背景渐变色样式类数组，对应枚举1-4
export const riskLevelImgClassName = [CommonStyles.riskLevelImgBg1, CommonStyles.riskLevelImgBg2, CommonStyles.riskLevelImgBg3, CommonStyles.riskLevelImgBg4];

export const preList = [
  {
    name: "年级",
    level: "mid-bg",
    key: "className",
    height: 21,
  },
  {
    name: "姓名",
    level: "mid-bg",
    key: "userName",
    height: 21,
  },
];

export const list = [
  {
    name: "总体情况",
    child: [
      {
        name: "向内攻击风险",
        key: "inwardAttackRisk",
        levelKey: "inwardAttackRiskLevel",
        height: 29,
      },
      {
        name: "向外攻击风险",
        key: "outwardAttackRisk",
        levelKey: "outwardAttackRiskLevel",
        height: 29,
      },
      {
        level: "deep-bg",
        name: "总体风险",
        isDynamicBg: true,
        key: "riskName",
        levelKey: "riskLevel",
        height: 29,
      },
    ],
  },
  {
    name: '保护因子',
    child: [
      {
        name: '当前表现',
        child: [
          {
            name: '归属感',
            key: "curBelonging",
            levelKey: "curBelongingLevel",
            height: 40,
          }
        ]
      },
      {
        name: '人格原因',
        child: [
          {
            name: '外向性',
            key: "perExtroversion",
            levelKey: "perExtroversionLevel",
          },
          {
            name: '自尊',
            key: "perSelfEsteem",
            levelKey: "perSelfEsteemLevel",
          },
          {
            name: '自我效能',
            key: "perSelfEfficacy",
            levelKey: "perSelfEfficacyLevel",
          }
        ]
      },
      {
        name: '环境原因',
        child: [
          {
            name: '同伴接受',
            key: "envPeersAccept",
            levelKey: "envPeersAcceptLevel",
          },
          {
            name: '社会支持',
            key: "envSocialSupport",
            levelKey: "envSocialSupportLevel",
          }
        ]
      },
      {
        name: '认知原因',
        child: [
          {
            name: '积极应对',
            key: "cogPositiveResponse",
            levelKey: "cogPositiveResponseLevel",
          },
          {
            name: '复原力',
            key: "cogResilience",
            levelKey: "cogResilienceLevel",
          }
        ]
      }
    ]
  },
  {
    name: '风险因子',
    child: [
      {
        name: '当前表现',
        child: [
          {
            name: '抑郁',
            key: "curDepression",
            levelKey: "curDepressionLevel",
          },
          {
            name: '焦虑',
            key: "curAnxiety",
            levelKey: "curAnxietyLevel",
          },
          {
            name: '精神病性',
            key: "curPsychotic",
            levelKey: "curPsychoticLevel",
          },
          {
            name: '对立违抗',
            key: "curDefiance",
            levelKey: "curDefianceLevel",
          },
          {
            name: '攻击行为',
            key: "curBehavior",
            levelKey: "curBehaviorLevel",
          },
          {
            name: '欺负行为',
            key: "curBullying",
            levelKey: "curBullyingLevel",
          },
          {
            name: '绝望',
            key: "curDespair",
            levelKey: "curDespairLevel",
          },
          {
            name: '累赘感知',
            key: "curPerception",
            levelKey: "curPerceptionLevel",
          },
          {
            name: '自我攻击行为',
            key: "curSelfAggressive",
            levelKey: "curSelfAggressiveLevel",
            height: 50,
          }
        ]
      },
      {
        name: '人格原因',
        child: [
          {
            name: '神经质',
            key: "perNeuroticism",
            levelKey: "perNeuroticismLevel",
          },
          {
            name: '完美主义',
            key: "perPerfectionism",
            levelKey: "perPerfectionismLevel",
          },
          {
            name: '冲动性',
            key: "perImpulsive",
            levelKey: "perImpulsiveLevel",
          },
          {
            name: '操纵性',
            key: "perManipulative",
            levelKey: "perManipulativeLevel",
          },
          {
            name: '精神病态',
            key: "perPsychopath",
            levelKey: "perPsychopathLevel",
          },
          {
            name: '自恋',
            key: "perNarcissism",
            levelKey: "perNarcissismLevel",
          }
        ]
      },
      {
        name: '环境原因',
        child: [
          {
            name: '家庭压力',
            key: "envFamilyStress",
            levelKey: "envFamilyStressLevel",
          },
          {
            name: '学业压力',
            key: "envStudyingPressure",
            levelKey: "envStudyingPressureLevel",
          },
          {
            name: '同伴拒绝',
            key: "envPeerRejection",
            levelKey: "envPeerRejectionLevel",
          },
          {
            name: '教师压力',
            key: "envTeacherPressure",
            levelKey: "envTeacherPressureLevel",
          },
          {
            name: '网络欺凌',
            key: "envCyberbullying",
            levelKey: "envCyberbullyingLevel",
          },
          {
            name: '消极情绪管理',
            key: "cogNegativeEmotions",
            levelKey: "cogNegativeEmotionsLevel",
            height: 50,
          },
          {
            name: '消极认知',
            key: "cogNegativeCognition",
            levelKey: "cogNegativeCognitionLevel",
          },
          {
            name: '注意缺陷',
            key: "cogAttentionDefects",
            levelKey: "cogAttentionDefectsLevel",
          }
        ]
      }
    ]
  }
];

export const ColorMap = {
  2: "slight-c",
  3: "superior-c",
  4: "high-c",
};

export const BgColorMap = {
  1: "lows-bg",
  2: "slight-bg",
  3: "superior-bg",
  4: "higher-bg",
};

export default {
  preList,
  list,
  ColorMap,
  BgColorMap
}