import logger from './logger';

// 初始化 nacos 配置信息
const getConfigInfo: () => Record<string, any> = () => {
  const baseConfig = {
    'dev': {
      namespace: '49aac66e-d549-4c94-84fd-eea8a4f47c3f',
      serverHost: 'mse-526422e0-nacos-ans.mse.aliyuncs.com'
    },
    'atest': {
      namespace: 'fa0b4f2c-e5b5-4ed6-933e-02f1d345b39a',
      serverHost: 'mse-526422e0-nacos-ans.mse.aliyuncs.com'
    },
    'pre': {
      namespace: 'b67bd09d-10e2-474a-a087-0cec69c7597e',
      serverHost: 'mse-b368e430-nacos-ans.mse.aliyuncs.com',
      AccessKey: 'LTAI5tGSTAzmUR7e4kuZwiat',
      SecretKey: '******************************',
    },
    'prod': {
      namespace: 'd60aadeb-b48b-4f16-81c6-d353b4e918e5',
      serverHost: 'mse-b368e430-nacos-ans.mse.aliyuncs.com',
      AccessKey: 'LTAI5tGSTAzmUR7e4kuZwiat',
      SecretKey: '******************************',
    }
  }
  return baseConfig[process.env.DEPLOYMENT_ENV as keyof typeof baseConfig];
}

const serviceName = 'api-gateway'; // 网关地址
type TNacosHost = {
  ip: string;
  port: number;
  healthy: boolean;
}

class NacosInstanceClass {
  healthyHosts: TNacosHost[] = [];
  client: any;
  constructor() {
    this.initNacos().catch(error => {
      this.logger('error', error);
    });
  }
  /**
   * 初始化Nacos
   * @returns
   */

  async initNacos() {
    if (typeof window === 'undefined') {
      // 初始化nacos配置
      const { serverHost, namespace, AccessKey, SecretKey } = getConfigInfo();
      this.logger('info', {
        type: '初始化 nacos 配置',
        config: { serverHost, env: process.env.DEPLOYMENT_ENV  }
      });
      const port = 8848;
      const initConfig = {
        logger,
        serverList: `${serverHost}:${port}`,
        namespace,
        AccessKey,
        SecretKey,
        requestTimeout: 6000,
      };
      const NacosNamingClient = require('nacos').NacosNamingClient;
      this.client = new NacosNamingClient(initConfig);
      this.client.on('error', (error: Error) => {
        this.logger('error', error);
      });
      await this.client.ready();
      return new Promise((resolve, reject) => {
        this.client.subscribe(serviceName, (hosts: TNacosHost[]) => {
          this.logger('info', {
            type: '订阅的 nacos hosts',
            hosts
          });
          const healthyHosts = (Array.isArray(hosts) ? hosts : []).filter((host) => {
            return host.healthy;
          })
          if (healthyHosts.length > 0) {
            this.healthyHosts = healthyHosts;
          } else {
            this.logger('error', new Error('暂无可用的网关服务'));
          }
          resolve(true);
        });
      })
    }
  }

  async getNacosHost() {
    try {
      if (!this.healthyHosts?.length) {
        await this.initNacos();
      }
      return this.healthyHosts?.length ? this.healthyHosts[Math.floor(Math.random() * this.healthyHosts.length)] : null;
    } catch (error: unknown) {
      this.logger('error', { message: 'nacos 订阅异常', error });
      return null;
    }
  }

  logger(type: 'info' | 'error', info: Record<string, any>) {
    try {
      logger[type](JSON.stringify(info));
    } catch (err) {
      console.log(err);
    }
  }
}

const nacosInstance = new NacosInstanceClass()

export default nacosInstance
