import moment from "moment";
import { getToken } from './tools';
const TOPIC = 'psychology-service-log';
const BASE_SOURCE = 'base';
type Win = Window & typeof globalThis & {
  __MST_LABEL__?: string;
};

/**
 * 封装更稳定的logger，减少三方sls产生的影响
 * 封装了error 和 info 方法
 */
class SafeLogger {
  private _source: string;
  private _logger: any; // 更改为适当的日志记录器类型
  private _loggerClass: any;
  static baseLogger: any;
  // 全局通用，无需额外实例化
  // 主要针对无业务耦合的场景
  /**
   * 构造方法
   * @param {*} source 源
   */
  constructor(source: string) {
    this._source = source || 'unknown';
   
    try {
      if(typeof window !== 'undefined'){
        const Logger = this._getLoggerClass();
        this._logger = new Logger(TOPIC, {
          topic: TOPIC,
          source: this._source,
          environment: process.env.DEPLOYMENT_ENV
        });
      }
    } catch (e) {
      console.log(`sls初始化失败：${source}`, e);
    }
  }

  private _getLoggerClass() {
    if (!this._loggerClass) {
      this._loggerClass = require('@ewt/sls-web-track');
      this._loggerClass.setGlobalProps({
        environment: process.env.DEPLOYMENT_ENV,
        // psy 暂无该属性，先注释掉
        // release: (window as Win)?.__MST_LABEL__ || 'baseline',
      });
    }
    return this._loggerClass;
  }

  /**
   * error日志
   * @param {string} event 事件
   * @param {object} more
   */
  public error(event: string, more = {}) {
    this._safe('error', { event, ...more });
  }

  /**
   * warn日志
   * @param {string} event 事件
   * @param {object} more
   */
  public warn(event: string, more = {}) {
    this._safe('warn', { event, ...more });
  }

  /**
   * info日志
   * @param {string} event 事件
   * @param {object} more
   */
  public info(event: string, more = {}) {
    this._safe('info', { event, ...more });
  }

  private _safe(type: string, data: any = {}) {
    try {
      if (this._logger) {
        // 忽略日志
        if (data && data.error) {
          // 忽略纯网络错误
          const error = data.error || {};
          if (
            error.name === 'AxiosError' &&
            [
              'Network Error',
              'timeout of 60000ms exceeded',
              'Request aborted'
            ].includes(error.message)
          ) {
            return;
          }
        }
        this._logger[type]({
          // 记录当前日志的用户本地时间，因为该时间可能和服务器时间不一致
          time: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
          token: getToken(),
          // 当前业务环境，prod、pre、sit
          environment: process.env.DEPLOYMENT_ENV || 'local',
          url: typeof window !== 'undefined' ? window.location.href : '',
          // psy 暂无该属性，先注释掉
          // 当前平台环境，baseline、gray-c
          // release: (window as Win)?.__MST_LABEL__ || 'baseline',
          ...data
        });
      }
    } catch (e) {
      console.log('sls上报失败', e);
    }
  }
}

// 创建全局基础logger，供全局使用
SafeLogger.baseLogger = new SafeLogger(BASE_SOURCE);

export default SafeLogger;
