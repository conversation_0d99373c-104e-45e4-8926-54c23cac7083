import * as winston from 'winston';
import { context, trace } from "@opentelemetry/api";

// 获取 TraceId
const getTraceId = () => {
  const span = trace.getSpan(context.active());
  if (span) {
    return span.spanContext().traceId;
  }
  return '';
}

const combineList = [
  winston.format.splat(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format((info) => {
    info.level = info.level.toUpperCase();
    return info;
  })(),
  !process.env.NO_COLOR && winston.format.colorize(),
  winston.format.printf(({ level, message, timestamp }) => {
    const traceId = getTraceId();
    return `${timestamp} ${level} [${traceId}] - ${message}`;
  }),
].filter(Boolean) as winston.Logform.Format[];

const logger = winston.createLogger({
  level: 'verbose',
  format: winston.format.combine(...combineList),
  defaultMeta: { serviceName: 'psychology-service' },
  transports: [new winston.transports.Console()],
  exitOnError: false,
});

export default logger;
