/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useCallback, useEffect, useRef } from "react";
import { subscribe } from "./tools";

/**
 * 只能在hooks中使用
 */
 export const useDebounce = (fn: (...arg: any) => any, wait = 300, deep = []) => {
  const { current } = useRef<{ t: number; delay: NodeJS.Timeout | any; fn: (...arg: any) => any }>({
    t: 0, delay: null, fn
  });
  useEffect(() => {
    current.fn = fn;
  }, [fn]);

  return useCallback(function (...args: any) {
    const n = Date.now() - current.t;
    clearTimeout(current.delay);
    if (n > wait) {
      current.fn.apply(null, args);
      current.t = Date.now();
    } else {
      current.delay = setTimeout(function () {
        current.fn.apply(null, args);
      }, wait);
    }
  }, deep);
};


/** 发布订阅  异步等待 */
export const useSubscription = () => {
  const sub = useRef(subscribe());
  const hash = useRef(new Map());

  useEffect(() => {
    return () => {
      sub.current.clear();
      hash.current.clear();
    };
  }, []);

  return {
    publish: (key: string, value?: string) => {
      hash.current.set(key, value);
      sub.current.publish(key, value);
    },
    waitSubcribe: async (key: string) => {
      return await new Promise((resolve, reject) => {
        if (hash.current.has(key)) {
          resolve(true);
        }
        sub.current.subscribe(key, () => {
          resolve(true);
        });
      });
    },
  };
};

// export const useSyncState = <T> (initialState: T): [() => T, (newState: T) => void] => {
//   const [state, updateState] = useState(initialState)
//   let current = state
//   const get = (): T => current
//   const set = (newState: T) => {
//       current = newState
//       updateState(newState)
//       return current
//   }
//   return [get, set]
// }


export const useSyncState = <T>(
  initState?: T
): [{ current: T }, (data: T) => void] => {
  const [state, setStateValue] = useState<T>(initState as T)
  const stateRef = useRef<T>(state)
  const setState = (value: T) => {
    stateRef.current = value
    setStateValue(value)
  }
  return [stateRef, setState]
}
