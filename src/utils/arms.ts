function initArms(): void {
  try {
    if (typeof window === 'undefined') return;

    // 动态导入 Arms SDK
    const BrowserLogger = require('@arms/js-sdk');

    const regex = new RegExp(
      '^(?!(/' +
        window.location.host +
        '|gateway\\d*\\.ewt360\\.com|\\S*\\.mistong\\.com))',
    );

    const sample = window.ARMS_GLOBAL_CONFIG_SAMPLE || 20;
    const version = window.__MST_LABEL__ || 'baseline';
    const environment = ['prod', 'pre'].includes(process.env.DEPLOYMENT_ENV!)
      ? process.env.DEPLOYMENT_ENV
      : 'daily';
    BrowserLogger.singleton({
      pid: 'aocb5mxsv0@50b9bebc090e6c3',
      appType: 'web',
      imgUrl: 'https://arms-retcode.aliyuncs.com/r.png?',
      environment,
      // 监听页面的hashchange事件并重新上报PV，适用于单页面应用场景。
      enableSPA: true,
      // 采集首屏FMP（First Meaningful Paint，首次有效渲染）数据。
      useFmp: true,
      // 开启 console 追踪
      enableConsole: true,
      // 页面load事件触发时会上报当前页面加载的静态资源信息
      sendResource: true,
      // 进行前后端链路追踪
      enableLinkTrace: true,
      // 采样率读取全局配置 默认为 5%（按 1/sample 比例采样）
      sample,
      behavior: true,
      pvSample: sample,
      release: version,
      ignore: {
        ignoreApis: [regex],
        ignoreErrors: [
          /^ResizeObserver loop/,
          /^Loading( CSS)? chunk [^ ]+ failed/,
          /^Network Error/,
          /^timeout of/,
          /^Script error\.?$/,
          /Request failed with status 0/,
        ],
      },
      c1: '0',
    });
    console.log('🚀 ~ initArms ~ 初始化成功');
  } catch (error) {
    console.error('Arms初始化失败:', error);
  }
}

export default initArms;
