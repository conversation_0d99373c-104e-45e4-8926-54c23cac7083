export interface ILatitudeItem {
  latitudeId: number | string;
  latitudeName: string;
  referenceScore: number;
  score: number;
  warnningScore: number;
}

export interface IAddressItem {
  areaCode: number;
  areaName: string;
  children?: IAddressItem[];
}

export interface IGradeItem {
  label: string;
  value: number;
  children?: IGradeItem[]
}

export type SingleValueType = (string | number)[];