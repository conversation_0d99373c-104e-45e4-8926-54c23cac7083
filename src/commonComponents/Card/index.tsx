import React from 'react';
import styles from './index.module.less';

export interface ICardProps {
  id?: string; // 锚点
  wrapperStyle?: React.CSSProperties; // 容器样式
  title?: React.ReactNode; // 标题
  titleStyle?: React.CSSProperties; // 标题样式
  content?: React.ReactNode; // 内容
  contentStyle?: React.CSSProperties; // 内容样式
  render?: () => React.ReactNode; // 渲染函数
  children?: React.ReactNode;
  theme?: string;
  boxShadow?: string;
  borderRadius?: string;
  boxShadowColor?: string | string[];
  wrapperClass?: string;
  contentClass?: string;
  titleClass?: string;
}

// export interface MyCustomCSS extends React.CSSProperties {
//   '--box-shadow-color'?: string | string[];
//   '--border-radius'?: string;
// }

export const Card: React.FC<ICardProps> = ({
  id,
  wrapperStyle,
  title,
  titleStyle,
  content,
  contentStyle,
  render,
  children,
  theme = 'primary',
  boxShadowColor = 'red',
  borderRadius,
  boxShadow,
  wrapperClass = '',
  contentClass = '',
  titleClass = '',
}) => {
  const style = {
    '--box-shadow-color': boxShadowColor,
    '--border-radius': borderRadius,
    ...(boxShadow ? { boxShadow } : {}),
    ...wrapperStyle,
  } as React.CSSProperties;
  return (
    <>
      {/* <h1>Card Component - 专题盒子</h1> */}
      <div
        {...{
          id,
          className: `${styles.card} ${wrapperClass}`,
          style,
        }}
      >
        {render ? (
          render()
        ) : (
          <>
            {title && (
              <div
                {...{
                  className: `${styles['card-title']} ${titleClass}`,
                  style: { ...titleStyle },
                }}
              >
                {title}
              </div>
            )}
            {content && (
              <div
                {...{
                  className: `${styles['card-content']} ${contentClass}`,
                  style: { ...contentStyle },
                }}
              >
                {content}
              </div>
            )}
          </>
        )}

        {children}
      </div>
    </>
  );
};
