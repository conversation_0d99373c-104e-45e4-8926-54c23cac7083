import React from 'react';
import styles from './index.module.less';

export interface ITaskSubItemProps {
  businessData?: any; // 业务数据-暂不需要
  showIcon?: boolean; // 是否显示星
  titleColor?: string; // 标题颜色
  titleStyle?: React.CSSProperties; // 标题样式
  icon?: React.ReactNode; // 图标组件
  children?: React.ReactNode;
  content?: React.ReactNode;
  title?: React.ReactNode;
  contentStyle?: React.CSSProperties;
}

export const TaskSubItem: React.FC<ITaskSubItemProps> = ({
  businessData,
  showIcon,
  titleColor,
  titleStyle,
  title,
  content,
  contentStyle,
  icon,
  children,
}) => {
  return (
    <div className={styles['task-sub-item']}>
      {/* <h1>TaskSubItem Component - 任务子项</h1> */}
      <div
        {...{
          className: styles['task-sub-item-title'],
          style: { color: titleColor, ...titleStyle },
        }}
      >
        {showIcon && (
          <div {...{ className: styles['task-sub-item-title-icon'] }}>
            {icon || ''}
          </div>
        )}
        {title}
      </div>
      <div
        {...{
          className: styles['task-sub-item-content'],
          style: { ...contentStyle },
        }}
      >
        {content}
      </div>
      {children}
    </div>
  );
};
