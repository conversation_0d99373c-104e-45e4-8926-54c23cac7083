import React from 'react';
import styles from './index.module.less';

export interface IStatusBarProps {
  title: React.ReactNode; // 标题内容
  theme?: string; // 主题枚举
  children?: React.ReactNode;
  wrapperStyle?: React.CSSProperties;
  wrapperClass?: string;
  titleStyle?: React.CSSProperties;
  fontColor?: string;
  fontStyle?: React.CSSProperties;
  backgroundColor: string | string[];
  background: '';
  content?: React.ReactNode;
  themeColor?: '',
  right?: React.ReactNode;
}

export const StatusBar: React.FC<IStatusBarProps> = ({
  title,
  theme,
  wrapperClass,
  children,
  wrapperStyle,
  content,
  right,
  themeColor,
  titleStyle
}) => {
  // const styles=
  return (
    <>
      {/* <h1>SmallIconTitle Component - 小图标版标题</h1> */}
      <div
        {...{
          className: `${styles['status-bar']} ${wrapperClass}`,
          style: { ...wrapperStyle, ...themeColor ? { '--theme-color': themeColor } : {} },
        }}
      >
        <div {...{ className: `${styles['status-bar-title']}`, style: { ...titleStyle } }}>{title}</div>
        {right}
      </div>
      {content}
      {children}
    </>
  );
};
