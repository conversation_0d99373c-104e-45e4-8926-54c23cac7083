import React, { useEffect, memo, useRef } from 'react';
import styles from './index.module.less';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TitleComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  LegendComponent,
  LegendComponentOption,
} from 'echarts/components';
import { Pie<PERSON>hart, PieSeriesOption } from 'echarts/charts';
import { LabelLayout } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import merge from 'lodash.merge';

type EChartsOption = echarts.ComposeOption<
  | TitleComponentOption
  | TooltipComponentOption
  | LegendComponentOption
  | PieSeriesOption
>;

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  Pie<PERSON>hart,
  CanvasRenderer,
  LabelLayout,
]);

export interface IChartProps {
  title?: TitleComponentOption;
  tooltip?: TooltipComponentOption;
  legend?: LegendComponentOption;
}

export interface IChartForPieProps extends IChartProps {
  children?: React.ReactNode;
  sortOptions?: (arg: EChartsOption) => EChartsOption;
  wrapperStyle?: React.CSSProperties;
  wrapperClass?: string;
  contentClass?: string;
  contentStyle?: React.CSSProperties;
  options?: EChartsOption;
  content?: React.ReactNode;
  id: string;
  height: string;
  data?: object[];
  color?: string[];
  fieldNames?: {
    value: string;
    name: string;
  };
  before?: React.ReactNode;
  [key: string]: any;
}

const useEChart = (
  chartRef: React.MutableRefObject<null>,
  options: EChartsOption
) => {
  let myChart: echarts.ECharts | null = null;
  const renderChart = () => {
    if (chartRef.current) {
      const chart = echarts.getInstanceByDom(chartRef.current!);
      if (chart) {
        myChart = chart;
      } else {
        myChart = echarts.init(chartRef.current!);
      }
      myChart.setOption(options);
    }
  };
  useEffect(() => {
    renderChart();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);
  useEffect(() => {
    return () => {
      myChart && myChart.dispose();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

const ChartForPieComponent: React.FC<IChartForPieProps> = ({
  wrapperStyle,
  contentStyle,
  children,
  options,
  id,
  height,
  fieldNames,
  data,
  wrapperClass = '',
  contentClass = '',
  content,
  before,
  ...rest
}) => {
  const chartRef = useRef(null);
  useEChart(chartRef, {
    ...merge(
      {
        series: [
          {
            type: 'pie',
            radius: '50%',
            data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      },
      options
    ),
    ...rest,
  });
  return (
    <div
      {...{
        className: `${styles['chart-for-pie']} ${wrapperClass}`,
        style: { ...wrapperStyle },
      }}
    >
      {before}
      { (data && data.length) ? <div
        {...{
          className: `${styles['chart-for-pie-content']} ${contentClass}`,
          style: { height, ...contentStyle },
          id,
          ref: chartRef,
        }}
      /> : null
      }
      {content}
      {children}
    </div>
  );
};

export const ChartForPie = memo(ChartForPieComponent);
