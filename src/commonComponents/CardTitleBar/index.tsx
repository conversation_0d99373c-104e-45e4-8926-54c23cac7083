import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import Color from 'color';

export interface ICardTitleBarProps {
  id?: string; // 锚点
  theme?: string; // 主题枚举
  title: React.ReactNode; // 标题
  subTitle: React.ReactNode;
  wrapperClass?: string; // 自定义样式
  wrapperStyle?: React.CSSProperties; // 容器样式样式
  titleStyle?: React.CSSProperties; // 容器样式样式
  children?: React.ReactNode;
  themeColor?: string;
  content?: React.ReactNode;
}

export const CardTitleBar: React.FC<ICardTitleBarProps> = ({
  id,
  theme,
  title,
  subTitle,
  wrapperClass = '',
  wrapperStyle,
  themeColor,
  children,
  titleStyle,
  content
}) => {
  const [colorData, setColorData] = useState<{
    color: string;
    backgroundColor: string;
  }>({ color: '', backgroundColor: '' });

  useEffect(() => {
    if (themeColor) {
      setColorData({
        color: themeColor,
        backgroundColor: Color(themeColor)
          .alpha(0.1)
          .string(),
      });
    }
  }, [themeColor, theme]);

  return (
    <div
      {...{id, className: `${styles['card-title-bar']} ${wrapperClass}` , style: { ...wrapperStyle } }}
    >
      <div
        {...{
          className: styles['card-title-bar-title'],
          style: {
            color: colorData.color,
            backgroundColor: colorData.backgroundColor,
            ...titleStyle,
          },
        }}
      >
        {title}
        <span className={styles['card-title-bar-subtitle']}>{subTitle}</span>
      </div>
      {content}
      {children}
    </div>
  );
};
