import React from 'react';
import styles from './index.module.less';

export interface ISmallIconTitleProps {
  title: React.ReactNode; // 标题内容
  subTitle?: React.ReactNode; // 小标题内容
  theme?: string; // 主题枚举
  icon?: boolean | React.ReactNode; // undefined
  children?: React.ReactNode;
  wrapperStyle?: React.CSSProperties;
  wrapperClass?: string;
  iconType: 'circle | half';
  alpha: '';
  iconColor?: string; // icon色值
  fontColor?: string;
  fontStyle?: React.CSSProperties;
  backgroundColor: string | string[];
  background: '';
  content?: React.ReactNode
}

export const SmallIconTitle: React.FC<ISmallIconTitleProps> = ({
  title,
  theme,
  subTitle,
  iconColor,
  icon,
  children,
  wrapperStyle,
  wrapperClass = '',
  content
}) => {
  return (
    <>
      <div
        {...{
          className: `${styles['small-icon-title']} ${wrapperClass}`,
          style: { ...wrapperStyle },
        }}
      >
        {icon && (
          <div
            {...{
              className: styles['small-icon-title-icon'],
              style: { background: iconColor },
            }}
          >
            {icon || ''}
          </div>
        )}
        {title}<span className={styles.subTitle}>{subTitle}</span>
      </div>
      {content}
      {children}
    </>
  );
};
