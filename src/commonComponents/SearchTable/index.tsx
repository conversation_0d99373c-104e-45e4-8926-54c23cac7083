import React, { useState } from 'react';
import { SearchForm } from '../SearchForm';
import { TopicBox } from '../TopicBox';
import type { ISearchFormProps } from '../SearchForm';
import type { ITopicBoxProps } from '../TopicBox';

export interface ISearchTableProps {
  searchForm?: ISearchFormProps;
  tableProps?: ITopicBoxProps;
  wrapperStyle?: React.CSSProperties;
  hideSearch?: boolean;
  wrapperClass?: string;
  before?: React.ReactNode;
}

export const SearchTable: React.FC<ISearchTableProps> = ({
  searchForm,
  tableProps,
  hideSearch,
  wrapperStyle,
  wrapperClass,
  before,
}) => {

  const [params, setParams] = useState<any>({});

  const onSubmit = (v: any) => {
    setParams({ ...v, pageIndex: 1});
  };

  return (
    <div {...{ style: { ...wrapperStyle }, className: `${wrapperClass}` }}>
      {before}
      {!hideSearch && (
        <SearchForm
          {...{ initialValues: tableProps?.params, ...searchForm, onSubmit }}
        />
      )}
      <TopicBox
        {...{ ...tableProps, params: { ...tableProps?.params, ...params } }}
      ></TopicBox>
    </div>
  );
};
