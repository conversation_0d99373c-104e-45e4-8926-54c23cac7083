import React from 'react';
import styles from './index.module.less';

export interface ISubjectCombinationProps {
  businessData?: any; // 业务数据
  colorScheme?: string[]; // 配色方案
  children?: React.ReactNode;
  wrapperStyle?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
  title: React.ReactNode;
  contentStyle?: React.CSSProperties;
  content: React.ReactNode;
}

export const SubjectCombination: React.FC<ISubjectCombinationProps> = ({
  businessData,
  colorScheme,
  children,
  wrapperStyle,
  title,
  titleStyle,
  contentStyle,
  content,
}) => {
  return (
    <>
      {/* <h1>SubjectCombination Component - 科目组合</h1> */}
      <div
        {...{
          className: styles['subject-combination'],
          style: { ...wrapperStyle },
        }}
      >
        <div {...{ className: styles['subject-combination-title'] }}>
          {title}
        </div>
        <div
          {...{
            className: styles['subject-combination-content'],
            style: { ...contentStyle },
          }}
        >
          {content}
        </div>

        {children}
      </div>
    </>
  );
};
