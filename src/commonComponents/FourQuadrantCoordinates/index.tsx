import React, { useEffect } from 'react';
import styles from './index.module.less';
import * as echarts from 'echarts/core';
import { GridComponent, GridComponentOption } from 'echarts/components';
import { Scatter<PERSON><PERSON>, ScatterSeriesOption } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([GridComponent, ScatterChart, CanvasRenderer, UniversalTransition]);

type EChartsOption = echarts.ComposeOption<
  GridComponentOption | ScatterSeriesOption
>;

let myChart: echarts.ECharts;

export interface IFourQuadrantCoordinatesProps {
  businessData?: any; // 业务数据
  title?: any; // 标题
  grid?: any; // 坐标位置
  colorScheme?: any; // 配色方案
  className?: any; // 自定义样式
  children?: React.ReactNode;
  wrapperStyle?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  options?: EChartsOption;
  id: string;
  height: string;
}

export const FourQuadrantCoordinates: React.FC<
  IFourQuadrantCoordinatesProps
> = ({
  businessData,
  title,
  grid,
  colorScheme,
  className,
  children,
  wrapperStyle,
  contentStyle,
  options,
  id,
  height,
}) => {
  const initialCharts = () => {
    const chartDom = document.getElementById(id)!;
    myChart = echarts.init(chartDom);
  };
  useEffect(() => {
    if (!myChart) initialCharts();
    options && myChart.setOption(options);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);
  return (
    <div
      {...{
        className: styles[`four-quadrant-coordinates-content`],
        style: { ...wrapperStyle },
      }}
    >
      {/* <h1>FourQuadrantCoordinates Component - 四象限坐标</h1> */}
      <div
        {...{
          className: styles[`four-quadrant-coordinates-content`],
          style: { height, ...contentStyle },
          id,
        }}
      ></div>

      {children}
    </div>
  );
};
