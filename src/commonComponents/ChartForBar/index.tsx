import React, { useEffect, useRef } from 'react';
import styles from './index.module.less';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TitleComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
  MarkLineComponent,
  MarkLineComponentOption,
} from 'echarts/components';
import { BarChart, BarSeriesOption } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import merge from 'lodash.merge';

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  <PERSON><PERSON><PERSON>,
  CanvasRenderer,
  MarkL<PERSON>Component,
]);

type EChartsOption = echarts.ComposeOption<
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | BarSeriesOption
  | MarkLineComponentOption
>;

export interface IChartForBarProps {
  children?: React.ReactNode;
  sortOptions?: (arg: EChartsOption) => EChartsOption;
  wrapperStyle?: React.CSSProperties;
  wrapperClass?: string;
  contentClass?: string;
  contentStyle?: React.CSSProperties;
  options?: EChartsOption;
  content?: React.ReactNode;
  id: string;
  height: string;
  data?: object[];
  color?: string[];
  fieldNames?: {
    value: string;
    name: string;
  };
  outBefore?: React.ReactNode;
  before?: React.ReactNode;
  [key: string]: any;
}

const useEChart = (
  chartRef: React.MutableRefObject<null>,
  options: EChartsOption
) => {
  let myChart: echarts.ECharts | null = null;
  const renderChart = () => {
    if (chartRef.current) {
      const chart = echarts.getInstanceByDom(chartRef.current!);
      if (chart) {
        myChart = chart;
      } else {
        myChart = echarts.init(chartRef.current!);
      }
      myChart.setOption(options);
    }
  };
  useEffect(() => {
    renderChart();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);
  useEffect(() => {
    return () => {
      myChart && myChart.dispose();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

export const ChartForBar: React.FC<IChartForBarProps> = ({
  wrapperStyle,
  contentStyle,
  children,
  options,
  id,
  height,
  fieldNames,
  data,
  wrapperClass = '',
  contentClass = '',
  content,
  before,
  outBefore,
  ...rest
}) => {
  const chartRef = useRef(null);
  useEChart(chartRef, {
    ...merge(
      {
        xAxis: {},
        yAxis: {},
        series: [
          {
            type: 'bar',
            // radius: '50%',
            // data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      },
      options
    ),
    ...rest,
  });
  return (
    <>
      {outBefore}
      <div
        {...{
          className: `${styles['chart-for-bar']} ${wrapperClass}`,
          style: { ...wrapperStyle },
        }}
      >
        {before}
        <div
          {...{
            className: `${styles['chart-for-bar-content']} ${contentClass}`,
            style: { height, ...contentStyle },
            id,
            ref: chartRef,
          }}
        ></div>
        {content}
        {children}
      </div>
    </>
  );
};
