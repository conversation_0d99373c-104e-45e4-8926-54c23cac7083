import React from 'react';
import styles from './index.module.less';
import Image from 'next/image'
export interface ICoverForSpecialProps {
  bgImg: string; // 报告背景图
  title?: React.ReactNode; // 标题内容
  content?: React.ReactNode; // 内容
  typeTitle?: any; // 报告类型标题-暂不需要
  mainTitle?: any; // 主标题-暂不需要
  basicInfo?: any; // 报告基本信息-暂不需要
  className?: string; // 自定义样式
  children?: React.ReactNode;
  bgStyle?: React.CSSProperties;
}

export const CoverForSpecial: React.FC<ICoverForSpecialProps> = ({
  bgImg,
  typeTitle,
  mainTitle,
  basicInfo,
  className,
  children,
  title,
  content,
  bgStyle,
}) => {
  return (
    <>
      <div {...{ className: styles['cover-for-special'] }}>
        {/* <h1>CoverForSpecial Component - 专项测评封面</h1> */}
        <Image
          {...{
            src: bgImg,
            className: styles['cover-for-special-bg'],
            style: { ...bgStyle },
          }}
          alt="图"
        />
        {title}
        {content}

        {children}
      </div>
    </>
  );
};
