import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TitleComponentOption,
  LegendComponent,
  LegendComponentOption,
} from 'echarts/components';
import { RadarChart, RadarSeriesOption } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import merge from 'lodash.merge';

echarts.use([TitleComponent, LegendComponent, RadarChart, CanvasRenderer]);

type EChartsOption = echarts.ComposeOption<
  TitleComponentOption | LegendComponentOption | RadarSeriesOption
>;

export interface IChartForRadarProps {
  children?: React.ReactNode;
  wrapperStyle?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  wrapperClass?: React.CSSProperties;
  options?: EChartsOption;
  id: string;
  height: string;
  before?: React.ReactNode;
  content?: React.ReactNode;
  indicator?: { name: string; value: number }[];
  data?: { name: string; value: number }[];
}

const useEChart = (
  chartRef: React.MutableRefObject<null>,
  options: EChartsOption
) => {
  let myChart: echarts.ECharts | null = null;
  const renderChart = () => {
    if (chartRef.current) {
      const chart = echarts.getInstanceByDom(chartRef.current!);
      if (chart) {
        myChart = chart;
      } else {
        myChart = echarts.init(chartRef.current!);
      }
      myChart.setOption(options);
    }
  };
  useEffect(() => {
    renderChart();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);

  useEffect(() => {
    return () => {
      myChart && myChart.dispose();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

export const ChartForRadar: React.FC<IChartForRadarProps> = ({
  children,
  wrapperStyle,
  contentStyle,
  wrapperClass = '',
  options,
  id,
  height,
  indicator,
  data,
  before,
  content,
  ...rest
}) => {
  const chartRef = useRef(null);
  useEChart(chartRef, {
    ...merge(
      {
        radar: {
          // shape: 'circle',
          indicator,
        },
        series: [
          {
            type: 'radar',
            data,
          },
        ],
      },
      options
    ),
    ...rest,
  });
  return (
    <div
      {...{
        className: `chart-for-radar`,
        style: { ...wrapperStyle },
      }}
    >
      {before}
      <div
        {...{
          className: `chart-for-radar-content ${wrapperClass}`,
          style: { height, ...contentStyle },
          id,
          ref: chartRef,
        }}
      ></div>
      {content}
      {children}
    </div>
  );
};
