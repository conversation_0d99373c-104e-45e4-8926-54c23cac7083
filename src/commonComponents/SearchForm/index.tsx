import React from 'react';
import { Form, Input, Select, Button, Cascader } from 'antd';
import type { FormProps, SelectProps, InputProps, CascaderProps, FormItemProps } from 'antd';

export interface IColumnItem {
  type: 'select' | 'input' | 'cascader';
  formItemProps?: FormItemProps;
  key: string;
  props: SelectProps | InputProps | CascaderProps<any>;
  isImmediate?: boolean;
}

export interface ISearchFormProps extends FormProps {
  wrapperStyle?: React.CSSProperties;
  wrapperClass?: string;
  optionBoxStyle?: string;
  onSubmit?: (params?: Record<string, any>) => void;
  columns?: IColumnItem[];
  hideButton?: boolean;
  hideSubmit?: boolean;
  showReset?: boolean;
  isChangeSubmit?: boolean;
}

export const FormChildren = ({ type, props }: IColumnItem) => {
  switch (type) {
    case 'input':
      return <Input {...{ ...(props as InputProps) }}></Input>;
    case 'select':
      return <Select {...{ ...(props as SelectProps) }}></Select>;
    case 'cascader':
      return <Cascader {...{ ...(props as CascaderProps<any>) }}></Cascader>;
    default:
      return <></>;
  }
};

export const SearchForm: React.FC<ISearchFormProps> = ({
  columns,
  onSubmit,
  wrapperStyle,
  wrapperClass,
  optionBoxStyle,
  isChangeSubmit,
  hideSubmit,
  hideButton,
  showReset,
  ...rest
}) => {
  const [form] = Form.useForm();

  const onValuesChange = (changedValues: any, values: any) => {
    const [key] = Object.entries(changedValues)[0];
    const { isImmediate } = columns?.find((item) => item.key === key)!;
    if (isChangeSubmit || isImmediate) {
      if (key.includes(',')) {
        const keysList = key.split(',') || [];
        const keyParams: any = {}
        keysList.forEach((value: string, index: number) => {
          keyParams[value] = values[key][index];
        })
        onSubmit?.(keyParams);
      } else {
        onSubmit?.(values);
      }
    }
  };

  const onRestForm = () => {
    form.resetFields();
    onSubmit?.(form.getFieldsValue());
  };

  const onFinish = (values: any) => {
    onSubmit?.(values);
  };
  return (
    <div
      {...{ style: { ...wrapperStyle }, className: `${wrapperClass || ''}` }}
    >
      <Form {...{ form, ...rest, onValuesChange, onFinish }}>
        {columns?.map((item, i) => (
          <Form.Item key={i} name={item.key} {...{ ...item.formItemProps }}>
            {FormChildren(item)}
          </Form.Item>
        ))}
        {!hideButton && (
          <Form.Item {...{ className: `${optionBoxStyle || ''}`}} >
            {!hideSubmit && <Button {...{ htmlType: 'submit' }}>搜索</Button>}
            {showReset && <Button {...{ onClick: onRestForm }}>重置</Button>}
          </Form.Item>
        )}
      </Form>
    </div>
  );
};
