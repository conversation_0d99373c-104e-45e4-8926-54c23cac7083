import React, { useState } from 'react';
import styles from './index.module.less';
import { Button, Timeline, message } from 'antd';
import { getToken, pollTaskStatus } from '~/utils/tools';
import MstQt from 'mst-analytics';
import API from '~/service/common';
import SafeLogger from '~/utils/safe-logger';

export interface IListItem {
  label: React.ReactNode;
  point: string;
  title: React.ReactNode;
  style?: React.CSSProperties;
  children?: IListItem[];
}
export interface IPageNavigationProps {
  wrapperStyle?: React.CSSProperties; // 容器样式
  printMode?: boolean; // 是否为打印模式
  sendParams?: any; // 上报参数
  detailReportMode?: boolean; // 是否为详报模式
  timeout?: number; // 等待时长
  className?: any; // 自定义样式
  theme?: any; // 主题
  list?: IListItem[]; // 导航列表
  children?: React.ReactNode;
  onClickItem?: (arg: IListItem) => void;
  onPrint?: () => void;
  content?: React.ReactNode;
}

export interface IPageNavigationItemProps {
  data?: IListItem | IListItem[];
  onClickItem?: (arg: IListItem) => void;
}

// 定位
const handleLocator = (item: { point: any; }) => {
  document?.getElementById(`${item.point}`)?.scrollIntoView({ behavior: "smooth", block: "start" });
};

export const PageNavigationItem: React.FC<IPageNavigationItemProps> = ({
  data,
}) => {
  if (!data) return <></>;
  if (Array.isArray(data)) {
    return (
      <>
        {data?.map((item, i) => (
          <PageNavigationItem key={i} {...{ data: item }} />
        ))}
      </>
    );
  }

  return (
    <a
      className={styles['navigation-child-label']}
      onClick={() => {
        handleLocator(data);
      }}
    >
      {data.label}
    </a>
  );
};

export const PageNavigation: React.FC<IPageNavigationProps> = ({
  printMode,
  detailReportMode,
  sendParams={},
  timeout,
  list,
  wrapperStyle,
  className,
  theme,
  children,
  onClickItem,
  content
}) => {
  const [downloading, setDownloading] = useState(false);
  const [filePath, setFilePath] = useState<string>('');

  // 跳转到详报
  const jumpToDetailReport = () => {
    if (sendParams && Object.keys(sendParams).length > 0) {
      MstQt.clickPv({
        eventCode: 'btp_moral-edu-center_gen-person-mental-report_det-report_click',
        analysis_report_scene: '简报',
        ...sendParams,
      });
    }
    const url = window.location.href + '&isDetailReport=true';
    window.open(url);
  }

  const goPollTaskStatus = async (hashId: string) => {
    try {
      const { status, filePath }: any = await pollTaskStatus(API.queryPDFStatus, { ids: [hashId] }, 180000, 5000);
      if (status === 1 && filePath) {
        setFilePath(filePath);
        setDownloading(false);
        message.success("生成成功，请点击右侧悬浮菜单“保存文件”进行下载~", 3);
      } else {
        throw new Error('轮询报告生成状态失败');
      }
    } catch (error) {
      setDownloading(false);
      message.error('生成报告失败，请稍后重试～');
      SafeLogger?.baseLogger?.error('async-pdf-download-error', {
        reason: '轮询报告生成状态失败',
        error
      });
    }
  }

  // 异步下载PDF
  const goAsyncPDFByPage = async(token: string) => {
    try {
      message.info("报告正在生成中，预计需要1~3分钟内完成，请稍等~", 3);
      const { data: { hashId } } = await API.asyncPDFByPage({
        type: 'pdf',
        modal: 'active',
        waitUntil: 'networkidle0',
        name: '测评报告',
        width: 1100,
        timeout: timeout || (1000 * 60 * 10),
        url: `${location.href}&isPrint=true&token=${token}`,
        activeTriggerFuncName: 'PSTScreenshotHook'
      })
      if (hashId) {
        goPollTaskStatus(hashId);
      } else {
        throw new Error('生成报告异常')
      }
    } catch (error) {
      setDownloading(false);
      message.error('生成报告异常');
      SafeLogger?.baseLogger?.error('async-pdf-download-error', {
        reason: '生成报告异常',
        error
      });
    }
  }


  const handleDownLoad = async () => {
    const token = getToken() || '';
    if (!token) {
      message.error("登录信息失效，请重新登录后重试！");
      return
    }

    if (downloading) {
      message.info("报告正在生成中，无需重复点击");
      return
    }
    if (sendParams && Object.keys(sendParams).length > 0) {
      MstQt.clickPv({
        eventCode: 'btp_moral-edu-center_det-person-mental-report_download-report_click',
        analysis_report_scene: detailReportMode ? '详报' : '简报',
      });
    }
    setDownloading(true);
    API.getUCBaseInfo().then(() => {
      goAsyncPDFByPage(token)
    }).catch(error => {
      console.warn('校验权限失败', error)
      message.info("校验权限失败，请重新登录后重试");
      setDownloading(false);
    })
  }

  return !printMode ? (
    <div
      {...{
        className: `${styles.navigation} ${theme === 'green' ? styles['theme-green'] : styles['theme-default']} ${className || ''}`,
        style: { ...wrapperStyle },
      }}
    >
      <Timeline>
        {list?.map((item, i) => (
          <Timeline.Item key={i}>
            <a
              className={styles['navigation-label']}
              onClick={() => {
                handleLocator(item);
              }}
            >{item.label}
            </a>
            {item.children ? <PageNavigationItem {...{ data: item.children, onClickItem }} /> : null
            }
          </Timeline.Item>
        ))}
      </Timeline>
      { filePath ? <Button
        onClick={() => window.open(filePath)}
        className={styles['btn-orange']}
        type="primary"
        size="large"
        block
      >
        保存文件
      </Button> : (downloading ? <Button
        className={styles.btn}
        type="primary"
        size="large"
        block
        loading={downloading}
      >
        生成中
      </Button> : <Button
        {...{ type: 'primary', size: 'large', onClick: handleDownLoad }}
        block
        className={styles.btn}
        loading={downloading}
      >
        {typeof detailReportMode === 'undefined' ? '下载' : detailReportMode ? '下载详报' : '下载简报'}
      </Button>)
      }
      {content}
      {children}
      {(typeof detailReportMode !== 'undefined' && !detailReportMode) ? (
        <span className={styles['detail-report-tip-text']}>
          此为简报，若需要下载详细报告，请点击
          <span onClick={() => jumpToDetailReport()}>&nbsp;详细报告</span>
        </span>
      ) : null}
    </div>
  ) : null;
};
