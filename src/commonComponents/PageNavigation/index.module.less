.navigation {
  width: 168px;
  position: fixed;
  z-index: 1000;
  right: 16px;
  top: 200px;
  background: #fff;
  padding: 40px 20px 30px 20px;
  border-radius: 8px;
  box-shadow: 0px 0px 20px 0px rgba(32, 39, 54, 0.15);

  .detail-report-tip-text {
    font-size: 14px;
    color: #fa8c16;
    margin-top: 10px;
    display: inline-block;
    & > span {
      text-decoration: underline;
      color: #4b80ff;
      cursor: pointer;
    }
  }
  .navigation-label,
  .navigation-child-label {
    font-size: 14px;
    color: rgba(0, 17, 57, 1);
    cursor: pointer;
    display: block;

    &:hover {
      color: rgba(0, 200, 101, 1);
    }
  }

  .navigation-child-label {
    color: rgba(0, 17, 57, 1);
    padding-top: 10px;
    font-size: 12px;
  }

  :global {
    .ant-timeline-item {
      padding-bottom: 8px;
    }

    .ant-timeline-item-content {
      min-height: 0;
    }
  }

  .btn,
  .btn-orange  {
    text-align: center;
    margin: 10px auto 0;
    display: block;
    width: 120px;
    height: 40px;
    background: #427aff;
    box-shadow: none;
  }
  .btn-orange {
    background: #FFA940;
    border-color: #ffa940;
  }

  &.theme-green {
    .navigation-label,
    .navigation-child-label {
      padding: 2px 0 2px 24px;

      &:hover {
        background: rgba(0, 200, 101, 0.2);
        border-radius: 12px;
      }
    }

    .navigation-child-label {
      color: rgba(0, 17, 57, 0.65);
      font-size: 12px;
      padding: 6px 0 6px 32px;
    }

    :global {
      .ant-timeline-item {
        padding-bottom: 8px;

        &:hover {
          .ant-timeline-item-head {
            background: #00c865;
            border-color: #fff;
          }
        }
      }

      .ant-timeline-item-tail {
        z-index: 1;
        left: 10px;
      }

      .ant-timeline-item-head {
        border-color: #edf3ff;
        z-index: 1;
        left: 6px;
      }

      .ant-timeline-item-content {
        margin: 0;
      }
    }

    .btn {
      background: #00dfa6;
      border-radius: 8px;
      border: none;
      font-size: 18px;
    }
  }

  &.theme-default {
    width: 200px;
    .navigation-label,
    .navigation-child-label {
      padding: 2px 0 2px 24px;
      &:hover {
        color: rgba(0, 17, 57, 1);
        background: rgba(237, 243, 255, 1);
        border-radius: 12px;
      }
    }

    .navigation-child-label {
      color: rgba(0, 17, 57, 0.85);
      font-size: 12px;
      padding: 6px 0 6px 32px;
    }

    .btn {
      background: rgba(75, 128, 255, 1);
      border-radius: 8px;
      border: none;
      font-size: 18px;
    }

    :global {

      .ant-timeline-item {
        padding-bottom: 8px;

        &:hover {
          .ant-timeline-item-head {
            background: rgba(75, 128, 255, 1);
            border-color: #fff;
          }
        }
      }

      .ant-timeline-item-tail {
        z-index: 1;
        left: 10px;
      }

      .ant-timeline-item-head {
        border-color: #edf3ff;
        z-index: 1;
        left: 6px;
      }

      .ant-timeline-item-content {
        margin: 0;
      }
    }
  }

  &.Mental-report {
    width: 200px;
    .navigation-label,
    .navigation-child-label {
      padding: 2px 0 2px 24px;
      &:hover {
        color: #5cdbd3;
        background: rgba(237, 243, 255, 1);
        border-radius: 12px;
      }
    }

    .navigation-child-label {
      color: rgba(0, 17, 57, 0.65);
      font-size: 12px;
      padding: 6px 0 6px 32px;
    }

    .btn {
      background: #5cdbd3;
      border-radius: 8px;
      border: none;
      font-size: 18px;
    }

    :global {
      .ant-timeline-item {
        padding-bottom: 8px;

        &:hover {
          .ant-timeline-item-head {
            background: #5cdbd3;
            border-color: #fff;
          }
        }
      }
    }
  }
}
