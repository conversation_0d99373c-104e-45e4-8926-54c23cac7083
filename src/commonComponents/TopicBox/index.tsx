import React, { useEffect, useState } from 'react';
import { Table, Pagination } from 'antd';
import type { TableProps } from 'antd';
import { useDebounce, useSyncState } from '~/utils/hooks';
import styles from './index.module.less';

export interface ITopicBoxProps extends TableProps<object> {
  themeColor?: string; // 主题色
  children?: React.ReactNode;
  info?: React.ReactNode;
  before?: React.ReactNode;
  rowKey?: string;
  beforeRender?: (...args: any[]) => React.ReactNode;
  params?: Record<string, any>;
  request?: (params?: Record<string, any>) => Promise<Record<string, any>>;
  sortParams?: (params?: {
    current: number;
    pageSize: number;
    [key: string]: any;
  }) => Promise<Record<string, any>>;
  sortData?: (
    data: Record<string, any>
  ) => Promise<{ list: object[]; total: number }>;
  hidePagination?: boolean;
  wrapperStyle?: React.CSSProperties;
  wrapperClass?: string;
  paginationClass?: string;
}

export const TopicBox: React.FC<ITopicBoxProps> = ({
  themeColor,
  children,
  hidePagination,
  params,
  request,
  dataSource,
  sortParams,
  sortData,
  pagination,
  info,
  wrapperStyle,
  before,
  wrapperClass,
  paginationClass,
  beforeRender,
  ...rest
}) => {

  const [pageState, setPageState] = useSyncState<{
    current: number;
    pageSize: number;
  }>({ current: 1, pageSize: params?.pageSize || 10 });

  const onChange = async (current: number, pageSize: number) => {
    setPageState({ current, pageSize });
    getTableData()
  };

  const [tableData, setTableData] = useState<Record<string, any>>({
    dataSource: [],
  });

  const getList = async () => {
    if (!request) return { dataSource };
    const toParams = { ...pageState.current, ...params };
    const toData: any = sortParams ? await sortParams(toParams) : toParams;
    const data = await request(toData);
    return sortData ? await sortData(data) : data;
  };

  const getTableData = useDebounce(async () => {
    const data = await getList();
    setTableData(data);
  });

  useEffect(() => {
    if (params?.pageIndex !== pageState.current.current) {
      setPageState({ ...pageState.current, current: params?.pageIndex});
    }
    getTableData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  return (
    <div
      {...{
        className: `${styles['topic-box']} ${wrapperClass}`,
        style: { ...wrapperStyle },
      }}
    >
      {before}
      {beforeRender?.({ params })}
      <Table
        {...{
          ...rest,
          ...tableData,
          pagination: false,
        }}
      />
      {info}
      {!hidePagination && (
        <Pagination
          {...{
            className: `${styles.paginationBox} ${paginationClass}`,
            ...pagination,
            ...pageState.current,
            onChange,
            total: tableData.total
          }}
        />
      )}
      {children}
    </div>
  );
};
