import React from 'react';
import CSS from 'csstype';
import styles from './index.module.less';

export interface ITHeaderControlProps {
  id?: string; // 锚点
  title: string | React.ReactNode; // 主标题
  subTitle?: string | React.ReactNode; // 子标题
  wrapperStyle?: CSS.Properties; // 容器样式
  wrapperClass?: string | React.ReactNode; //
  titleStyle?: CSS.Properties; // 主标题样式
  subTitleStyle?: CSS.Properties; // 子标题样式
  children?: React.ReactNode;
}

export const THeaderControl: React.FC<ITHeaderControlProps> = ({
  id,
  title,
  subTitle,
  wrapperStyle,
  wrapperClass,
  titleStyle,
  subTitleStyle,
  children,
}) => {
  return (
    <div
      {...{ id, className: `${styles['t-header-control']} ${wrapperClass}`, style: wrapperStyle || {} }}
    >
      <div
        {...{
          className: styles['t-header-control-title'],
          style: titleStyle || {},
        }}
      >
        {title}
      </div>
      <div
        {...{
          className: styles['t-header-control-subtitle'],
          style: subTitleStyle || {},
        }}
      >
        {subTitle}
      </div>
      {children}
    </div>
  );
};
