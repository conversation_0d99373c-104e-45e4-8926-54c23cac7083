'use client'
import { useEffect, useState } from 'react';

export const useSafeHeight = ({ position }: { position: 'top' | 'bottom' }) => {
  const [safeHeight, setSafeHeight] = useState<any>({
    top: 0,
    bottom: 0,
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      getSafeHeight();
    }
  }, []);

  async function getSafeHeight() {
    try {
      const mstJsBridge = (await import('mst-js-bridge')).default;
      const { data } = await mstJsBridge?.getNativeData({
        domain: 'systemInfo',
        action: 'getDevicePortraitSafeArea',
        params: {
          safe_area: 'top,bottom',
        },
      });
      setSafeHeight({
        top: +(data?.top || 0),
        bottom: +(data?.bottom || 0),
      });
    } catch (err) {
      console.error(err);
    }
  }

  return [safeHeight[position]];
};
