// import Cookie from 'cookie';
// import { getUrlParams } from '@/utils/tools';
import { env } from '@/utils/hosts';
import { Button } from 'antd';
import { observer } from 'mobx-react-lite';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import Router from 'next/router';
import { useEffect } from 'react';
import { useModels } from '~/models';
import yearReportApi from '@/service/api/year-report';
import { TBuildItem } from '@/service/mock';
import styles from './index.module.less';
import dynamic from 'next/dynamic';

const Card = dynamic(() => import('@/components/TestCard'), { ssr: false });

// 首次渲染和getInitialProps无无别
// 跳页面有区别，用这个方式切换后，getServerSideProps会在服务端请求完，再以json格式返回给前端
// 如果不跳页面，2种使用无无别，否则，推荐使用getServerSideProps的方式
// 只能在页面内使用，不可在组件内使用

export const getServerSideProps: GetServerSideProps<{
  list: TBuildItem[];
}> = async (ctx: any) => {
  try {
    const { query } = ctx;
    const { recordId } = query;
    const dataRes = await yearReportApi.overallAnalysisModule(
      { evaluationRecordId: recordId as string },
      ctx
    );

    console.log(dataRes, 'dataRes');
    return { props: { list: [] } };
  } catch (error) {
    console.log(error);
    return { props: { list: [] } };
  }
};

function Page({
  list,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
  const { globalModel } = useModels();
  const { buildInfo } = globalModel;
  console.log(process.env.DEPLOYMENT_ENV, '---');
  // console.log(UserAgent);
  useEffect(() => {
    // console.log(buildInfo);
  }, [buildInfo]);
  return (
    <div>
      <Card
        wrapperStyle={{
          backgroundColor: 'rgba(255, 141, 0, 0.1)',
          color: '#ff8d00',
        }}
      >
        <div>
          <div className={styles.page}>
            发布记录
            <Button
              onClick={() => {
                Router.push({
                  pathname: '/test-page',
                  query: { realName: '李姗姗' },
                });
              }}
            >
              去另一个页面吧
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default observer(Page);
