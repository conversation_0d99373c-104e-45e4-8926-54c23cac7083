.personWrap {
}
.report-main-finding-wrapper {
  padding: 30px 0 0 0;
  .report-main-finding-text {
    p {
      font-size: 18px;
      font-family: MicrosoftYaHeiUI;
      text-align: left;
      color: rgba(0, 17, 57, 0.85);
      line-height: 24px;
      padding-bottom: 16px;
      padding-left: 20px;
      span {
        font-weight: 700;
      }
    }
  }

    .report-main-finding-bottom{
        .report-main-finding-card-wapper{
            padding-left: 20px;
            .report-main-finding-card{
                vertical-align: top;
                margin-top: 20px;
                border: 1px solid #f2f6ff;
                border-radius: 9px;
                display: inline-block;
                position: relative;
                width: 276px;
                margin-right: 40px;
                &:nth-child(3n+3) {
                    margin-right:0;
                }
                .report-main-finding-circle{
                    position: absolute;
                    left: 20px;
                    top: 50%;
                    margin-top: -20px;
                    width: 40px;
                    height: 40px;
                    opacity: 1;
                    background: linear-gradient(180deg,#ffa46a 1%, #ff7a65 100%);
                    border-radius: 50%;
                    p{
                        width: 40px;
                        font-size: 24px;
                        font-family: MicrosoftYaHeiUI, MicrosoftYaHeiUI-Bold;
                        font-weight: 700;
                        text-align: center;
                        color: #ffffff;
                        line-height: 40px;
                        .border{
                            font-weight: bold;
                        }
                    }
                }
                .report-main-finding-right{
                    margin-left: 78px;
                    padding: 15px 10px 15px 0;
                    .school{
                        opacity: 0.85;
                        font-size: 16px;
                        font-family: MicrosoftYaHeiUI;
                        text-align: left;
                        color: #001139;
                        line-height: 20px;
                        padding-bottom: 5px;
                        // white-space: nowrap;
                        // text-overflow:ellipsis;
                        // overflow: hidden;
                    }
                    .text{
                        font-size: 14px;
                        font-family: MicrosoftYaHeiUI;
                        text-align: left;
                        color: #475370;
                        line-height: 16px;
                     }
                }
            }

        }
        .report-main-finding-bottom{
            padding-left: 20px;
            font-size: 16px;
            font-family: MicrosoftYaHeiUI;
            text-align: left;
            color: rgba(0,17,57,0.85);
            line-height: 24px;
            padding-top: 20px;
        }
    }
}
.additional-info{
        .title{
            font-size: 18px;
            font-weight: 700;
            text-align: left;
            color: rgba(0,17,57,0.85);
            line-height: 23px;
            padding-bottom: 30px;
        }
        p{
            font-size: 16px;
            text-align: left;
            color: rgba(0,17,57,0.85);
            line-height: 24px;
            // padding: 0 40px 40px 40px;
        }
        .bottom{
            padding-bottom: 50px;
        }
    }
