import React from 'react';
import { NextPage } from 'next';
import dynamic from 'next/dynamic';
import Layout from '~/businessComponents/Layout';
import MentalCover from '~/businessComponents/MentalAdmission/MentalCover';
// import MentalHeader from "~/bussinessComponents/MentalAdmission/MentalHeader/index1"
import EntranceReportInfo from "~/businessComponents/MentalAdmission/MentalHeader"
import styles from "./index.module.less"
import api from "@/service/api/mentalAdmissionReport"
import ModuleHeader from "~/businessComponents/MentalAdmission/ModuleHeader"
import AnalyPage from "~/businessComponents/MentalAdmission/AnalyPage"
import MainFinding from "./mainFindings";
import Footer from "~/businessComponents/MentalAdmission/Footer"
import Guide from "~/businessComponents/MentalAdmission/Guide"
import personHeader from "@/assets/MentalAdmission/entrance-banner-person.png"
import type { ICardProps } from '@/commonComponents/Card';
import { TUserReportResponse } from '@/service/api/mentalAdmissionReport/types';

const Card = dynamic<ICardProps>(() =>
  import('@/commonComponents/Card').then(({ Card }) => Card)
);

type UserReportTypes = {
  basicData: TUserReportResponse;
};
const PersonReport: NextPage<UserReportTypes> = ({
  basicData,
}: UserReportTypes) => {
  // const ListPage = [
  //   { label: "1、报告导读", point: "Introduction" },
  //   {
  //     label: "2、结果分析",
  //     point: "Analysis",
  //     child: [
  //       { label: "2.1 主要发现", point: "mainFindings" },
  //       {
  //         label: "2.2 心理健康状态分析",
  //         point: "psychologyRisk",
  //       },
  //       {
  //         label: "2.3 学习准备度分析",
  //         point: "psychologyStatus",
  //       },
  //       { label: "2.4 性格类型分析", point: "structureDetail" },
  //       { label: "2.5 家庭教养风格分析", point: "upbringing" },
  //     ],
  //   },
  //   { label: "3、附加说明", point: "PotentialRiskIndiv" },
  // ]
  return (
    <div className={styles.personWrap}>
      <MentalCover src={personHeader} />
      <Layout>
        <EntranceReportInfo infoData={basicData} type={1} />
        <div className={"min"}>
          <ModuleHeader
            vsize={true}
            title="报告导读"
            subtitle="Introduction"
            id="Introduction"
            name="Introduction"
          />
          <Card title="尊敬的老师:" wrapperStyle={{ boxShadow: '0px 0px 20px 0px rgb(32 39 54 / 15%)' }} ><Guide /></Card>
          <ModuleHeader
            vsize={true}
            isPrint={true}
            title="结果分析"
            subtitle="Analysis"
            id="Analysis"
            name="Analysis"
          />
          <AnalyPage
            serial="01"
            isPrint={false}
            title="主要发现"
            id="mainFindings"
            name="mainFindings"
          >
            <MainFinding mainFindings={basicData.mainFindings} />
          </AnalyPage>
          {/* <AnalyPage
                        serial="02"
                        isPrint={true}
                        title="心理健康状态分析"
                        children={
                            <Psychology
                                type={1}
                                isHealth={true}
                                {...riskAnalysis}
                                factorAnalysis={factorAnalysis}
                                factorAnalysisDetails={factorAnalysisDetails}
                                infoData={infoData}
                            />
                        }
                        id="psychologyRisk"
                        name="psychologyRisk"
                    />
                    <AnalyPage
                        serial="03"
                        isPrint={true}
                        title="学习准备度分析"
                        children={
                            <Psychology
                                isPrint={false}
                                type={2}
                                {...learningReadiness}
                                studyReadinessDetails={studyReadinessDetails}
                                factorAnalysis={studyReadinessAnalysis}
                                studyReadinessAnalysis={studyReadinessAnalysis}
                            />
                        }
                        id="psychologyStatus"
                        name="psychologyStatus"
                    />
                    <AnalyPage
                        serial="04"
                        isPrint={true}
                        title="性格类型分析"
                        children={
                            <Character personalityTypeAnalysis={personalityTypeAnalysis} />
                        }
                        id="structureDetail"
                        name="structureDetail"
                    />
                    <AnalyPage
                        serial="05"
                        isPrint={true}
                        title="家庭教养风格分析"
                        children={<Upbringing {...familyEducation} />}
                        id="upbringing"
                        name="upbringing"
                    /> */}
          <ModuleHeader
            isPrint={false}
            vsize={true}
            title="附加说明"
            subtitle="Additional Notes"
            id="PotentialRiskIndiv"
            name="PotentialRiskIndiv"
          />
          <Card
            wrapperStyle={{ boxShadow: '0px 0px 20px 0px rgb(32 39 54 / 15%)' }}>
            <div className={styles["additional-info"]}>
              <p className="title">1、 任何心理测评都存在一定误差</p>
              <p>
                学生在测评时的情绪状态、对测评的态度等主观因素，以及测评过程中的环境干扰等客观因素都会影响测评结果。因此在阅读本测评报告时，请参照学生在日常生活的实际表现来理解。
              </p>
              <p className="title">2、请勿将测评结果当作永久“标签”</p>
              <p className="bottom">
                请不要把本测评结果看成是对学生的“标签”或最终“宣判”，而应视为学生的阶段性状态。对于处在青春期的高中学生来说，其大多数心理和性格特征都是可变的，因此本测评结果仅为了解学生近期状态提供参考。
              </p>
            </div>
          </Card>
        </div>
        <Footer flagType="EntranceBanner" />
      </Layout>
    </div>
  )
};
export const getServerSideProps = async (ctx: any) => {
  try {
    const { data: basicData } = await api.getUserReport(
      {
        evaluationRecordId: 1518513261101518850,
      },
      ctx
    );
    return { props: { basicData } };
  } catch (error) {
    console.log(error, "error");
    return {
      props: { basicData: {} },
    };
  }
};

export default PersonReport;
