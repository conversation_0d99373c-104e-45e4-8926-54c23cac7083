import React from 'react';
import styles from './index.module.less';

const EntranceMainFinding = (props: any) => {
  const { mainFindings } = props;
  return <div className={styles['report-main-finding-wrapper']}>
    <div className={styles['report-main-finding-text']}>
      {
        mainFindings && mainFindings.length > 0 ? mainFindings.map((html: string, key: number) => (<p key={key} dangerouslySetInnerHTML={{ __html: html }} />)) : null
      }
    </div>
  </div>

}
export default EntranceMainFinding
