function Error({ statusCode }: any) {
  return (
    <div className="errorPage">
      { statusCode
        ? <div className="errorBox">
          <div className="number">{statusCode}</div>
          <div className="description">页面异常，请检查地址是否正常！</div>
        </div>
        : <div className="errorBox">
          <div className="description">客户端发生错误！</div>
        </div>
      }
    </div>
  )
}

Error.getInitialProps = ({ res, err }: any) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404
  console.log('res', res, 'err', err, 'statusCode', statusCode);
  return { statusCode }
}

export default Error