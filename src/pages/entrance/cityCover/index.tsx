// 入学测-市级入口页面
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import dynamic from 'next/dynamic';
import treeList from '~/lib/entrance/cityCover';
import styles from './index.module.less';
import API from '@/service/entrance/cityCover'

const ComplexComponents = dynamic(() => import('~/complexComponents'));

export const getServerSideProps: GetServerSideProps<{
  list: any[];
}> = async (ctx: any) => {
  try {;
    const { data } = await API.getGradeInfoList({}, ctx)
    return { props: { list: data } };
  } catch (error) {
    console.log(error);
    return { props: { list: [] } };
  }
};
const Page = ({
  list,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {

  return (
    <div {...{ className: styles['entrance-city'] }}>
      入学测-市级入口页面-test
      { list?.map((v: number, index: number) => (
        <p key={index}>{v}</p>
      )) }
      <ComplexComponents {...{ tree: treeList, data: list }} />
    </div>
  );
};
export default Page;
