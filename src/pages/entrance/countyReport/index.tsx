// 入学测-区县报告页面
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import React from 'react'
import treeList from '@/lib/entrance/countyReport/index';
import dynamic from 'next/dynamic';
import API from "@/service/entrance/county"
import { TCountyReportParams, TCountyReportResponse } from "@/service/entrance/county/types"
import styles from './index.module.less';

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseData: any;
  queryParams: any;
}> = async (ctx: any) => {
  const { templateId, gradeId, cityCode, areaCode, ff, isPrint, isDetailReport } = ctx.query as unknown as TCountyReportParams;
  const printMode = !!(ff || isPrint);
  const detailReportMode = isDetailReport ? Boolean(isDetailReport) : false;
  try {
    const areaReportReponse = await API.getAreaReport(
      { templateId, gradeId, cityCode, areaCode },
      ctx
    );
    return {
      props: {
        baseData: {
          areaReportReponse,
        },
        queryParams: {
          ...ctx.query,
          printMode,
          detailReportMode
        }
      }
    };
  } catch (error: any) {
    console.log("getAreaReport", error);
    const { code, msg } = error;
    return {
      props: {
        baseData: {
          success: false,
          code,
          msg
        },
        queryParams: {
          ...ctx.query,
          printMode,
          detailReportMode
        }
      },
    };
  }
};
const Page = ({
  baseData,
  queryParams
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {


  return (
    <div {...{ className: styles['entrance-county'] }}>
      <div className={styles.container}>
        <ComplexComponents {...{ tree: treeList, data: { baseData, queryParams }}} />
      </div>
    </div>
  );
};

export default Page;
