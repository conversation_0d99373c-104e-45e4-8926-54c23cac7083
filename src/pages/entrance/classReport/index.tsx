// 入学测-个人报告页面
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { message } from 'antd';
import React, { useEffect } from 'react';
import treeList from '@/lib/entrance/classReport';
import dynamic from 'next/dynamic';
import API from "@/service/entrance/class"
import { TQueryParams, TClassReportResponse } from "@/service/entrance/class/types"
import EBanner from '~/components/CommonBanner';
import bannerImg from '~/assets/entrance/entrance-banner-class.png';
import bannerImg370 from '~/assets/entrance/entrance-banner-class-370.png';
import footerImg from '~/assets/entrance/entrance-banner-person-footer.png';
import { mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';
import styles from './index.module.less';
import ECard from '~/components/ECard';
import moment from 'moment';

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseData: TClassReportResponse;
  queryParams: TQueryParams;
}> = async (ctx: any) => {
  const { evaluationTaskId, gradeId, classId, bizType, ff, isPrint, isDetailReport } = ctx.query as unknown as TQueryParams;
  const printMode = !!(ff || isPrint);
  const detailReportMode = isDetailReport ? Boolean(isDetailReport) : false;
  const queryParams = {
    ...ctx.query,
    printMode,
    detailReportMode
  }

  try {
    const report = await API.getClassReport({ evaluationTaskId, gradeId, classId, bizType, clientType: 1 }, ctx);
    return {
      props: {
        baseData: {
          ...report
        },
        queryParams
      }
    };
  } catch (error: any) {
    console.log("getClassReport", error);
    const { code, msg } = error;
    return {
      props: {
        baseData: {
          success: false,
          code,
          msg
        },
        queryParams
    },
    };
  }
};
const Page = ({
  baseData,
  queryParams
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { printMode, detailReportMode } = queryParams;
  useEffect(() => {
    if (baseData && !baseData.success) {
      message.error(baseData.msg)
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: baseData.code || '500',
          errorMessage: baseData.msg || '接口异常',
        })
      }
      return
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: detailReportMode ? 6000 : 3000
      })
    }
  }, [])
  return (
    <div {...{ className: styles['entrance-class'] }}>
      <EBanner imgSrc={ printMode ? bannerImg370 : bannerImg} height={ printMode ? 360 : 470} />
      <div className={styles.container}>
        <ECard wrapperstyle={{ marginBottom: printMode ? 10 : 30, padding: printMode ? '16px' : 24 }}>
          <div className={styles.overView}>
            <ul>
              <li>班级:<span>{baseData.data?.className}</span>
              </li>
              <li>毕业年份:<span>{baseData.data?.gradeName}</span>
              </li>
              <li>学校:<span>{baseData.data?.schoolName}</span>
              </li>
              <li>地区:<span>{baseData.data?.areaName}</span>
              </li>
              <li>
                报告日期:
                <span>{moment(baseData.data?.reportTime).format('YYYY-MM-DD')}</span>
              </li>
              <li>报告编码:<span>{baseData.data?.reportCode}</span>
              </li>
              <li>
              测评时间:
                <span>{moment(baseData.data?.startTime).format('YYYY-MM-DD')} -- {moment(baseData.data?.endTime).format('YYYY-MM-DD')}</span>
              </li>
              <li>
              参与报告人数:
                <span>{baseData.data?.evaluatedPeople}</span>
              </li>
            </ul>
          </div>
        </ECard>
        <ComplexComponents {...{ tree: treeList, data: { ...baseData?.data, queryParams} }} />
      </div>
      <EBanner imgSrc={footerImg} height={80} />
    </div>
  );
};

export default Page;
