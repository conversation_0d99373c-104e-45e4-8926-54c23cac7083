import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import React, { useEffect } from 'react';
import { message } from 'antd';
import dynamic from 'next/dynamic';
import moment from 'moment';
import treeList from '@/lib/entrance/gradeReport/index';
import api from "@/service/entrance/grade"
import { TGradeReportParams, TGradeReportResponse } from "@/service/entrance/grade/types"
import styles from './index.module.less';
import ECard from '~/components/ECard';
import EBanner from '~/components/CommonBanner';
import bannerImg from '~/assets/entrance/entrance-banner-grade.png';
import bannerImg370 from '~/assets/entrance/entrance-banner-grade-370.png';
import footerImg from '~/assets/entrance/entrance-banner-person-footer.png';
import { mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseData: TGradeReportResponse;
  queryParams: TGradeReportParams;
}> = async (ctx: any) => {
  const { evaluationTaskId, gradeId, classId, ff, isPrint, isDetailReport } = ctx.query as unknown as TGradeReportParams
  const printMode = !!(ff || isPrint);
  const detailReportMode = isDetailReport ? Boolean(isDetailReport) : false;
  const queryParams = {
    ...ctx.query,
    printMode,
    detailReportMode
  }
  try {
    const response = await api.getGradeReport(
      { evaluationTaskId, gradeId, classId },
      ctx
    );
    return {
      props: {
        baseData: {
          ...response
        },
        queryParams
      }
    };
  } catch (error: any) {
    console.log("getGradeReport", error);
    const { code, msg } = error;
    return {
      props: {
        baseData: {
          success: false,
          code,
          msg
        },
        queryParams
      },
    };
  }
};
const Page = ({
  baseData,
  queryParams
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { printMode, detailReportMode } = queryParams;
  useEffect(() => {
    if (baseData && !baseData.success) {
      message.error(baseData.msg)
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: baseData.code || '500',
          errorMessage: baseData.msg || '接口异常',
        })
      }
      return
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: detailReportMode ? 6000 : 3000
      })
    }
  }, [])
  return (
    <div {...{ className: styles['entrance-garde'] }}>
      <EBanner imgSrc={ bannerImg } height={370} />
      <div className={styles.container}>
        <ECard wrapperstyle={{ marginBottom: printMode ? 10 : 30, padding: printMode ? '16px' : 24 }}>
          <div className={styles.overView}>
            <ul>
              <li>毕业年份:<span>{baseData.data?.gradeName}</span>
              </li>
              <li>学校:<span>{baseData.data?.schoolName}</span>
              </li>
              <li>地区:<span>{baseData.data?.areaName}</span>
              </li>
              <li>
                报告日期:
                <span>{moment(baseData.data?.reportTime).format('YYYY-MM-DD')}</span>
              </li>
              <li>报告编码:<span>{baseData.data?.reportCode}</span>
              </li>
              <li>
              测评时间:
                <span>{moment(baseData.data?.startTime).format('YYYY-MM-DD')} -- {moment(baseData.data?.endTime).format('YYYY-MM-DD')}</span>
              </li>
              <li>
              参与报告人数:
                <span>{baseData.data?.evaluatedPeople}</span>
              </li>
            </ul>
          </div>
        </ECard>
        <ComplexComponents {...{ tree: treeList, data: { ...baseData?.data, queryParams} }} />
      </div>
      <EBanner imgSrc={footerImg} height={80} />
    </div>
  );
};

export default Page;
