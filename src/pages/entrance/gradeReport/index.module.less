.entrance-garde {
  font-size: 14px;
  .container {
    width: 1000px;
    box-sizing: border-box;
    margin: -30px auto 0;
  }
  .overView {
    li {
      display: inline-block;
      width: 310px;
      font-size: 18px;
      line-height: 36px;
      font-weight: 700;
      white-space: nowrap;
      span {
        font-weight: 300;
        display: inline-block;
        vertical-align: middle;
        padding-left: 10px;
        max-width: 90%;
        word-wrap:break-word;
      }
    }
  }
  .textBox {
    padding: 0 30px 20px;
    color:rgba(0, 17, 57, 0.85);
    p {
      line-height: 28px;
      font-size: 18px;
      margin-top: 20px;
      span {
        color: #162755;
        font-weight: 700;
      }
    }
    .center {
      text-align: center;
    }
  }
  .latitudeBeans {
    font-size: 18px;
    padding: 30px 0 0 20px;
    line-height: 1.5;
    ol {
      margin-bottom: 32px;
      li {
        list-style: disc;
        margin:15px 0 15px 20px;
      }
    }
  }

}
.report-main-finding-wrapper {
  padding: 30px 0 0 0;
  .report-main-finding-text {
    p {
      font-size: 18px;
      font-family: MicrosoftYaHeiUI;
      text-align: left;
      color: rgba(0, 17, 57, 0.85);
      line-height: 24px;
      padding-bottom: 16px;
      padding-left: 20px;
      span {
        font-weight: 700;
      }
    }
  }
}
.customTable {
    display: block;
    position: relative;
    text-align: left;
    margin-bottom: 30px;
    border-radius: 8px;
    thead > tr > th {
      background: #e3ebff;
      font-size: 16px;
      padding: 10px;
      text-align: center;
    }
    tbody > tr {
      &:nth-child(even) {
        background: #e3ebff;
      }
      td {
        font-size: 14px;
        padding: 10px 4px;
        text-align: center;
      }
      a {
        color: #fa8c16;
        text-decoration: underline;
      }
      .empty {
        color: #ccc;
      }
    }
    :global {
      .ant-pagination-item:hover,
      .ant-pagination-item-link:hover {
        border:1px solid #e3ebff;
        color: #e3ebff;
        a {
          color:#e3ebff;
        }
      }
      .ant-pagination-item-active {
        background: #e3ebff;
        border: none;
        a {
          color: #fff;
        }
      }
    }
    p {
      line-height: 24px;
      font-size: 18px;
      color: #373a44;
      margin-top: 15px;
    }
  }
.searchBox {
    display: block;
    button {
      height: 36px;
      background: #00c865;
      border-radius: 8px;
      font-size: 16px;
      border:0;
      color: #fff;
    }
  }
  .downloadBox {
    display:block;
    margin-bottom: 30px;
    text-align: right;
    button {
      margin-left: 30px;
      height: 36px;
      background: #00c865;
      border-radius: 8px;
      font-size: 16px;
      border:0;
      color: #fff;
      .iconDownload {
        display: inline-block;
      }
    }
  }