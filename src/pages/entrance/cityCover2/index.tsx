import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { useEffect, useState } from 'react';
import { Cascader, Modal } from 'antd';
import dynamic from 'next/dynamic';
import EBanner from '~/components/CommonBanner';
import treeList from '~/lib/entrance/cityCover2';
import bannerImg from '~/assets/entrance/entrance-banner.png';
import buttonCityEntrance from '~/assets/entrance/entrance-city-footer.png';
import styles from './index.module.less';
import API from '~/service/entrance/cityCover2';
import { useSyncState } from '~/utils/hooks';
import { objectToUrlParams } from '~/utils/tools'

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseInfo: any;
}> = async (ctx: any) => {
  try {
    const { data } = await API.getUserViewAuthority({ type: 1 }, ctx)
    const gradeTermRes = await API.getGradeInfoList({ cityCode: data.cityCode, areaCode: 0 }, ctx)
    return {
      props: {
        baseInfo: {
          authority: data,
          gradeInfoList: gradeTermRes.data
        }
      }
    };
  } catch (error: any) {
    console.log('getViewAuthority', error);
    const { code, msg } = error;
    return {
      props: {
        baseInfo: {
          authority: {
            success: false,
            code,
            msg
          },
          gradeInfoList: []
        }
      }
    };
  }
};

const Page = ({
  baseInfo,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { authority, gradeInfoList } = baseInfo;
  const [currentGrade, setCurrentGrade] = useSyncState<any[]>([gradeInfoList && gradeInfoList[0].value, gradeInfoList && gradeInfoList[0].children?.[0].value])
  const [currentData, setCurrentData] = useSyncState({
    ...authority,
    graduationYear: gradeInfoList && gradeInfoList[0].value,
    reportDateStr: gradeInfoList && gradeInfoList[0].children?.[0].value
  })
  const [areaOptions, setAreaOptions] = useState<any[]>([])
  const [areaCityEntrance, setAreaCityEntrance] = useState<any>({})

  // 初始化 权限判断
  const initAuthority = () => {
    if (!authority?.hasViewAuthority) {
      Modal.warning({
        onOk: () => {
          location.href = `${location.origin}`;
        },
        title: "无权限",
        content: "您暂无权限查看该报告",
      });
    }
    getAreaSelectOptions()
    getAreaCityEntranceList()
  }

  // 获取地区
  const getAreaSelectOptions = async () => {
    try {
      const { cityCode, areaCode=0, graduationYear, reportDateStr } = currentData.current;
      const { data: { schoolStaticsList } } = await API.getAreaCityEntranceList({ clientType:1, type: 1, cityCode, areaCode, graduationYear, reportDateStr })
      setAreaOptions(schoolStaticsList)
    } catch (error) {
      console.log('getAreaSelectOptions', error)
    }
  }

  // 获取区县报告
  const getAreaCityEntranceList = async () => {
    try {
      const { cityCode, graduationYear, reportDateStr } = currentData.current;
      const { data } = await API.getAreaCityEntranceList({ type: 1, cityCode, graduationYear, reportDateStr })
      setAreaCityEntrance(data)
    } catch (error) {
      console.log('getAreaCityEntranceList', error)
    }
  }

  // 切换 年级 学期
  const handleGrade = (values: any[]) => {
    setCurrentGrade([...values])
    setCurrentData({
      ...currentData.current,
      graduationYear: values[0],
      reportDateStr: values[1],
    })
    getAreaSelectOptions()
    getAreaCityEntranceList()
  };

  const handleJumpLink = () => {
    const { cityCode, areaCode, graduationYear, reportDateStr } = currentData.current;
    const params = {
      cityCode,
      areaCode,
      gradeId: graduationYear,
      graduationYear,
      reportDateStr,
      version: 2
    }
    window.open(`${location.origin}/psychology-service/entrance/cityReport?${objectToUrlParams(params)}`, '_blank')
  }

  useEffect(() => {
    initAuthority()
  }, [])
  return (
    <div {...{ className: styles['entrance-city'] }}>
      <EBanner imgSrc={bannerImg} height={250} />
      <div className={styles.container}>
        <div className={styles.location}>当前地址: {currentData.current.cityName}</div>
        <div className={styles.selectBox}>
          报告覆盖人群
          <Cascader
            options={gradeInfoList}
            defaultValue={currentGrade.current}
            placeholder="请选择报告覆盖人群"
            onChange={(value) => handleGrade(value)}
            allowClear={false}
            style={{ width: 320, marginLeft: 15, color: '#333' }}
          />
        </div>
        <div className={styles.note}>已有<strong>{areaCityEntrance?.schoolCount}</strong>所学校生成有效集体报告</div>
        <ComplexComponents {...{ tree: treeList, data: { currentData: currentData.current, areaOptions, areaCityEntrance } }} />
        <div className={styles.watching}>
          <p>注：</p>
          <p>支持查看最近一年内本地区布置高中生入学状态综合测评的学校数据</p>
          <p>地市报告生成条件：至少2所学校生成校级报告，且每所学校完成测评人数≥ 100</p>
          <p>查看区县报告条件：至少2所学校生成校级报告，且每所学校完成测评人数≥ 100</p>
          <p>查看集体报告条件：每所学校完成人数≥ 100人，且学校布置的测评作业至少有一个班级完成人数≥ 10</p>
        </div>
        <div className={styles.buttonCityEntrance}>
          <EBanner imgSrc={buttonCityEntrance} width={305} height={114} contentStyle={{cursor: 'pointer', display: 'inline-block' }} onClick={() => handleJumpLink()}/>
        </div>
      </div>
    </div>
  );
};
export default Page;
