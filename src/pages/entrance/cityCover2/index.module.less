.entrance-city {
  font-size: 14px;
  box-sizing: border-box;
  margin: 0 auto;
  color: rgba(0, 17, 57, 0.85);
  .location {
    position: relative;
    font-size: 18px;
    color: #fff;
    top: -100px;
  }
  .container {
    position: relative;
    top: -125px;
    width: 1000px;
    box-sizing: border-box;
    margin: 0 auto;
  }
  .selectBox {
    position: relative;
    z-index: 5;
    display: block;
    text-align: center;
    margin-bottom: 40px;
    color:#fff;
    font-size: 18px;
  }
  .downloadBox {
    display: inline-block;
    position: absolute;
    top: -60px;
    right: 0;
    margin-bottom: 30px;
    text-align: right;
    button {
      margin-left: 30px;
      height: 36px;
      background: #4b80ff;
      border-radius: 4px;
      font-size: 16px;
      border:0;
      color: #fff;
      .iconDownload {
        display: inline-block;
      }
    }
  }
  .customTable {
    display: block;
    position: relative;
    text-align: left;
    margin-bottom: 30px;
    border-radius: 8px;
    color: rgba(0,0,0,.65);
    thead > tr > th {
      background: #e3ebff;
      font-size: 16px;
      padding: 15px;
      text-align: center;
    }
    tbody > tr {
      &:nth-child(even) {
        background: #f3f7ff;
      }
      td {
        font-size: 16px;
        padding: 15px 4px;
        text-align: center;
        color: rgba(0,0,0,.65);
        border-bottom: 1px solid #e5eaf6 !important;
      }
      a {
        color: #fa8c16;
        // text-decoration: underline;
      }
      .empty {
        color: #ccc;
      }
    }

    p {
      line-height: 24px;
      font-size: 18px;
      color: #001139;
      margin-top: 15px;
    }
  }
  .note {
    margin: 0 auto;
    width: 432px;
    height: 54px;
    line-height: 54px;
    text-align: center;
    font-size: 24px;
    font-family: "MicrosoftYaHeiUI";
    color: #542c14;
    background: -webkit-gradient(linear, left top, right top, from(#f4c78e), to(#ffb05a));
    background: linear-gradient(90deg, #f4c78e, #ffb05a);
    border-radius: 30px;
    strong {
      font-weight: 700;
      font-size: 26px;
      padding: 0 4px;
    }
  }
  .watching {
    margin:20px 0 60px 40px;
    p {
      line-height: 28px;
      font-size: 16px;
    }
  }
  .buttonCityEntrance {
    margin:0 auto;
    text-align: center;
    img {
      cursor: pointer;
    }
  }
}
