.entrance-person {
  box-sizing: border-box;
  margin: 0 auto;
  font-size: 14px;
  .container {
    width: 1000px;
    box-sizing: border-box;
    margin: -80px auto 0;
  }
  .overView {
    li {
      display: inline-block;
      width: 230px;
      font-size: 18px;
      line-height: 36px;
      font-weight: 700;
      white-space: nowrap;
      span {
        font-weight: 300;
        display: inline-block;
        vertical-align: middle;
        padding-left: 10px;
        max-width: 90%;
        word-wrap:break-word;
      }
    }
  }
  .reportDescription {
    h3 {
      font-size: 18px;
      font-weight: 700;
      text-align: left;
      color: rgba(0, 17, 57, 0.85);
      line-height: 23px;
      padding-bottom: 30px;
    }
    p {
      font-size: 16px;
      text-align: left;
      color: rgba(0, 17, 57, 0.85);
      line-height: 24px;
      padding: 0 40px 40px 0;
    }
  }
}
.report-main-finding-wrapper {
  padding: 30px 0 0 0;
  .report-main-finding-text {
    p {
      font-size: 18px;
      font-family: MicrosoftYaHeiUI;
      text-align: left;
      color: rgba(0, 17, 57, 0.85);
      line-height: 24px;
      padding-bottom: 16px;
      padding-left: 20px;
      span {
        font-weight: 700;
      }
    }
  }
}

