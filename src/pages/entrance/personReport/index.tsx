// 入学测-个人报告页面
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import React, { useEffect } from 'react';
import styles from './index.module.less';
import treeList from '@/lib/entrance/personReport';
import dynamic from 'next/dynamic';
import api from '@/service/entrance/person';
import { TPersonReportParams, TUserReportResponse } from '@/service/entrance/person/types';
import EBanner from '~/components/CommonBanner';
import bannerImg from '~/assets/entrance/entrance-banner-person.png';
import bannerImg370 from '~/assets/entrance/entrance-banner-person-370.png';
import footerImg from '~/assets/entrance/entrance-banner-person-footer.png';
import ECard from '~/components/ECard';
import { message } from 'antd';
import { mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';
import moment from 'moment';
const ComplexComponents = dynamic(() => import('~/complexComponents'));

export const getServerSideProps: GetServerSideProps<{
  baseData: TUserReportResponse;
  queryParams: TPersonReportParams;
}> = async (ctx: any) => {
  const { evaluationRecordId, version, ff, isPrint, isDetailReport } = ctx.query as unknown as TPersonReportParams;
  const printMode = !!(ff || isPrint);
  const detailReportMode = isDetailReport ? Boolean(isDetailReport) : false;
  try {
    const reportData = await api.getUserReport({ evaluationRecordId, version, clientType: 1 }, ctx);
    return {
      props: {
        baseData: reportData,
        queryParams: {
          ...ctx.query,
          printMode,
          detailReportMode
        }
      }
    };
  } catch (error: any) {
    console.log('getUserReport', error);
    const { code, msg } = error;
    return {
      props: {
        baseData: {
          success: false,
          code,
          msg
        },
        queryParams: {
          ...ctx.query,
          printMode,
          detailReportMode
        }
      },
    };
  }
};
const Page = ({
  baseData,
  queryParams
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {

  const { printMode, detailReportMode } = queryParams;

  useEffect(() => {
    if (baseData && !baseData.success) {
      message.error(baseData.msg)
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: baseData.code || '500',
          errorMessage: baseData.msg || '接口异常',
        })
      }
      return
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: detailReportMode ? 6000 : 3000
      })
    }
  }, [])

  return (
    <div {...{ className: styles['entrance-person'] }}>
      <EBanner imgSrc={ printMode ? bannerImg370 : bannerImg} height={ printMode ? 320 : 470} />
      <div className={styles.container} style={ printMode ? { marginTop: -60 }: {marginTop: -80}}>
        <ECard wrapperstyle={{ marginBottom: printMode ? 10 : 30}}>
          <div className={styles.overView}>
            <ul>
              <li>姓名:<span>{baseData.data?.userName}</span>
              </li>
              <li>班级:<span>{baseData.data?.className}</span>
              </li>
              <li>年级:<span>{baseData.data?.gradeName}</span>
              </li>
              <li>学校:<span>{baseData.data?.schoolName}</span>
              </li>
              <li>地区:<span>{baseData.data?.areaName}</span>
              </li>
              <li>
                报告日期:
                <span>{moment(baseData.data?.reportTime).format('YYYY-MM-DD')}</span>
              </li>
              <li>报告编码:<span>{baseData.data?.reportCode}</span>
              </li>
            </ul>
          </div>
        </ECard>
        <ComplexComponents {...{ tree: treeList, data: { ...baseData?.data, queryParams} }} />
      </div>
      <EBanner imgSrc={footerImg} height={80} />
    </div>
  );
};

export default Page;
