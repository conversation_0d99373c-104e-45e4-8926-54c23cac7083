.entrance-county {
  font-size: 14px;
  box-sizing: border-box;
  margin: 0 auto;
  color: rgba(0, 17, 57, 0.85);
  .location {
    position: relative;
    font-size: 18px;
    color: #fff;
    top: -200px;
  }
  .container {
    position: relative;
    top: -30px;
    width: 1000px;
    box-sizing: border-box;
    margin: 0 auto;
  }
  .selectBox {
    position: relative;
    z-index: 5;
    display: block;
    text-align: center;
    margin-bottom: 40px;
    color:#fff;
    font-size: 18px;
  }
  .downloadBox {
    display: inline-block;
    position: absolute;
    top: -60px;
    right: 0;
    margin-bottom: 30px;
    text-align: right;
    button {
      margin-left: 30px;
      height: 36px;
      background: #00c865;
      border-radius: 8px;
      font-size: 16px;
      border:0;
      color: #fff;
      .iconDownload {
        display: inline-block;
      }
    }
  }
  .customTable {
    display: block;
    position: relative;
    text-align: left;
    margin-bottom: 30px;
    border-radius: 8px;
    thead > tr > th {
      background: #e3ebff;
      font-size: 16px;
      padding: 10px;
      text-align: center;
    }
    tbody > tr {
      &:nth-child(even) {
        background: #f3f7ff;
      }
      td {
        font-size: 14px;
        padding: 10px 4px;
        text-align: center;
      }
      a {
        color: #fa8c16;
        // text-decoration: underline;
      }
      .empty {
        color: #ccc;
      }
    }

    p {
      line-height: 24px;
      font-size: 18px;
      color: #001139;
      margin-top: 15px;
    }
  }
  .note {
    margin: -30px auto 0;
    width: 474px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    font-size: 27px;
    color: #542c14;
    background: -webkit-gradient(linear, left top, right top, from(#f4c78e), to(#ffb05a));
    background: linear-gradient(90deg, #f4c78e, #ffb05a);
    border-radius: 30px;
  }
  .watching {
    margin-bottom:40px;
    p {
      line-height: 28px;
      font-size: 16px;
    }
  }
  .buttonEntrance {
    margin:0 auto;
    text-align: center;
    img {
      cursor: pointer;
    }
  }
}
