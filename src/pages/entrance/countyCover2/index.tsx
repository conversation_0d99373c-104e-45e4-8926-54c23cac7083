import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { useEffect, useState } from 'react';
import { Modal } from 'antd';
import dynamic from 'next/dynamic';
import treeList from '@/lib/entrance/countyCover2';
import API from '@/service/entrance/countyCover2';
import EBanner from '~/components/CommonBanner';
import bannerImg from '~/assets/entrance/entrance-banner-county-cover.png';
import buttonCountyEntrance from '@/assets/entrance/entrance-county-cover-footer.png';
import styles from './index.module.less';
import { objectToUrlParams } from '~/utils/tools';
import { areaCitySchoolEntranceListInfoProps } from '~/service/entrance/countyCover2/types';

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseInfo: any;
}> = async (ctx: any) => {
  try {
    const authorityRespones = await API.getUserViewAuthority({ type: 2 }, ctx)
    const { data } = authorityRespones;
    const gradeTermRes  = await API.getGradeInfoList({ cityCode: data.cityCode, areaCode: data.areaCode }, ctx)
    return { props: { baseInfo: { authority: authorityRespones, gradeTerm: gradeTermRes } }};
  } catch (error: any) {
    console.error('getUserViewAuthority', error);
    const { code, msg } = error;
    return {
      props: {
        baseInfo: {
          authority: {
            success: false,
            code,
            msg
          },
          gradeTerm: {
            success: false,
            code,
            msg
          }
        }
      }
    };
  }
};

const Page = ({
  baseInfo,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { authority, gradeTerm: { data: gradeTermList } } = baseInfo;
  const [currentData] = useState({ ...authority.data, reportDateStr: gradeTermList[0]?.children[0]?.value, graduationYear: gradeTermList[0]?.value})
  const [countyCoverEntrance, setCountyCoverEntrance] = useState<areaCitySchoolEntranceListInfoProps>({
    schoolList: [],
    schoolCount: 0
  })

  const initAuthority = () => {
    const { data } = authority;
    if (!data?.hasViewAuthority) {
      Modal.warning({
        onOk: () => {
          location.href = "https://www.ewt360.com/";
        },
        title: "无权限",
        content: "您暂无权限查看该报告",
      });
      return
    }

    if (data?.hasViewAuthority && data?.cityCode && !data?.areaCode) {
      location.replace(`${location.origin}/psychology-service/entrance/cityCover2`)
      return
    }
    getAreaCitySchoolEntranceListInfo()
  }

  const getAreaCitySchoolEntranceListInfo = async () => {
    try {
      const { cityCode, areaCode, graduationYear, reportDateStr } = currentData;
      const { data } = await API.getAreaCitySchoolEntranceListInfo({ type: 2, clientType: 1, cityCode, areaCode, graduationYear, reportDateStr })
      setCountyCoverEntrance(data)
    } catch (error) {
      console.log('getAreaCitySchoolEntranceListInfo', error)
    }
  }

  const handleJumpLink = () => {
    const { cityCode, areaCode, graduationYear, reportDateStr } = currentData;
    const params = {
      cityCode,
      areaCode,
      gradeId: graduationYear,
      graduationYear,
      reportDateStr,
      version: 2
    }
    const link = `${location.origin}/psychology-service/entrance/countyReport?${objectToUrlParams(params)}`
    window.open(link)
  }

  useEffect(() => {
    initAuthority()
  },[])

  return (
    <div className={styles['entrance-county']}>
      <EBanner imgSrc={bannerImg} height={250} />
      <div className={styles.container}>
        <div className={styles.location}>当前地址: {currentData.cityName}-{currentData.areaName}</div>
        <div className={styles.note}>已有 <strong>{ countyCoverEntrance?.schoolCount }</strong> 所学校生成有效集体报告</div>
        <ComplexComponents {...{ tree: treeList, data: { currentData, gradeTermList, countyCoverEntrance }}} />
        <div className={styles.watching}>
          <p>注：</p>
          <p>1、支持查看最近一年内本地区布置高中生入学状态综合测评的学校数据</p>
          <p>2、区县报告生成条件：至少2所学校生成校级报告，且每所学校完成测评人数≥ 100</p>
          <p>3、查看集体报告条件：每所学校完成人数≥ 100人，且学校布置的测评作业至少有一个班级完成人数≥ 10</p>
        </div>
        <div className={styles.buttonEntrance}>
          <EBanner imgSrc={buttonCountyEntrance} width={305} height={114} contentStyle={{cursor: 'pointer', display: 'inline-block' }} alt="查看区县报告" onClick={() => handleJumpLink()}/>
        </div>
      </div>
    </div>
  );
};
export default Page;
