
// 查看市区县与权限
export interface viewAuthorityProps {
  areaCode?: string | number | null;
  areaName?: string | null;
  cityCode?: string | number;
  cityName?: string;
  hasViewAuthority?: boolean;
}

export interface gradeItem {
  label: string;
  value: number;
  children?: gradeItem[]
}

export interface areaSelectItem {
  areaId: number;
  areaName: string;
}

export interface schoolStaticsItem {
  areaCode: number;
  areaName: string;
  cityCode: number;
  finishedNum: number;
  finishedPercentage: number;
  gradeName: string;
  graduationYear: number;
  hasAreaReport: number | null;
  schoolNum: number;
  totalNum: number;
}

export interface areaCityEntranceProps {
  schoolCount?: number;
  schoolStaticsList?: schoolStaticsItem[];
}

export interface schoolItem {
  answerTaskId: string | null;
  areaCode: number;
  areaName: string;
  assignDate: number | null;
  endDate: number | null;
  finishedNum: number;
  finishedPercentage: number;
  gradeFlag: number | null;
  gradeName: string;
  schoolCode: number;
  schoolName: string;
  totalNum: number;
}

export interface areaCitySchoolEntranceProps {
  schoolCount?: number | null;
  schoolList?: schoolItem[];
}

export interface baseInfoProps {
  authority: viewAuthorityProps,
  gradeInfoList: gradeItem[]
}