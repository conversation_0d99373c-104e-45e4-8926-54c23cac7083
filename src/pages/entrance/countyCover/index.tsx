/*
 * @Author: twenty-five
 * @Date: 2022-08-03 21:40:10
 * @LastEditors: your name
 * @LastEditTime: 2022-08-03 21:48:33
 * @Description: file content
 */

// 入学测-区县入口页面
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import dynamic from 'next/dynamic';
import treeList from '~/lib/entrance/countyCover';
import styles from './index.module.less';
import API from '@/service/entrance/countyCover'
const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  list: object[];
}> = async (ctx: any) => {
  try {
    // const { query } = ctx;
    const { data } = await API.getGradeInfoList({}, ctx)
    return { props: { list: data } };
  } catch (error) {
    console.log(error);
    return { props: { list: [] } };
  }
};
const Page = ({
  list,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  return (
    <div {...{ className: styles['entrance-county'] }}>
      入学测-区县入口页面-test
      <ComplexComponents {...{ tree: treeList, data: list }} />
    </div>
  );
};
export default Page;
