// 入学测-区县报告页面
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import React from 'react'
import treeList from '@/lib/entrance/cityReport/index';
import dynamic from 'next/dynamic';
import api from "@/service/entrance/city"
import { TCityReportParams, TCityReportResponse } from "@/service/entrance/city/types"
import styles from './index.module.less';
const ComplexComponents = dynamic(() =>
	import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
	basicData: TCityReportResponse
}> = async (ctx: any) => {
	try {
		const { templateId, gradeId, cityCode } = ctx.query as unknown as TCityReportParams
		const { data: basicData } = await api.getCityReport(
			{ templateId, gradeId, cityCode },
			ctx
		);
		return { props: { basicData } };
	} catch (error) {
		console.log(error, "error");
		return {
			props: { basicData: {} },
		};
	}
};
const Page = ({
	basicData
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {

	return (
		<div {...{ className: styles['entrance-city'] }}>
			{/* 入学测-班级报告页面-test */}
			<div className={styles.container}>
				<ComplexComponents {...{ tree: treeList, data: basicData }} />
			</div>
		</div>
	);
};

export default Page;
