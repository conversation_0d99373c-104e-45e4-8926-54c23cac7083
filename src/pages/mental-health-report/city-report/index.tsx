// 心理健康测北师大版-市级报告
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { useEffect, useState } from 'react';
import { Modal, Select } from 'antd';
import dynamic from 'next/dynamic';
import EBanner from '~/components/CommonBanner';
import treeList from '@/lib/mental-health-report/city-report';
import bannerImg from '@/assets/mental-health-report/mentalcover.png';
import styles from './index.module.less';
import API from '@/service/mental-health-report/city-report';
import { TQueryParams } from '@/service/mental-health-report/city-report/types'
import { objectToUrlParams } from '@/utils/tools'

const { Option } = Select;

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseInfo: any;
}> = async (ctx: any) => {

  try {
    const { cityCode, gradeCode, gradeId, semester } = ctx.query as TQueryParams;
    const params = {
      cityCode, gradeCode, gradeId, semester
    }

    // const baseInfoResponse = await API.getCityBaseInfo({ ...params, clientType: 1 }, ctx)
    // const currentPerformanceResponse = await API.getCityCurrentPerformance(params, ctx)
    // const personalityReasonsResponse = await API.getCityPersonalityReasons(params, ctx)
    // const environmentalReasonsResponse = await API.getCityEnvironmentalReasons(params, ctx)
    // const focusSchoolListResponse = await API.getCityFocusSchoolList(params, ctx)
    // const cognitiveReasonsResponse = await API.getCityCognitiveReasons(params, ctx)
    // const warningDetailResponse  = await API.getPageCityWarningDetail(params, ctx)

    return { props: {
      baseInfo: {
        // cityBaseInfo: baseInfoResponse.data,
        // currentPerformance: currentPerformanceResponse.data,
        // personalityReasons: personalityReasonsResponse.data,
        // environmentalReasons: environmentalReasonsResponse.data,
        // cognitiveReasons: cognitiveReasonsResponse.data,
        // focusSchoolList: focusSchoolListResponse.data,
        // warningDetail: warningDetailResponse.data
      }
    }};
  } catch (error) {
    console.log(error);
    return { props: { baseInfo: {}}};
  }
};

const Page = ({
  baseInfo,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {

  useEffect(() => {
  },[])
  // 当前页面没有使用，先注释掉
  return (
    <div {...{ className: styles['psychology-city-report'] }}>
      <div className={styles.container}>
        <EBanner imgSrc={bannerImg} height={749} />
        {/* <ComplexComponents {...{ tree: treeList, data: { ...baseInfo }  }} /> */}
      </div>
    </div>
  );
};
export default Page;
