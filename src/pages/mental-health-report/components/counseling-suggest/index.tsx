import React, { Fragment } from 'react';
import styles from './index.module.less';
import { ICounselingSuggest, ISuggestionFactorInfo } from '~/service/mental-health-report/person-report/types';
import LabelLineTitle from '../label-line-title';
import { EReportType } from '../../types.d';

interface ICounselingSuggestProps {
  reportType: EReportType;
  list: ICounselingSuggest[],
  defaultSuggest?: (string | any)[];
}

// 辅导建议
const CounselingSuggest: React.FC<ICounselingSuggestProps> = (props) => {
  const { list, defaultSuggest, reportType } = props;
  return (
    <div className={styles['counseling-suggest-container']}>
      {list?.length !== 0 && (
        <p className={styles.tip}>
          {reportType === EReportType.person ? '该生' : reportType === EReportType.class ? '本班学生' : '本年级学生'}{reportType === EReportType.person ? '在部分维度上存在不同程度的风险' : '在部分维度上要差于全国平均水平'} ，建议老师可以从以下层面入手进行干预：
        </p>
      )}
      {list?.map((item: ICounselingSuggest, index: number) => {
        const texts = item.analysisFactorSuggestionList?.map((item: ISuggestionFactorInfo) => `${item.counselingAdvice}<br />`) || [];
        const textDoms = texts?.join('<br />');
        return (
          textDoms ?
            <Fragment key={`counseling-suggest-key-${index + 1}`}>
              <LabelLineTitle label={`${item.latitudeGroupName}层面`} remarkText={textDoms} />
            </Fragment> : null
        );
      })}

      {list?.length === 0 && defaultSuggest?.length !== 0 && (
        <div className={styles['remark-text']}>
          {defaultSuggest?.map((item: string, index: number) => (
            <div key={`mental-person-report-suggest-mep-key-${index + 1}`}>
              {item}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CounselingSuggest;
