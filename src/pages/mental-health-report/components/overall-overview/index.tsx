import React, { Fragment, useState } from 'react';
import styles from './index.module.less';
import ArrowLineTitle from '../arrow-line-title';
import TestRiskResult from '../test-risk-result';
import type { IFactorItem } from '../../types';
import { EReportType, ERiskLevel } from '../../types.d';
import AttackFlag from '../attack-flag';
import LabelLineTitle from '../label-line-title';
import FactorRadar from '../factor-radar';
import FactorList from './components/factor-list';
import { IFactorDetail, IUserResultFactors } from '~/service/mental-health-report/person-report/types';
import FactorRemarkModal from '~/businessComponents/FactorRemarkModal';
import Api from '~/service/mental-health-report/person-report';
import CustomPie from '~/businessComponents/CustomPie';
import { ELayoutType } from '~/businessComponents/CustomPie/types.d';
import RiskFlagDemo from '../risk-flag-demo';
import FactorDistributionBar from '../factor-distribution-bar';
import { cls } from '~/utils/tools';

interface IOverAllOverviewProps {
  data: any;
  groupName: string;
  printMode: boolean;
  reportType: EReportType;
  protectFactors: IUserResultFactors[];
  riskFactors: IUserResultFactors[];
  reportId: string;
}

// 总体概况
const OverAllOverview: React.FC<IOverAllOverviewProps> = (props) => {
  const {
    data, groupName, printMode, reportType, protectFactors, riskFactors
  } = props;
  const [factorRemarkVisible, setFactorRemarkVisible] = useState<Boolean>(false);
  const [factorRemarkInfo, setFactorRemarkInfo] = useState<IFactorDetail | null>(null);
  const tipText = reportType === EReportType.person ? '生' : reportType === EReportType.class ? '班级' : '年级';

  const allData = JSON.parse(JSON.stringify(data?.overallRisk?.classStatusDistributionBean || []));
  const inData = JSON.parse(JSON.stringify(data?.inwardAttack?.classStatusDistributionBean || []));
  const outData = JSON.parse(JSON.stringify(data?.outwardAttack?.classStatusDistributionBean || []));

  // 风险注释数组
  const remarks = [
    { label: '总体风险', text: data?.overallRisk?.remarks },
    { label: '向内攻击风险', text: data?.inwardAttack?.remarks },
    { label: '向外攻击风险', text: data?.outwardAttack?.remarks },
  ];

  const advantageFactors: IUserResultFactors[] = []; // 优势因子
  const developFactors: IUserResultFactors[] = []; // 待发展因子
  const highRiskFactors: IUserResultFactors[] = []; // 高风险因子

  // 遍历找出优势、待发展、风险因子
  protectFactors?.forEach((item: IUserResultFactors) => {
    if (item.riskLevel === ERiskLevel.health) {
      advantageFactors.push(item);
    } else {
      developFactors.push(item);
    }
  });
  riskFactors?.forEach((item: IUserResultFactors) => {
    if (item.riskLevel !== ERiskLevel.health) {
      highRiskFactors.push(item);
    }
  });

  // 获取因子详情
  const onFactorRemarkClick = async (item: IFactorItem) => {
    try {
      const { data } = await Api.getFactorRemarkDetail({
        latitudeId: item.latitudeId
      });

      if (data) {
        setFactorRemarkVisible(true);
        setFactorRemarkInfo({
          ...data,
          riskLevel: item.riskLevel,
          latitudeName: item.latitudeName
        });
      }
    } catch (error) {
      console.log('获取因子详情出错', error);
    }
  };

  // 获取柱状图容器的高度，如果小于400就是使用400
  const getBarBoxHeight = (list: any) => {
    const curHeight = list?.length * 55 || 400;
    if (curHeight < 400) {
      return 400;
    }
    return curHeight;
  };

  return (
    <div className={styles['overall-overview-container']}>
      <ArrowLineTitle title="总体风险" />
      {/* 总体风险结果信息 */}
      <TestRiskResult
        groupName={groupName}
        riskLevelName={data?.overallRisk?.riskLevelName}
        riskLevel={data?.overallRisk?.riskLevel}
      />

      {/* 非个人场景需要显示总体风险饼图、柱状图 */}
      {reportType !== EReportType.person && (
        <div className={styles['risk-distribution-box']}>
          <CustomPie
            layout={ELayoutType.horizontal}
            classType={reportType === EReportType.class ? '班级' : '年级'}
            riskType='总体'
            list={data?.overallRisk?.statusDistributionBean || []}
            title="风险状态分布"
          />

          {/* 年级需要显示各班风险分布柱状图 */}
          {reportType === EReportType.grade && (
            <Fragment>
              <LabelLineTitle
                label="各班风险分布情况"
                remarkText=""
                customStyle={{ marginLeft: 24 }}
              />
              <RiskFlagDemo isCenter={true} customStyle={{ paddingTop: 4 }} />
              <FactorDistributionBar
                dataList={allData.reverse()}
                reportType={reportType}
                nameKey='className'
                height={getBarBoxHeight(allData)}
              />
            </Fragment>
          )}
        </div>
      )}

      <div
        className={styles['attack-risk-box']}
        style={{ paddingTop: reportType === EReportType.person ? 12 : 0 }}
      >
        {reportType !== EReportType.grade ? (
          <Fragment>
            <div className={styles['attack-flag-list-box']}>
              <AttackFlag label="向内" data={data?.inwardAttack} />
              <AttackFlag label="向外" data={data?.outwardAttack} customStyle={{ marginLeft: 12 }} />
            </div>
            {/* 班级场景需要显示内外攻击的分布图 */}
            {reportType !== EReportType.person && (
              <div className={styles['risk-distribution-list-box']}>
                <CustomPie
                  layout={ELayoutType.vertical}
                  classType='班级'
                  riskType='向内攻击'
                  list={data?.inwardAttack?.statusDistributionBean || []}
                  title="风险状态分布"
                />
                <CustomPie
                  layout={ELayoutType.vertical}
                  classType='班级'
                  riskType='向外'
                  list={data?.outwardAttack?.statusDistributionBean || []}
                  title="风险状态分布"
                />
              </div>
            )}

          </Fragment>
        ) : (
          <Fragment>
            <div className={cls([styles['attack-flag-list-box'], styles['layout-h']])}>
              {/* 向内攻击信息 */}
              <div className='singlePrint'>
                <AttackFlag
                  label="向内"
                  data={data?.inwardAttack}
                  className={cls([styles['grade-layout-item']])}
                />
                <div className={styles['grade-overview-list-box']}>
                  {/* 向内攻击-风险分布 */}
                  <CustomPie
                    layout={ELayoutType.horizontal}
                    classType="年级"
                    riskType='总体'
                    list={data?.inwardAttack?.statusDistributionBean || []}
                    title="风险状态分布"
                  />
                  <LabelLineTitle
                    label="各班风险分布情况"
                    remarkText={
                      <Fragment>
                        <RiskFlagDemo
                          isCenter={true}
                          customStyle={{ paddingTop: 24, paddingBottom: 32 }}
                        />
                        <FactorDistributionBar
                          dataList={inData.reverse()}
                          reportType={reportType}
                          nameKey='className'
                          height={getBarBoxHeight(inData)}
                        />
                      </Fragment>
                    }
                  />
                </div>
              </div>

              <div className='singlePrint'>
                {/* 向外攻击信息 */}
                <AttackFlag
                  label="向外"
                  data={data?.outwardAttack}
                  className={cls([styles['grade-layout-item']])}
                />
                <div className={styles['grade-overview-list-box']}>
                  {/* 向外攻击 - 风险分布 */}
                  <CustomPie
                    layout={ELayoutType.horizontal}
                    classType="年级"
                    riskType='总体'
                    list={data?.outwardAttack?.statusDistributionBean || []}
                    title="风险状态分布"
                  />
                  <LabelLineTitle
                    label="各班风险分布情况"
                    remarkText={
                      <Fragment>
                        <RiskFlagDemo
                          isCenter={true}
                          customStyle={{ paddingTop: 24, paddingBottom: 32 }}
                        />
                        <FactorDistributionBar
                          dataList={outData.reverse()}
                          reportType={reportType}
                          nameKey='className'
                          height={getBarBoxHeight(outData)}
                        />
                      </Fragment>
                    }
                  />
                </div>
              </div>
            </div>
          </Fragment>
        )}
        <hr className={cls([
          styles['dashed-line'],
          reportType !== EReportType.person ? 'singlePrint' : ''
        ])} />
        <FactorRadar
          protectFactors={protectFactors}
          riskFactors={riskFactors}
          reportType={reportType}
        />
      </div>

      {/* 以下为保护和风险因子的雷达图相关，多报告场景一致 */}
      {advantageFactors.length !== 0 && (
        <FactorList
          title="优势因子"
          titleColor="#13C2C2"
          tipText={`以下保护因子分数高，说明该${tipText}在这些因子上表现优秀`}
          list={advantageFactors}
          onClick={(item: IFactorItem) => onFactorRemarkClick(item)}
          isPrint={printMode}
        />
      )}

      {developFactors.length !== 0 && (
        <FactorList
          title="待发展保护因子"
          titleColor="#FA8C16"
          tipText={`以下保护因子分数较低，说明该${tipText}学生在这些因子上表现一般，需要进一步鼓励支持。`}
          list={developFactors}
          onClick={(item: IFactorItem) => onFactorRemarkClick(item)}
          isPrint={printMode}
        />
      )}

      {highRiskFactors.length !== 0 && (
        <FactorList
          title="需要关注的因子"
          titleColor="#EB2F96"
          tipText={`以下风险因子分数高，说明该${tipText}学生在这些因子上表现一般，需要进一步关注。`}
          list={highRiskFactors}
          onClick={(item: IFactorItem) => onFactorRemarkClick(item)}
          isPrint={printMode}
        />
      )}

      <ArrowLineTitle title="注释" className="singlePrint" />
      {remarks.map((item: any, index: number) => (
        <LabelLineTitle
          key={`overview-risk-remarks-key-${index + 1}`}
          label={item.label} remarkText={item.text} />
      ))}

      {factorRemarkVisible && (
        <FactorRemarkModal
          onClose={() => {
            setFactorRemarkInfo(null);
            setFactorRemarkVisible(false);
          }}
          factorRemark={factorRemarkInfo}
          title={factorRemarkInfo?.latitudeName} />
      )}
    </div>
  );
};

export default OverAllOverview;
