import React from 'react';
import styles from './index.module.less';
import { cls } from '~/utils/tools';
import { IFactorItem, IFactorRemark } from '~/pages/mental-health-report/types';

interface IFactorListProps {
  list: IFactorItem[];
  title: string;
  titleColor: string;
  tipText: string;
  onClick?: (item: IFactorItem) => void;
  isPrint: boolean;
}

// 指定范围的因子列表
const FactorList: React.FC<IFactorListProps> = (props) => {
  const { list = [], title, titleColor, tipText, onClick, isPrint = false } = props;
  
  // 快速按照顺序从高到低排序
  const quickSort: any = (arr: any[]) => {
    if (arr.length <= 1) { return arr; }
    const pivotIndex = Math.floor(arr.length / 2);
    const pivot = arr.splice(pivotIndex, 1)[0];
    const left = [];
    const right = [];
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].riskLevel > pivot.riskLevel) {
        left.push(arr[i]);
      } else {
        right.push(arr[i]);
      }
    }
    return quickSort(left).concat([pivot], quickSort(right));
  };

  const newList = quickSort([...list]); // 排序过后的数据

  return (
    <div className={styles['factor-list-container']}>
      <div className={styles['left-box']}>
        <span>{title}</span>
        <span style={{ color: titleColor }}>{newList?.length || 0}个</span>
      </div>
      <div className={styles['right-box']}>
        {!isPrint && (
          <div className={styles['tip-text-box']}>
            <span>{tipText}</span>
            <span>点击标签，展示对应说明</span>
          </div>
        )}
        <ul className={styles['factor-list-box']}>
          {newList?.map((item: IFactorItem, index: number) => {
            // @ts-ignore
            const clsName: any = styles[`risk-level-${item.riskLevel}`];
            return (
              <li
                onClick={() => onClick && onClick(item)}
                key={`${item.latitudeId}_${index + 1}_key`}
                className={clsName}>
                {item.latitudeName}
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default FactorList;
