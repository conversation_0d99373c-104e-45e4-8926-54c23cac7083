.overall-overview-container {
  background-color: #fff;
  box-shadow: 0 4px 15px 0 #0000001a;
  padding-bottom: 24px;
  border-radius: 8px;

  // 攻击风险容器
  .attack-risk-box {
    padding: 0 40px;

    // 风险flag盒子
    .attack-flag-list-box {
      display: flex;
      height: 64px;
      margin: 0 0 8px;

      &>div {
        width: 50%;
      }

      &.layout-h {
        flex-wrap: wrap;
        height: auto;
        display: block;

        .grade-layout-item {
          width: 100%;
          height: 64px;
        }

        .grade-layout-item:nth-child(2) {
          margin-left: 0;
        }

        &>div {
          width: 100%;
        }
      }

      .grade-overview-list-box {
        border: 1px solid #CCC;
        border-radius: 8px;
        margin-top: 8px;
        margin-bottom: 24px;

        &>div:nth-child(2) {
          margin-left: 24px;
        }
      }

      // 最后一个向外攻击容器不需要下边距
      &>div:last-child {
        margin-bottom: 0;
      }
    }

    // 一条虚线
    .dashed-line {
      border: 0;
      border-top: 1px dashed #CCCCCC;
      margin-top: 30px;
    }

    // 雷达图容器
    .radar-chart-list-box {
      display: flex;
      align-items: center;
    }
  }

  .overall-remarks {
    padding: 0;
  }

  // 总体风险分布
  .risk-distribution-box {
    margin: 8px 40px 24px;
    border: 1px solid #CCC;
    border-radius: 8px;
  }

  .risk-distribution-list-box {
    display: flex;
    align-items: center;
    margin-bottom: 31px;

    &>div {
      border: 1px solid #CCCCCC;
      border-radius: 8px;
    }

    &>div:nth-child(2) {
      margin-left: 12px;
    }
  }
}