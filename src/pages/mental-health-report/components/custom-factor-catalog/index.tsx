import React, { useEffect, useState } from "react";
import { riskLevelClassName } from '@/utils/const';
import { Table } from 'antd';
import styles from './styles/index.module.less';
import CommonStyles from '~/styles/common.module.less';
import { cls } from '~/utils/tools';
import { IFactorCatalogRemark } from '~/service/mental-health-report/person-report/types';

interface ICustomRiskTableProps {
  data: IFactorCatalogRemark[],
  children?: any;
}

const CustomFactorCatalog: React.FC<any> = (props: ICustomRiskTableProps) => {
  const { children, data } = props;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const columns = [
    {
      title: '',
      dataIndex: 'firstTypeName',
      rowScope: 'row',
      width: 46,
      onCell: (record: any) => ({
        rowSpan: record.firstRowSpan,
        className: cls([
          CommonStyles['text-center'],
          CommonStyles['table-head']
        ])
      })
    },
    {
      title: '一级维度',
      dataIndex: 'secondTypeName',
      width: 90,
      onCell: (record: any) => ({
        rowSpan: record.secondRowSpan,
        className: cls([
          CommonStyles['text-center'],
          CommonStyles['table-head'],
          CommonStyles['font-size-14']
        ])
      })
    },
    {
      title: '二级维度',
      dataIndex: 'latitudeName',
      width: 85,
      onCell: () => ({
        className: cls([
          CommonStyles['text-center'],
          styles['second-bg']
        ])
      })
    },
    {
      title: '维度解释',
      width: 200,
      dataIndex: 'factorDescription',
      onCell: (record: any) => ({
        className: cls([
          record?.riskLevel && `${riskLevelClassName[record.riskLevel - 1]}`,
          styles['normal-td']
        ])
      }),
    },
    {
      title: '高分表现',
      width: 294,
      dataIndex: 'highScorePerformance',
      onCell: () => ({
        className: cls([
          styles['normal-td']
        ])
      })
    },
    {
      title: '低分表现',
      width: 200,
      dataIndex: 'lowScorePerformance',
      onCell: () => ({
        className: cls([
          styles['normal-td']
        ])
      })
    }
  ];

  const initData = () => {
    let resultDataList: any[] = [];
    data?.forEach((itemFirst) => {
      let itemFirstArr: any[] = [];
      itemFirst.parentLatitudeInfoList.forEach((itemSecond) => {
        const itemSecondArr: any[] = [];
        itemSecond.latitudeInfoList.forEach((itemThird) => {
          const tempData = {
            ...itemThird,
            firstTypeName: itemFirst.positiveFlag ? "保护因子" : "风险因子",
            firstRowSpan: 0,
            secondRowSpan: 0,
            secondTypeName: itemSecond.latitudeGroupName,
          };
          itemSecondArr.push(tempData);
        });
        itemSecondArr[0].secondRowSpan = itemSecondArr.length;
        itemFirstArr = [...itemFirstArr, ...itemSecondArr];
      });
      itemFirstArr[0].firstRowSpan = itemFirstArr.length;
      resultDataList = [...resultDataList, ...itemFirstArr];
    });
    setDataSource(resultDataList);
  };

  useEffect(() => { initData(); }, []);

  return (
    <div className={styles['custom-factor-catalog-container']}>
      <Table
        rowKey={'latitudeName'}
        columns={columns}
        dataSource={dataSource}
        bordered={true}
        pagination={false}
      />
      {children}
    </div>
  );
};

export default CustomFactorCatalog;
