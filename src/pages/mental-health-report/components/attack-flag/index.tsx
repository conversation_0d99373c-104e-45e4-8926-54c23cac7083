import React from 'react';
import AttackPNG from '~/assets/mental-health-report/attack-bg.png';
import AttackOutPNG from '~/assets/mental-health-report/outward.png';
import styles from './index.module.less';
import { cls } from '~/utils/tools';
import type { IOverallRisk } from '../../types';
import { EArrangeType } from '../../types.d';
import { riskLevelImgClassName } from '~/utils/const';

interface IAttackFlagProps {
  className?: string;
  label: string;
  arrangeType?: EArrangeType;
  data: IOverallRisk;
  customStyle?: React.CSSProperties;
}

enum EAttackLabelType {
  in = '向内',
  out = '向外'
}

// 攻击标识，分为向内攻击、向外攻击
const AttackFlag: React.FC<IAttackFlagProps> = (props) => {
  const {
    className = '', label = '', arrangeType = EArrangeType.vertical,
    data, customStyle = {}
  } = props;

  return (
    <div className={cls([styles['attack-flag-container'], className])} style={{ ...customStyle }}>
      <div className={cls([
        styles['flag-box'],
        arrangeType === EArrangeType.vertical ? styles['box-arrange-v'] : styles['box-arrange-h'],
      ])}>
        <img src={label === EAttackLabelType.in ? AttackPNG : AttackOutPNG} alt="" />
        <div className={cls([
          styles['flag-type'],
          arrangeType === EArrangeType.vertical ? styles['arrange-v'] : styles['arrange-h']
        ])}>
          <span>{label}</span>
          <span>攻击风险</span>
        </div>
      </div>
      <div className={cls([
        styles['risk-level-box'],
        riskLevelImgClassName[data?.riskLevel - 1]
      ])}>
        <span className={styles['risk-name']}>{data?.riskLevelName}</span>
        <div className={styles['risk-score']} />
      </div>
    </div>
  );
};

export default AttackFlag;
