import React from 'react';
import styles from './index.module.less';
import { riskLevelColors, riskLevelImgClassName } from '~/utils/const';
import { cls } from '~/utils/tools';

interface ITestRiskResultProps {
  groupName: string;
  riskName?: string;
  riskLevelName: string;
  riskLevel: number;
}
// 向右的箭头小标题组件，右侧带有虚线
const TestRiskResult: React.FC<ITestRiskResultProps> = (props) => {
  const { groupName, riskName = '总体风险', riskLevelName, riskLevel = 1 } = props;

  return (
    <div className={styles['test-risk-result-container']}>
      <p className={styles['risk-result-overview']}>
        从测评结果来看，该{groupName}{riskName}为
        <span style={{ color: `${riskLevelColors[riskLevel - 1]}` }}>
          {riskLevelName}
        </span>
      </p>
      <div className={cls([styles['level-warp'], riskLevelImgClassName[riskLevel - 1]])}>
        <p>风险等级</p>
        <p className={styles.bar}></p>
        <p className={styles['risk-level']}> {riskLevelName}</p>
      </div>
    </div>
  );
};

export default TestRiskResult;
