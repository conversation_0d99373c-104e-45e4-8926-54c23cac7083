import React from 'react';
import styles from './index.module.less';
import CommonStyles from '~/styles/common.module.less';
import { IFactorRemark } from '../../types';
import { Table } from 'antd';
import { cls } from '~/utils/tools';

interface IFactorRemarkProps {
  list: IFactorRemark[];
}

// 总体概况
const FactorRemark: React.FC<IFactorRemarkProps> = (props) => {
  const { list } = props;
  const columns: any[] = [{
    title: '维度',
    dataIndex: 'latitudeName',
    rowScope: 'row',
    width: 109,
    render: (text: string) => <span style={{ color: "#fff" }}>{text}</span>,
    onCell: () => ({
      className: cls([
        CommonStyles['text-center'],
        CommonStyles['table-head'],
        CommonStyles['font-size-14']
      ])
    })
  }, {
    title: '维度解释',
    width: 200,
    dataIndex: 'factorDescription',
    onCell: () => ({
      className: `${CommonStyles['font-size-14']}`
    }),
  }, {
    title: '高分表现',
    width: 411,
    dataIndex: 'highScorePerformance',
    onCell: () => ({
      className: `${CommonStyles['font-size-14']}`
    }),
  }, {
    title: '低分表现',
    width: 200,
    dataIndex: 'lowScorePerformance',
    onCell: () => ({
      className: `${CommonStyles['font-size-14']}`
    }),
  }];

  return (
    <div className={styles['factor-remark-container']}>
      <Table
        columns={columns}
        dataSource={list}
        bordered={true}
        pagination={false}
      />
    </div>
  );
};

export default FactorRemark;
