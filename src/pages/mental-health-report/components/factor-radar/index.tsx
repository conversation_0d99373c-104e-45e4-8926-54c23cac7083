import React from 'react';
import styles from './index.module.less';
import CustomRadar from '~/businessComponents/CustomRadar';
import { IUserResultFactors } from '~/service/mental-health-report/person-report/types';
import { EReportType } from '../../types.d';

interface IFactorRadarProps {
  protectFactors: IUserResultFactors[];
  riskFactors: IUserResultFactors[];
  reportType: EReportType;
}
// 因子雷达
const FactorRadar: React.FC<IFactorRadarProps> = (props) => {
  const { protectFactors, riskFactors, reportType } = props;
  const typeText = reportType === EReportType.class ? '班级' : '年级';
  
  const protectText = reportType === EReportType.person ? '学生在对应因子上有无风险的分界线，对于保护因子来说，若在某因子上得分超过预警分，则表明该生在该因子上不存在问题，得分低于预警分则表明该生在该因子上存在一定程度的心理问题，需要引起重视。' : `${typeText}在对应因子上有无风险的分界线，对于保护因子来说，若在某因子上得分超过预警分，则表明该${typeText}在该因子上大部分学生无风险，得分低于预警分则表明该班级在该因子上较多学生存在不同程度的风险，需要引起重视。`;

  const riskText = reportType === EReportType.person ? '学生在对应因子上有无风险的分界线，对于风险因子来说，若在某因子上的得分超过预警分，则表明该生在该因子上存在一定程度的心理问题，需要引起重视，得分低于预警分则表明该生在该因子上不存在问题。' : `${typeText}在对应因子上有无风险的分界线，对于风险因子来说，若在某因子上的得分超过预警分，则表明该${typeText}在该因子上较多学生存在不同程度的风险，需要引起重视，得分低于预警分则表明该${typeText}在该因子上大部分学生无风险。`;

  return (
    <div className={styles['factor-radar-container']}>
      <div className={styles['radar-list-box']}>
        <div className={styles['radar-item-box']}>
          <div className={styles['item-title']}>
            保护因子
          </div>
          <div className={styles['radar-item']}>
            <CustomRadar list={protectFactors || []} reportType={reportType} />
            <p className={styles['warning-remark-text']}>
              预警分：{protectText}
            </p>
          </div>
        </div>

        <div className={styles['radar-item-box']} style={{ marginLeft: 12 }}>
          <div className={styles['item-title']}>
            风险因子
          </div>
          <div className={styles['radar-item']}>
            <CustomRadar list={riskFactors || []} reportType={reportType} />
            <p className={styles['warning-remark-text']}>
              预警分：{riskText}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FactorRadar;
