import React from 'react';
import styles from './index.module.less';
import { cls } from '~/utils/tools';

interface ILabelLineTitleProps {
  className?: string;
  label: string;
  remarkText: any;
  customStyle?: React.CSSProperties;
}

// 通用的附加说明，北师大、心理测等报告的附加说明不太一样，因此目前放到北师大目录内
const LabelLineTitle: React.FC<ILabelLineTitleProps> = (props) => {
  const { className = '', label = '', remarkText, customStyle = {} } = props;
  return (
    <div
      className={cls([styles['label-line-title-container'], className])}
      style={{ ...customStyle }}
    >
      <div className={styles['left-warp']}>
        <div className={styles['title-warp']}>
          <p>{label}</p>
        </div>
        <hr className={styles['split-line']} />
      </div>
      {typeof remarkText === 'string' ? (
        <div
          className={styles['remark-text']}
          dangerouslySetInnerHTML={{ __html: remarkText || "" }}
        />
      ) : remarkText}
    </div>
  );
};

export default LabelLineTitle;
