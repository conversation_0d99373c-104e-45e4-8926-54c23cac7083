import React from 'react';
import styles from './index.module.less';

// 通用的附加说明，北师大、心理测等报告的附加说明不太一样，因此目前放到北师大目录内
const AdditionalInstructions = () => {

  return (
    <div className={styles['additional-instructions-container']}>
      <p>1、 任何心理测评都存在一定误差</p>
      <p>
        学生在测评时的情绪状态、对测评的态度等主观因素，以及测评过程中的环境干扰等客观因素都会影响测评结果。因此在阅读本测评报告时，请参照学生在日常生活的实际表现来理解。
      </p>
      <p>2、请勿将测评结果当作永久“标签”</p>
      <p>
        请不要把本测评结果看成是对学生的“标签”或最终“宣判”，而应视为学生的阶段性状态。对于处在青春期的高中学生来说，其大多数心理和性格特征都是可变的，因此本测评结果仅为了解学生近期状态提供参考。
      </p>
    </div>
  );
};

export default AdditionalInstructions;
