import React from 'react';
import styles from './index.module.less';
import { EReportType } from '../../types.d';
import { cls } from '~/utils/tools';

interface IRiskFlagDemoProps {
  reportType?: EReportType;
  className?: string;
  isCenter?: boolean;
  customStyle?: React.CSSProperties;
}

// 风险标识示例，固定内容
const RiskFlagDemo: React.FC<IRiskFlagDemoProps> = (props) => {
  const { reportType, className, isCenter, customStyle = {} } = props;
  return (
    <ul
      className={cls([
        styles['risk-flag-demo'],
        isCenter && styles.center,
        className
      ])}
      style={{ ...customStyle }}
    >
      <li>
        <b className={styles.health} />
        <span>{reportType === EReportType.person ? "健康" : "无风险"}</span>
      </li>
      <li>
        <b className={styles['small-risk']} />
        <span>轻微风险</span>
      </li>
      <li>
        <b className={styles['higher-risk']} />
        <span>较高风险</span>
      </li>
      <li>
        <b className={styles['high-risk']} />
        <span>高风险</span>
      </li>
    </ul>
  );
};

export default RiskFlagDemo;
