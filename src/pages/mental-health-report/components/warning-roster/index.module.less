.left-head {
  display: flex;
  font-size: 14px;

  .title {
    width: 30px;
    text-align: center;
    font-size: 16px;
    line-height: 16.5px;
    color: #ffffff;
    background: #5b6f8c;
    justify-content: center;
    align-items: center;
    display: flex;
    border-right: 1px solid #fff;
    border-bottom: 1px solid #fff;
    flex-shrink: 0;
    padding: 8px 0;
  }

  .list {
    display: flex;
    flex-direction: column;
    border-right: 1px solid #fff;

    span {
      padding: 1px 0;
      width: 130px;
      font-size: 14px;
      background: #e4ebf5;
      text-align: center;
      border-bottom: 1px solid #fff;
    }
  }

  .second-list {
    flex: 1;

    .second-item {
      display: flex;

      .second-title {
        width: 58px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 15px;
        background-color: #8BA3C5;
        color: #fff;
        border-bottom: 1px solid #fff;
      }
    }
  }

  .third-list {
    display: flex;
    flex-direction: column;
    text-align: center;
    flex: 1;

    span {
      background-color: #E4EBF5;
      color: #333;
      border-bottom: 1px solid #fff;
      width: 72px;
      padding: 5px 4px;
    }
  }

  .only-one-third-title {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .no-third {
    flex: 1;
    text-align: center;
    padding: 3px 0!important;
    height: 32px;
  }
}

.opposite {
  width: 159px;
  height: 47px;
  background-color: #5b6f8c;
  border-bottom: 1px solid #ffffff;
  border-right: 1px solid #ffffff;
  position: relative;
  box-sizing: content-box;
  background: linear-gradient(16.5deg,
      #5b6f8c 50%,
      #ffffff 49.5%,
      #ffffff 36.5%,
      #5b6f8c 51.5%);
}

.deep-bg {
  background: #5b6f8c !important;
  color: #ffffff;
}

.high-bg {
  background: #8ba3c5 !important;
  color: #ffffff;
}

.wrap {
  display: flex;
}

.left-wrap {
  // display: flex;
  // flex-direction: column;
  margin-right: 1px;
}

.list-wrap {
  display: flex;
  flex-direction: column;

  span {
    height: 21px;
    padding: 1px 0;
    border-bottom: 1px solid #fff;
    width: 75px;
    text-align: center;
    background: #f5f8fb;
    border-right: 1px solid #fff;
    font-size: 14px;
    box-sizing: content-box;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 29px;
    white-space: nowrap;
    overflow: hidden;
  }
}

.list-mid-bg {
  background: #e4ebf5 !important;
}

.list-deep-bg {
  background: #fff !important;
}

.list-high-bg {
  background: #fff !important;
}

.normal-bg {
  background: #f5f8fb;
}

.high-c {
  color: #ff4d4f;
}

.superior-c {
  color: #ffa940;
}

.slight-c {
  color: #40a9ff;
}

.higher-bg {
  background: #ff4d4f !important;
  color: #fff !important;
}

.superior-bg {
  background: #ffa940 !important;
  color: #fff !important;
}

.slight-bg {
  background: #40a9ff !important;
  color: #fff !important;
}

.lows-bg {
  background: #5b6f8c !important;
  color: #fff !important;
}

.page {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;

  :global {
    .ant-pagination-item {
      border: none;
      color: #000000;
      font-size: 14px;
    }

    .ant-pagination-item-active {
      background-color: #5b6f8c;
      
      a {
        color: #fff;
      }
    }

    .ant-pagination-item-link {
      border: none;
    }
  }
}

.warning-container {
  background-color: #fff;
  padding: 40px;
  box-shadow: 0 4px 15px 0 #0000001a;
  margin-top: 60px;

  &>div:nth-child(1) {
    margin-top: 0;
  }

  &.single-print {
    margin-top: 0;
    padding-top: 0;
  }
}