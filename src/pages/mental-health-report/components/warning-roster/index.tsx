import React, { useState, useEffect } from "react";
import { Pagination } from "antd";
import styles from "./index.module.less";
import Api from "~/service/mental-health-report/class-report";
import SearchBar from "../search-bar";
import warningJson from "~/utils/const";
import Mark from "./mark";
import ExplainText from "./explain-text";
import { cls } from '~/utils/tools';
import ModuleTitle, { ETitleMpa } from '../module-title-for-img';

const LeftHeads = () => {
  return (
    <div className={styles["left-wrap"]}>
      {/* 左上角的格子 */}
      <div className={styles.opposite} />
      <div>
        {warningJson.list.map((i) => {
          return (
            <div key={i.name} className={styles["left-head"]}>
              {/* 第一级标题，如保护因子、风险因子 */}
              <span className={styles.title}>{i.name}</span>
              {/* 一级因子下集合容器 */}
              <div className={styles['second-list']}>
                {i.child.map((child: any, secondIndex: number) => {
                  // @ts-ignore
                  const hasLevel = child?.level || false;
                  // @ts-ignore
                  const styles1 = styles?.[child?.level];
                  return (
                    <div
                      key={`${i.name}_${child.name}_${secondIndex + 1}`}
                      className={styles['second-item']}
                    >
                      {/* 二级因子标题，如当前表现、人格原因 */}
                      <span className={`${(hasLevel && styles1) || ""} ${styles['second-title']} ${!child?.child ? styles['no-third'] : ''}`} >
                        {child.name}
                      </span>
                      {child?.child ? (
                        <div className={styles['third-list']}>
                          {child?.child?.map((item: any, thirdIndex: number) => {
                            // 三级因子标题，如自我效能
                            return (
                              <span
                                key={`${i.name}_${child.name}_${item.name}_${thirdIndex + 1}`}
                                className={cls([
                                  child?.child.length === 1 && styles['only-one-third-title']
                                ])}>
                                {item?.name}
                              </span>
                            );
                          })}
                        </div>
                      ) : null}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const ListData = (params: any) => {
  const { data } = params;

  const newKeys: any[] = [];
  const deepList = (list: any[]) => {
    list.forEach((item) => {
      if (item?.child?.length) {
        deepList(item?.child);
        return '';
      }
      newKeys.push(item);
    });
  };
  deepList(warningJson.list);
  const allList = warningJson.preList.concat(newKeys);

  return (
    <div className={styles["list-wrap"]}>
      {allList.map((i: any, idx) => {
        const levelNum = data[i?.levelKey];
        let keyName = (i.key && data[i?.key]) || "";
        if (i?.key === "className") {
          keyName = keyName.replace("（", "").replace("）", "");
        }

        // @ts-ignore
        const styles1 = i?.level ? styles?.[`list-${i?.level}`] : '';
        // @ts-ignore
        const styles2 = (i?.isDynamicBg && styles?.[warningJson.BgColorMap?.[levelNum] || warningJson.BgColorMap?.[1]]) || "";
        // @ts-ignore
        const styles3 = styles?.[warningJson.ColorMap?.[levelNum]] || "";
        const customHeight = i?.height ? { height: i.height } : {};

        return (
          <span
            key={idx}
            className={cls([styles1, styles2, styles3])}
            style={customHeight}
          >
            {keyName}
          </span>
        );
      })}
    </div>
  );
};

interface IWarningRosterProps {
  queryParams: any;
  printMode: any;
  textClassName?: string;
  clickPvParams: any
}

// 预警名单
const WarningRoster: React.FC<IWarningRosterProps> = (props) => {
  const { queryParams = {}, printMode: isPrint, textClassName, clickPvParams } = props;
  const {
    evaluationTaskId, gradeId, classId, classGroupNum
  } = queryParams;
  const [dataList, setDataList] = useState<any[]>([]);
  const [pageInfo, setPage] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
  });

  const getWarningList = async (params: any) => {
    const {
      current = params.current || pageInfo.current,
      sort = 2,
      userName = '',
    } = params;

    let { data: { data = [], totalRecords = 0, pageIndex = 1 } } = await Api.getWarningList({
      pageIndex: current,
      sort,
      userName,
      evaluationTaskId,
      gradeId,
      classGroupNum,
      classId,
      pageSize: 10,
    });

    console.log('预警名单', data);

    let dLen = data?.length;
    if (dLen > 10) {
      data = data.slice(0, 10);
      dLen = 10;
    }

    const restLen = 10 - dLen;
    const dataList = restLen
      ? data.concat(new Array(restLen).fill({}, 0, restLen))
      : data;
    setDataList(dataList);
    setPage((pre) => ({
      ...pre,
      total: totalRecords,
      current: pageIndex,
    }));
  };

  useEffect(() => {
    getWarningList({});
  }, []);

  const onChangePage = (page: any) => getWarningList({ current: page });

  const handleSearch = (values: any) => getWarningList({
    current: 1,
    ...values,
  });

  return (
    <div className={cls([
      isPrint ? 'singlePrint' : '',
      styles['warning-container'],
      isPrint ? styles['single-print'] : ''
    ])}>
      <ModuleTitle printMode={isPrint} titleType={ETitleMpa.warningList} anchorPointName="warningList" />
      {!isPrint && (
        <SearchBar
          handleSearch={handleSearch}
          searchParams={queryParams}
          clickPvParams={clickPvParams}
        />
      )}

      <div>
        <div className={styles.wrap}>
          <LeftHeads />
          {dataList.map((i, idx) => {
            return <ListData data={i} key={idx} />;
          })}
        </div>
        <div className={styles.page}>
          <Mark />
          <Pagination {...pageInfo} onChange={onChangePage} showSizeChanger={false}/>
        </div>
      </div>
      <ExplainText className={textClassName} />
    </div>
  );
};

export default WarningRoster;
