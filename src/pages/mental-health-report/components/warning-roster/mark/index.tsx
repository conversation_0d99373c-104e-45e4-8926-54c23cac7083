import React from "react";
import styles from "./index.module.less";
import stylesBg from "../index.module.less";
const list = [
  {
    name: "健康",
    key: "lows-bg",
  },
  {
    name: "轻微风险",
    key: "slight-bg",
  },
  {
    name: "较高风险",
    key: "superior-bg",
  },
  {
    name: "高风险",
    key: "higher-bg",
  },
];

const Mark = () => {
  return (
    <div className={styles['mark-box']}>
      {list.map((i) => {
        // @ts-ignore
        const styles1 = stylesBg[i.key];
        return (
          <div key={i.name}>
            <span className={styles1}></span>
            <span>{i.name}</span>
          </div>
        );
      })}
    </div>
  );
};

export default Mark;
