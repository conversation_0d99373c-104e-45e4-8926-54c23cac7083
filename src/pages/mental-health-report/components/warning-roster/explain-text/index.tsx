import React, { Fragment } from "react";
import styles from "./index.module.less";
import { cls } from '~/utils/tools';
import ArrowLineTitle from '../../arrow-line-title';
import LabelLineTitle from '../../label-line-title';

const ExplainText = ({ className }: any) => {
  return (
    <div className={cls([styles['hospital-content'], className || '', 'singlePrint'])}>
      <ArrowLineTitle title="注释" className={styles['special-left']} />
      <LabelLineTitle label='个体风险判断标准' remarkText={
        <Fragment>
          <p style={{marginTop: 12}}> 1、总体风险判断标准</p>
          <p>
            只有当学生既不存在向内攻击风险，又不存在向外攻击风险时，才属于健康；只要学生存在向内攻或或向外攻击中的一种行为风险，即判断学生总体存在风险，风险的程度取更高等级的那个。
          </p>
          <p>2、向内攻击风险判断标准</p>
          <p>
            a.风险等级采用学生向内攻击风险指数与学生不同等级重要风险因子个数的双重预警筛查机制。
          </p>
          <p>b.风险指数区间为0-100分，指数越高，提示向内攻击风险越高。</p>
          <p>
            c.重要风险因子共15个，每个因子划分了4个风险等级，分别为高风险、较高风险、轻微风险和健康。
          </p>
          <p>
            d.健康：高风险因子个数≤1且较高风险因子个数≤3且轻微风险因子个数≤5或0≤风险指数＜56
          </p>
          <p>
            {" "}
            轻微风险：1＜高风险因子个数≤4或3＜较高风险因子个数≤5或5＜轻微风险因子个数≤6或56≤风险指数＜61
          </p>
          <p>
            较高风险：4＜高风险因子个数≤6或5＜较高风险因子个数≤8或6＜轻微风险因子个数或61≤风险指数＜73.80
          </p>
          <p>
            高风险：6＜高风险因子个数或8＜较高风险因子个数或4＜高风险因子个数≤6且5＜较高风险因子个数≤8或73.80≤风险指数≤100
          </p>
          <p>3、向外攻击风险判断标准</p>
          <p>
            a.风险等级采用学生向外攻击风险指数与学生不同等级重要风险因子个数的双重预警筛查机制。
          </p>
          <p>b.风险指数区间为0-100分，指数越高，提示向外攻击风险越高。</p>
          <p>
            c.重要风险因子共6个，每个因子划分了4个风险等级，分别为高风险、较高风险、轻微风险和健康。
          </p>
          <p>
            d.健康：高风险因子个数≤1且较高风险因子个数≤1且轻微风险因子个数≤3或0≤风险指数＜40.40
          </p>
          <p>
            {" "}
            轻微风险：1＜高风险因子个数≤2或1＜较高风险因子个数≤2或3＜轻微风险因子个数≤4或40.40≤风险指数＜44.40
          </p>
          <p>
            {" "}
            较高风险：2＜高风险因子个数≤3或2＜较高风险因子个数≤4或4＜轻微风险因子个数或44.40≤风险指数＜52.80
          </p>
          <p>
            {" "}
            高风险：3＜高风险因子个数或4＜较高风险因子个数或2＜高风险因子个数≤3且2＜较高风险因子个数≤4或52.80≤风险指数≤100
          </p>
        </Fragment>
      } />
      <LabelLineTitle label='各因子判断标准' remarkText="根据升学e网通建立的全国百分位常模，找到各维度与99%、95%和90%常模百分点位上对应的分值，按照所得到的分值划分四个等级的风险区间。" />
    </div>
  );
};

export default ExplainText;
