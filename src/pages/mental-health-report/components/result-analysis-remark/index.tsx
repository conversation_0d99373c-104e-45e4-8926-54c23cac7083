import React, { Fragment } from 'react';
import styles from './index.module.less';

// 通用的附加说明，北师大、心理测等报告的附加说明不太一样，因此目前放到北师大目录内
const ResultAnalysisRemark = () => {

  return (
    <Fragment>
      <p className={styles['remark-title']} style={{ marginBottom: 24 }}>
        <span>常模均分</span>：全国常模的平均分，对于保护因子来说，得分高于某个因子所对应的常模均分，表明该生在该因子上的表现好于全国平均水平，得分低于某个因子所对应的常模均分，表明该生在该因子上的表现差于全国平均水平。对于风险因子来说，得分高于某个因子所对应的常模均分，表明该生在该因子上的表现要差于全国平均水平，得分低于某个因子所对应的常模均分，表明该生在该因子上的表现好于全国平均水平。
      </p>
      <p className={styles['remark-title']}>
        <span>预警分</span>：学生在对应因子上有无风险的分界线，对于保护因子来说，若在某因子上得分超过预警分，则表明该生在该因子上不存在问题，得分低于预警分则表明该生在该因子上存在一定程度的心理问题，需要引起重视。对于风险因子来说，若在某因子上的得分超过预警分，则表明该生在该因子上存在一定程度的心理问题，需要引起重视，得分低于预警分则表明该生在该因子上不存在问题。
      </p>
    </Fragment>
  );
};

export default ResultAnalysisRemark;
