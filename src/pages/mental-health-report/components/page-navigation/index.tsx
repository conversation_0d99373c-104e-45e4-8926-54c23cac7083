import React, { useState } from 'react';
import styles from './index.module.less';
import { Button, Timeline, message } from 'antd';
import { getToken, pollTaskStatus } from '~/utils/tools';
import MstQt from 'mst-analytics';
import API from '~/service/common';
import SafeLogger from '~/utils/safe-logger';

export interface IListItem {
  label: React.ReactNode;
  point: string;
  title: React.ReactNode;
  style?: React.CSSProperties;
  children?: IListItem[];
}
export interface IPageNavigationProps {
  wrapperStyle?: React.CSSProperties; // 容器样式
  printMode?: boolean; // 是否为打印模式
  sendParams: any; // 上报参数
  timeout?: number; // 等待时长
  className?: any; // 自定义样式
  theme?: any; // 主题
  list?: IListItem[]; // 导航列表
  children?: React.ReactNode;
  onPrint?: () => void;
  content?: React.ReactNode;
}

// 定位
const handleLocator = (item: { point: any; }) => {
  document?.getElementById(`${item.point}`)?.scrollIntoView({ behavior: "smooth", block: "start" });
};

const PageNavigation: React.FC<IPageNavigationProps> = ({
  printMode,
  sendParams = {},
  timeout,
  list,
  wrapperStyle,
  className,
  theme,
  children,
  content
}) => {
  const [downloading, setDownloading] = useState(false);
  const [filePath, setFilePath] = useState<string>('');

  const goPollTaskStatus = async (hashId: string) => {
    try {
      const { status, filePath }: any = await pollTaskStatus(API.queryPDFStatus, { ids: [hashId] }, 180000, 5000);
      if (status === 1 && filePath) {
        setFilePath(filePath);
        setDownloading(false);
        message.success("生成成功，请点击右侧悬浮菜单“保存文件”进行下载~", 3);
      } else {
        throw new Error('生成报告异常')
      }
    } catch (error) {
      setDownloading(false);
      message.error('生成报告失败，请稍后重试～');
      SafeLogger?.baseLogger?.error('async-pdf-download-error', {
        reason: '轮询报告生成状态异常',
        error
      });
    }
  }


  const handleDownLoad = async () => {
    const token = getToken() || '';
    if (!token) {
      message.error("登录信息失效，请重新登录后重试！");
      return;
    }

    if (downloading) {
      message.info("报告正在生成中，无需重复点击");
      return;
    }

    if (!downloading) {
      setDownloading(true);

      MstQt.clickPv({
        eventCode: 'ewt_pc_teacher_psychologymanage_mental_report_download_button_click',
        source_type: '报告',
        ...sendParams
      });
      try {
        API.getUCBaseInfo().then(async () => {
          message.info("报告正在生成中，预计需要1~3分钟内完成，请稍等~", 3);
          const {
            school_name: schoolName,
            class_name: className,
            grade_name: gradeName,
            user_name: userName,
            report_type: reportType
          } = sendParams;
          // 前缀学校名称固定
          // 个人报告用班级+个人名字
          // 年级报告用年级名称
          // 班级报告用班级名称
          const reportName = reportType === '个人' ? className + userName : !className ? gradeName : className;
          const { data: { hashId } } = await API.asyncPDFByPage({
            type: 'pdf',
            modal: 'active',
            waitUntil: 'networkidle0',
            name: `${schoolName}${reportName}测评报告`,
            width: 1100,
            timeout: timeout || (1000 * 60 * 10),
            url: `${location.href}&isPrint=true&token=${token}`,
            activeTriggerFuncName: 'PSTScreenshotHook'
          });
          if (hashId) {
            goPollTaskStatus(hashId)
          } else {
            throw new Error('生成报告异常')
          }
        });
      } catch (error) {
        setDownloading(false);
        message.error('生成报告失败，请稍后重试～');
        SafeLogger?.baseLogger?.error('async-pdf-download-error', {
          reason: '生成报告异常',
          error
        });
      }
    }
  };

  // 保存文件点击
  const saveFile = () => {
    MstQt.clickPv({
      eventCode: 'ewt_pc_teacher_psychologymanage_mental_report_save_button_click',
      source_type: '报告',
      ...sendParams
    });

    window.open(filePath);
  };

  // 下载因子说明点击
  const fileButtonClick = () => {
    MstQt.clickPv({
      eventCode: 'ewt_pc_teacher_psychologymanage_mental_report_download_button_click',
      source_type: '因子说明',
      ...sendParams
    });
  };

  return !printMode ? (
    <div
      {...{
        className: `${styles.navigation} ${theme === 'green' ? styles['theme-green'] : styles['theme-default']} ${className || ''}`,
        style: { ...wrapperStyle },
      }}
    >
      <Timeline>
        {list?.map((item, i) => (
          <Timeline.Item key={i}>
            <a className={styles['navigation-label']} onClick={() => handleLocator(item)}>
              {item.label}
            </a>
          </Timeline.Item>
        ))}
      </Timeline>

      {filePath ? (
        <Button type="primary" size="large" block
          onClick={() => saveFile()}
          className={styles['btn-orange']}>
          保存文件
        </Button>
      ) : downloading ? (
        <Button type="primary" size="large" block
          className={styles.btn} loading={downloading}>
          生成中
        </Button>
      ) : (
        <Button type="primary" size="large" block className={styles.btn}
          onClick={handleDownLoad} loading={downloading}>
          下载报告
        </Button>
      )}

      <Button
        {...{
          type: 'primary',
          size: 'large',
          href: "//file.ewt360.com/file/1678531355956773621",
          target: "_blank",
          onClick: fileButtonClick,
        }}
        block
        className={`${styles.btn} ${styles['ghost-btn']}`}
      >
        下载因子说明
      </Button>

      {content}
      {children}
    </div>
  ) : null;
};

export default PageNavigation;
