import React from "react";
import { Form, Button, Select, Input, Popover } from "antd";
import styles from "./index.module.less";
import { DownloadOutlined } from '@ant-design/icons';
import MstQtAnalytics from 'mst-analytics';

const { Option } = Select;

interface ISearchBarProps {
  handleSearch: (values: any) => void;
  searchParams: any;
  clickPvParams: any;
}

const SearchBar: React.FC<ISearchBarProps> = (props) => {
  const { searchParams, handleSearch, clickPvParams } = props;
  const [form] = Form.useForm();

  const onFinish = async (values: any) => handleSearch(values);

  const onDownExcel = (hide: boolean) => {
    const {
      evaluationTaskId,
      gradeId,
      classId = "",
      classGroupNum,
    } = searchParams;
    const { sort, userName } = form.getFieldsValue();

    const isClassGroup = classGroupNum ? `&classGroupNum=${classGroupNum}` : '';
    const downUrl = `/api/psychology/mentalHealthV2Report/exportMentalHealthWarning?gradeId=${gradeId}&evaluationTaskId=${evaluationTaskId}&classId=${classId}&sort=${sort}&userName=${userName}&hide=${hide}${isClassGroup}`;

    try {
      MstQtAnalytics.clickPv({
        download_type: hide ? '隐私名单' : '全名名单',
        ...clickPvParams
      });
    } catch (error) {
      console.log('预警名单点击下载失败');
    }

    window.open(downUrl);
  };

  // const formLayout = {
  //   labelCol: { span: 7 },
  //   wrapperCol: { span: 14 },
  // };

  const formLayout = {
    labelCol: {
      xs: { span: 6 },
      md: { span: 6 }
    },
    wrapperCol: {
      xs: { span: 12 },
      md: { span: 12 }
    }
  };

  return (
    <div className={styles['search-bar-container']}>
      <Form
        form={form}
        {...formLayout}
        onFinish={onFinish}
        layout="inline"
        initialValues={{
          userName: "",
          sort: 2
        }}
      >
        <Form.Item label="学生查询" name="userName">
          <Input placeholder="请输入学生名称" />
        </Form.Item>

        <Form.Item label="排序" name="sort">
          <Select placeholder="选择排序类型" style={{ width: 170 }}>
            <Option value={1}>按照姓名排序</Option>
            <Option value={2}>按总体风险高低排序</Option>
          </Select>
        </Form.Item>

        <Form.Item>
          <Button type="primary" className={styles['search-btn']} htmlType="submit">
            搜索
          </Button>
        </Form.Item>

        <div className={styles['buttons-box']}>
          <Popover content={<p>请注意保护学生隐私安全</p>}>
            {/* @ts-ignore */}
            <Button type="ghost" onClick={() => onDownExcel(false)} icon={<DownloadOutlined />}>
              下载全名版本名单
            </Button>
          </Popover>
          {/* @ts-ignore */}
          <Button type="ghost" onClick={() => onDownExcel(true)} icon={<DownloadOutlined />}>
            下载隐私保护名单
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default SearchBar;
