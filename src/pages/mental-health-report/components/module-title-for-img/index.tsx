import React from 'react';
import styles from './index.module.less';
import IntroductionPNG from "~/assets/mental-health-report/module-title/reportIntroduction.png";
import ResultAnalysisPNG from "~/assets/mental-health-report/module-title/resultAnalysis.png";
import OverviewPNG from "~/assets/mental-health-report/module-title/overview.png";
import WarningListPNG from "~/assets/mental-health-report/module-title/warningList.png";
import ExtraDescPNG from "~/assets/mental-health-report/module-title/extraDesc.png";
import KeySchoolPNG from "~/assets/mental-health-report/module-title/keySchool.png";
import WarningTablePNG from "~/assets/mental-health-report/module-title/warningTable.png";
import WorkIntroductionPNG from "~/assets/mental-health-report/module-title/workInstruction.png";
import FactorDescription from "~/assets/mental-health-report/module-title/factorDescription.png";
import CounselingAdvice from "~/assets/mental-health-report/module-title/counselingAdvice.png";
import { cls } from '~/utils/tools';

export enum ETitleMpa {
  /** 报告导读 */
  reportIntroduction = IntroductionPNG,
  /** 附加说明 */
  extraDesc = ExtraDescPNG,
  /** 结果分析 */
  resultAnalysis = ResultAnalysisPNG,
  /** 总体概况 */
  overview = OverviewPNG,
  /** 预警名单 */
  warningList = WarningListPNG,
  /** 关注重点学校 */
  keySchool = KeySchoolPNG,
  /** 预警汇总表 */
  warningTable = WarningTablePNG,
  /** 工作指导 */
  workInstruction = WorkIntroductionPNG,
  /** 因子说明 */
  factorDescription = FactorDescription,
  /** 辅导建议 */
  counselingAdvice = CounselingAdvice
}

interface IModuleTitleForImgProps {
  anchorPointName: string;
  titleType: any,
  imgWidth?: number;
  className?: string;
  printMode?: boolean;
}

// 图片类型的模块标题
const ModuleTitleForImg: React.FC<IModuleTitleForImgProps> = (props) => {
  const { anchorPointName, titleType, imgWidth, className, printMode = false } = props;
  const isPrint = printMode && Boolean(className) && className?.toLocaleLowerCase()?.indexOf('print') !== -1;

  return (
    <div
      className={cls([
        styles['module-title-for-img-container'],
        className
      ])}
      style={isPrint ? { marginTop: 0 } : {}}
    >
      <a id={anchorPointName}>
        <img src={titleType} alt="" style={imgWidth ? { width: imgWidth } : {}} />
      </a>
    </div>
  );
};

export default ModuleTitleForImg;
