.report-base-info-container {
  width: 100%;

  // 报告编码、时间盒子
  .report-code-box {
    display: flex;
    margin-left: 24px;
    margin-top: -134px;
    position: relative;
    z-index: 2;

    p {
      font-weight: bold;
      font-size: 14px;
      color: #003963;
      line-height: 24px;
    }

    p:nth-child(2) {
      margin-left: 35px;
    }
  }

  // 报告基础信息盒子
  .report-base-info-box {
    height: 120px;
    max-width: 1000px;
    background-color: #fff;
    box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding-left: 20px;
    display: flex;
    align-items: center;
    margin-top: 50px;

    // 名字盒子
    .name-box {
      width: 200px;
    }

    // 主名字title
    .info-name {
      font-size: 22px;
      font-weight: bold;
      text-align: center;
    }

    // 副标题
    .info-name-bottom {
      font-size: 16px;
      color: #666666;
      line-height: 24px;
      text-align: center;
    }

    // 其他信息盒子
    .other-info-box {
      margin-left: 30px;
      font-size: 16px;
      height: 100%;
      width: 100%;

      // 每一项信息盒子
      .wrap-item {
        height: 50%;
        display: flex;
        align-items: center;
        width: 100%;

        // 信息item项
        .info-left {
          white-space: nowrap;
          font-size: 16px;
          color: #333;
          font-weight: bold;

          &.person {
            width: 200px;
            flex-shrink: 0;
          }
        }

        // 针对学校的？
        .info-left-school {
          font-size: 16px;
          color: #333;
          font-weight: bold;
        }

        // 展示内容的具体h3
        h3 {
          height: 100%;
          display: flex;
          align-items: center;
          padding-left: 24px;
          border-left: 1px dashed #cccccc;
          border-bottom: 1px dashed #cccccc;
          width: 100%;

          // 正文
          span {
            font-weight: normal;
            display: inline-block;
            vertical-align: middle;
            padding-left: 10px;
            color: #666666;
            font-size: 16px;
          }
        }
      }

      // 下方的item不需要底边框
      .wrap-item:last-child h3 {
        border-bottom: none;
      }
    }
  }
}