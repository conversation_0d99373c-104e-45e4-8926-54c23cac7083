import React from 'react';
import moment from 'moment';
import type { IMentalReportBaseInfo } from '../../types';
import { EReportType } from '../../types.d';

import styles from './index.module.less';
import { cls } from '~/utils/tools';

interface IReportBaseInfoProps {
  baseInfo: IMentalReportBaseInfo; // 报告基础信息数据
  reportType: EReportType; // 报告类型枚举，个人、班级、年级
  nameKey: string; // 名字展示的key，以下规则相同
  oneTdKey: string | string[]; // 支持字符串数组以防后续出现多个字段拼接展示
  twoTdKey: string | string[];
  threeTdKey: string | string[];
  fourTdKey: string | string[];
  oneTdTitle: string;
  twoTdTitle: string;
  threeTdTitle: string;
  fourTdTitle: string;
}
// 报告基础信息组件
const ReportBaseInfo: React.FC<IReportBaseInfoProps> = (props) => {
  const {
    baseInfo, reportType, nameKey, oneTdKey, twoTdKey, threeTdKey,
    fourTdKey, oneTdTitle, twoTdTitle, threeTdTitle, fourTdTitle
  } = props;

  const formatTime = (time: number) => moment(time).format('YYYY-MM-DD');
  const makeValue = (targetKey: string | string[]) => {
    if (typeof targetKey === 'string') {
      // 需要动态获取指定字段的内容，忽略校验规则，下同
      // @ts-ignore
      return baseInfo?.[targetKey] || '--';
    }
    if (targetKey?.length === 1) {
      // @ts-ignore
      return baseInfo?.[targetKey?.[0]];
    }
    // @ts-ignore
    return `${formatTime(baseInfo?.[targetKey?.[0]])} 至 ${formatTime(baseInfo?.[targetKey?.[1]])}`;
  };

  const showName = makeValue(nameKey); // 显示的名称，姓名、班级名、年级等
  const oneTdValue = makeValue(oneTdKey);
  const twoTdValue = makeValue(twoTdKey);
  const threeTdValue = makeValue(threeTdKey);
  const fourTdValue = makeValue(fourTdKey);

  return (
    <div className={styles['report-base-info-container']}>
      {/* 报告编号、报告时间 */}
      <div className={styles['report-code-box']}>
        <p>
          报告编号：
          <span>{baseInfo?.reportCode}</span>
        </p>
        <p>
          {reportType === EReportType.person ? "测评布置日期：" : "报告日期: "}
          <span>
            {moment(baseInfo?.reportTime).format("YYYY-MM-DD") || "--"}
          </span>
        </p>
      </div>

      {/* 报告信息 */}
      <div className={styles['report-base-info-box']}>
        <div className={styles["name-box"]}>
          <h3 className={styles["info-name"]}>
            <span>{showName || "--"}</span>
          </h3>
          {reportType === EReportType.class && (
            <h3 className={styles["info-name-bottom"]}>
              <span>{baseInfo?.gradeName || "--"}</span>
            </h3>
          )}
        </div>
        <div className={styles["other-info-box"]}>
          <div className={styles["wrap-item"]}>
            <h3 className={cls([
              styles["info-left"],
              reportType === EReportType.person ? styles.person : ''
            ])}>
              {oneTdTitle}: <span>{oneTdValue}</span>
            </h3>
            <h3 className={styles["info-left-school"]}>
              {twoTdTitle}: <span>{twoTdValue}</span>
            </h3>
          </div>
          <div className={styles["wrap-item"]}>
            <h3 className={cls([
              styles["info-left"],
              reportType === EReportType.person ? styles.person : ''
            ])}>
              {threeTdTitle}: <span>{threeTdValue}</span>
            </h3>
            <h3 className={styles["info-left"]}>
              {fourTdTitle}: <span>{fourTdValue}</span>
            </h3>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportBaseInfo;
