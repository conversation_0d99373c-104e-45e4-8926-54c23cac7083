import React from 'react';
import styles from './index.module.less';
import { cls } from '~/utils/tools';
import ArrowRightPNG from '~/assets/mental-health-report/iconRightArrow.png';

interface IModuleTitleForImgProps {
  title: string;
  hasPadding?: boolean;
  className?: string;
}
// 向右的箭头小标题组件，右侧带有虚线
const ArrowLineTitle: React.FC<IModuleTitleForImgProps> = (props) => {
  const { title, hasPadding = true, className = "" } = props;

  return (
    <div className={cls([
      styles['arrow-line-title-container'],
      !hasPadding ? styles["no-padding"] : "",
      className
    ])}>
      <img alt="" src={ArrowRightPNG} />
      <p>{title}</p>
      <hr className={styles.line} />
    </div>
  );
};

export default ArrowLineTitle;
