import React from 'react';
import styles from './index.module.less';
import { EReportType } from '~/pages/mental-health-report/types.d';
import { ILatitudeListBeanList } from '~/service/mental-health-report/class-report/types';
import ChartForBar from '@/components/ChartForBar';
import { cls } from '~/utils/tools';

interface IFactorDistributionBarProps {
  dataList: ILatitudeListBeanList[];
  reportType: EReportType;
  className?: string;
  nameKey?: string;
  height?: number;
}

// 因子分布柱状堆叠图
const FactorDistributionBar: React.FC<IFactorDistributionBarProps> = (props) => {
  const { dataList, reportType, className, nameKey = 'latitudeName', height = 400 } = props;

  const makeChartsForBarOptions = (item: ILatitudeListBeanList[]) => {
    const keyData: any[] = [];
    const keys = ['healthPercentage', 'minorRiskPercentage', 'higherRiskPercentage', 'highestRiskPercentage'];
    const positions = ['inside', 'top', 'bottom', 'right'];
    const risks = ["无风险", "轻微风险", "较高风险", "高风险"];

    item?.forEach((v: ILatitudeListBeanList, index: number) => {
      // @ts-ignore
      keyData.push(v[nameKey]);
    });

    const newResult: any[] = [];
    risks?.forEach((riskNameItem: string, riskNameIndex: number) => {
      newResult.push({
        type: 'bar',
        name: riskNameItem,
        stack: "total",
        barWidth: dataList?.length > 5 ? 15 : 25,
        barCategoryGap: reportType === EReportType.grade ? 36 : 10,
        emphasis: {
          focus: "series",
        },
        data: item?.map((dataItem: any) => {
          const curValue = dataItem?.[keys[riskNameIndex]];
          return {
            value: curValue,
            areaInfo: curValue,
            label: {
              show: true,
              color: 'rgba(0,0,0,.5)',
              position: positions[riskNameIndex],
              formatter: (params: any) => {
                const { data: { value } } = params;
                if (Number(value) === 0) return '';
                return `${value}%`;
              },
            },
          };
        }),
      });
    });

    const result = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '5%',
        right: reportType === EReportType.grade ? '7%' : '6%',
        top: 10,
        bottom: 0,
        containLabel: true
      },
      height: height - 30, // 要给图表留白空间
      color: ["#5CDBD3", "#40A9FF", "#FFA940", "#FF4D4F"],
      xAxis: {
        type: 'value',
        max: 100,
        splitNumber: 4,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          formatter: "{value}%",
          interval: "auto",
        },
        splitLine: { show: false },
      },
      yAxis: {
        type: 'category',
        data: [...keyData],
        axisLabel: {
          fontSize: 14,
          color: "rgba(0, 17, 57, 0.85)",
          height: 30,
        },
        axisTick: {
          show: false,
        },
        offset: reportType === EReportType.grade ? 20 : 0,
      },
      series: [...newResult],
    };

    return result;
  };

  return (
    <div className={cls([styles['factor-distribution-bar-container'], className])}>
      <ChartForBar
        customOption={makeChartsForBarOptions(dataList)}
        height={height}
      />
    </div>
  );
};

export default FactorDistributionBar;
