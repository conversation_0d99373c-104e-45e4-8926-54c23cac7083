import React from 'react';
import styles from './index.module.less';
import { EReportType } from '~/pages/mental-health-report/types.d';
import FactorDistributionBar from '~/pages/mental-health-report/components/factor-distribution-bar';
import { cls } from '~/utils/tools';
import { riskLevelImgClassName } from '~/utils/const';
import StartRemark from '~/businessComponents/StartRemark';
import LabelLineTitle from '~/pages/mental-health-report/components/label-line-title';

interface IGradeFactorRemarkProps {
  data: any;
  className?: string;
}

// 班级及诶过分析
const GradeFactorRemark: React.FC<IGradeFactorRemarkProps> = (props) => {
  const { data, className } = props;
  const classList = data?.eachClassRiskDistributionBOS || [];
  const newClassList = JSON.parse(JSON.stringify(classList));

  // 获取柱状图容器的高度，如果小于400就是使用400
  const getBarBoxHeight = (list: any) => {
    const curHeight = list?.length * 55 || 400;
    if (curHeight < 400) {
      return 400;
    }
    return curHeight;
  };

  return (
    <div className={cls([
      styles['grade-factor-remark-container'],
      riskLevelImgClassName[data?.riskLevel - 1 || 0],
      className
    ])}>
      <div className={styles['factor-title-box']}>
        <div className={styles['factor-name-box']}>
          <span className={styles['factor-name']}>{data?.latitudeName}</span>
          <span>{data?.firstTypeName} / {data?.secondTypeName}</span>
        </div>
        <div className={styles.line} />
        <span className={styles['risk-level-name']}>{data?.riskLevelName}</span>
        <span className={styles['score-text']}>年级均分：{data?.score}</span>
        <span className={styles['score-text']}>常模均分：{data?.referenceScore}</span>
      </div>

      <div className={styles['factor-detail-box']}>
        <StartRemark title="年级情况" text={data?.studentSituation} customStyle={{ marginTop: 0 }} />
        <LabelLineTitle label='各班风险分布' remarkText={
          <FactorDistributionBar
            reportType={EReportType.grade}
            dataList={newClassList.reverse()}
            nameKey='className'
            className={styles['grade-factor-remark-bar']}
            height={getBarBoxHeight(newClassList)}
          />
        }
          customStyle={{ marginBottom: 0 }}
        />
      </div>
    </div>
  );
};

export default GradeFactorRemark;
