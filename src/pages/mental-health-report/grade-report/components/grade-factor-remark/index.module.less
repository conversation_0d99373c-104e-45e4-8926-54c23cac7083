.grade-factor-remark-container {
  margin: 0 40px 16px;
  border-radius: 8px;
  padding: 0 8px 1px;

  // 因子标题盒子
  .factor-title-box {
    display: flex;
    height: 72px;
    align-items: center;

    // 名字容器
    .factor-name-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 21px;

      // 具体的因子名字
      .factor-name {
        font-size: 24px;
        font-weight: bold;
        color: rgba(0, 0, 0, .6);

        // 所属的大类
        &+span {
          color: rgba(0, 0, 0, .6);
          font-size: 12px;
        }
      }
    }

    // 分割线
    .line {
      height: 40px;
      border: 0;
      border-right: 1px solid #fff;
      width: 1px;
      margin-left: 21px;
      margin-right: 40px;
    }

    // 风险等级名称
    .risk-level-name {
      font-size: 24px;
      color: #fff;
      font-weight: bold;
    }
    // 得分文案
    .score-text {
      color: rgba(0, 0, 0, .6);
      font-size: 18px;
      font-weight: bold;
      margin-left: 40px;
    }
  }

  .factor-detail-box {
    background-color: #fff;
    border-radius: 4px;
    padding-top: 24px;
    margin-bottom: -13px;

    .grade-factor-remark-bar {
      // padding-top: 24px;
      margin: 20px auto 0!important;
    }
  }
}