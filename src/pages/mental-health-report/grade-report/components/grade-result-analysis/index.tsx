import React, { Fragment } from 'react';
import styles from './index.module.less';
import ArrowLineTitle from '~/pages/mental-health-report/components/arrow-line-title';
import LabelLineTitle from '~/pages/mental-health-report/components/label-line-title';
import RiskFlagDemo from '~/pages/mental-health-report/components/risk-flag-demo';
import { EReportType } from '~/pages/mental-health-report/types.d';
import FactorDistributionBar from '~/pages/mental-health-report/components/factor-distribution-bar';
import { IEachClassRiskDistributionBOS, IGradeFactorAnalysis, ILatitudeListBeanGroups, ILatitudeListBeanList } from '~/service/mental-health-report/grade-report/types';
import GradeFactorRemark from '../grade-factor-remark';

interface IGradeResultAnalysisProps {
  data: IGradeFactorAnalysis[];
  reportType: EReportType;
}

// 班级及诶过分析
const GradeResultAnalysis: React.FC<IGradeResultAnalysisProps> = (props) => {
  const { data, reportType } = props;
  const protectList: IGradeFactorAnalysis[] = [];
  const riskList: IGradeFactorAnalysis[] = [];

  // 各自类型数据分开
  data?.forEach((item: IGradeFactorAnalysis) => {
    const targetObj = item.positiveFlag ? protectList : riskList;
    targetObj.push(item);
  });

  const makeTargetList = (
    paramList: IGradeFactorAnalysis[],
    targetList: any[],
    overviewList: any[],
    isRisk: boolean = false
  ) => {
    paramList?.forEach((rootItem: IGradeFactorAnalysis) => {
      rootItem?.latitudeListBeanGroups?.forEach((groupItem: ILatitudeListBeanGroups) => {
        // 该层有”当前表现“等group数据
        const groupList: any[] = [];
        groupItem?.latitudeListBeanList?.forEach((latitudeItem: ILatitudeListBeanList) => {
          // 该层有”抑郁“等维度数据
          overviewList.push({
            ...latitudeItem?.riskDistributionVO,
            latitudeName: latitudeItem?.latitudeName
          });
          groupList.push();

          const newRiskDistributionVO = latitudeItem?.eachClassRiskDistributionBOS?.map((classItem: IEachClassRiskDistributionBOS) => ({
            classId: classItem?.classId,
            className: classItem?.className,
            ...classItem?.riskDistributionVO,
          }));

          targetList.push({
            ...latitudeItem,
            eachClassRiskDistributionBOS: [...newRiskDistributionVO],
            firstTypeName: rootItem?.positiveFlag ? '保护因子' : '风险因子',
            secondTypeName: groupItem?.message,
            secondTypeCode: groupItem?.latitudeCode,
          });
        });
      });
    });
  };

  const protectDistributions: any[] = []; // 保护因子的风险分布集合
  const protectFactorList: any[] = [];

  makeTargetList(protectList, protectFactorList, protectDistributions);

  const riskResult: any[] = [];
  riskList?.forEach((rootItem: IGradeFactorAnalysis) => {
    // 这一层遍历是 维度 的遍历，风险因子需要再这里开始进行组合，区别与保护因子
    rootItem?.latitudeListBeanGroups?.forEach((groupItem: ILatitudeListBeanGroups) => {
      const currentWeiDu: any = {};
      currentWeiDu.name = groupItem.message;
      currentWeiDu.latitudeCode = groupItem.latitudeCode;
      currentWeiDu.overviewBarList = []; // 维度内因子总体概览柱状图集合
      currentWeiDu.factorList = []; // 每个因子的详情集合
      // 这里是具体的因子开始遍历了
      groupItem?.latitudeListBeanList?.forEach((latitudeItem: ILatitudeListBeanList) => {
        const newRiskDistributionVO = latitudeItem?.eachClassRiskDistributionBOS?.map((classItem: IEachClassRiskDistributionBOS) => ({
          classId: classItem?.classId,
          className: classItem?.className,
          ...classItem?.riskDistributionVO,
        }));

        currentWeiDu.overviewBarList.push({
          ...latitudeItem?.riskDistributionVO,
          latitudeName: latitudeItem?.latitudeName
        });

        currentWeiDu.factorList.push({
          ...latitudeItem,
          eachClassRiskDistributionBOS: [...newRiskDistributionVO],
          firstTypeName: '风险因子',
          secondTypeName: groupItem?.message,
          secondTypeCode: groupItem?.latitudeCode,
        });
      });

      riskResult.push(currentWeiDu);
    });
  });

  const isSinglePrint = (item: any, index: number) => {
    if (item?.eachClassRiskDistributionBOS?.length <= 5) {
      return index !== 0 && index % 2 !== 0 ? 'singlePrint' : '';
    }
    return 'singlePrint';
  };

  return (
    <div className={styles['grade-result-analysis-container']}>
      <ArrowLineTitle title="保护因子" />
      <RiskFlagDemo customStyle={{ paddingTop: 0, paddingBottom: 0 }} />
      <FactorDistributionBar
        dataList={protectDistributions.reverse()}
        reportType={EReportType.grade}
        className={styles['grade-factor-overview-bar-box']}
        height={450}
      />
      {protectFactorList?.map((item: any, index: number) => (
        <GradeFactorRemark
          data={item}
          key={`grade-result-analysis-protect-factor-${index + 1}`}
          className={isSinglePrint(item, index)}
        />
      ))}

      <ArrowLineTitle title="风险因子" className='singlePrint' />
      {riskResult?.map((item: any, index: number) => (
        <Fragment key={`grade-result-analysis-risk-wei-du-${index + 1}`}>
          <LabelLineTitle
            label={item?.name}
            className={index !== 0 ? 'singlePrint' : ''}
            remarkText={(
              <Fragment>
                <RiskFlagDemo />
                <FactorDistributionBar
                  reportType={EReportType.grade}
                  dataList={item?.overviewBarList.reverse()}
                  height={450}
                />
              </Fragment>
            )}
            customStyle={index !== 0 ? { marginTop: 40 } : {}}
          />

          {item?.factorList?.map((factorItem: any, factorIndex: number) => (
            <GradeFactorRemark
              data={factorItem}
              key={`grade-result-analysis-risk-factor-${index + 1}`}
              className={isSinglePrint(factorItem, factorIndex)}
            />
          ))}
        </Fragment>
      ))}
    </div>
  );
};

export default GradeResultAnalysis;
