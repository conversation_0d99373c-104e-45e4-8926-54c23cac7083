import React, { Fragment, useEffect } from 'react';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
// 年级报告api
import Api from '@/service/mental-health-report/grade-report';
// 年级报告api类型
import {
  IFactorCatalogRemark, ILatitudeInfoList, TQueryParams, IGradeBaseInfoProps,
  IGradeResultFactors, IGradeFactorAnalysis, IGradeSuggestFactors
} from '~/service/mental-health-report/grade-report/types';
// 公共枚举等
import { EReportType } from '../types.d';
// 年级报告头和尾图片
import headerBanner from '@/assets/mental-health-report/grade-header.png';
import footerBanner from '@/assets/mental-health-report/grade-footer.png';
// 公共函数 - 打印
import { filterInvalidValues, mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';

import EBanner from '~/components/CommonBanner';
import ReportBaseInfo from '../components/report-base-info';
import ModuleTitle, { ETitleMpa } from '../components/module-title-for-img';
import ReportIntroduction from '../components/report-Introduction';
import OverAllOverview from '../components/overall-overview';
import AdditionalInstructions from '~/pages/mental-health-report/components/additional-instructions';
import FactorRemark from '../components/factor-remark';
import CounselingSuggest from '../components/counseling-suggest';
import CustomFactorCatalog from '../components/custom-factor-catalog';
import PageNavigation from '../components/page-navigation';
import MentalSuggestConst from '@/lib/mental-health-report/const';
import WarningRoster from '../components/warning-roster';
import GradeResultAnalysis from './components/grade-result-analysis';

import styles from './index.module.less';
import MstQtAnalytics from 'mst-analytics';
import { message } from 'antd';

export const getServerSideProps: GetServerSideProps<{
  reportId: string;
  baseInfo: IGradeBaseInfoProps | null,
  protectFactor: IGradeResultFactors[],
  riskFactor: IGradeResultFactors[],
  resultAnalysis: IGradeFactorAnalysis[],
  analysisSuggestion: IGradeSuggestFactors[],
  factorRemark: ILatitudeInfoList[],
  factorCatalogRemark: IFactorCatalogRemark[];
  queryParams: TQueryParams;
  error: string;
  logMsg: string;
}> = async (ctx: any) => {
  // 报告id，新老打印模式
  const {
    evaluationTaskId = '',
    ff,
    isPrint,
    gradeId,
    classGroupNum,
    initRecordId
  } = ctx.query as unknown as TQueryParams;
  // 兼容新老打印模式
  const printMode = !!(ff || isPrint);
  const commonQueryParams = filterInvalidValues({
    evaluationTaskId,
    gradeId,
    classGroupNum,
    initRecordId
  })
  try {
    const [
      baseInfoRes, protectFactorRes, riskFactorRes, resultAnalysisRes, analysisSuggestionRes, factorRemarkRes, factorCatalogRemarkRes
    ] = await Promise.all([
      Api.getGradeBaseInfo({ ...commonQueryParams, clientType: 1}, ctx),
      Api.getGradeProtectiveFactors(commonQueryParams, ctx),
      Api.getGradeRiskFactors(commonQueryParams, ctx),
      Api.getGradeResultAnalysis(commonQueryParams, ctx),
      Api.getGradeSuggestion(commonQueryParams, ctx),
      Api.getFactorRemarkList(commonQueryParams, ctx), // 因子说明
      Api.getFactorCatalogRemarkList(commonQueryParams, ctx), // 维度
    ]);

    return {
      props: {
        reportId: evaluationTaskId,
        baseInfo: baseInfoRes?.data || null,
        protectFactor: protectFactorRes?.data || [],
        riskFactor: riskFactorRes?.data || [],
        resultAnalysis: resultAnalysisRes?.data || [],
        analysisSuggestion: analysisSuggestionRes?.data || [],
        factorRemark: factorRemarkRes?.data || [],
        factorCatalogRemark: factorCatalogRemarkRes?.data || [],
        queryParams: { ...ctx.query, printMode },
        error: '',
        logMsg: '',
      }
    };
  } catch (error: any) {
    const numCode = Number(error?.code || 0);
    const msg = numCode !== 200 ? (error?.msg || error?.toString() || '服务异常，请稍后重试') : '';
    return {
      props: {
        reportId: evaluationTaskId,
        baseInfo: null,
        protectFactor: [],
        riskFactor: [],
        resultAnalysis: [],
        analysisSuggestion: [],
        factorRemark: [],
        factorCatalogRemark: [],
        queryParams: { ...ctx.query, printMode },
        error: Number(error?.code) !== 200 ? error?.msg || '服务异常,请稍后重试！' : '',
        logMsg: msg
      }
    };
  }
};

// 北师大-个人报告
const Page = ({
  reportId,
  baseInfo,
  protectFactor,
  riskFactor,
  resultAnalysis,
  analysisSuggestion,
  factorRemark,
  factorCatalogRemark,
  queryParams,
  error,
  logMsg
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { printMode } = queryParams;
  // 导航json
  const sidePageNavList: any[] = [
    { label: "报告导读", point: "introduction" },
    { label: "总体概况", point: "overview" },
    { label: "结果分析", point: "resultAnalysis" },
    { label: "辅导建议", point: "counselingAdvice" },
    { label: "因子说明", point: "factorDescription" },
    { label: "预警名单", point: "warningList" },
    { label: "附加说明", point: "extraDesc" },
  ];

  useEffect(() => {
    if (error) {
      message.error(error);
    }
    if (logMsg) {
      console.log(logMsg);
    }
    window.setTimeout(() => {
      try {
        MstQtAnalytics.separatelyReport('ewt_pc_teacher_psychologymanage_mental_grade_report_view', {
          school_name: baseInfo?.schoolName || '没有学校名称',
          grade_name: baseInfo?.gradeName || '没有毕业年份'
        });
      } catch (error) {
        console.log('个人报告QT埋点失败', error);
      }
    }, 10);
    if (!baseInfo) {
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: '500',
          errorMessage: '接口异常',
        });
      }
      return;
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: 6000
      });
    }
  }, []);

  return (
    <div {...{ className: styles['mental-health-report'] }}>
      <EBanner imgSrc={headerBanner} height={380} />
      <div className={styles["main-content-box"]}>
        {/* 报告基本信息 */}
        {baseInfo && (
          <ReportBaseInfo
            baseInfo={baseInfo}
            reportType={EReportType.grade}
            nameKey='gradeName'
            oneTdKey='evaluatedPeople'
            oneTdTitle='参与报告人数'
            twoTdKey='schoolName'
            twoTdTitle='学校'
            threeTdKey={['startTime', 'endTime']}
            threeTdTitle='测评时间'
            fourTdKey='areaName'
            fourTdTitle='地区' />
        )}
        {/* 报告导读 */}
        <ModuleTitle printMode={printMode} titleType={ETitleMpa.reportIntroduction} anchorPointName="introduction" />
        <ReportIntroduction
          principleVideoURL={baseInfo?.principleVideoURL || ""}
          reportType={EReportType.grade}
          printMode={Boolean(printMode)}
        />
        {/* 总体概况 */}
        <ModuleTitle printMode={printMode}
          titleType={ETitleMpa.overview}
          anchorPointName="overview"
          className="singlePrint"
        />
        {baseInfo && (
          <OverAllOverview
            reportId={reportId}
            data={baseInfo}
            groupName="年级"
            printMode={printMode || false}
            reportType={EReportType.grade}
            protectFactors={protectFactor}
            riskFactors={riskFactor}
          />
        )}
        {/* 结果分析 */}
        <ModuleTitle printMode={printMode}
          titleType={ETitleMpa.resultAnalysis}
          anchorPointName="resultAnalysis"
          className="singlePrint"
        />
        <GradeResultAnalysis data={resultAnalysis} reportType={EReportType.grade} />
        {/* 辅导建议 */}
        <ModuleTitle printMode={printMode}
          titleType={ETitleMpa.counselingAdvice}
          anchorPointName="counselingAdvice"
          className="singlePrint"
        />
        <CounselingSuggest
          reportType={EReportType.grade}
          list={analysisSuggestion || []}
          defaultSuggest={MentalSuggestConst.mentalHealthGradeSuggest} />
        {!printMode && (
          <Fragment>
            {/* 因子说明 */}
            <ModuleTitle printMode={printMode}
              titleType={ETitleMpa.factorDescription}
              anchorPointName="factorDescription"
              className="singlePrint"
            />
            {/* 单层的因子说明 */}
            <FactorRemark list={factorRemark} />
            {/* 这里需要二级目录的因子说明 */}
            <CustomFactorCatalog data={factorCatalogRemark} />
          </Fragment>
        )}
        {/* 预警名单 */}
        <WarningRoster
          queryParams={queryParams}
          printMode={printMode}
          clickPvParams={{
            eventCode: 'ewt_pc_teacher_psychologymanage_mental_grade_report_warning_list_download_button_click',
            school_name: baseInfo?.schoolName
          }}
        />
        {/* 附加说明 */}
        <ModuleTitle printMode={printMode}
          titleType={ETitleMpa.extraDesc}
          anchorPointName="extraDesc"
          className='singlePrint'
        />
        <AdditionalInstructions />
        {printMode && <div style={{ height: 854 }} />}
      </div>
      <PageNavigation
        printMode={printMode}
        list={sidePageNavList}
        theme="green"
        sendParams={{
          report_type: '年级',
          school_name: baseInfo?.schoolName,
          grade_name: baseInfo?.gradeName
        }}
      />
      <EBanner imgSrc={footerBanner} height={120} contentStyle={{ marginTop: 60 }} />
    </div>
  );
};

export default Page;
