import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import dynamic from 'next/dynamic';
import treeList from '@/lib/mental-health-report/county-report';
import API from '@/service/mental-health-report/county-report';
import { TQueryParams, baseInfoProps } from '@/service/mental-health-report/county-report/types'
import styles from './index.module.less'

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseInfo: baseInfoProps;
}> = async (ctx: any) => {
  try {
    const { areaCode, cityCode, gradeCode, gradeId, semester } = ctx.query as unknown as TQueryParams;
    const params = {
      areaCode,
      cityCode,
      gradeCode,
      gradeId,
      semester
    }

    const baseInfoResult = await API.getAreaBaseInfo({
      ...params,
      clientType: 1,
    }, ctx)

    const areaCurrentPerformanceResult = await API.getAreaCurrentPerformance(params, ctx)
    const areaPersonalityReasonsResult = await API.getAreaPersonalityReasons(params, ctx)

    return {
      props: {
        baseInfo: {
          areaBaseInfo: baseInfoResult.data,
          areaCurrentPerformance: areaCurrentPerformanceResult.data,
          areaPersonalityReasons: areaPersonalityReasonsResult.data
        }
      }
    }
  } catch (err) {
    console.error('mental-health-report/county-report', err)
    return {
      props: {
        baseInfo: {}
      }
    }
  }
}

const Page = ({
  baseInfo,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  // 当前页面没有使用，先注释掉
  return (
    <div {...{ className: styles['psychology-county-report'] }}>
      {/* <ComplexComponents {...{ tree: treeList, data: { ...baseInfo }  }} /> */}
    </div>
  )
};

export default Page;