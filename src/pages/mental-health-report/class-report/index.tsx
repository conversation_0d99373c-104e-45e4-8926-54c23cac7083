import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import Api from '@/service/mental-health-report/class-report';
import headerBanner from '@/assets/mental-health-report/class-header.png';
import footerBanner from '@/assets/mental-health-report/class-footer.png';
import { ICounselingSuggest, IFactorCatalogRemark, ILatitudeInfoList, IUserFactorAnalysis, IUserResultFactors, TQueryParams } from '~/service/mental-health-report/class-report/types';
import { filterInvalidValues, mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';
import type { IMentalReportBaseInfo } from '../types';
import { EReportType } from '../types.d';

import EBanner from '~/components/CommonBanner';
import ReportBaseInfo from '../components/report-base-info';
import ModuleTitle, { ETitleMpa } from '../components/module-title-for-img';
import ReportIntroduction from '../components/report-Introduction';
import OverAllOverview from '../components/overall-overview';

import styles from './index.module.less';
import AdditionalInstructions from '~/pages/mental-health-report/components/additional-instructions';
import FactorRemark from '../components/factor-remark';
import CounselingSuggest from '../components/counseling-suggest';
import CustomFactorCatalog from '../components/custom-factor-catalog';
import PageNavigation from '../components/page-navigation';
import MentalSuggestConst from '@/lib/mental-health-report/const';
import { Fragment, useEffect } from 'react';
import WarningRoster from '../components/warning-roster';
import ClassResultAnalysis from './components/class-result-analysis';
import MstQtAnalytics from 'mst-analytics';
import { message } from 'antd';

export const getServerSideProps: GetServerSideProps<{
  reportId: string;
  baseInfo: IMentalReportBaseInfo | null,
  protectFactor: IUserResultFactors[],
  riskFactor: IUserResultFactors[],
  resultAnalysis: IUserFactorAnalysis[],
  analysisSuggestion: ICounselingSuggest[],
  factorRemark: ILatitudeInfoList[],
  factorCatalogRemark: IFactorCatalogRemark[];
  queryParams: TQueryParams;
  error: string;
  logMsg: string;
}> = async (ctx: any) => {
  // 报告id，新老打印模式
  const {
    evaluationTaskId = '',
    ff,
    isPrint,
    gradeId,
    classId,
    initRecordId
  } = ctx.query as unknown as TQueryParams;
  // 兼容新老打印模式
  const printMode = !!(ff || isPrint);
  const commonQueryParams = filterInvalidValues({
    evaluationTaskId,
    gradeId,
    classId,
    initRecordId
  })
  try {
    const [
      baseInfoRes, protectFactorRes, riskFactorRes, resultAnalysisRes,
      analysisSuggestionRes, factorRemarkRes, factorCatalogRemarkRes
    ] = await Promise.all([
      Api.getUserBaseInfo({ ...commonQueryParams, clientType: 1 }, ctx),
      Api.getUserProtectiveFactors(commonQueryParams, ctx), // 保护因子
      Api.getUserRiskFactors(commonQueryParams, ctx), // 风险因子
      Api.getClassFactorAnalysis(commonQueryParams, ctx), // 结果分析
      Api.getLatitudeAnalysisSuggestion(commonQueryParams, ctx), // 辅导建议
      Api.getFactorRemarkList({ initRecordId }, ctx), // 因子说明
      Api.getFactorCatalogRemarkList({ initRecordId }, ctx), // 一二级维度因子说明
    ]);

    return {
      props: {
        reportId: evaluationTaskId,
        baseInfo: baseInfoRes?.data || null,
        protectFactor: protectFactorRes?.data || [],
        riskFactor: riskFactorRes?.data || [],
        resultAnalysis: resultAnalysisRes?.data|| [],
        analysisSuggestion: analysisSuggestionRes?.data || [],
        factorRemark: factorRemarkRes?.data || [],
        factorCatalogRemark: factorCatalogRemarkRes?.data || [],
        queryParams: { ...ctx.query, printMode },
        error: '',
        logMsg: '',
      }
    };
  } catch (error: any) {
    const numCode = Number(error?.code || 0);
    const msg = numCode !== 200 ? (error?.msg || error?.toString() || '服务异常，请稍后重试') : '';
    return {
      props: {
        reportId: evaluationTaskId,
        baseInfo: null,
        protectFactor: [],
        riskFactor: [],
        resultAnalysis: [],
        analysisSuggestion: [],
        factorRemark: [],
        factorCatalogRemark: [],
        queryParams: { ...ctx.query, printMode },
        error: Number(error?.code) !== 200 ? error?.msg || '服务异常,请稍后重试！' : '',
        logMsg: msg
      }
    };
  }
};

// 北师大-个人报告
const Page = ({
  reportId,
  baseInfo,
  protectFactor,
  riskFactor,
  resultAnalysis,
  analysisSuggestion,
  factorRemark,
  factorCatalogRemark,
  queryParams,
  error,
  logMsg
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { printMode } = queryParams;
  // 导航json
  const sidePageNavList: any[] = [
    { label: "报告导读", point: "introduction" },
    { label: "总体概况", point: "overview" },
    { label: "结果分析", point: "resultAnalysis" },
    { label: "辅导建议", point: "counselingAdvice" },
    { label: "因子说明", point: "factorDescription" },
    { label: "预警名单", point: "warningList" },
    { label: "附加说明", point: "extraDesc" },
  ];

  useEffect(() => {
    if (error) {
      message.error(error);
    }
    if (logMsg) {
      console.log(logMsg);
    }
    window.setTimeout(() => {
      try {
        MstQtAnalytics.separatelyReport('ewt_pc_teacher_psychologymanage_mental_class_report_view', {
          school_name: baseInfo?.schoolName || '没有学校名称',
          class_name: baseInfo?.className || '没有班级名称'
        });
      } catch (error) {
        console.log('个人报告QT埋点失败', error);
      }
    }, 10);

    if (!baseInfo) {
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: '500',
          errorMessage: '接口异常',
        });
      }
      return;
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: 6000
      });
    }
  }, []);

  return (
    <div {...{ className: styles['mental-health-report'] }}>
      <EBanner imgSrc={headerBanner} height={380} />
      <div className={styles["main-content-box"]}>
        {/* 报告基本信息 */}
        {baseInfo && (
          <ReportBaseInfo
            baseInfo={baseInfo}
            reportType={EReportType.class}
            nameKey='className'
            oneTdKey='evaluatedPeople'
            oneTdTitle='参与报告人数'
            twoTdKey='schoolName'
            twoTdTitle='学校'
            threeTdKey='areaName'
            threeTdTitle='地区'
            fourTdKey={['startTime', 'endTime']}
            fourTdTitle='测评时间' />
        )}
        {/* 报告导读 */}
        <ModuleTitle printMode={printMode} titleType={ETitleMpa.reportIntroduction} anchorPointName="introduction" />
        <ReportIntroduction
          principleVideoURL={baseInfo?.principleVideoURL || ""}
          reportType={EReportType.class}
          printMode={Boolean(printMode)}
        />
        {/* 总体概况 */}
        <ModuleTitle printMode={printMode}
          titleType={ETitleMpa.overview}
          anchorPointName="overview"
          className="singlePrint"
        />
        {baseInfo && (
          <OverAllOverview
            reportId={reportId}
            data={baseInfo}
            groupName="班"
            printMode={printMode || false}
            reportType={EReportType.class}
            protectFactors={protectFactor}
            riskFactors={riskFactor}
          />
        )}
        {/* 结果分析 */}
        <ModuleTitle printMode={printMode}
          titleType={ETitleMpa.resultAnalysis}
          anchorPointName="resultAnalysis"
          className="singlePrint"
        />
        <ClassResultAnalysis data={resultAnalysis} reportType={EReportType.class} />
        {/* 辅导建议 */}
        <ModuleTitle printMode={printMode}
          titleType={ETitleMpa.counselingAdvice}
          anchorPointName="counselingAdvice"
          className="singlePrint"
        />
        <CounselingSuggest
          reportType={EReportType.class}
          list={analysisSuggestion || []}
          defaultSuggest={MentalSuggestConst.mentalHealthClassSuggest} />
        {!printMode && (
          <Fragment>
            {/* 因子说明 */}
            <ModuleTitle printMode={printMode}
              titleType={ETitleMpa.factorDescription}
              anchorPointName="factorDescription"
              className="singlePrint"
            />
            {/* 单层的因子说明 */}
            <FactorRemark list={factorRemark} />
            {/* 这里需要二级目录的因子说明 */}
            <CustomFactorCatalog data={factorCatalogRemark} />
          </Fragment>
        )}
        {/* 预警名单 */}
        <WarningRoster
          queryParams={queryParams}
          printMode={printMode}
          clickPvParams={{
            eventCode: 'ewt_pc_teacher_psychologymanage_mental_class_report_warning_list_download_button_click',
            school_name: baseInfo?.schoolName,
            class_name: baseInfo?.className
          }}
        />
        {/* 附加说明 */}
        <ModuleTitle printMode={printMode}
          titleType={ETitleMpa.extraDesc}
          anchorPointName="extraDesc"
          className='singlePrint'
        />
        <AdditionalInstructions />
        {printMode && <div style={{ height: 854 }} />}
      </div>
      <PageNavigation
        printMode={printMode}
        list={sidePageNavList}
        theme="green"
        sendParams={{
          report_type: '班级',
          school_name: baseInfo?.schoolName,
          grade_name: baseInfo?.gradeName,
          class_name: baseInfo?.className
        }}
      />
      <EBanner imgSrc={footerBanner} height={120} contentStyle={{ marginTop: 60 }} />
    </div>
  );
};

export default Page;
