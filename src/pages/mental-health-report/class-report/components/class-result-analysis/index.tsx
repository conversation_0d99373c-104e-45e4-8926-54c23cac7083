import React from 'react';
import styles from './index.module.less';
import ArrowLineTitle from '~/pages/mental-health-report/components/arrow-line-title';
import LabelLineTitle from '~/pages/mental-health-report/components/label-line-title';
import RiskFlagDemo from '~/pages/mental-health-report/components/risk-flag-demo';
import { EReportType } from '~/pages/mental-health-report/types.d';
import { ILatitudeListBeanGroups, ILatitudeListBeanList, IUserFactorAnalysis } from '~/service/mental-health-report/class-report/types';
import CustomRiskTable from '~/businessComponents/CustomRiskTable';
import FactorDistributionBar from '~/pages/mental-health-report/components/factor-distribution-bar';

interface IClassResultAnalysisProps {
  data: IUserFactorAnalysis[];
  reportType: EReportType;
}

// 班级及诶过分析
const ClassResultAnalysis: React.FC<IClassResultAnalysisProps> = (props) => {
  const { data, reportType } = props;
  const protectList: IUserFactorAnalysis[] = [];
  const riskList: IUserFactorAnalysis[] = [];

  // 各自类型数据分开
  data?.forEach((item: IUserFactorAnalysis) => {
    const targetObj = item.positiveFlag ? protectList : riskList;
    targetObj.push(item);
  });

  const protectOverview: ILatitudeListBeanList[] = [];
  protectList?.forEach((rootItem: IUserFactorAnalysis) => {
    rootItem?.latitudeListBeanGroups?.forEach((groupItem: ILatitudeListBeanGroups) => {
      groupItem?.latitudeListBeanList?.forEach((latitudeItem: ILatitudeListBeanList) => {
        protectOverview.push({
          ...latitudeItem
        });
      });
    });
  });

  // 风险因子的数组长度，需要将风险因子每个二级维度拆出来
  const riskLen = riskList?.[0]?.latitudeListBeanGroups?.length;
  const resultListForRisk: IUserFactorAnalysis[] = [];
  if (riskLen) {
    // 生成指定长度的数组
    const tempRiskArr = Array.from({ length: riskLen }, () => riskList[0]);
    tempRiskArr.forEach((item: IUserFactorAnalysis, index: number) => {
      const itemObj = JSON.parse(JSON.stringify(item));
      const tempList = JSON.parse(JSON.stringify(item.latitudeListBeanGroups[index]));
      itemObj.latitudeListBeanGroups = [tempList];
      resultListForRisk.push(itemObj);
    });
  }

  return (
    <div className={styles['class-result-analysis-container']}>
      <ArrowLineTitle title="保护因子" />
      <LabelLineTitle label="保护因子风险分布" remarkText="" />
      <RiskFlagDemo reportType={EReportType.class} className={styles['class-risk-flag-demo']} />
      <FactorDistributionBar
        dataList={protectOverview?.reverse()}
        reportType={reportType}
        height={protectOverview?.length > 6 ? 450 : 380}
      />
      <CustomRiskTable data={protectList} reportType={EReportType.class} />
      {resultListForRisk?.length !== 0 && (
        <div className={styles['risk-list']}>
          <ArrowLineTitle title="风险因子" className='singlePrint' />
          {resultListForRisk.map((item: IUserFactorAnalysis, index: number) => {
            // 如果数据是空的就不处理了
            if (!item.latitudeListBeanGroups?.length) {
              return null;
            }
            return item.latitudeListBeanGroups.map((tmp: ILatitudeListBeanGroups) => {
              const newArr = JSON.parse(JSON.stringify([...tmp.latitudeListBeanList]));
              return (
                <div
                  key={`class-result-analysis-risk-factor-list-key-${tmp.latitudeCode}`}
                  className={index === 0 ? '' : 'singlePrint'}
                >
                  <LabelLineTitle label={`${tmp.message}风险分布`} remarkText={(
                    <FactorDistributionBar
                      height={tmp.latitudeListBeanList?.length > 6 ? 500 : 380}
                      dataList={[...newArr].reverse()}
                      reportType={reportType} />
                  )} />
                  <CustomRiskTable data={[item]} reportType={EReportType.class} />
                </div>
              )
            });
          })}
        </div>
      )}
    </div>
  );
};

export default ClassResultAnalysis;
