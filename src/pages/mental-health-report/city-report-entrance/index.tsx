import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { useEffect, useState } from 'react';
import { Button, Modal, Select } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import dynamic from 'next/dynamic';
import EBanner from '~/components/CommonBanner';
import treeList from '@/lib/mental-health-report/city-report-entrance';
import bannerImg from '@/assets/mental-health-report/banner.png';
import footerImg from '@/assets/mental-health-report/PsychologyReportV2_footer.png';
import styles from './index.module.less';
import API from '@/service/mental-health-report/report-entrance';
import { baseInfoProps, areaSelectItem, cityAreaEntrySummaryInfoProps, entrySchoolInfoItem, cityAreaEntryItem } from '@/service/mental-health-report/report-entrance/types'
import { objectToUrlParams } from '@/utils/tools'
import CardBox from '~/lib/mental-health-report/components/CardBox';

const { Option } = Select;
const gradeOptions = ['', '高一', '高二', '高三']

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseInfo: baseInfoProps;
}> = async (ctx: any) => {

  try {
    const { data } = await API.getUserViewAuthority({ type: 1 }, ctx)
    const gradeTermRes = await API.getGradeTermList({ cityCode: data.cityCode }, ctx)

    return { props: { baseInfo: { authority: data, gradeTermList: gradeTermRes.data } } };
  } catch (error) {
    console.log(error);
    return { props: { baseInfo: { authority: {}, gradeTermList: [] } } };
  }
};

const Page = ({
  baseInfo,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { authority, gradeTermList } = baseInfo;
  const [isNeedFixed, setIsNeedFixed] = useState(false);
  const currentGradeTerm = gradeTermList && gradeTermList.length > 0 ? gradeTermList[0] : {};
  const [currentData, setCurrentData] = useState<any>({ ...authority, ...currentGradeTerm })
  const [summaryInfo, setSummaryInfo] = useState<cityAreaEntrySummaryInfoProps>();
  const [areaOptions, setAreaOptions] = useState<areaSelectItem[]>([])
  const [areaCountyList, setAreaCountyList] = useState<cityAreaEntryItem[]>([])

  const initAuthority = () => {
    if (!authority?.hasViewAuthority) {
      Modal.warning({
        onOk: () => {
          location.href = `${location.origin}/psychology-service/psychology/countyCover`;
        },
        title: "无权限",
        content: "您暂无权限查看该报告",
      });
    }
    getCityAreaEntrySummaryInfo()
    getAreaCityEntranceList()
    getAreaSelectOptions()
  }

  // 获取概览
  const getCityAreaEntrySummaryInfo = async () => {
    try {
      const { areaCode, cityCode, gradeCode, graduationYear, semester } = currentData;
      const { data } = await API.getCityAreaEntrySummaryInfo({
        areaCode: areaCode || 0,
        cityCode,
        gradeCode,
        gradeId: graduationYear,
        semester,
        clientType: 1
      })
      setSummaryInfo(data)
    } catch (error) {
      console.error('getCityAreaEntrySummaryInfo', error)
    }
  }

  // 获取区县列表
  const getAreaCityEntranceList = async () => {
    try {
      const { areaCode, cityCode, graduationYear, gradeCode, semester } = currentData;
      const { data } = await API.getCityAreaEntryList({
        areaCode: areaCode || 0,
        type: 1,
        cityCode,
        gradeId: graduationYear,
        gradeCode,
        semester
      })
      setAreaCountyList(data)
    } catch (error) {
      console.log('getAreaCityEntranceList', error)
    }
  }

  // 获取区县 - 下拉选项
  const getAreaSelectOptions = async () => {
    try {
      const { areaCode, cityCode, gradeCode, graduationYear, semester } = currentData;
      const { data } = await API.getAreaSelect({
        areaCode: areaCode || 0,
        cityCode,
        gradeCode,
        gradeId: graduationYear,
        semester
      })
      setAreaOptions(data)
    } catch (error) {
      console.log('getAreaSelectOptions', error)
    }
  }

  // 切换年级
  const handleOnChangeGrade = (index: number) => {
    const current = gradeTermList && gradeTermList[index]
    setCurrentData({ ...currentData, ...current })
    getAreaSelectOptions()
    getAreaCityEntranceList()
  }

  // 查看市级报告
  const handleJumpCityReport = (data = '') => {
    const { cityCode, graduationYear, gradeCode, semester } = currentData;
    const exportUrl = objectToUrlParams({
      cityCode,
      areaCode: '',
      gradeId: graduationYear,
      gradeCode,
      semester,
    })
    window.open(`${location.origin}/psychology-service/mental-health-report/city-report?` + exportUrl);
  }

  // 查看区县报告
  const handleJumpLink = (data: cityAreaEntryItem) => {
    const { cityCode, graduationYear, gradeCode, semester } = currentData;
    const areaCode = data ? data.areaCode : 0
    const exportUrl = objectToUrlParams({
      cityCode,
      areaCode,
      gradeId: graduationYear,
      gradeCode,
      semester,
    })
    window.open(`${location.origin}/psychology-service/mental-health-report/county-report?` + exportUrl);
  }

  // 下载表格
  const handleDownExecl = () => {
    const { cityCode, graduationYear, gradeCode, semester } = currentData;
    const exportUrl = objectToUrlParams({
      cityCode,
      areaCode: 0,
      gradeId: graduationYear,
      gradeCode,
      semester,
      clientType: 1
    })
    window.open(`${location.origin}/api/psychology/mentalHealthAreaV2Report/exportEntryInfo` + exportUrl);
  }

  useEffect(() => {
    window.addEventListener("scroll", function () {
      const element = document.getElementById('overview') || document.querySelector('.overview');
      const offsetTop = element?.offsetTop || 0;
      const scrollTop = document.body.scrollTop || document.documentElement.scrollTop;
      const h = element?.offsetHeight || 0;
      if (scrollTop >= offsetTop && scrollTop && !isNeedFixed) {
        setIsNeedFixed(true);
      } else if(offsetTop < h || scrollTop < (offsetTop - h)) {
        setIsNeedFixed(false);
      }
    })
  }, []);

  useEffect(() => {
    initAuthority()
  }, [])

  return (
    <div {...{ className: styles['psychology-city'] }}>
      <EBanner imgSrc={bannerImg} height={380} />
      <div className={styles.container}>
        <div className={styles.currentAreaBox}>
          <div className={styles.areaName}>当前地区：{authority?.cityName}</div>
          <p className={styles.description}>该市已有<span className={styles.dangerColor}>{summaryInfo?.schoolCnt} </span>所学校生成有效集体报告</p>
        </div>
      </div>
      <div className={styles.container}>
        <div className={styles.entranceContent}>
        <CardBox
          title="市级报告"
          titleStyle={{ display: 'inline-block' }}
          id="overview"
          className={`overview ${styles.sticky} ${isNeedFixed ? styles.compactLayout: ''}`}>
          <div className={styles.optionBox}>
            年级范围：
            {gradeTermList &&
              <Select defaultValue={0} placeholder="请选择年级范围" onChange={handleOnChangeGrade} style={{ width: '240px' }}>
                {gradeTermList.map((v, index) => {
                  return <Option key={v.graduationYear} value={index}>
                    {`${gradeOptions[v.gradeCode]} ${v.semester === 1 ? '上' : '下'} （${v.graduationYear - 3}入学）`}
                  </Option>
                })
                }
              </Select>
            }
            {summaryInfo && Object.keys(summaryInfo).length > 0 &&
              <div className={styles.rightOption}>
                <Button className={`${styles['primary-btn-border']} ${summaryInfo.reportFlag ? '' : styles['btn-disabled']}`} onClick={() => handleJumpCityReport()} disabled={!summaryInfo.reportFlag}>查看市级报告</Button>
                {/* @ts-ignore */}
                <Button type="primary" onClick={() => handleDownExecl()}><DownloadOutlined style={{ fontSize: 20 }} />下载Excel表格</Button>
              </div>
            }
          </div>
          {summaryInfo && Object.keys(summaryInfo).length > 0 &&
            <div className={styles.dataOverview}>
              <div className={styles.item}>
                <div className={styles.title}>参与学校数</div>
                <div className={styles.count}>{summaryInfo.schoolCnt}</div>
              </div>
              <div className={styles.item}>
                <div className={styles.title}>布置人数</div>
                <div className={styles.count}>{summaryInfo.allTotalCnt}</div>
              </div>
              <div className={styles.item}>
                <div className={styles.title}>完成人数</div>
                <div className={styles.count}>{summaryInfo.allFinishCnt}</div>
              </div>
              <div className={styles.item}>
                <div className={styles.title}>完成率</div>
                <div className={styles.count}>{summaryInfo.allFinishRate}%</div>
              </div>
            </div>
          }
        </CardBox>
        <ComplexComponents {...{ tree: treeList, data: { currentData, gradeOptions, areaOptions, areaCountyList } }} />
        </div>
        <div className={styles.watching}>
          <p>注：</p>
          <p>市级报告生成条件：至少2所学校生成校级报告，且每所学校完成测评人数≥100人</p>
          <p>区县报告生成条件：至少2所学校生成校级报告，且每所学校完成测评人数≥100人</p>
          <p>集体报告生成条件：只要有一个班级完成的测评人数≥10人</p>
        </div>
      </div>
      <EBanner imgSrc={footerImg} height={120} />
    </div>
  );
};
export default Page;
