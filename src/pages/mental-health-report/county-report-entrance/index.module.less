.psychology-city {
  font-size: 14px;
  box-sizing: border-box;
  margin: 0 auto;
  background-color: #f5f5f5;
  color: rgba(0, 17, 57, 0.85);
  .entranceContent {
    width: 1000px;
    margin: -180px auto 0;
  }
  .container {
    position: relative;
    width: 1000px;
    box-sizing: border-box;
    margin: 0 auto;
    .currentAreaBox {
      display: block;
      position: relative;
      top: -200px;
      width: 940px;
      margin: 0 auto 20px;
      .areaName {
        width: 513px;
        line-height: 40px;
        background-image: linear-gradient(
          270deg,
          rgba(0, 57, 99, 0) 12%,
          #003963 100%
        );
        border-radius: 8px;
        font-weight: 600;
        font-size: 18px;
        color: #ffffff;
        padding-left: 20px;
      }
      p {
        line-height: 40px;
        color: #fff;
        font-size: 18px;
      }
      .description {
        margin-top: 10px;
        margin-left: 20px;
        font-size: 18px;
        color: #000;
      }
      .description::before {
        display: inline-block;
        content: '';
        margin-left: -18px;
        margin-right: 12px;
        width: 4px;
        height: 10px;
        background-color: #003963;
        border-radius: 5px;
      }
      .dangerColor {
        color: #ff1818;
        font-weight: 600;
      }
    }
    .optionBox,
    .rightOption {
      display: inline-block;
      button {
        margin-left: 16px;
      }
      .primary-btn-border {
        border-color: #0091ff;
        color: #0091ff;
      }
      .btn-disabled {
        border-color: #ccc;
        color: #ccc;
      }
    }
    .optionBox {
      width: 820px;
      padding-left: 30px;
      vertical-align: inherit;
      font-size: 16px;
      color: #666666;
    }
    .rightOption {
      text-align: right;
      float: right;
    }
    .dataOverview {
      display: flex;
      flex-direction: row;
      margin: 30px auto 0;
      .item {
        flex: 1;
        border-right: 1px dashed #ccc;
        padding-left: 48px;
        .title {
          font-size: 16px;
          color: #999;
        }
        .count {
          font-size: 40px;
          color: #003963;
          font-weight: 700;
        }
      }
      .item:last-child {
        border-right: 0;
      }
    }
    .compactLayout {
      padding: 20px 32px;
      .dataOverview {
        margin: 20px auto 0;
        .item {
          text-align: left;
          .title,
          .count {
            display: inline-block;
          }
          .count {
            font-size: 20px;
            padding-left: 16px;
          }
        }
      }
    }
    .sticky {
      position: sticky;
      z-index: 100;
      top: 0;
    }
  }

  .selectBox {
    position: relative;
    z-index: 5;
    display: block;
    text-align: center;
    margin-bottom: 40px;
    color: #fff;
    font-size: 18px;
  }
  .downloadBox {
    display: inline-block;
    position: absolute;
    top: -60px;
    right: 0;
    margin-bottom: 30px;
    text-align: right;
    button {
      margin-left: 30px;
      height: 36px;
      background: #00c865;
      border-radius: 8px;
      font-size: 16px;
      border: 0;
      color: #fff;
      .iconDownload {
        display: inline-block;
      }
    }
  }
  .watching {
    margin: 20px 0 30px 40px;
    p {
      line-height: 28px;
      font-size: 16px;
    }
  }
}
.customTable {
  margin: 32px auto 0;
  .optionBox {
    display: inline-flex;
    font-size: 16px;
    color: #666666;
  }
  thead > tr > th {
    font-family: "MicrosoftYaHeiUI";
    text-align: center;
    background-color: #5B6F8C;
    padding: 10px 4px;
    color: #fff;
    font-size: 16px;
  }
  tbody > tr {
    &:nth-child(even) {
      background: #F5F8FB;
    }
    td {
      font-size: 16px;
      padding: 8px 4px;
      border-bottom:1px solid #fff;
    }
    td:first-child {
      background: #E4EBF5;
    }
    a {
      color: #0091FF;
      text-decoration: underline;
    }
    .empty {
      color: #CCC;
    }
  }
}
.modalSchoolBox {
  .schoolTitle {
    line-height: 40px;
    font-size: 20px;
    font-weight: 700;
    margin: -24px 0 10px;
    color: #000;
  }
  p {
    color: #666;
    font-size: 14px;
    line-height: 24px;
  }
}