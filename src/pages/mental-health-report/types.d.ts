// 报告类型
export enum EReportType {
  // 个人
  person = 'person',
  // 班级
  class = 'class',
  // 年级
  grade = 'grade'
}

// 排列方式，horizontal-水平，vertical-垂直
export enum EArrangeType {
  horizontal = 'horizontal',
  vertical = 'vertical'
}

export interface IReportServiceResponse {
  code?: string | number;
  msg?: string;
  success?: boolean;
  data?: any;
  queryParams?: any;
}

export interface IOverallRisk {
  /** 维度说明 */
  description: string;
  /** 结论:从测评结果来看，该年级总体风险为 */
  inConclusion: string;
  /** 风险指数，得分 */
  riskIndexScore: number;
  /** 风险级别 */
  riskLevel: number;
  /** 风险级别名称 */
  riskLevelName: string;
  /** 备注 */
  remarks: string;
  statusDistributionBean?: IStatusDistributionBean[]
}

export interface IStatusDistributionBean {
  /** 风险id */
  riskId: number;
  /** 风险名称 */
  riskName: string;
  /** 人数 */
  number: number;
  /** 百分比 */
  percentage: number;
}

export interface IMentalReportBaseInfo {
  /** 报告编号 */
  reportCode: string;
  /** 姓名 */
  userName?: string;
  /** 班级 */
  className?: string;
  /** 毕业年份 */
  gradeName?: string;
  /** 学校名称 */
  schoolName?: string;
  /** 浙江省杭州市 */
  areaName?: string;
  /** 报告时间: 1596102128693 */
  reportTime?: number;
  /** 测评开始时间 */
  startTime?: number;
  /** 测评结束时间 */
  endTime?: number;
  /** 完成人数 */
  evaluatedPeople?: number;
  /** 测评视频原理url */
  principleVideoURL: string;
  /** 总体风险 ,RiskIndexBean */
  overallRisk: IOverallRisk;
  /** 向内攻击 ,RiskIndexBean */
  inwardAttack: IOverallRisk;
  /** 向外攻击 ,RiskIndexBean */
  outwardAttack: IOverallRisk;
}

// 因子项
export interface IFactorItem {
  /** 因子分类标识，true为保护因子，false为危险因子 */
  positiveFlag: boolean;
  /** 维度id */
  latitudeId: number;
  /** 父维度id */
  latitudeGroupId?: number;
  /** 父纬度名称 */
  latitudeGroupName?: string;
  /** 纬度名称 */
  latitudeName: string;
  /** 得分 */
  score: number;
  /** 参考分均分 */
  referenceScore: number;
  /** 预警分，接口类老早就定义错误了，也不好改，报告都用到了 */
  warnningScore: number;
  /** 总体风险级别 */
  riskLevel: number;
}

/**
 * 风险等级枚举
 * health：level-1，健康
 * slightRisk：level-2，轻微
 * higherRisk：level-3，较高
 * highRisk：level-4，高风险
 */
export enum ERiskLevel {
  health = 1,
  slightRisk = 2,
  higherRisk = 3,
  highRisk = 4
}

// 因子说明
export interface IFactorRemark {
  /** 维度id */
  latitudeId: number;
  /** 纬度名称 */
  latitudeName: string;
  /** 因子说明 */
  factorDescription: string;
  /** 高分表现 */
  highScorePerformance: string;
  /** 低分表现 */
  lowScorePerformance: string;
}
