import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import React, { useEffect, useState } from 'react';
import { Table, message } from 'antd';
import classNames from 'classnames'
import moment from 'moment';
import { gradeReportResponse, gradeReportProps, customTableColumnsProps, dataAnalysisItem, percentageAnalysisItem, scoreListItem } from './types'
import EBanner from '~/components/CommonBanner'
import ECard from '@/components/ECard'
import ECompoundTitle from '@/components/ECompoundTitle';
import EModuleTitle from '@/components/EModuleTitle';
import EModuleSubtitle from '~/components/EModuleSubtitle';
import SideNavigation from '@/components/SideNavigation';
import ChartForPie from '@/components/ChartForPie';
import bannerImg from "@/assets/GroupReport/banner.png";
import reportImg from "@/assets/GroupReport/report.png";
import footerImg from "@/assets/GroupReport/footerGradeBanner.png";
import styles from './index.module.less';
import API from '@/service/subjectSelectionAssessment/report';
import { gradeReportParams } from '@/service/subjectSelectionAssessment/types';
import { mountPSTScreenshot } from '~/utils/tools';
import { web } from '~/utils/hosts';

export const getServerSideProps: GetServerSideProps<{
  baseInfo: gradeReportResponse;
}> = async (ctx: any) => {
  const { evaluationTaskId, scoolId, gradeId, ff, isPrint } = ctx.query as unknown as gradeReportParams;
  const printMode = !!(ff || isPrint);
  try {
    const dataRes = await API.getGradeReport(
      { evaluationTaskId, scoolId, gradeId },
      ctx
    );
    return {
      props: {
        baseInfo: {
          ...dataRes,
          printMode
        }
      }
    };
  } catch (error: any) {
    console.error('getGradeReport', error)
    const { success, code, msg } = error;
    return {
      props: {
        baseInfo: {
          data: {
            potentialAnalysis: {},
            hollandAnalysis: {},
          },
          printMode,
          success,
          code,
          msg,
        }
      }
    };
  }
};

const Page = ({
  baseInfo,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const sideNavList = [
    { label: "1、报告导读", point: "reportReading" },
    {
      label: "2、测评说明",
      point: "resultAnalyseIn",
    },
    {
      label: "3、结果分析",
      point: "resultAnalyse",
      child: [
        { label: "3.1 综合分析", point: "synthesis" },
        { label: "3.2 学科潜能分析", point: "potency" },
        { label: "3.3 职业兴趣分析", point: "interest" },
      ],
    },
    { label: "4、报告结束语", point: "summarize" },
  ];

  const { data, success, msg, printMode } = baseInfo;
  const { potentialAnalysis, hollandAnalysis, comprehensiveAnalysis } = data;
  const [customTableColumns, setCustomTableColumns] = useState<customTableColumnsProps[]>([]) // 自定义潜能学科栏
  const [careerInterestsColumns, setCareerInterestsColumns] = useState<customTableColumnsProps[]>([]) //
  const [careerInterestsList, setCareerInterestsList] = useState<percentageAnalysisItem[]>([]) // 自定义职业兴趣
  const [hollandCareerInterestsList, setHollandCareerInterestsList] = useState<percentageAnalysisItem[]>([]) // 霍兰德职业兴趣剖析图

  // 初始化
  const initial = () => {
    if (!success) {
      message.error(msg)
    }

    // 生成潜能学科栏
    const customColumns: any[] = [];
    potentialAnalysis?.classPotential?.[0].scoreList.forEach((item: scoreListItem) => {
      customColumns.push({
        title: item.name,
        key: `${item.id}`,
        dataIndex: item.id,
        render: (record: { id: string; classAvgScore: number; gradeAvgScore: number }) => {
          return (
            <div key={item.id}>
              <span style={{ color: 'rgba(0,200,101,1)' }}>
                {item.classAvgScore}
              </span>/<span>{item.gradeAvgScore}</span>
            </div>
          );
        },
      })
    });

    // 生成职业兴趣表格栏
    const careerInterestsColumns = hollandAnalysis?.percentageAnalysis?.[0].hollandList.map((item: { hollandName: string; hollandCode: string; }) => {
      return {
        title: item.hollandName,
        key: `${item.hollandCode}`,
        dataIndex: item.hollandCode,
      }
    });

    // 生成职业兴趣表格 数据
    const percentageAnalysisList: any[] = []
    hollandAnalysis?.percentageAnalysis?.forEach((item: percentageAnalysisItem) => {
      const record: Record<string | number, any> = {};
      item.hollandList.forEach((item1: dataAnalysisItem) => {
        record[`${item1.hollandCode}`] = `${item1.percentage}%`;
      });
      record.classId = item.classId;
      record.className = item.className;
      record.detailUrl = item.detailUrl;
      percentageAnalysisList.push(record);
    });

    // 霍兰德职业兴趣剖析图
    const hollandCareerInterestsList: any[] = [];
    hollandAnalysis?.dataAnalysis?.forEach((item: dataAnalysisItem) => hollandCareerInterestsList.push({
      ...item, name: `${item.hollandName}\n${item.percentage}%`
    }))
    setCustomTableColumns(customColumns)
    setCareerInterestsColumns(careerInterestsColumns || [])
    setCareerInterestsList(percentageAnalysisList);
    setHollandCareerInterestsList(hollandCareerInterestsList)
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: 3000
      })
    }
  }

  useEffect(() => {
    initial()
  }, [])
  const columnsPotential: any[] = [
    {
      title: '班级/学科',
      dataIndex: 'className',
    },
    ...customTableColumns,
    {
      title: '优势学科',
      key: 'advancedSubject',
      dataIndex: 'advancedSubject',
      render: (tags: string[], index: number) => (
        <div
          style={{
            maxWidth: '150px',
            margin: '0 auto',
            textAlign: 'center',
            padding: 0,
            fontSize: 13
          }}
          key={index}
        >
          {tags && tags.length
            ? tags.map((item: string, key: number) => {
              return (
                <b key={key}>
                  {item} {`${key === tags.length - 1 ? '' : '、'}`}
                </b>
              );
            }) : '-'}
        </div>
      ),
    },
    {
      title: '劣势学科',
      key: 'weakSubject',
      dataIndex: 'weakSubject',
      render: (tags: string[], index: number) => (
        <div
          style={{
            width: '118px',
            margin: '0 auto',
            textAlign: 'center',
            padding: 0,
            fontSize: 13
          }}
          key={index}
        >
          {tags && tags.length
            ? tags.map((item, key) => {
              return (
                <b key={index + key}>{item} {`${key === tags.length - 1 ? '' : '、'}`}</b>
              )
            })
            : '-'}
        </div>
      ),
    },
    {
      title: '详情',
      key: 'detailUrl',
      dataIndex: 'detailUrl',
      render: (tags: string, index: number) => (
        <a
          key={index}
          style={{ color: 'rgba(250,140,22,1)', textDecoration: 'underline' }}
          target='_blank'
          href={`${web}/psychology-service/subjectSelectionAssessment/classReport?${tags.split('?')[1]}`}
        >
          班级报告
        </a>
      ),
    },
  ];

  const careerInterestsTableColumns = [
    {
      title: "班级/学科",
      dataIndex: "className",
    },
    ...careerInterestsColumns,
    {
      title: "详情",
      key: "detailUrl",
      dataIndex: "detailUrl",
      render: (tags: string) => (
        <a
          style={{ color: "rgba(250,140,22,1)", textDecoration: "underline" }}
          target="_blank"
          href={`${web}/psychology-service/subjectSelectionAssessment/classReport?${tags.split('?')[1]}`}
        >
          班级报告
        </a>
      ),
    },
  ];
  return (
    <div className={styles.vitaReportPage}>
      <EBanner imgSrc={bannerImg} height={470} />
      {data && Object.keys(data).length > 0 &&
        <div className={styles.container}>
          <div className={styles.flag}>年级分析报告</div>
          <ECard wrapperstyle={{ position: 'relative', marginBottom: 40, background: '#fff', boxShadow: '0px 0px 20px 0px rgba(66,122,255,0.25)', padding: 24, borderRadius: 20 }}>
            <div className={styles.overView}>
              <li>
                <b>年级:</b> {data.gradeName}
              </li>
              <li>
                <b>学校:</b> {data.schoolName}
              </li>
              <li>
                <b>地区:</b> {data.areaName}
              </li>
              <li>
                <b>报告日期:</b> {moment(data.reportTime).format('YYYY-MM-DD')}
              </li>
              <li>
                <b>报告编码:</b> {data.reportCode}
              </li>
            </div>
          </ECard>
          <ECompoundTitle title="报告导读" decription="Introduction" id={sideNavList[0].point} />
          <ECard wrapperstyle={{ marginBottom: 60, padding: '40px' }}>
            <h3>亲爱的<span className={styles.blueColor}>{data.loginName}</span>老师</h3>
            <p>
              您好，非常感谢您使用升学e网通新高考高中生选科测评，以下是我们根据贵校学校的学生的作答结果而提供的选科指导分析报告。
            </p>
            <p>
              您可以把本报告作为您对学生的选科指导的参考，或者作为学校开设相应组合的参考，但不建议您把它作为唯一依据。 在阅读本报告前，请注意以下几点：
            </p>
            <p>
              1.本报告涉及学生的个人隐私，请严格保管此报告，不给无关人员查阅，不在公共场合讨论报告内容。
            </p>
            <p>2.请注意本报告的有效参考期限：</p>
            <p>
              2个月以内：可以参考
              <span />
              2~6个月：谨慎参考
              <span />
              6个月以上：不建议参考;
            </p>
            <p>
              3.测评结果通常具有一定误差，请结合测评结果与学生的实际表现进行判断，切勿单纯以测评结果给学生贴标签。
            </p>
          </ECard>
          <ECompoundTitle title="测评说明" decription="Specification" id={sideNavList[1].point} />
          <ECard wrapperstyle={{ marginBottom: 60 }}>
            <div className={styles.textBox}>
              <p className={styles.subheading}>测评工具</p>
              <ol>
                <li>高中生选科测评是杭州铭师堂教育科技发展有限公司生涯规划研究团队联合北京师范大学心理专家团队共同开发。</li>
                <li>开发团队深入剖析新高考选科政策并收集大量新高考地区学生的选科数据，建立了科学的选科因素结构模型。</li>
                <li>本次测评主要从学生的学科兴趣、学习表现、学科成绩及学科效能感等多方面评估学生在各门学科上的学科潜能，同时通过霍兰德职业兴趣测试评估学生的未来职业发展方向，能够助力您的选科指导工作。</li>
              </ol>
              <p className={styles.subheading}>测评时间</p>
              <ol>
                <li>{`${moment(data.startTime).format('YYYY-MM-DD')} ~ ${moment(data.endTime).format('YYYY-MM-DD')}`}</li>
              </ol>
              <p className={styles.subheading}>参与人数</p>
              <ol>
                <li>已参与/总人数：{`${data.evaluatedPeople} / ${data.totalPeople}`}（人）</li>
              </ol>
            </div>
          </ECard>
          <ECompoundTitle title="结果分析" decription="Analysis" id={sideNavList[2].point} />
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[2].child?.[0]?.point} >
            <EModuleTitle title="01 综合分析" wrapperStyle={{ backgroundColor: "rgba(75, 128, 255, 0.1)", color: "#4B80FF" }}/>
            <EModuleSubtitle title="推荐选科组合覆盖学生占比分析" themeColor="#4b80ff" />
            <div className={styles.textBox}>
              <p>从测评结果来看，覆盖学生数量最多的是{comprehensiveAnalysis && comprehensiveAnalysis.mostCombinations}，覆盖学生最少的科目组合是{comprehensiveAnalysis && comprehensiveAnalysis.leastCombinations}。</p>
            </div>
              <div className={styles.subjectCombinationDetail}>
                <ul>
                  {comprehensiveAnalysis &&
                    comprehensiveAnalysis.subjectDetail ?
                    comprehensiveAnalysis.subjectDetail.map((v: { combinationCode: string; select: any; combinationName: string; percentage: string | number; }) => {
                      return <li
                        key={v.combinationCode}
                        className={`${!v.select ? classNames(styles.disabled) : ''}`}
                      >
                        <span className={styles.combinationName}>{v.combinationName}</span>
                        {v.select && (
                          <span>
                            {v.percentage}%
                          </span>
                        )}
                      </li>;
                    }
                    ) : null
                  }
                </ul>
              </div>
              <div className={styles.textBox}>
                <p>*灰色的选科组合是学校预先确定无法开设的科目组合，不在学生的个人报告中呈现。</p>
                <p>*“占比”指该组合适合的学生人数占比，每个学生会获取三个科目组合推荐。</p>
              </div>
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[2].child?.[1]?.point}>
            <EModuleTitle title="02 学科潜能分析" wrapperStyle={{marginBottom:16, backgroundColor: "rgba(0, 200, 101, 0.1)", color: "#00c865" }} 
              wrapperClass={`${printMode ? 'typePrint' : ''}`} />
            <div className={styles.textBox}>
              <p>该测评主要用于衡量和判断高中生六门主要科目的学科潜能，作为进一步提供选科指导、学科教学和学习指导提供参考依据。该潜能测评维度包括学科核心素养、学科兴趣、学科学习表现、自我效能、学科成绩等方面进行测量，全面地评估6门学科的倾向，了解学生学习多方面的表现。</p>
            </div>
            <EModuleSubtitle title="本年级各班各学科潜能情况" themeColor="#00c865" />
            <div className={styles.textBox}>
              <p>从测评结果来看，本校各班各学科学科潜能情况如下：</p>
              <p className={styles.note}>各班各学科潜能情况（各学科班级均值/年级均值）</p>
              <Table
                rowKey='classId'
                className={styles.customTable}
                columns={columnsPotential}
                dataSource={potentialAnalysis?.classPotential}
                pagination={false}
              />
            </div>
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[2].child?.[2]?.point}>
            <EModuleTitle title="03 职业兴趣分析" wrapperStyle={{marginBottom:16, backgroundColor: "rgba(255, 141, 0, 0.1)", color: "#ff8d00" }} />
            <div className={styles.textBox}>
              <p>本部分测评主要用于帮助学生探索其未来发展方向，所有的报告内容都是基于学生对测试问题的回答做出。研究表明个体的职业兴趣在较长一段时间是较为稳定的，但是对于青少年来说可能会由成长发育、教育和环境的影响，出现有一些变化，因此建议用动态的眼光看待测试结果。</p>
            </div>
            <EModuleSubtitle title="本年级职业兴趣类型分布" themeColor="#ff8d00" />
            <div className={styles.textBox}>
              <p>从测评结果来看，本年级学生的霍兰德职业兴趣类型丰富多彩，其中{hollandAnalysis?.mostHollandName}学生占比最多，{hollandAnalysis?.leastHollandName}学生占比最少。</p>
            </div>
            {hollandCareerInterestsList.length &&
              <ChartForPie
                id='gradeReport-ChartForPie'
                list={hollandCareerInterestsList}
                height={320}
                title='霍兰德职业兴趣剖析图'
              />
            }
            <EModuleSubtitle title="各年级职业兴趣类型汇总" themeColor="#ff8d00"
              wrapperClass={`${printMode ? 'typePrint' : ''}`}  />
            <Table
              rowKey='classId'
              className={styles.careerInterestsTable}
              columns={careerInterestsTableColumns}
              dataSource={careerInterestsList}
              pagination={false}
            />
            <div className={styles.careerTendencies}>
              <ECard wrapperstyle={{ marginBottom: 30, boxShadow: '0px 0px 20px 0px rgb(255 141 0 / 25%)' }}>
                <div className={styles.careerAnalysisDetails}>
                  <div className={styles.analysisTitle}>
                    实际型（R）
                    <span className={styles.rightBox}>
                      本校学生占比: <span className={styles.score}>
                        {hollandAnalysis?.dataAnalysis?.map((item: { hollandCode: string; percentage: any; }, index: any) =>
                          item.hollandCode === "R" ?
                            <span key={`${item.hollandCode + index}`} className={"yellow-num"}>
                              {item.percentage || 0}%
                            </span> : null
                        )}</span>
                    </span>
                  </div>
                  <div className={styles.analysisBox}>
                    <h3>类型特点</h3>
                    <p>偏好于具体任务，喜欢使用工具、机器，需要基本操作技能的工作。不善言辞，做事保守，较为谦虚。缺乏社交能力，通常喜欢独立做事</p>
                    <h3>教育建议</h3>
                    <p>性格代码为R的学生非常愿意且擅长使用工具从事操作性工作，例如电教委员，劳动委员等，负责班级的投影仪的使用及劳动工具的维护等。</p>
                  </div>
                </div>
              </ECard>
              <ECard wrapperstyle={{ marginBottom: 30, boxShadow: '0px 0px 20px 0px rgb(255 141 0 / 25%)' }}>
                <div className={styles.careerAnalysisDetails}>
                  <div className={styles.analysisTitle}>
                    常规型(C)
                    <span className={styles.rightBox}>
                      本校学生占比: <span className={styles.score}>
                        {hollandAnalysis?.dataAnalysis?.map((item: { hollandCode: string; percentage: any; }, index: any) =>
                          item.hollandCode === "C" ?
                            <span key={`${item.hollandCode + index}`} className={"yellow-num"}>
                              {item.percentage || 0}%
                            </span> : null
                        )}</span>
                    </span>
                  </div>
                  <div className={styles.analysisBox}>
                    <h3>类型特点</h3>
                    <p>喜欢关注实际和细节情况，通常较为谨慎和保守，尊重权威和规章制度，喜欢按计划办事，缺乏创造性，不喜欢冒险和竞争，富有自我牺牲精神。</p>
                    <h3>教育建议</h3>
                    <p>性格代码为C的学生做事情注重细节，精确有度，有系统有条理。在班级管理中，非常适合各科学习课代表，收发作业，代替老师布置作业等工作往往能做到让老师非常放心。</p>
                  </div>
                </div>
              </ECard>

              <ECard wrapperstyle={{ marginBottom: 30, boxShadow: '0px 0px 20px 0px rgb(255 141 0 / 25%)' }}>
                <div className={styles.careerAnalysisDetails}>
                  <div className={styles.analysisTitle}>
                    企业型(E)
                    <span className={styles.rightBox}>
                      本校学生占比: <span className={styles.score}>
                        {hollandAnalysis?.dataAnalysis?.map((item: { hollandCode: string; percentage: any; }, index: any) => item.hollandCode === "E" ?
                          <span key={`${item.hollandCode + index}`} className={"yellow-num"}>
                            {item.percentage || 0}%
                          </span> : null
                        )}</span>
                    </span>
                  </div>
                  <div className={styles.analysisBox}>
                    <h3>类型特点</h3>
                    <p>喜欢竞争、敢冒风险、有野心、抱负。为人务实，习惯以利益得失，权利、地位、金钱等来衡量做事的价值，做事有较强的目的性。</p>
                    <h3>教育建议</h3>
                    <p>性格代码为E的学生，一般都有较好的经营、管理、劝服、监督和领导才能，在班级中，非常适合班长这样的职务，会是协助班主任老师完成班级管理的一把好手。</p>
                  </div>
                </div>
              </ECard>

              <ECard wrapperstyle={{ marginBottom: 30, boxShadow: '0px 0px 20px 0px rgb(255 141 0 / 25%)' }}>
                <div className={styles.careerAnalysisDetails}>
                  <div className={styles.analysisTitle}>
                    社会型(S)
                    <span className={styles.rightBox}>
                      本校学生占比: <span className={styles.score}>
                        {hollandAnalysis?.dataAnalysis?.map((item: { hollandCode: string; percentage: any; }, index: any) =>
                          item.hollandCode === "S" ?
                            <span key={`${item.hollandCode + index}`} className={"yellow-num"}>
                              {item.percentage || 0}%
                            </span> : null
                        )}</span>
                    </span>
                  </div>
                  <div className={styles.analysisBox}>
                    <h3>类型特点</h3>
                    <p>能够不断结交新的朋友，从事提供信息、启迪、帮助、培训、开发或治疗等事务，并具备相应能力。</p>
                    <h3>教育建议</h3>
                    <p>性格代码为S的学生，喜欢要求与人打交道，并且非常愿意关注同学的身心健康。在班级中，像生活委员，心理委员等这样的职位比较适合交给他们。</p>
                  </div>
                </div>
              </ECard>

              <ECard wrapperstyle={{ marginBottom: 30, boxShadow: '0px 0px 20px 0px rgb(255 141 0 / 25%)' }}>
                <div className={styles.careerAnalysisDetails}>
                  <div className={styles.analysisTitle}>
                    艺术型(A)
                    <span className={styles.rightBox}>
                      本校学生占比: <span className={styles.score}>
                        {hollandAnalysis?.dataAnalysis?.map((item: { hollandCode: string; percentage: any; }, index: any) =>
                          item.hollandCode === "A" ?
                            <span key={`${item.hollandCode + index}`} className={"yellow-num"}>
                              {item.percentage || 0}%
                            </span> : null
                        )}</span>
                    </span>
                  </div>
                  <div className={styles.analysisBox}>
                    <h3>类型特点</h3>
                    <p>有创造力，乐于创造新颖、与众不同的成果，渴望表现自己的个性，实现自身的价值，并且致力于自我表达。做事理想化，追求完美，不重实际。</p>
                    <h3>教育建议</h3>
                    <p>性格代码为A的学生喜欢的工作要求具备艺术修养、创造力、表达能力。在班级中，推荐文艺委员、宣传委员类职务，负责班级板报设计，宣传类工作。</p>
                  </div>
                </div>
              </ECard>

              <ECard wrapperstyle={{ marginBottom: 30, boxShadow: '0px 0px 20px 0px rgb(255 141 0 / 25%)' }}>
                <div className={styles.careerAnalysisDetails}>
                  <div className={styles.analysisTitle}>
                    研究型(I)
                    <span className={styles.rightBox}>
                      本校学生占比: <span className={styles.score}>
                        {hollandAnalysis?.dataAnalysis?.map((item: { hollandCode: string; percentage: any; }, index: any) =>
                          item.hollandCode === "I" ?
                            <span key={`${item.hollandCode + index}`} className={"yellow-num"}>
                              {item.percentage || 0}%
                            </span> : null
                        )}</span>
                    </span>
                  </div>
                  <div className={styles.analysisBox}>
                    <h3>类型特点</h3>
                    <p>抽象思维能力强，求知欲强，知识渊博，肯动脑，善思考，喜欢独立工作，不善于领导他人。考虑问题理性，做事喜欢精确，喜欢逻辑分析和推理，不断探讨未知的领域。</p>
                    <h3>教育建议</h3>
                    <p>性格代码为I的学生喜欢独立的和富有创造性的工作。这部分学生往往成绩较为优异，可以尝试学习委员等职务。</p>
                  </div>
                </div>
              </ECard>
            </div>
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 60 }} id={sideNavList[3].point} wrapperClass={`${printMode ? 'typePrint' : ''}`}>
            <ECompoundTitle title="报告结束语" decription="Conclusion" />
            <div className={styles.textBox}>
              <p>本测评在开发前期，通过收集大量新高考地区学生的选科数据，建立了如下科学的选科因素结构模型。该模型认为，科学选科需要考虑两方面的因素，一是学科潜能，二是未来职业发展方向，只有结合两者才能作出更科学的选科决策。</p>
              <p><img src={reportImg} width="920" height="368" alt="选科流程图" /></p>
              <p>1. 我们衷心地希望本测评能为您在对学生选科指导时提供科学的参考依据，帮助您能够全面客观地了解您的学生在选科时各因素上的表现。</p>
              <p>2. 在完成测评的过程中，学生的选择可能会受到环境和情绪的影响，希望您在参考本报告的同时也可以结合您在实际中观察到的学生表现。</p>
              <p>3. 青少年是处在自我同一性建立的关键时期，所以他们的兴趣可能还未完全分化，本测评力求挖掘学生的发展优势，但是绝不代表学生的最终成就。</p>
            </div>
          </ECard>
        </div>
      }
      {!printMode &&
        <SideNavigation list={sideNavList} />
      }
      <EBanner imgSrc={footerImg} height={90} />
    </div>
  );
}

export default Page;
