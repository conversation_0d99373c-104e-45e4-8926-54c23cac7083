.vitaReportPage {
  position: relative;
  padding: 0;
  margin: 0;
  background-color: #fff;
  color: #001139;
  .blueColor {
    color: #4b80ff;
  }
  h3 {
    font-size: 16px;
    font-weight: bold;
  }
  .container {
    width: 1000px;
    box-sizing: border-box;
    margin: -160px auto 0;
    .flag {
      position: relative;
      width: 150px;
      line-height: 44px;
      background-color: #fa8c16;
      border-radius: 10px;
      text-align: center;
      color: #fff;
      font-size: 18px;
      margin-bottom: 30px;
    }
    .overView {
      li {
        display: inline-block;
        min-width: 25%;
        font-size: 14px;
        line-height: 36px;
        b {
          padding-right: 6px;
        }
      }
    }
    .textBox {
      margin-bottom: 20px;
      padding:0 14px;
      p {
        line-height: 24px;
        span {
          padding-right: 0;
        }
      }
      .center {
        text-align: center;
      }
      .note {
        text-align: center;
        font-size: 13px;
        margin-top: 20px;
      }
      .subheading {
        padding-top: 20px;
        font-weight: 700;
        font-size: 16px;
        color: #427aff;
      }
      .subheading::before {
        content: '★';
        display: inline-block;
        padding-right: 6px;
      }
      ol,
      ul {
        padding: 10px 20px;
        li {
          padding-top: 20px;
          color: rgba(0, 17, 57, 0.85);
          font-size: 16px;
          list-style: disc;
        }
      }
    }
    .customTable,
    .careerInterestsTable {
      margin-top: 20px;
      tr > th {
        padding: 8px 10px;
        background: rgba(0, 200, 101, 0.1);
        text-align: center;
      }
      tr > td {
        padding: 5px;
        text-align: center;
        color: rgba(0, 17, 57, 0.65);
        font-size: 14px;
      }
    }
    .careerInterestsTable {
      tr > th {
        padding: 8px 10px;
        background: rgba(255, 141, 0, 0.1);
        text-align: center;
      }
    }
    .subjectCombinationDetail {
      margin: 30px 5px 5px;
      padding: 24px 24px 0 24px;
      background: #f2f6ff;
      border-radius: 8px;
      ul {
        display: flex;
        flex-flow: row wrap;
        justify-content: flex-start;
        justify-items: center;
        li {
          display: flex;
          align-items: center;
          margin: 0 24px 20px 0;
          line-height: 45px;
          padding: 0 10px;
          width: 205px;
          background: #ffffff;
          border-radius: 8px;
          color: rgba(0, 17, 57, 0.85);
          font-size: 16px;
          .combinationName {
            padding-right: 20px;
          }
        }
        li:nth-child(4n) {
          margin: 0 0 20px 0;
        }
        .disabled {
          color: #fff;
          background: #cbd2e1;
        }
      }
    }
    .echartBox {
      text-align: center;
      margin: 0 auto;
      p {
        line-height: 40px;
        font-size: 14px;
        color: rgba(0, 17, 57, 0.85);
      }
    }

    .careerAnalysisDetails {
      .analysisBox {
        h3 {
          padding-top: 20px;
          font-weight: 700;
          font-size: 16px;
          color: #ff8d00;
        }
        ol,
        ul {
          padding: 10px 20px;
          li {
            padding-top: 20px;
            color: rgba(0, 17, 57, 0.85);
            font-size: 16px;
            list-style: disc;
          }
        }
      }
    }
    .analysisTitle {
      line-height: 44px;
      background: linear-gradient(270deg, #ffffff, #ff8d00);
      border-radius: 8px;
      font-size: 16px;
      font-weight: 700;
      color: #fff;
      padding: 0 20px;
      .rightBox {
        float: right;
        color: #475370;
        font-size: 16px;
        font-weight: 700;
        .score {
          color: #ff8d00;
        }
      }
    }
    .careerTendencies {
      margin: 30px 10px 30px 10px;
      font-size: 16px;
    }
  }
}
