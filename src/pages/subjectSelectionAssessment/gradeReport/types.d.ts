import React from 'react';
export interface subjectDetailItem {
  combinationCode: string;
  combinationCount: string;
  combinationName: string;
  percentage: number;
  select: boolean;
}

export interface comprehensiveAnalysisProps {
  leastCombinations: string;
  mostCombinations: string;
  subjectDetail: subjectDetailItem[];
}

export interface dataAnalysisItem {
  hollandCode: string;
  hollandName: string;
  percentage: number;
  value?: string;
}

export interface percentageAnalysisItem {
  classId: string;
  className: string;
  detailUrl: string;
  hollandList: dataAnalysisItem[];
}

export interface hollandAnalysisProps {
  dataAnalysis?: dataAnalysisItem[];
  leastHollandCode?: string | null;
  leastHollandName?: string;
  mostHollandCode?: string | null;
  mostHollandName?: string;
  percentageAnalysis?: percentageAnalysisItem[];
}

export interface scoreListItem {
  classAvgScore: number;
  gradeAvgScore: number;
  id: string;
  name: string;
}

export interface classPotentialItem {
  advancedSubject: string[];
  classId: string;
  className: string;
  detailUrl: string;
  scoreList: scoreListItem[];
  weakSubject: string[];
}

export interface potentialAnalysisProps {
  classPotential?: classPotentialItem[];
}

// 年级报告
export interface gradeReportProps {
  areaName?: string;
  comprehensiveAnalysis?: comprehensiveAnalysisProps;
  endTime?: number;
  evaluatedPeople?: number;
  gradeName?: string;
  hollandAnalysis?: hollandAnalysisProps;
  loginName?: string;
  potentialAnalysis?: potentialAnalysisProps;
  reportCode?: string;
  reportTime?: number;
  schoolName?: string;
  startTime?: number;
  totalPeople?: number;
}

export interface gradeReportResponse {
  code?: string | number;
  msg?: string;
  success?: boolean;
  data: gradeReportProps;
  printMode:boolean;
}

export interface customTableColumnsProps {
  dataIndex: string;
  key: string;
  title: string;
  width?: number | string;
  render?: () => React.ReactNode;
}

export interface recommendedsItem {
  id?: number | string;
  name: string;
}
