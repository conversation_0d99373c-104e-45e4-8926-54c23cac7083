export interface dataAnalysisItem {
  averageScore: number;
  hollandCode: string;
  hollandName: string;
  totalScore: number;
}

export interface hollandRecommendedsItem {
  hollandName: string;
  hollandCode: string;
  averageScore?: string;
  totalScore?: number;
}
export interface hollandAnalysisProps {
  dataAnalysis: dataAnalysisItem[]
  hollandRecommendeds: hollandRecommendedsItem[];
}

export interface potentialItem {
  id: string;
  name: string;
  score: number;
  totalScore: number;
}
export interface subjectPotentialItem {
  abilityScore: number;
  id: string;
  interestScore: number;
  name:number;
}

export interface subjectPotentialsItem {
  averageScore: number;
  overallSituation:string;
  subjectAbility: string;
  subjectCode: string;
  subjectInterest: string;
  subjectName: string;
  subjectPerformanceCode: string;
  subjectPerformanceName: string;
  totalScore: number;
}

export interface overallSituationProps {
  potential?: potentialItem[];
  subjectPotential?: subjectPotentialItem[];
  subjects: string[];
}
export interface potentialAnalysisProps {
  overallSituation: overallSituationProps
  subjectPotentials: subjectPotentialsItem[];
}

export interface subjectDetailItem {
  advantageAnalysis: string[];
  combinationImgUrl: string;
  combinationName: string;
  combinationType: string;
  disadvantageAnalysis: string[];
  suitableStudents: string[];
}
export interface subjectRecommendProps {
  subject: string[];
  subjectDetail: subjectDetailItem[];
  subjectMatch: string[];
}

export interface userPeportProps {
  areaName?: string;
  bizType?: string;
  className?: string;
  gradeName?: string;
  hollandAnalysis?: hollandAnalysisProps;
  potentialAnalysis?: potentialAnalysisProps;
  reportCode?: string;
  reportTime?: number;
  schoolName?: string;
  subjectRecommend?: subjectRecommendProps;
  userName?: string;
}

export interface userPeportResponse {
  code?: string | number;
  msg?: string;
  success?: boolean;
  data: userPeportProps;
  printMode: boolean;
}


export interface recommendedsItem {
  id?: number | string;
  name: string;
}

export interface subjectPerformanceItem {
  name: string;
  list: string[];
}

export interface careerRecommendedsProps {
  type: string;
  hollandName?: string;
  capabilityFeatures: recommendedsItem[];
  careerDirection: string;
  characterFeatures: recommendedsItem[];
  professionRecommend: string;
  specialtyRecommend: recommendedsItem[];
}
