/*
 * @Author: twenty-five
 * @Date: 2022-08-02 19:06:29
 * @LastEditors: your name
 * @LastEditTime: 2022-08-04 10:49:04
 * @Description: file content
 */
.vitaReportPage {
  position: relative;
  padding: 0;
  margin: 0;
  background-color: #fff;
  color: #001139;
  .blueColor {
    color: #4b80ff;
  }
  h3 {
    font-size: 16px;
    font-weight: bold;
  }
  .container {
    width: 1000px;
    box-sizing: border-box;
    margin: -160px auto 0;
    .flag {
      position: relative;
      width: 150px;
      line-height: 44px;
      background-color: #fa8c16;
      border-radius: 10px;
      text-align: center;
      color: #fff;
      font-size: 18px;
      margin-bottom: 30px;
    }
    .overView {
      li {
        display: inline-block;
        min-width: 25%;
        font-size: 14px;
        line-height: 36px;
        b {
          padding-right: 6px;
        }
      }
    }
    .textBox {
      p {
        line-height: 24px;
        span {
          padding-right: 0;
        }
      }
    }
    .echartBox {
      text-align: center;
      margin: 0 auto;
      p {
        line-height: 40px;
        font-size: 14px;
        color: rgba(0, 17, 57, 0.85);
      }
    }
    .subjectRecommend {
      margin: 40px auto;
      text-align: center;
      & > div:nth-child(2n) {
        margin: 0 60px;
      }
      p {
        text-align: left;
        font-size: 14px;
        color: rgba(0, 17, 57, 0.85);
        margin: 0 auto;
        padding-top: 25px;
      }
    }
    .report-capacity-list{
      display: flex;
      flex-flow: row wrap;
      justify-content: space-between;
      width: 564px;
      margin: 20px auto 0;
      li{
          width: 164px;
          height: 31px;
          margin: 0 20px 15px 0;
          border-radius: 14px;
          display: inline-block;
          line-height: 31px;
          font-size: 14px;
          text-align: center;
          background: rgba(0, 200, 101, 0.1);
      }
    }
    .analysisDetails,
    .potentialAnalysisDetails,
    .careerAnalysisDetails {
      background-color: #f8faff;
      border-radius: 14px;
      padding: 20px;
      margin: 30px 0;
      .flagBox {
        .flagItem {
          display: inline-block;
          line-height: 60px;
          margin-right: 20px;
          background-color: #fff;
          border-radius: 8px;
          padding: 0 20px;
          font-size: 18px;
          font-weight: 700;
          color: rgba(0, 17, 57, 0.85);
          .label {
            color: #4b80ff;
            font-size: 16px;
            padding-right: 20px;
          }
        }
      }
      .previewImg {
        width: 346px;
        margin: 55px auto 0;
        img {
          width: 100%;
        }
        p {
          padding-top: 30px;
          font-size: 14px;
          color: rgba(0, 17, 57, 0.65);
          text-align: center;
        }
      }
      .analysisBox {
        h3 {
          padding-top: 20px;
          font-weight: 700;
          font-size: 16px;
          color: #427aff;
        }
        h3::before {
          content: '★';
          display: inline-block;
          padding-right: 6px;
        }
        ol,
        ul {
          padding: 10px 20px;
          li {
            padding-top: 20px;
            color: rgba(0, 17, 57, 0.85);
            font-size: 16px;
            list-style: disc;
          }
        }
      }
    }
    .potentialAnalysisTitle,
    .careerAnalysisTitle {
      line-height: 44px;
      background: -webkit-gradient(
        linear,
        right top,
        left top,
        from(#ffffff),
        to(#00c865)
      );
      background: linear-gradient(270deg, #ffffff, #00c865);
      border-radius: 8px;
      font-size: 16px;
      font-weight: 700;
      color: #fff;
      padding: 0 20px;
      .rightBox {
        float: right;
        color: #000;
        font-size: 20px;
        font-weight: 700;
        .score {
          color: #00c865;
        }
      }
    }
    .careerAnalysisTitle {
      background: linear-gradient(270deg, #ffffff, #ff8d00);
    }
    .potentialAnalysisDetails,
    .careerAnalysisDetails {
      background: none;
      border-radius: 0;
      padding: 0;
      margin: 0;
      .analysisBox {
        h3 {
          color: #00c865;
          line-height: 44px;
        }
        ul {
          padding: 0 20px;
          li {
            padding-top: 10px;
            list-style: none;
            line-height: 1.5;
          }
        }
      }
    }
    .careerAnalysisDetails {
      .analysisBox {
        h3 {
          color: #ff8d00;
          .tips {
            font-size: 14px;
            font-weight: 400;
          }
        }
        a {
          color: #4b80ff;
          padding-right: 20px;
          text-decoration: underline;
        }
      }
    }
    .subjectPerformance {
      margin: 40px 10px;
      h3 {
        color: rgba(0, 17, 57, 0.85);
        margin-top: 40px;
        font-weight: 700;
        font-size: 18px;
      }
      ul {
        width: 830px;
        margin: 30px auto;
        text-align: center;
        display: flex;
        flex-flow: row nowrap;
        -webkit-box-pack: justify;
        display: flex;
        justify-content: space-between;
        li {
          display: inline-block;
          width: 178px;
          padding: 20px 29px;
          background: #ffffff;
          border-radius: 14px;
          box-shadow: 0px 0px 20px 0px rgb(0 200 101 / 25%);
          .title {
            color: #00c865;
            font-size: 20px;
            text-align: center;
            font-weight: 700;
          }
          .subjectName {
            padding-top: 20px;
            font-size: 16px;
            text-align: center;
          }
        }
      }
    }
    .careerTendencies {
      margin: 20px 0 30px 10px;
      font-size: 16px;
      color: #ff8d00;
      font-weight: 700;
    }
  }
}
