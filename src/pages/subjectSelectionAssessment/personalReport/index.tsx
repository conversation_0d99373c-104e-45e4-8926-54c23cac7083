import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import Cookie from 'cookie';
import { observer } from 'mobx-react-lite';
import moment from 'moment';
import API from '~/service/subjectSelectionAssessment/report';
import {
  userPeportResponse,
  careerRecommendedsProps,
  subjectPerformanceItem,
  subjectDetailItem,
  subjectPotentialsItem,
  recommendedsItem,
  hollandRecommendedsItem,
  subjectPotentialItem,
  dataAnalysisItem,
  potentialItem,
} from './types';
import { careerList } from '~/constants/subjectSelectionAssessment/personalReport';
import { userReportParams } from '@/service/subjectSelectionAssessment/types';
import EBanner from '~/components/CommonBanner';
import ECard from '@/components/ECard';
import bannerImg from '@/assets/GroupReport/banner.png';
import reportImg from '@/assets/GroupReport/report.png';
import footerImg from '@/assets/GroupReport/footerPersonalBanner.png';
import ECompoundTitle from '@/components/ECompoundTitle';
import EModuleTitle from '@/components/EModuleTitle';
import EModuleSubtitle from '@/components/EModuleSubtitle';
import ECombinationTag from '@/components/ECombinationTag';
import SideNavigation from '@/components/SideNavigation';
import ChartForRadar from '@/components/ChartForRadar';
import ChartForFourQuadrant from '@/components/ChartForFourQuadrant';
import styles from './index.module.less';
import { web } from '@/utils/hosts';
import { mountPSTScreenshot } from '@/utils/tools';

export const getServerSideProps: GetServerSideProps<{
  baseInfo: userPeportResponse;
}> = async (ctx: any) => {
  const { evaluationRecordId, ff, isPrint } = ctx.query as unknown as userReportParams;
  const printMode = !!(ff || isPrint);
  try {
    const cookie = ctx.req?.headers.cookie;
    const cookieRes = Cookie.parse(cookie || '');
    const userId = (cookieRes && (cookieRes.UserID || (cookieRes.token && cookieRes.token.split('-')[0])));
    const dataRes = await API.getUserReport(
      { evaluationRecordId, userId },
      ctx
    );
    return { props: { baseInfo: { ...dataRes, printMode } } };
  } catch (error: any) {
    console.error('接口异常getUserReport', error);
    const { code, msg } = error;
    return { props: { baseInfo: { data: {}, printMode, success: false, code, msg } } }
  }
};

function Page({ baseInfo }: InferGetServerSidePropsType<typeof getServerSideProps>) {
  const sideNavList = [
    { label: "1、报告导读", point: "reportReading" },
    {
      label: "2、结果分析",
      point: "resultAnalyseIn",
      child: [
        { label: "2.1 综合分析", point: "synthesis" },
        { label: "2.2 学科潜能分析", point: "potency" },
        { label: "2.3 职业兴趣分析", point: "interest" },
      ],
    },
    { label: "3、总结&建议", point: "summarize" },
  ];
  const { data, printMode, success, msg } = baseInfo;
  const { hollandAnalysis, potentialAnalysis } = data;
  const [hollandCareerRecommendedsList, setHollandCareerRecommendedsList] = useState<careerRecommendedsProps[]>([]);
  const [subjectShowList, setSubjectShowList] = useState<subjectPerformanceItem[]>([]); // 各学科表现
  const [isSubjectShow, setIsSubjectShow] = useState(false); // 各个学科都为空时，不显示此模块
  const [subjectsCount, setSubjectsCount] = useState(0);
  const [raderList, setRaderList] = useState<any[]>([]);
  const [abilityInterestList, setAbilityInterestList] = useState<any[]>([]);
  const [profileHollandCareerInterestsList, setProfileHollandCareerInterestsList] = useState<any[]>([]);

  const initialBaseInfo = () => {
    if (!success) {
      message.error(msg)
    }

    // 霍兰德推荐
    const { overallSituation } = potentialAnalysis || {};
    const newList: any = [];
    const subjectList: { name: string; list: string[] }[] = [
      { name: '高兴趣高能力', list: [] },
      { name: '高兴趣低能力', list: [] },
      { name: '低兴趣高能力', list: [] },
      { name: '低兴趣低能力', list: [] },
    ];
    let flagShow = false;
    const abilityInterestList: any[] = [];
    hollandAnalysis?.hollandRecommendeds.forEach((item: hollandRecommendedsItem) => {
      careerList.forEach((v) => {
        if (v.type === item.hollandCode) {
          newList.push({
            ...v,
            hollandCode: item.hollandCode,
            hollandName: item.hollandName,
          });
        }
      });
    });

    // 各学科具体表现
    potentialAnalysis?.subjectPotentials?.forEach((item: subjectPotentialsItem) => {
      if (item.subjectPerformanceCode === '3') {
        // 高能力高兴趣
        subjectList[0].list.push(item.subjectName);
        flagShow = true;
      }
      if (item.subjectPerformanceCode === '1') {
        // 高兴趣低能力
        subjectList[1].list.push(item.subjectName);
        flagShow = true;
      }
      if (item.subjectPerformanceCode === '9') {
        // 低兴趣高能力
        subjectList[2].list.push(item.subjectName);
        flagShow = true;
      }
      if (item.subjectPerformanceCode === '7') {
        // 低能力低兴趣
        subjectList[3].list.push(item.subjectName);
        flagShow = true;
      }
    });

    const count = overallSituation && overallSituation?.subjects.length - 1;

    // 雷达图所需数据
    const raderList: any[] = []
    overallSituation?.potential?.map((item: potentialItem) =>
      raderList.push({
        name: item.name,
        max: item.totalScore,
        value: item.score
      })
    )

    // 四象限坐标图
    overallSituation?.subjectPotential?.forEach((item: subjectPotentialItem) => {
      const axisList = [];
      axisList[0] = item.abilityScore - 3 >= 0 ? Number((item.abilityScore - 3).toFixed(1)) : Number((item.abilityScore - 3).toFixed(1));
      axisList[1] = item.interestScore - 3 >= 0 ? Number((item.interestScore - 3).toFixed(1)) : Number((item.interestScore - 3).toFixed(1));
      axisList[2] = item.name;
      abilityInterestList.push(axisList)
    })

    // 企业类型雷达图
    const profileHollandCareerInterestsList: any[] = [];
    hollandAnalysis?.dataAnalysis?.forEach((item: dataAnalysisItem) => {
      profileHollandCareerInterestsList.push({
        name: `${item.hollandName}(${item.hollandCode})`,
        value: item.averageScore,
        max: Number(item.totalScore) + 1
      })
    })
    setSubjectsCount(count || 0)
    setIsSubjectShow(flagShow);
    setSubjectShowList(subjectList);
    setHollandCareerRecommendedsList(newList);
    setRaderList(raderList);
    setAbilityInterestList(abilityInterestList);
    setProfileHollandCareerInterestsList(profileHollandCareerInterestsList)

    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: 3000
      })
    }
  };

  useEffect(() => {
    initialBaseInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [baseInfo]);

  return (
    <div className={styles.vitaReportPage}>
      <EBanner imgSrc={bannerImg} height={470} />
      { data && Object.keys(data).length > 0 && (
        <div className={styles.container}>
          <div className={styles.flag}>个人分析报告</div>
          <ECard wrapperstyle={{ marginBottom: 30 }}>
            <div className={styles.overView}>
              <li>
                <b>姓名:</b> {data.userName}
              </li>
              <li>
                <b>班级:</b> {data.className}
              </li>
              <li>
                <b>年级:</b> {data.gradeName}
              </li>
              <li>
                <b>学校:</b> {data.schoolName}
              </li>
              <li>
                <b>地区:</b> {data.areaName}
              </li>
              <li>
                <b>报告日期:</b>{' '}
                {moment(data.reportTime).format('YYYY-MM-DD')}
              </li>
              <li>
                <b>报告编码:</b> {data.reportCode}
              </li>
            </div>
          </ECard>
          <ECompoundTitle title="报告导读" decription="Introduction" id={sideNavList[0].point} />
          <ECard wrapperstyle={{ marginBottom: 30, padding: '40px' }}>
            <h3>
              亲爱的
              <span className={styles.blueColor}>{data.userName}</span>同学
            </h3>
            <p>
              恭喜你完成了高中生选科测评，以下是我们根据你的测评结果提供的选科指导分析报告。
            </p>
            <p>
              本报告作为选科参考，但不建议将其作为你选科决策的唯一依据，更理想的方式是带着报告与家人、老师进行讨论和综合分析。
            </p>
            <p>在阅读本报告前，请注意以下几点：</p>
            <p>
              1.本报告涉及你的个人隐私，请严格保管此报告，不给无关人员查阅，不在公共场合讨论报告内容。
            </p>
            <p>2.请注意本报告的有效参考期限：</p>
            <p>
              2个月以内：可以参考
              <span />
              2~6个月：谨慎参考
              <span />
              6个月以上：不建议参考{' '}
            </p>
            <p>
              3.测评结果通常具有一定误差，请结合测评结果与实际表现进行判断，切勿单纯以测评结果给自己贴标签。
            </p>
          </ECard>
          <ECompoundTitle title="结果分析" decription="Analysis" id={sideNavList[1].point} />
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[1].child?.[0].point}>
            <EModuleTitle
              title="01 综合分析"
              wrapperStyle={{
                backgroundColor: 'rgba(75, 128, 255, 0.1)',
                color: '#4B80FF',
              }}
            />
            <EModuleSubtitle title="总体情况" themeColor="#4b80ff" />
            <div className={styles.textBox}>
              <p>根据你的学科潜能优势，本测评为你推荐的学科组合如下：</p>
              <div className={styles.subjectRecommend}>
                {data.subjectRecommend?.subject
                  ? data.subjectRecommend.subject.map((item: string, index: number) => {
                    return (
                      <ECombinationTag
                        wrapperstyle={{
                          display: 'inline-block',
                        }}
                        key={index}
                        title={`组合${index + 1}`}
                        name={item}
                      />
                    );
                  })
                  : null}
                <p style={{ width: 800 }}>
                  *
                  建议在选科时优先参考以上依据学科潜能推荐的科目组合，同时参考职业兴趣分析中提供的专业方向，最终根据本省公布的具体选科要求文件做出科学选科决策。
                </p>
              </div>
              <EModuleSubtitle title="科目组合特点分析" themeColor="#4b80ff" />
              {data.subjectRecommend &&
                data.subjectRecommend.subjectDetail
                ? data.subjectRecommend.subjectDetail.map((item: subjectDetailItem, index: number) => {
                  return (
                    <div key={index} className={styles.analysisDetails}>
                      <div className={styles.flagBox}>
                        <div className={styles.flagItem}>
                          <span className={styles.label}>
                            组合{index + 1}
                          </span>
                          {item.combinationName}
                        </div>
                        <div className={styles.flagItem}>
                          <span className={styles.label}>组合类型 </span>
                          {item.combinationType}
                        </div>
                      </div>
                      <div className={styles.previewImg}>
                        <img src={item.combinationImgUrl} width="346" height="296" alt="科目组合模型图" />
                        <p>科目组合模型图</p>
                      </div>
                      <div className={styles.analysisBox}>
                        <h3>该组合优势分析:</h3>
                        <ol>
                          { item.advantageAnalysis &&
                            item.advantageAnalysis.map((text, index) => <li key={index}>{text}</li>)}
                        </ol>
                        <h3>该组合劣势分析:</h3>
                        <ol>
                          {item.disadvantageAnalysis &&
                            item.disadvantageAnalysis.map((text, index) => <li key={index}>{text}</li>)}
                        </ol>
                        <h3>该组合适宜学生特点:</h3>
                        <ol>
                          {item.suitableStudents &&
                            item.suitableStudents.map((text, index) => <li key={index}>{text}</li>)}
                        </ol>
                      </div>
                    </div>
                  );
                })
                : null}
            </div>
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[1].child?.[1].point}>
            <EModuleTitle
              title="02 学科潜能分析"
              wrapperStyle={{
                backgroundColor: 'rgba(0, 200, 101, 0.1)',
                color: '#00c865',
              }}
              wrapperClass={printMode ? 'typePrint' : ''}
            />
            <EModuleSubtitle title="总体情况" themeColor="#00c865" />
            <div className={styles.textBox}>
              <p>
                根据你的作答结果，你的各学科潜能由强到弱依次是：
                {potentialAnalysis?.overallSituation?.subjects?.map(
                  (item: string, index: number) => {
                    if (index === subjectsCount) {
                      return <span key={index}>{item}。</span>;
                    } else {
                      return <span key={index}>{item}、</span>;
                    }
                  }
                )
                }
              </p>
              {raderList.length &&
                <ChartForRadar
                  id='personReport-ChartForRader'
                  list={raderList}
                  height={320}
                  areaColor='#00C865'
                  showIndicatorValue
                  title='学科潜能剖析图'
                />
              }
              <p>根据你的作答结果，你的各学科潜能矩阵分布情况如下图：</p>
              { abilityInterestList.length &&
                <ChartForFourQuadrant
                  id='personReport-ChartForFourQuadrant'
                  list={abilityInterestList}
                  width={500}
                  height={420}
                  areaColor='#00C865'
                  showIndicatorValue
                  title='各学科潜能矩阵分析图'
                />
              }
              <ul className={styles["report-capacity-list"]}>
                {potentialAnalysis?.overallSituation?.subjectPotential?.map(
                  (item: subjectPotentialItem) =>
                    <li key={item.id}>
                      {item.name}: 能力
                      {`${item.abilityScore}`}, 兴趣 {item.interestScore}
                    </li>
                )}
              </ul>
            </div>
            <div className={`${styles.subjectPerformance} ${printMode ? 'typePrint' : ''}`}>
              <h3>您的各学科表现为：</h3>
              <ul>
                {isSubjectShow &&
                  subjectShowList.length > 0 &&
                  subjectShowList.map((v: subjectPerformanceItem, index: number) => {
                    return (
                      <li key={index}>
                        <p className={styles.title}>{v.name}</p>
                        <p className={styles.subjectName}>
                          {v.list.length > 0
                            ? v.list.map((name: string, index1: number) => {
                              if (index1 === v.list.length - 1) {
                                return <span key={index1}>{name}</span>;
                              } else {
                                return <span key={index1}>{name}、</span>;
                              }
                            })
                            : '-'}
                        </p>
                      </li>
                    );
                  })}
              </ul>
            </div>
            <EModuleSubtitle title="各学科潜能分析" themeColor="#00c865" />
            {potentialAnalysis?.subjectPotentials?.map((item: subjectPotentialsItem) =>
              <ECard
                key={item.subjectCode}
                wrapperstyle={{
                  margin: '30px 0 10px',
                  boxShadow: '0px 0px 20px 0px rgb(0 200 101 / 25%)',
                }}
              >
                <div className={styles.potentialAnalysisDetails}>
                  <div className={styles.potentialAnalysisTitle}>
                    {item.subjectName}
                    <span className={styles.rightBox}>
                      <span className={styles.score}>
                        {item.averageScore}
                      </span>{' '}
                      / {item.totalScore}
                    </span>
                  </div>
                  <div className={styles.analysisBox}>
                    <h3>整体情况</h3>
                    <ul>
                      <li>{item.overallSituation}</li>
                    </ul>
                    <h3>学科兴趣</h3>
                    <ul>
                      <li>{item.subjectInterest}</li>
                    </ul>
                    <h3>学科能力</h3>
                    <ul>
                      <li>{item.subjectAbility}</li>
                    </ul>
                  </div>
                </div>
              </ECard>
            )}
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[1].child?.[2].point}>
            <EModuleTitle
              title="03 职业兴趣分析"
              wrapperStyle={{
                backgroundColor: 'rgba(255, 141, 0, 0.1)',
                color: '#ff8d00',
              }}
              wrapperClass={printMode ? 'typePrint' : ''}
            />
            <div className={styles.textBox}>
              <p>
                根据你的作答结果，你的职业兴趣倾向表现如下。注意，一般情况下，职业兴趣倾向在较长一段时间是较为稳定的，但对于青少年来说可能会随时间、教育和环境的影响，出现一些变化，建议用动态的眼光看待本测评结果。
              </p>
            </div>
            { profileHollandCareerInterestsList.length &&
              <ChartForRadar
                id='personReport-hollandCareer-ChartForRader'
                list={profileHollandCareerInterestsList}
                height={320}
                areaColor='#FF9631'
                title='霍兰德职业兴趣剖析图'
              />
            }
            <EModuleSubtitle title="你较为突出职业兴趣倾向：" themeColor="#ff8d00" />
            <div className={styles.careerTendencies}>
              {hollandAnalysis?.hollandRecommendeds?.map(
                (v: hollandRecommendedsItem, index: number) =>
                  <span style={{ paddingRight: '10px' }} key={index}>
                    {v.hollandName}({v.hollandCode})
                  </span>
              )}
            </div>
            {hollandCareerRecommendedsList &&
              hollandCareerRecommendedsList.length > 0
              ? hollandCareerRecommendedsList.map((v: careerRecommendedsProps, index: number) => {
                return (
                  <ECard
                    key={index}
                    wrapperstyle={{
                      margin: '40px 10px 20px',
                      boxShadow: '0px 0px 20px 0px rgb(255 141 0 / 25%)',
                    }}
                  >
                    <div className={styles.careerAnalysisDetails}>
                      <div className={styles.careerAnalysisTitle}>
                        {v.hollandName}({v.type})
                      </div>
                      <div className={styles.analysisBox}>
                        <h3>职业方向</h3>
                        <ul>
                          <li>{v.careerDirection}</li>
                        </ul>
                        <h3>能力特点</h3>
                        <ul>
                          {v.capabilityFeatures.map((item: { name: string }, index: number) => {
                            return <li key={index}>{item.name}</li>;
                          })}
                        </ul>
                        <h3>性格特点</h3>
                        <ul>
                          {v.characterFeatures.map((item: { name: string }, index: number) => {
                            return <li key={index}>{item.name}</li>;
                          })}
                        </ul>
                        <h3>
                          专业推荐
                          <span className={styles.tips}>(点击跳转)</span>
                        </h3>
                        <ul>
                          <li>
                            {v.specialtyRecommend.map((item: recommendedsItem, index: number) => {
                              return (
                                <a
                                  key={index}
                                  href={`${web}/career/careerlibs/index.html#/profession/detail/${item.id}`}
                                  target="_blank"
                                >
                                  {item.name}
                                </a>
                              );
                            })}
                          </li>
                        </ul>
                        <h3>
                          职业推荐
                        </h3>
                        <ul>
                          <li>{v.professionRecommend}</li>
                        </ul>
                      </div>
                    </div>
                  </ECard>
                );
              })
              : null}
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 60 }} id={sideNavList[2].point}>
            <ECompoundTitle
              title="总结和建议"
              decription="Summary and suggestion"
            />
            <div className={styles.textBox}>
              <p>
                本测评在开发前期，通过收集大量新高考地区学生的选科数据，建立了如下科学的选科因素结构模型。该模型认为，科学选科需要考虑两方面的因素，一是学科潜能，二是未来职业发展方向，只有结合两者才能作出更科学的选科决策。
              </p>
              <p style={{ margin: 20 }}>
                <img src={reportImg} width="920" height="368" alt="科学选科示意图" />
              </p>
              <p>
                1.
                我们衷心地希望本测评能为你的选科提供科学的参考依据，帮助你全面客观地认识自己，让你知道自己喜欢做什么、适合做什么和擅长做什么，做到有的放矢的选科。
              </p>
              <p>
                2.
                在完成测评的过程中，你的选择可能会受到环境和情绪的影响，希望你在参考本报告的同时也可以结合自己的实际生活场景。
              </p>
              <p>
                3.
                青少年是处在自我同一性建立的关键时期，所以你的兴趣可能还未完全分化，本测评力求帮你挖掘你的发展优势，但是绝不代表你的最终成就。
              </p>
              <p>
                4.
                最后，再次提醒你，科学的选科除了需要进行深入的自我剖析外，还需要参考老师、家长的意见，本报告不是你选科的唯一参考依据。
              </p>
            </div>
          </ECard>
        </div>
      )}
      {!printMode &&
        <SideNavigation list={sideNavList} />
      }
      <EBanner imgSrc={footerImg} height={90} />
    </div>
  )
}

export default observer(Page);
