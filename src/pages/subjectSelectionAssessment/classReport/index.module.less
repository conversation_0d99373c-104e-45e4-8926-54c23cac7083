.vitaReportPage {
  position: relative;
  padding: 0;
  margin: 0;
  background-color: #fff;
  color: #001139;
  .blueColor {
    color: #4b80ff;
  }
  h3 {
    font-size: 16px;
    font-weight: bold;
  }
  .container {
    width: 1000px;
    box-sizing: border-box;
    margin: -160px auto 0;
    .flag {
      position: relative;
      width: 150px;
      line-height: 44px;
      background-color: #fa8c16;
      border-radius: 10px;
      text-align: center;
      color: #fff;
      font-size: 18px;
      margin-bottom: 30px;
    }
    .overView {
      li {
        display: inline-block;
        min-width: 25%;
        font-size: 14px;
        line-height: 36px;
        b {
          padding-right: 6px;
        }
      }
    }
    .textBox {
      margin-bottom: 20px;
      p {
        line-height: 24px;
        span {
          padding-right: 0;
        }
      }
      .subheading {
        padding-top: 20px;
        font-weight: 700;
        font-size: 16px;
        color: #427aff;
      }
      .subheading::before {
        content: '★';
        display: inline-block;
        padding-right: 6px;
      }
      ol,
      ul {
        padding: 10px 20px;
        li {
          padding-top: 20px;
          color: rgba(0, 17, 57, 0.85);
          font-size: 16px;
          list-style: disc;
          line-height: 22px;
        }
      }
    }

    .subjectDetailBox {
      height: 600px;
      .combinationList {
        background: #f2f6ff;
        border-radius: 8px;
        height: 570px;
        overflow-y: scroll;
        ul {
          list-style: none;
          padding: 10px;
          li {
            line-height: 44px;
            border-radius: 8px;
            margin: 10px auto 0;
            padding: 0 10px;
            box-shadow: 0px 0px 10px 0px rgb(66 122 255 / 25%);
            background:#fff;
            font-size: 16px;
            cursor: pointer;
            .combinationName {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .percentage {
              float: right;
              vertical-align: top;
            }
          }
          .selected {
            background: #4b80ff;
            color: #fff;
          }
          .disabled {
            background: #cbd2e1;
            color: #fff;
          }
        }
      }
      .tableSearchBox {
        font-size: 16px;
        padding-bottom: 20px;
        .searchTitle {
          line-height: 40px;
        }
        .searchTitle:before {
          display: inline-block;
          margin:0 4px 3px 0;
          vertical-align: middle;
          content: ' ';
          width: 4px;
          height: 16px;
          background: #4b80ff;
        }
        .searchFitler {
          display: inline-block;
          margin:0 20px;
          .fitlerInput {
            width: 180px;
            border-radius: 4px;
            margin-left: 10px;
          }
        }
        .optionBox {
          display: inline-block;
          text-align: right;
          float: right;
          .blue {
            line-height: 32px;
            font-size: 18px;
            padding:0 15px;
            border-color: #4b80ff;
            background:#4b80ff;
            border-radius: 4px;
            margin-left: 20px;
          }
        }
      }
      .potentialTable {
        tr > th {
          padding: 8px 10px;
          background: #4b80ff;
          text-align: center;
          color: #fff;
        }
        .potentialTd {
          text-align: center;
          td {
            padding: 12px 10px;
          }
        }
        .potentialTd:nth-child(even),
        .potentialTd:hover{
          background: #edf3ff;
          border-bottom: 1px solid #e8e8e8;
        }
        :global {
          .ant-table-pagination {
            .ant-pagination-item-active,
            li:hover {
              border-color: #4b80ff;
              background: #4b80ff;
              color: #fff;
            }
            .ant-pagination-item a:hover {
              text-decoration: none
            }
            .ant-pagination-item:focus,
            .ant-pagination-item:hover,
            .ant-pagination-next:hover .ant-pagination-item-link {
              border-color: #4b80ff;
            }
            .ant-pagination-item-active a,
            .ant-pagination-item:focus a,
            .ant-pagination-item:hover a {
              color: #fff
            }
          }
        }
      }
    }
    .classGradePotentialComparisonTable {
      display: block;
      margin-bottom: 40px;
      ul {
        margin:20px 0;
        padding:0;
        li {
          display: flex;
          flex-flow: row nowrap;
          justify-content: flex-start;
          height: 40px;
          line-height: 40px;
          padding:0;
          &:nth-child(odd) {
            background: rgba(0, 200, 101, 0.1);
          }
          span {
            &:first-child {
              font-weight: 700;
              width: 100px;
              margin: 0 40px;
              padding: 0;
            }
          }
        }
        .td,
        .th {
          span {
            display: block;
            width: 90px;
            text-align: center;
            color: rgba(0,17,57,0.65);
          }
        }
        .th {
          span {
            font-weight: 700;
            color:#00C865;
          }
        }
      }
      .explain {
        text-align: center;
        color:rgba(0, 17, 57, 0.65)
      }
    }

    .tableBox {
      margin: 20px 10px;
      .explain {
        padding-bottom:20px;
        font-size: 13px;
        text-align: center;
      }
      .tableSearchBox {
        font-size: 16px;
        padding:20px 0;
        .searchTitle {
          line-height: 40px;
        }
        .searchTitle:before {
          display: inline-block;
          margin:0 4px 3px 0;
          vertical-align: middle;
          content: ' ';
          width: 4px;
          height: 16px;
          background: #00c865;
        }
        .searchFitler {
          display: inline-block;
          margin:0 20px;
          .fitlerInput {
            width: 180px;
            border-radius: 4px;
            margin-left: 10px;
          }
        }
        .optionBox {
          display: inline-block;
          text-align: right;
          float: right;
          .green {
            line-height: 32px;
            font-size: 18px;
            padding:0 15px;
            border-color: #00c865;
            background:#00c865;
            border-radius: 4px;
            margin-left: 20px;
          }
        }
      }
      .potentialTable {
        tr > th {
          padding: 8px 10px;
          background: rgba(0, 200, 101, 0.1);
          text-align: center;
        }
        .potentialTd {
          padding: 16px 10px;
          text-align: center;
        }
        .potentialTd:nth-child(even),
        .potentialTd:hover{
          background: #edf3ff;
          border-bottom: 1px solid #e8e8e8;
        }
        :global {
          .ant-table-pagination {
            .ant-pagination-item-active,
            li:hover {
              border-color: #38d486;
              background: #38d486;
              color: #fff;
            }
            .ant-pagination-item a:hover {
              text-decoration: none
            }
            .ant-pagination-item:focus,
            .ant-pagination-item:hover,
            .ant-pagination-next:hover .ant-pagination-item-link {
              border-color: #38d486;
            }
            .ant-pagination-item-active a,
            .ant-pagination-item:focus a,
            .ant-pagination-item:hover a {
              color: #fff
            }
          }
        }
      }
    }
    .emptyData {
      margin: 20px auto;
      text-align: center;
      p {
        padding: 0;
        font-size: 14px;
        color: rgba(0, 17, 57, 0.65);
      }
    }

    .tableBox {
      margin: 20px 10px;
      .explain {
        padding-bottom:20px;
        font-size: 13px;
        text-align: center;
      }
      .tableSearchBox {
        font-size: 16px;
        padding:20px 0;
        .searchTitle {
          line-height: 40px;
        }
        .searchTitle:before {
          display: inline-block;
          margin:0 4px 3px 0;
          vertical-align: middle;
          content: ' ';
          width: 4px;
          height: 16px;
          background: #ff8d00;
        }
        .searchFitler {
          display: inline-block;
          margin:0 20px;
          .fitlerInput {
            width: 180px;
            border-radius: 4px;
            margin-left: 10px;
          }
        }
        .optionBox {
          display: inline-block;
          text-align: right;
          float: right;
          .orange {
            line-height: 32px;
            font-size: 18px;
            padding:0 15px;
            border-color: #ff8d00;
            background:#ff8d00;
            border-radius: 4px;
            margin-left: 20px;
          }
        }
      }
      .hollandTable {
        tr > th {
          padding: 8px 10px;
          background:rgba(255, 141, 0, 0.1);
          text-align: center;
        }
        .hollandTd {
          padding: 6px 10px;
          text-align: center;
        }
        .hollandTd:nth-child(even),
        .hollandTd:hover,
        .ant-table-row:hover,
        .ant-table-cell-row-hover {
          background: rgba(255, 141, 0, 0.1);
          border-bottom: 1px solid #e8e8e8;
        }
        :global {
          .ant-table-pagination {
            .ant-pagination-item-active,
            li:hover {
              border-color: #ff8d00;
              background: #ff8d00;
              color: #fff;
            }
            .ant-pagination-item a:hover {
              text-decoration: none
            }
            .ant-pagination-item:focus,
            .ant-pagination-item:hover,
            .ant-pagination-next:hover .ant-pagination-item-link {
              border-color: #ff8d00;
            }
            .ant-pagination-item-active a,
            .ant-pagination-item:focus a,
            .ant-pagination-item:hover a {
              color: #fff
            }
          }
        }
      }
    }

    .careerAnalysisDetails {
      .analysisBox {
        h3 {
          padding-top: 20px;
          font-weight: 700;
          font-size: 16px;
          color: #ff8d00;
        }
        ol,
        ul {
          padding: 10px 20px;
          li {
            padding-top: 20px;
            color: rgba(0, 17, 57, 0.85);
            font-size: 16px;
            list-style: disc;
          }
        }
      }
    }
    .analysisTitle {
      line-height: 44px;
      background: linear-gradient(270deg, #ffffff, #ff8d00);
      border-radius: 8px;
      font-size: 16px;
      font-weight: 700;
      color: #fff;
      padding: 0 20px;
      .rightBox {
        float: right;
        color: #475370;
        font-size: 16px;
        font-weight: 700;
        .score {
          color: #ff8d00;
        }
      }
    }
    .careerTendencies {
      margin: 30px 10px 30px 10px;
      font-size: 16px;
    }
  }
}
