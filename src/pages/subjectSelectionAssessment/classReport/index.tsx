import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Button, Col, Empty, Input, InputRef, message, Row, Table } from 'antd';
import { VerticalAlignBottomOutlined } from '@ant-design/icons';
import { classReportResponse, ColumnsDataType, dataAnalysisItem, percentageAnalysisItem, potentialItem, subjectDetailItem } from './types';
import EBanner from '~/components/CommonBanner';
import ECard from '@/components/ECard';
import ECompoundTitle from '@/components/ECompoundTitle';
import EModuleTitle from '@/components/EModuleTitle';
import SideNavigation from '@/components/SideNavigation';
import EModuleSubtitle from '~/components/EModuleSubtitle';
import ChartForPie from '~/components/ChartForPie';
import ChartForRadar from '~/components/ChartForRadar';
import bannerImg from '@/assets/GroupReport/banner.png';
import reportImg from '@/assets/GroupReport/report.png';
import footerImg from '@/assets/GroupReport/footerClassBanner.png';
import iconEmpty from '@/assets/subjectSelectionAssessment/emptyData.png';
import styles from './index.module.less';
import API from '~/service/subjectSelectionAssessment/report';
import { classReportParams } from '~/service/subjectSelectionAssessment/types';
import { useSyncState } from '@/utils/hooks';
import { ColumnsType } from 'antd/lib/table';
import { mountPSTScreenshot, objectToUrlParams } from '~/utils/tools';
import { web } from '~/utils/hosts';

export const getServerSideProps: GetServerSideProps<{
  classReportData: any;
}> = async (ctx: any) => {

  const { evaluationTaskId, gradeId, classId, ff, isPrint } = ctx.query as classReportParams;
  const printMode = !!(ff || isPrint);
  
  try {
    const dataRes = await API.getClassReport(
      { evaluationTaskId, gradeId, classId },
      ctx
    ); 
    const classSubjectCombinationRes = await API.getListClassSubjectCombination(
      { evaluationTaskId, classId },
      ctx
    );
    console.log('getListClassSubjectCombination', classSubjectCombinationRes)
    return {
      props: {
        classReportData: {
          ...dataRes,
          classSubjectCombinationRes,
          query: {
            evaluationTaskId,
            gradeId,
            classId
          },
          printMode
        }
      }
    };
  } catch (error: any) {
    console.log('ClassReportModule', error)
    const { success, code, msg } = error;
    return {
      props: {
        classReportData: {
          data: {},
          classSubjectCombinationRes: {
            success: false,
            msg: '接口异常'
          },
          query: { evaluationTaskId, gradeId, classId },
          success,
          code,
          msg,
          printMode
        }
      }
    };
  }
};

function Page({
  classReportData
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
  const sideNavList = [
    { label: "1、报告导读", point: "reportReading" },
    { label: "2、测评说明", point: "resultAnalyseIn" },
    {
      label: "3、结果分析", point: "resultAnalyse",
      child: [{ label: "3.1 综合分析", point: "synthesis" }, { label: "3.2 学科潜能分析", point: "potency" }, { label: "3.3 职业兴趣分析", point: "interest" }]
    },
    { label: "4、报告结束语", point: "summarize" }
  ]
  const classCareerInterestSummaryList = [
    { code: 'R', name: '实际型', typeCharacteristics: '偏好于具体任务，喜欢使用工具、机器，需要基本操作技能的工作。不善言辞，做事保守，较为谦虚。缺乏社交能力，通常喜欢独立做事。', suggestion: '性格代码为R的学生非常愿意且擅长使用工具从事操作性工作，例如电教委员，劳动委员等，负责班级的投影仪的使用及劳动工具的维护等。'},
    { code: 'C', name: '常规型', typeCharacteristics: '喜欢关注实际和细节情况，通常较为谨慎和保守，尊重权威和规章制度，喜欢按计划办事，缺乏创造性，不喜欢冒险和竞争，富有自我牺牲精神。', suggestion: '性格代码为C的学生做事情注重细节，精确有度，有系统有条理。在班级管理中，非常适合各科学习课代表，收发作业，代替老师布置作业等工作往往能做到让老师非常放心。'},
    { code: 'E', name: '企业型', typeCharacteristics: '喜欢竞争、敢冒风险、有野心、抱负。为人务实，习惯以利益得失，权利、地位、金钱等来衡量做事的价值，做事有较强的目的性。', suggestion: '性格代码为E的学生，一般都有较好的经营、管理、劝服、监督和领导才能，在班级中，非常适合班长这样的职务，会是协助班主任老师完成班级管理的一把好手。'},
    { code: 'S', name: '社会型', typeCharacteristics: '能够不断结交新的朋友，从事提供信息、启迪、帮助、培训、开发或治疗等事务，并具备相应能力。', suggestion: '性格代码为S的学生，喜欢要求与人打交道，并且非常愿意关注同学的身心健康。在班级中，像生活委员，心理委员等这样的职位比较适合交给他们。'},
    { code: 'A', name: '艺术型', typeCharacteristics: '有创造力，乐于创造新颖、与众不同的成果，渴望表现自己的个性，实现自身的价值，并且致力于自我表达。做事理想化，追求完美，不重实际。', suggestion: '性格代码为A的学生喜欢的工作要求具备艺术修养、创造力、表达能力。在班级中，推荐文艺委员、宣传委员类职务，负责班级板报设计，宣传类工作。'},
    { code: 'I', name: '研究型', typeCharacteristics: '抽象思维能力强，求知欲强，知识渊博，肯动脑，善思考，喜欢独立工作，不善于领导他人。考虑问题理性，做事喜欢精确，喜欢逻辑分析和推理，不断探讨未知的领域。', suggestion: '性格代码为I的学生喜欢独立的和富有创造性的工作。这部分学生往往成绩较为优异，可以尝试学习委员等职务。'},
  ]
  const [subjectCombinationIndex, setSubjectCombinationIndex] = useSyncState(-1)
  const [classGradeComparison, setClassGradeComparison] = useState<any>({ above: [], par: [], below: []})
  const [raderCustomOptions, setRaderCustomOptions] = useState<any>();
  const [hollandCareerInterestsList, setHollandCareerInterestsList] = useState<percentageAnalysisItem[]>([]) // 霍兰德职业兴趣剖析图
  const refStudentNameCombination = React.useRef<InputRef>(null);
  const refStudentName = React.useRef<InputRef>(null);
  const refStudentNameHolland = React.useRef<InputRef>(null);
  const [pageClassSubjectCombination, setPageClassSubjectCombination] = useSyncState<any>({ userName: '', combinationCode: '', list: [], pageIndex: 1, pageSize: 10, total: 0 })
  const [pageClassPotentialAnalysis, setPageClassPotentialAnalysis] = useSyncState<any>({ userName: '',list: [], pageIndex: 1, pageSize: 10, total: 0 })
  const [pageClassHolland, setPageClassHolland] = useSyncState<any>({ userName: '',list: [], pageIndex: 1, pageSize: 10, total: 0 })

  const {
    data: {
      className,
      gradeName,
      schoolName,
      areaName,
      reportTime,
      reportCode,
      loginName,
      startTime,
      endTime,
      evaluatedPeople,
      totalPeople,
      hollandAnalysis,
      potentialAnalysis
    },
    classSubjectCombinationRes,
    success,
    msg,
    query,
    printMode
  } = classReportData;

  const subjectCombinationColumns: ColumnsType<ColumnsDataType> = [
    { title: '姓名', dataIndex: 'userName'},
    { title: '组合推荐1', dataIndex: 'subjectOneName'},
    { title: '组合推荐2', dataIndex: 'subjectTwoName' },
    { title: '组合推荐3', dataIndex: 'subjectThreeName' },
    { title: '详情',dataIndex: 'evaluationRecordId', render:(id: string) => <a href={`${location.origin}/psychology-service/subjectSelectionAssessment/personalReport?evaluationRecordId=${id}`} target="_blank" style={{ color: 'rgb(250, 140, 22)', textDecoration: 'underline'}} >个人报告</a> }
  ]
  const classPotentialColumns: ColumnsType<ColumnsDataType> = [
    { title: '姓名', dataIndex: 'userName', align: 'center' },
    { title: '物理', dataIndex: 'physicalPotential', align: 'center' },
    { title: '化学', dataIndex: 'chemistryPotential', align: 'center' },
    { title: '生物', dataIndex: 'biologicalPotential', align: 'center' },
    { title: '政治', dataIndex: 'politicalPotential', align: 'center' },
    { title: '历史', dataIndex: 'historyPotential', align: 'center' },
    { title: '地理', dataIndex: 'geographyPotential', align: 'center' },
    { title: '技术', dataIndex: 'technologyPotential', align: 'center' },
    { title: '详情',dataIndex: 'evaluationRecordId', align: 'center', render:(id: string) => <a href={`${location.origin}/psychology-service/subjectSelectionAssessment/personalReport?evaluationRecordId=${id}`} target="_blank" style={{ color: 'rgb(250, 140, 22)', textDecoration: 'underline'}} >个人报告</a> }
  ]

  const classHollandColumns: ColumnsType<ColumnsDataType> = [
    { title: '姓名', dataIndex: 'userName', align: 'center' },
    { title: '主导兴趣倾向', dataIndex: 'hollandOneName', align: 'center' },
    { title: '次级兴趣倾向1', dataIndex: 'hollandTwoName', align: 'center' },
    { title: '次级兴趣倾向2', dataIndex: 'hollandThreeName', align: 'center' },
    { title: '详情',dataIndex: 'evaluationRecordId', align: 'center', render:(id: string) => <a href={`${location.origin}/psychology-service/subjectSelectionAssessment/personalReport?evaluationRecordId=${id}`} target="_blank" style={{ color: 'rgb(250, 140, 22)', textDecoration: 'underline'}} >个人报告</a> }
  ]

  // 初始化
  const initial = () => {
    if (!success || !classSubjectCombinationRes.success) {
      message.error(msg || classSubjectCombinationRes.msg)
    }

    // 霍兰德职业兴趣剖析图
    const hollandCareerInterestsList: any[] = [];
    hollandAnalysis?.dataAnalysis?.forEach((item: dataAnalysisItem) => hollandCareerInterestsList.push({
      ...item, name: `${item.hollandName}\n${item.percentage}%`
    }))

    // 本班各学科潜能与年级情况对比
    const above: string[] = [];
    const par: string[] = [];
    const below: string[] = [];
    const dataname: { name: string; max: number; id: string; }[] = [];
    const datavaule: any[] = [];
    const datamax: any[] = [];
    potentialAnalysis?.potential?.forEach((item: potentialItem) => {
      if (item.classAvgScore > item.gradeAvgScore) {
        above.push(item.name);
      }
      if (item.classAvgScore === item.gradeAvgScore) {
        par.push(item.name);
      }
      if (item.classAvgScore < item.gradeAvgScore) {
        below.push(item.name);
      }
      dataname.push({ name: item.name, max: 5, id: item.id });
      datavaule.push(item.classAvgScore);
      datamax.push(item.gradeAvgScore);
    });

    // 雷达图
    const radarCustomOption = {
      legend: {
        bottom: 0,
        data: ["本班平均水平", "年级平均水平"],
      },
      radar: [
        {},
        {
          indicator: dataname,
          center: ["50%", "46%"],
          radius: 120,
          axisName: {
            color: "#001139",
          },
          splitArea: {
            color: ["#F2F6FF", "#fff"].reverse(),
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(160,188,255,1)",
            },
          },
        },
      ],
      series: [
        {
          name: "成绩单",
          type: "radar",
          radarIndex: 1,
          symbol: "none",
          data: [
            {
              type: "radar",
              value: datavaule,
              name: "本班平均水平",
              itemStyle: {
                color: "#00C865",
              },
              areaStyle: {
                color: "#00C865",
              },
            },
            {
              value: datamax,
              type: "radar",
              name: "年级平均水平",
              itemStyle: {
                color: "rgba(250,140,22,1)",
              },
            },
          ],
        },
      ],
    };
    setRaderCustomOptions(radarCustomOption);
    setClassGradeComparison({ above, par, below })
    setHollandCareerInterestsList(hollandCareerInterestsList)
    getClassSubjectCombination()
    getPageClassPotentialAnalysis()
    getPageClassHolland()

    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: 3000
      })
    }
  }

   // 本班科目组合汇总
   const getClassSubjectCombination = async () => {
    try {
      const { userName, combinationCode, pageIndex, pageSize } = pageClassSubjectCombination.current;
      const { data: { records, total, current, size } } = await API.getClassSubjectCombination({
        ...query,
        userName,
        combinationCode,
        pageIndex,
        pageSize,
      })
      setPageClassSubjectCombination({
        pageIndex: current,
        pageSize: size,
        list: records,
        userName,
        combinationCode,
        total
      })
    } catch (e: any) {
      message.error('getClassSubjectCombination', e)
    }
  }

  // 选择科目组合
  const handleSelectSubjectCombination = (item: subjectDetailItem, index: number) => {
    setSubjectCombinationIndex(index)
    if (index === -1) {
      setPageClassSubjectCombination({
        ...pageClassSubjectCombination.current,
        combinationCode: '',
        pageIndex: 1,
      })
      getClassSubjectCombination();
      return
    }
    if (item.select) {
      const current = classSubjectCombinationRes?.data?.subjectDetail?.[index];
      setPageClassSubjectCombination({
        ...pageClassSubjectCombination.current,
        combinationCode: current?.combinationCode,
        pageIndex: 1,
      })
      getClassSubjectCombination();
    }
  }

  // 查询学生
  const handleSearchStudentCombination = () => {
    const userName = refStudentNameCombination?.current?.input?.value || '';
    setPageClassSubjectCombination({
      ...pageClassPotentialAnalysis.current,
      list: [],
      pageIndex: 1,
      userName
    })
    getClassSubjectCombination()
  }

  const combinationPaginationPage = async (pageIndex: number) => {
    setPageClassSubjectCombination({
      ...pageClassSubjectCombination.current,
      pageIndex
    })
    getClassSubjectCombination()
  }

  const combinationPagination = {
    current: pageClassSubjectCombination.current.pageIndex,
    pageSize:pageClassSubjectCombination.current.pageSize,
    total: pageClassSubjectCombination.current.total,
    onChange: combinationPaginationPage,
  }

  // 本班学生学科潜能分析汇总
  const getPageClassPotentialAnalysis = async () => {
    try {
      const { userName, pageIndex, pageSize } = pageClassPotentialAnalysis.current;
      const { data: { records, total, current, size } } = await API.getPageClassPotentialAnalysis({
        ...query,
        pageIndex,
        pageSize,
        userName
      })
      setPageClassPotentialAnalysis({
        pageIndex: current,
        pageSize: size,
        list: records,
        userName,
        total
      })
    } catch (e: any) {
      message.error('getPageClassPotentialAnalysis', e)
    }
  }

  // 本班职业兴趣类型汇总
  const getPageClassHolland = async () => {
    try {
      const { userName, pageIndex, pageSize } = pageClassHolland.current;
      const { data: { records, total, current, size } } = await API.getPageClassHolland({
        ...query,
        pageIndex,
        pageSize,
        userName
      })
      setPageClassHolland({
        pageIndex: current,
        pageSize: size,
        list: records,
        userName,
        total
      })
    } catch (e: any) {
      message.error('getPageClassHolland', e)
    }
  }

  // 查询学生
  const handleSearchStudent = () => {
    const userName = refStudentName?.current?.input?.value || '';
    setPageClassPotentialAnalysis({
      ...pageClassPotentialAnalysis.current,
      pageIndex: 1,
      userName
    })
    getPageClassPotentialAnalysis()
  }

  const classPotentialPaginationPage = async (pageIndex: number) => {
    setPageClassPotentialAnalysis({
      ...pageClassPotentialAnalysis.current,
      pageIndex
    })
    getPageClassPotentialAnalysis()
  }

  const classPotentialPagination = {
    current: pageClassPotentialAnalysis.current.pageIndex,
    pageSize:pageClassPotentialAnalysis.current.pageSize,
    total: pageClassPotentialAnalysis.current.total,
    onChange: classPotentialPaginationPage,
  }

  // 查询学生 -- 职业兴趣
  const handleSearchHollandStudent = () => {
    const userName = refStudentNameHolland?.current?.input?.value || '';
    setPageClassHolland({
      ...pageClassHolland.current,
      pageIndex: 1,
      userName
    })
    getPageClassHolland()
  }

  const classHollandPaginationPage = (pageIndex: number) => {
    setPageClassHolland({
      ...pageClassHolland.current,
      pageIndex
    })
    getPageClassHolland()
  }

  const classHollandPagination = {
    current: pageClassHolland.current.pageIndex,
    pageSize:pageClassHolland.current.pageSize,
    total: pageClassHolland.current.total,
    onChange: classHollandPaginationPage,
  }

  // 科目组合
  const handleDownLoadCombinationExcel = () => {
    const { evaluationTaskId, classId } = query;
    const { userName } = pageClassSubjectCombination.current;
    // 需求：下载全部
    const paramsStr = objectToUrlParams({
      evaluationTaskId, classId,
      combinationCode: '',
      userName
    })
    window.open(`${web}/api/psychology/evaluationReport/exportClassSubjectCombination?${paramsStr}`);
  }

  // 下载学生表格
  const handleDownLoadStudentExcel = () => {
    const paramsStr = objectToUrlParams({
      ...query,
      userName: pageClassPotentialAnalysis.current.userName
    })
    window.open(`${web}/api/psychology/evaluationReport/exportClassPotentialAnalysis?${paramsStr}`);
  }

  // 下载学生表格 -- 霍兰德
  const handleDownLoadStudentHollandExcel = () => {
    const paramsStr = objectToUrlParams({
      ...query,
      userName: pageClassHolland.current.userName
    })
    window.open(`${web}/api/psychology/evaluationReport/exportClassHolland?${paramsStr}`);
  }

  useEffect(() => {
    initial()
  }, [])
  return (
    <div className={styles.vitaReportPage}>
      <EBanner imgSrc={bannerImg} height={470} />
      {Object.keys(classReportData).length > 0 && Object.keys(classReportData.data).length > 0 ?
        <div className={styles.container}>
          <div className={styles.flag}>班级分析报告</div>
          <ECard
            wrapperstyle={{
              position: 'relative',
              marginBottom: 40,
              background: '#fff',
              boxShadow: '0px 0px 20px 0px rgba(66,122,255,0.25)',
              padding: 24,
              borderRadius: 20,
            }}
          >
            <div className={styles.overView}>
              <li>
                <b>班级:</b> {className}
              </li>
              <li>
                <b>班级:</b> {gradeName}
              </li>
              <li>
                <b>学校:</b> {schoolName}
              </li>
              <li>
                <b>地区:</b> {areaName}
              </li>
              <li>
                <b>报告日期:</b>
                {moment(reportTime).format('YYYY-MM-DD')}
              </li>
              <li>
                <b>报告编码:</b> {reportCode}
              </li>
            </div>
          </ECard>
          <ECompoundTitle title="报告导读" decription="Introduction" id={sideNavList[0].point} />
          <ECard wrapperstyle={{ marginBottom: 30, padding: '40px' }}>
            <h3>
              亲爱的
              <span className={styles.blueColor}>{loginName}</span>老师
            </h3>
            <p>
              您好，非常感谢您使用升学e网通新高考高中生选科测评，以下是我们根据贵校学校的学生的作答结果而提供的选科指导分析报告。
            </p>
            <p>
              您可以把本报告作为您对学生的选科指导的参考，或者作为学校开设相应组合的参考，但不建议您把它作为唯一依据。
              在阅读本报告前，请注意以下几点：
            </p>
            <p>
              1.本报告涉及学生的个人隐私，请严格保管此报告，不给无关人员查阅，不在公共场合讨论报告内容。
            </p>
            <p>2.请注意本报告的有效参考期限：</p>
            <p>
              2个月以内：可以参考
              <span />
              2~6个月：谨慎参考
              <span />
              6个月以上：不建议参考;
            </p>
            <p>
              3.测评结果通常具有一定误差，请结合测评结果与学生的实际表现进行判断，切勿单纯以测评结果给学生贴标签。
            </p>
          </ECard>
          <ECompoundTitle title="测评说明" decription="Specification" id={sideNavList[1].point} />
          <ECard wrapperstyle={{ marginBottom: 60 }}>
            <div className={styles.textBox}>
              <p className={styles.subheading}>测评工具</p>
              <ol>
                <li>
                  高中生选科测评是由杭州铭师堂教育科技有限公司素养成长部生涯规划研究团队联合北京师范大学心理学专家团队共同开发。
                </li>
                <li>
                  开发团队深入剖析新高考选科政策及大量收集新高考地区学生的选科数据，建立了科学的选科因素结构模型。模型认为最佳的选科决策需要综合考虑学科潜能和未来发展方向。
                </li>
                <li>
                  本次测评我们主要从学生的学科兴趣、学习表现、学科成绩及学科效能感等多方面评估学生在各门学科上的学科潜能，同时通过霍兰德职业兴趣测试评估学生的未来专业职业发展方向，力求能为您在对学生选科指导时提供最有价值的信息。
                </li>
                <li>
                  所有涉及学科潜能测评的题目均是开发人员深入研究学科核心素养内涵及各学科新课程标准，并辅以对多名一线学科名师深入访谈，确保测评题目能够真实有效的衡量学生的各学科潜能及学生适宜的未来发展方向。
                </li>
              </ol>
              <p className={styles.subheading}>测评时间</p>
              <ol>
                <li>{`${moment(startTime).format(
                  'YYYY-MM-DD HH:mm'
                )} ~ ${moment(endTime).format('YYYY-MM-DD HH:mm')}`}</li>
              </ol>
              <p className={styles.subheading}>参与人数</p>
              <ol>
                <li>
                  已参与/总人数：
                  {`${evaluatedPeople} / ${totalPeople}`}
                  （人）
                </li>
              </ol>
            </div>
          </ECard>
          <ECompoundTitle title="结果分析" decription="Analysis" id={sideNavList[2].point} />
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[2].child?.[0].point}>
            <EModuleTitle
              title="01 综合分析"
              wrapperStyle={{
                backgroundColor: 'rgba(75, 128, 255, 0.1)',
                color: '#4B80FF',
                marginBottom:16
              }}
            />
            <div className={styles.textBox}>
              <p>从测评结果来看，覆盖学生数量最多的是{classSubjectCombinationRes?.data?.mostCombinations}，覆盖学生最少的科目组合是{ classSubjectCombinationRes?.data?.leastCombinations}。</p>
              </div>
            <div>
              <Row gutter={24} className={styles.subjectDetailBox}>
                <Col span={6}>
                  <div className={styles.combinationList}>
                    <ul>
                    <li
                      className={`${subjectCombinationIndex.current === -1 && styles.selected }`}
                      onClick={() => handleSelectSubjectCombination({
                        combinationCode: '',
                        combinationCount: '',
                        combinationName: '',
                        percentage: 100,
                        select: true
                      }, -1)}
                      >
                      <span className={styles.combinationName}>全部</span>
                      <span className={styles.percentage}>100%</span>
                    </li>
                  { classSubjectCombinationRes?.data?.subjectDetail?.map((item:subjectDetailItem, index: number) =>
                    <li
                      key={item.combinationCode}
                      className={`${subjectCombinationIndex.current === index && styles.selected} ${!item.select ? styles.disabled : ''}`}
                      onClick={() => handleSelectSubjectCombination(item, index)}
                    >
                      <span className={styles.combinationName}>{item.combinationName}</span>
                      { item.select && (
                        <span className={styles.percentage}>{item.percentage ? `${item.percentage}%` : "0%"}</span>
                      )}
                      </li>)
                    }
                    </ul>
                  </div>
                </Col>
                <Col span={18}>
                <div className={styles.tableSearchBox}>
                  <span className={styles.searchTitle}>{subjectCombinationIndex.current === -1 ? '全部': classSubjectCombinationRes?.data?.subjectDetail[subjectCombinationIndex.current].combinationName}</span>
                  <span className={styles.searchFitler}>
                    学生查询 <Input
                      ref={refStudentNameCombination}
                      className={styles.fitlerInput}
                      placeholder="输入 学生名称 搜索"
                    />
                  </span>
                  <div className={styles.optionBox}>
                    <Button
                      type="primary"
                      className={styles.blue}
                      onClick={() => handleSearchStudentCombination()}
                    >
                      搜索
                    </Button>
                    <Button
                      type="primary"
                      /* @ts-ignore */
                      icon={<VerticalAlignBottomOutlined style={{ fontSize: 20,fontWeight: "bold" }} />}
                      className={styles.blue}
                      onClick={() => handleDownLoadCombinationExcel()}
                    >
                      Excel下载
                    </Button>
                  </div>
                </div>
                { pageClassSubjectCombination.current.list.length > 0 ?
                  <Table
                    rowKey='userName'
                    columns={subjectCombinationColumns}
                    dataSource={pageClassSubjectCombination.current.list}
                    pagination={combinationPagination}
                    className={styles.potentialTable}
                    rowClassName={styles.potentialTd}
                  />: <div className={styles.emptyData}>
                    <img src={iconEmpty} width="100" alt="空数据"/>
                    <p>该组合分类下没有此学生</p>
                  </div>
                }

                </Col>
              </Row>
              <p>*灰色的选科组合是学校预先确定无法开设的科目组合，不在学生的个人报告中呈现。</p>
            </div>
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[2].child?.[1].point}>
            <EModuleTitle
              title="02 学科潜能分析"
              wrapperStyle={{
                backgroundColor: 'rgba(0, 200, 101, 0.1)',
                color: '#00c865',
                marginBottom: 20
              }}
              wrapperClass={`${printMode ? 'typePrint' : ''}`}
            />
            <div className={styles.textBox}>
              <p>该测评主要用于衡量和判断高中生选考科目的学科潜能，为进一步提供选科指导、学科教学和学习指导提供参考依据。每个学科分别从学科核心素养、学科兴趣、学科学习表现、自我效能、学科成绩等方面进行测量，全面的评估各学科的倾向，了解学生学习多方面的表现。</p>
              <EModuleSubtitle title="本班各学科潜能与年级情况对比" themeColor="#00c865" wrapperStyle={{ marginTop: 30 }} />
              <p>从测评结果来看本班学生的
                { classGradeComparison.above.length ?
                  classGradeComparison.above.map((text: string, index: number) =>{
                    if (index + 1 < classGradeComparison.par.length) {
                      return <span key={index}>{text}、</span>;
                    } else {
                      return (
                        <span key={index}>
                          {text} 学科潜能高于全年级平均水平
                          {(!classGradeComparison.par.length && !classGradeComparison.below.length)? "。" : "，"}
                        </span>
                      );
                    }
                  }) : null
                }

                { classGradeComparison.par.length ?
                  classGradeComparison.par.map((text: string, index: number) => {
                    if (index + 1 < classGradeComparison.par.length) {
                      return <span key={index}>{text}、</span>;
                    } else {
                      return (
                        <span key={index}>
                          { text} 学科潜能与全年级平均水平持平
                          {classGradeComparison.below.length === 0 ? "。" : "，"}
                        </span>
                      );
                    }
                  }) : null
                }

                { classGradeComparison.below.length ?
                  classGradeComparison.below.map((text: string, index: number) => {
                    if (index + 1 < classGradeComparison.below.length) {
                      return <span key={index}>{text}、</span>;
                    } else {
                      return (
                        <span key={index}>
                          {text}学科潜能低于年级平均水平。
                        </span>
                      );
                    }
                  }) : null
                }
              </p>

              { raderCustomOptions ?
                <ChartForRadar
                  id='classReport-ChartForRader'
                  height={360}
                  customOption={raderCustomOptions}
                /> : null
              }
              <div className={styles.classGradePotentialComparisonTable}>
                <p>本班各学科潜能与年级对比剖面图</p>
                <ul>
                  <li className={styles.th}>
                    <span />
                    { potentialAnalysis?.potential?.map((item: potentialItem) => <span key={item.id}>{item.name}</span>) }
                  </li>
                  <li className={styles.td}>
                    <span>本班平均</span>
                    { potentialAnalysis?.potential?.map((item: potentialItem) => <span key={item.id}>{item.classAvgScore}</span>) }
                  </li>
                  <li className={styles.td}>
                    <span>年级平均</span>
                  { potentialAnalysis?.potential?.map((item: potentialItem) => <span key={item.id}>{item.gradeAvgScore}</span>) }
                  </li>
                </ul>
                <p className={styles.explain}>*本年级已有{ potentialAnalysis?.gradeEvaluatedPeople}人参加本测评。</p>
              </div>
              </div>
              <EModuleSubtitle title="本班学生学科潜能分析汇总" themeColor="#00c865" />
              <div className={styles.tableBox}>
                <div className={styles.tableSearchBox}>
                  <span className={styles.searchTitle}>全部</span>
                  <span className={styles.searchFitler}>
                    学生查询 <Input
                      ref={refStudentName}
                      className={styles.fitlerInput}
                      placeholder="输入 学生名称 搜索"
                    />
                  </span>
                  <div className={styles.optionBox}>
                    <Button
                      type="primary"
                      className={styles.green}
                      onClick={() => handleSearchStudent()}
                    >
                      搜索
                    </Button>
                    <Button
                      type="primary"
                      /* @ts-ignore */
                      icon={<VerticalAlignBottomOutlined style={{ fontSize: 20,fontWeight: "bold" }} />}
                      className={styles.green}
                      onClick={() => handleDownLoadStudentExcel()}
                    >
                      Excel下载
                    </Button>
                  </div>
                </div>
                <p className={styles.explain}>各学科潜能分与年级比较（得分/年级均分）</p>
                { pageClassPotentialAnalysis.current.list.length > 0 ?
                  <Table
                    rowKey='userName'
                    columns={classPotentialColumns}
                    dataSource={pageClassPotentialAnalysis.current.list}
                    pagination={classPotentialPagination}
                    className={styles.potentialTable}
                    rowClassName={styles.potentialTd}
                  />: <div className={styles.emptyData}>
                    <img src={iconEmpty} width="100" alt="空数据"/>
                    <p>该组合分类下没有此学生</p>
                  </div>
                }
            </div>
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 30 }} id={sideNavList[2].child?.[2].point} >
            <EModuleTitle
              title="03 职业兴趣分析"
              wrapperStyle={{
                backgroundColor: 'rgba(255, 141, 0, 0.1)',
                color: '#ff8d00',
              }}
            />
            <div className={styles.textBox}>
              <p>本部分测评主要用于帮助学生探索其未来发展方向，所有的报告内容都是基于学生对测试问题的回答做出。研究表明个体的职业兴趣在较长一段时间是较为稳定的，但是对于青少年来说可能会随时间、教育和环境的影响，出现有一些变化，因此建议用动态的眼光看待测试结果。</p>
              <EModuleSubtitle title="本班职业兴趣类型分布" themeColor="#ff8d00" wrapperStyle={{margin: '30px 0'}} />
              <p>本部分测评主要用于帮助学生探索其未来发展方向，所有的报告内容都是基于学生对测试问题的回答做出。研究表明个体的职业兴趣在较长一段时间是较为稳定的，但是对于青少年来说可能会随时间、教育和环境的影响，出现有一些变化，因此建议用动态的眼光看待测试结果。</p>
              <p>从测评结果来看，本班学生的霍兰德职业兴趣类型丰富多彩，其中{hollandAnalysis?.most}学生占比最多，{hollandAnalysis?.least}学生占比最少。</p>
            </div>
            { hollandCareerInterestsList.length &&
              <ChartForPie
                id='classReport-ChartForPie'
                list={hollandCareerInterestsList}
                height={320}
                title='霍兰德职业兴趣剖析图'
              />
            }
            <EModuleSubtitle title="本班职业兴趣类型汇总" themeColor="#ff8d00"/>
              <div className={styles.tableBox}>
                <div className={styles.tableSearchBox}>
                  <span className={styles.searchTitle}>学生查询</span>
                  <span className={styles.searchFitler}>
                    <Input
                      ref={refStudentNameHolland}
                      className={styles.fitlerInput}
                      placeholder="输入 学生名称 搜索"
                    />
                  </span>
                  <div className={styles.optionBox}>
                    <Button
                      type="primary"
                      className={styles.orange}
                      onClick={() => handleSearchHollandStudent()}
                    >
                      搜索
                    </Button>
                    <Button
                      type="primary"
                      /* @ts-ignore */
                      icon={<VerticalAlignBottomOutlined style={{ fontSize: 20,fontWeight: "bold" }} />}
                      className={styles.orange}
                      onClick={() => handleDownLoadStudentHollandExcel()}
                    >
                      Excel下载
                    </Button>
                  </div>
                </div>
                { pageClassHolland.current.list.length > 0 ?
                  <Table
                    rowKey='userName'
                    columns={classHollandColumns}
                    dataSource={pageClassHolland.current.list}
                    pagination={classHollandPagination}
                    className={styles.hollandTable}
                    rowClassName={styles.hollandTd}
                  />: <div className={styles.emptyData}>
                    <img src={iconEmpty} width="100" alt="空数据"/>
                    <p>暂无数据</p>
                  </div>
                }
            </div>
            { classCareerInterestSummaryList.map((item) =>
              <ECard key={item.code} wrapperstyle={{ margin: '40px 10px 20px', boxShadow: '0px 0px 20px 0px rgb(255 141 0 / 25%)' }}>
                <div className={styles.careerAnalysisDetails}>
                  <div className={styles.analysisTitle}>
                    {item.name}（{item.code}）
                  </div>
                  <div className={styles.analysisBox}>
                    <h3>类型特点</h3>
                    <p>{item.typeCharacteristics}</p>
                    <h3>班级管理建议</h3>
                    <p>{item.suggestion}</p>
                  </div>
                </div>
              </ECard>
            )}
          </ECard>
          <ECard wrapperstyle={{ marginBottom: 60 }} id={sideNavList[3].point} wrapperClass={`${printMode ? 'typePrint' : ''}`} >
            <ECompoundTitle title="报告结束语" decription="Conclusion" />
            <div className={styles.textBox}>
              <p>本测评在开发前期，通过收集大量新高考地区学生的选科数据，建立了如下科学的选科因素结构模型。该模型认为，科学选科需要考虑两方面的因素，一是学科潜能，二是未来职业发展方向，只有结合两者才能作出更科学的选科决策。</p>
              <p style={{ margin: 20 }}>
                <img src={reportImg} width="920" height="368" alt="科学选科示意图" />
              </p>
              <p>1.我们衷心地希望本测评能为您在对学生选科指导时提供科学的参考依据，帮助您能够全面客观地了解您的学生在选科时各因素上的表现。</p>
              <p>2.在完成测评的过程中，学生的选择可能会受到环境和情绪的影响，希望您在参考本报告的同时也可以结合您在实际中观察到的学生表现。</p>
              <p>3.青少年是处在自我同一性建立的关键时期，所以他们的兴趣可能还未完全分化，本测评力求挖掘学生的发展优势，但是绝不代表学生的最终成就。</p>
            </div>
          </ECard>
        </div> : null
      }
      {!printMode &&
        <SideNavigation list={sideNavList} />
      }
      <EBanner imgSrc={footerImg} height={90} />
    </div>
  )
}

export default Page;
