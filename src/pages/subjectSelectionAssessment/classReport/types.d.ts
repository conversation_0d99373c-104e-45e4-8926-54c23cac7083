export interface dataAnalysisItem {
  hollandCode?: string;
  hollandName?: string;
  percentage?: number;
  value?: string;
}

export interface hollandAnalysisProps {
  dataAnalysis?: dataAnalysisItem[];
  least?: string;
  most?: string;
}

export interface potentialItem {
  classAvgScore: number;
  gradeAvgScore:  number;
  id: string;
  name: string;
}

export interface percentageAnalysisItem {
  classId: string;
  className: string;
  detailUrl: string;
  hollandList: dataAnalysisItem[];
}
export interface potentialAnalysisProps {
  gradeEvaluatedPeople?: number;
  potential?: potentialItem[];
}

// 报告
export interface classReportProps {
  areaName?: string;
  bizType?: string;
  className?: string;
  endTime?: number;
  evaluatedPeople?: number;
  gradeName?: string;
  hollandAnalysis?: hollandAnalysisProps;
  loginName?: string;
  potentialAnalysis?: potentialAnalysisProps;
  reportCode?: string;
  reportTime?: number;
  schoolName?: string;
  startTime?: number;
  totalPeople?: number;
  records?: number;
  total?: number;
  current?: number;
  size?: number;
}

export interface subjectDetailItem {
  combinationCode: string;
  combinationCount: string;
  combinationName: string;
  percentage: number;
  select: true;
}

export interface listClassSubjectCombinationProps {
  leastCombinations?: string;
  mostCombinations?: string;
  subjectDetail?: subjectDetailItem[];
}

export interface listClassSubjectCombinationResponse {
  code?: string | number;
  msg?: string;
  success?: boolean;
  data?: listClassSubjectCombinationProps
}

export interface classReportResponse {
  code?: string | number;
  msg?: string;
  success?: boolean;
  query?: any;
  data: classReportProps;
  classSubjectCombinationRes?: listClassSubjectCombinationResponse,
  printMode: boolean;
}

export interface ColumnsDataType {
  title: string;
  dataIndex: string;
  align: string;
}


export interface recommendedsItem {
  id?: number | string;
  name: string;
}




