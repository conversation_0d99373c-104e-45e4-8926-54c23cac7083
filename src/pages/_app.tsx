import type { AppProps } from 'next/app';
import isMobile from 'is-mobile';
import { ConfigProvider } from 'antd';
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import MstQt from 'mst-analytics';
import 'antd/dist/antd.css';
// import "antd-mobile/dist/antd-mobile.css";
import '@/assets/iconfont/iconfont.css';
import 'moment/locale/zh-cn';
import zhCN from 'antd/lib/locale/zh_CN';
import PageHead from '~/components/PageHead';
import Script from 'next/script';
import initArms from '~/utils/arms';

import '~/styles/globals.css';
import '~/styles/reset.css';

// QT埋点路由配置
const qtRoutes: any = {
  '/Entrance/EntrancePersonReport': {
    report_name: '入学测-个人',
    analysis_dimension: '个人',
    bxl_evaluation_name: '高中生入学状态综合测评',
    bxl_evaluation_type: '心理综合筛查',
  },
  '/Entrance/EntranceClassReport': {
    report_name: '入学测-班级',
    analysis_dimension: '班级',
    bxl_evaluation_name: '高中生入学状态综合测评',
    bxl_evaluation_type: '心理综合筛查',
  },
  '/Entrance/EntranceGradeReport': {
    report_name: '入学测-年级',
    analysis_dimension: '班级',
    bxl_evaluation_name: '高中生入学状态综合测评',
    bxl_evaluation_type: '心理综合筛查',
  },
  '/Entrance/EntranceCountyReport': {
    report_name: '入学测-区',
  },
  '/Entrance/EntranceCityReport': {
    report_name: '入学测-市级',
  },
  '/Entrance/EntranceCountyV2': {
    report_name: '入学测-区县',
  },
  '/Entrance/EntranceCoverV2': {
    report_name: '入学测-市',
  },
  '/MentalReport/countyReport': {
    report_name: '心理健康测北师大-区',
  },
  '/MentalReport/cityReport': {
    report_name: '心理健康测北师大-市级',
  },
  '/MentalHealthReport/CountyReportEntrance': {
    report_name: '心理健康测北师大-区县',
  },
  '/MentalHealthReport/CityReportEntrance': {
    report_name: '心理健康测北师大-市',
  },
  '/subjectSelectionAssessment/gradeReport': {
    report_name: '选科测-年级',
  },
  '/subjectSelectionAssessment/classReport': {
    report_name: '选科测-班级',
  },
  '/subjectSelectionAssessment/personalReport': {
    report_name: '选科测-个人',
  },
  '/special-assess/person-report': {
    report_name: '专项测-个人',
  },
  '/special-assess/class-report': {
    report_name: '专项测-班级',
  },
  '/special-assess/grade-report': {
    report_name: '专项测-个人',
  },
  '/psychology/cityCover': {
    report_name: '心理健康测SCL90-市',
  },
  '/psychology/countyCover': {
    report_name: '心理健康测SCL90-区县',
  },
  '/psychology/cityReport': {
    report_name: '心理健康测SCL90-市级',
  },
  '/psychology/countyReport': {
    report_name: '心理健康测SCL90-区',
  },
  '/psychology/personReport': {
    report_name: '心理健康测SCL90-个人',
    analysis_dimension: '个人',
    bxl_evaluation_name: '高中生心理健康诊断测验',
    bxl_evaluation_type: '心理综合筛查',
  },
  '/psychology/classReport': {
    report_name: '心理健康测SCL90-班级',
    analysis_dimension: '班级',
    bxl_evaluation_name: '高中生心理健康诊断测验',
    bxl_evaluation_type: '心理综合筛查',
  },
  '/psychology/gradeReport': {
    report_name: '心理健康测SCL90-年级',
    analysis_dimension: '年级',
    bxl_evaluation_name: '高中生心理健康诊断测验',
    bxl_evaluation_type: '心理综合筛查',
  },
  '/career/cvt-report': {
    report_name: '生涯测评-生涯价值观',
    pageCode: 'ewt_pc_career_evaluation_report_home_view',
    type: 'cvt',
  },
  '/career/holland-report': {
    report_name: '生涯测评-霍兰德职业兴趣',
    pageCode: 'ewt_pc_career_evaluation_report_home_view',
    type: 'sds',
  },
  '/career/mbti-report': {
    report_name: '生涯测评-人格类型',
    pageCode: 'ewt_pc_career_evaluation_report_home_view',
    type: 'mbti',
  },
  '/career/mi-report': {
    report_name: '生涯测评-多元智能',
    pageCode: 'ewt_pc_career_evaluation_report_home_view',
    type: 'mi',
  },
  '/upgrade-major-report': {
    report_name: '升学专业选择测评',
    gData: (query: any) => {
      const { clientType } = query;
      const clientTypeString = clientType === '4' ? 'h5' : 'pc';
      const pageCode = `ewt_${clientTypeString}_career_evaluation_upgrade_major_report_home_view`;
      return {
        pageCode,
      }
    }
  },
  '/answer': {
    gData: (query: any) => {
      const { clientType, evaluationTemplateType } = query;
      const pageCode = clientType === '4' ? 'ewt_h5_career_evaluation_answer_home_view' : 'ewt_pc_base_public_evaluation_home_view';
      return {
        pageCode,
        evaluation_template_type: evaluationTemplateType,
      }
    }
  },
};

function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const {
    pathname,
    query = {},
    query: { sourceType, isDetailReport = false },
  } = router;

  // 初始化QT埋点
  const mstAnalyticsInit = () => {
    const pageConfig: any = {};
    const pathConfig = qtRoutes[pathname];
    const gData = pathConfig?.gData?.(query) || {};
    pageConfig[pathname] = {
      pageCode:
        (sourceType && sourceType === '1') || isDetailReport
          ? 'btp_moral-edu-center_det-mental-report_view'
          : 'btp_moral-edu-center_gen-mental-report_view',
      report_type: 1, // 1（心理报告）、2（假期报告）
      operation_name: sourceType && sourceType === '1' ? '行政端' : '其他',
      ...pathConfig,
      ...gData
    };
    MstQt.init({
      appKey: 'wg22vlfgaqtfqywspolipkqt',
      isHistoryMode: true,
      pageConfig,
    });
  };

  const initMstJsBridge = async () => {
    if (typeof window !== 'undefined') {
      const mstJsBridge = (await import('mst-js-bridge')).default;
      window.mstJsBridge = mstJsBridge;
    }
  }

  useEffect(() => {
    initMstJsBridge();
    mstAnalyticsInit();
    initArms();
    // router.beforePopState(({ url, as, options }) => {
    //   // I only want to allow these two routes!
    //   if (as !== '/' && as !== '/other') {
    //     // Have SSR render bad routes as a 404.
    //     window.location.href = as
    //     return false
    //   }
    //   return true
    // })

    // const handleRouteChangeError = (err: { cancelled: any; }, url: any) => {
    //   if (err.cancelled) {
    //     console.log(`Route to ${url} was cancelled!`)
    //   }
    // }

    // const handleRouteChange = (url: any, { shallow }: any) => {
    //   console.log(
    //     `App is changing to ${url} ${
    //       shallow ? 'with' : 'without'
    //     } shallow routing`
    //   )
    // }

    // router.events.on('routeChangeStart', handleRouteChange)
    // router.events.on('routeChangeError', handleRouteChangeError)

    // If the component is unmounted, unsubscribe
    // from the event with the `off` method:
    // return () => {
    //   router.events.off('routeChangeError', handleRouteChangeError)
    //   router.events.off('routeChangeStart', handleRouteChange)
    // }
  }, []);


  return (
    <ConfigProvider locale={zhCN}>
      <PageHead pathname={pathname}/>
      <Component {...pageProps} />
      {process.env.DEPLOYMENT_ENV !== 'prod' && isMobile() && (
        <Script
          src="//web.ewt360.com/common/vconsole.min.js"
          onLoad={() => {
            new window.VConsole();
          }}
        />
      )}
    </ConfigProvider>
  );
}

export default MyApp;
