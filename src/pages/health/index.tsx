import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { observer } from 'mobx-react-lite';


interface dataProps {
  code: number;
  msg: string;
  success: boolean;
}

export const getServerSideProps: GetServerSideProps<{
  data: dataProps;
}> = async (ctx) => {
  ctx.res.statusCode = 200;
  ctx.res.setHeader("Content-Type", "application/json");
  ctx.res.end(JSON.stringify({code: 200, msg: 'OK', success: true }));
  return { props: { data: { code: 200, msg: 'OK', success: true }}};
}

function Page({
  data,
} : InferGetServerSidePropsType<typeof getServerSideProps>) {
  return (
    <>{JSON.stringify(data)}</>
  )
}

export default observer(Page);
