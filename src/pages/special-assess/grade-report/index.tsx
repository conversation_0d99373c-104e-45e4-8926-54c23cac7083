import { NextPage } from 'next';
import React, { useEffect } from 'react';
import { message } from 'antd';
import moment from 'moment';
import styles from './index.module.less';
import api from '@/service/api/special-report';
import ModuleTitle from '~/businessComponents/ModuleTitle';
import Layout from '~/businessComponents/Layout';
import {
  TGradeOverallResponse,
  TGradeCaseResponse,
  TGradeBasicResponse,
  TUserCommonResponse,
  TGradeReportModuleParams,
  TGradeDegreeDistributionItem,
  TGradeClassWarningItem,
} from '@/service/api/special-report/types';
import Cover from '@/lib/special-assess/shared/components/cover';
import ModuleSubtitle from '~/businessComponents/ModuleSubtitle';
import CaseAnalysisCard, {
  ChartCard,
} from '@/lib/special-assess/shared/components/analysis-card';
import ScoreSituationItem from '~/businessComponents/ScoreItem';
import ReportResultTable from '~/businessComponents/ReportResultTable';
import DownloadPDF from '@/components/DownloadPDF';
import WarningRoster from '@/lib/special-assess/shared/components/warning-roster';
import {
  transformLatitudeRelationToView,
  renderBarOptions,
  renderPieOptions,
  getColorValue,
  getNavList,
} from '@/lib/special-assess/shared/logic/index';

import {
  mountPSTScreenshot,
  stopPSTScreenshotHook,
  filterInvalidValues,
} from '~/utils/tools';
import { useClient } from '@/hooks';
import SideNavigation from '@/components/career/SideNavigation';
import FooterBg from '@/assets/special-assess/footer.png';
import { BLOCK_F_STRING } from '@/lib/special-assess/shared/types';

type IGradeReportProps = {
  basicData: TGradeBasicResponse;
  overallData: TGradeOverallResponse;
  caseData: TGradeCaseResponse[];
  commonData: TUserCommonResponse;
  printMode: boolean;
  config: any;
  queryParams: TGradeReportModuleParams;
  error: any;
};

const GradeReport: NextPage<IGradeReportProps> = ({
  basicData,
  commonData,
  overallData,
  caseData,
  printMode,
  config,
  queryParams,
  error,
}: IGradeReportProps) => {
  const [render] = useClient();
  const { latitudeLevelList } = config;
  const { gradeLatitudeOverallAnalysisList, overallAnalysisList } = overallData;
  const coverTimeStrToDate = (startTime: any, endTime: any) => {
    const formatStr = 'YYYY-MM-DD';
    if (startTime && endTime) {
      return `${moment(startTime).format(formatStr)}-${moment(endTime).format(
        formatStr
      )}`;
    }
    return '';
  };

  const { identify, relation } = transformLatitudeRelationToView(
    {
      caseAnalysis: caseData,
      overallAnalysis: overallData,
    },
    {
      ...config,
      firstLevelKey: 'gradeLatitudeOverallAnalysisList',
    }
  );

  const renderFirstLevel = (v: any) => {
    return (
      <div
        className={styles['level-top-info']}
        style={{
          backgroundImage: `linear-gradient(90deg, ${
            getColorValue(v, latitudeLevelList).color
          } 13%, #ffffff 85%)`,
        }}
      >
        <div className={styles['main-box']}>
          <div className={styles.left}>{v.latitudeTitle}</div>
          <div className={styles.right}>
            得分说明
            <br />
            {v.description}
          </div>
        </div>
        <div
          className={styles['score-box']}
          style={{
            backgroundColor: getColorValue(v, latitudeLevelList).secondColor,
          }}
        >
          <div
            className={styles.before}
            style={{
              borderColor: `#fff ${
                getColorValue(v, latitudeLevelList).secondColor
              }`,
            }}
          />
          <div>{v.latitudeLevelTitle}</div>
          <div className={styles.score}>
            {v.score} / {v.totalScore}
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    console.log(
      `🚀 ~ {
      basicData,
      commonData,
      overallData,
      caseData,
      printMode,
      config,
      queryParams,
      error,
    }:`,
      {
        basicData,
        commonData,
        overallData,
        caseData,
        printMode,
        config,
        queryParams,
        error,
      }
    );
    if (error) {
      const errorCode = Number(error?.code || 0);
      const errorMessage =
        errorCode !== 200 ? error?.msg || '服务异常,请稍后重试！' : '';
      message.error(errorMessage);

      if (printMode) {
        stopPSTScreenshotHook({
          errorCode,
          errorMessage,
        });
      }
      return;
    }

    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: 3000,
      });
    }
  }, []);

  return (
    <div className={styles.hbs}>
      <Cover
        isPrint={printMode}
        reportTitle={commonData?.title}
        userBaseInfo={basicData}
        reportTypeTitle="年级分析报告"
      />
      <Layout>
        <ModuleTitle
          title="报告说明"
          id={BLOCK_F_STRING['Report Description']}
          description="Report Description"
        >
          <div className={styles['card-wrapper']}>
            <div className={styles.description}>
              <p className={styles.title}>★ 报告导读</p>
              <div
                className={styles.desc}
                dangerouslySetInnerHTML={{
                  __html: (render && commonData?.reportGuide) || '',
                }}
              />
              <p className={styles.title}>★ 测评目的</p>
              <div className={styles.desc}>{commonData.evaluationAim}</div>
              <p className={styles.title}>★ 测评工具</p>
              <div className={styles.desc}>{commonData.evaluationTool}</div>
              <p className={styles.title}>★ 测评时间</p>
              <div className={styles.desc}>
                {coverTimeStrToDate(basicData.startTime, basicData.endTime)}
              </div>
              <p className={styles.title}>★ 测评群体</p>
              <div className={styles.desc}>
                人数：完成测评 {basicData.finishedNum}人
              </div>
            </div>
          </div>
        </ModuleTitle>

        {gradeLatitudeOverallAnalysisList?.length > 0 && (
          <ModuleTitle
            title="年级整体概况"
            id={BLOCK_F_STRING['Overall Overview']}
            description="Overall Overview"
          >
            <div className={styles['card-wrapper']}>
              <div className={styles.overall}>
                {gradeLatitudeOverallAnalysisList.map((v, i) => (
                  <div className={styles['overall-list-item']} key={i}>
                    <div className={styles.left}>
                      <div className={styles.round} />
                      <div className={styles.label}>
                        {i + 1 >= 10 ? i + 1 : `0${i + 1}`} {v.latitudeTitle}
                      </div>
                      <div className={styles.line} />
                    </div>
                    <div className={styles.right}>
                      <div className={styles.score}>
                        {v.score} / {v.totalScore}
                      </div>
                      <div>{v.latitudeLevelTitle}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {gradeLatitudeOverallAnalysisList.map((v, index) => (
              <div
                key={index}
                className={`${styles['card-wrapper']} ${styles.overview}`}
              >
                {renderFirstLevel(v)}
                <div className={styles['result-info']}>
                  <p>从测评结果来看，你的年级：</p>
                  <div
                    className={styles.content}
                    dangerouslySetInnerHTML={{ __html: v.result }}
                  />
                </div>
                <div className={styles['chart-wrapper']}>
                  <ChartCard
                    type="grade"
                    data={v}
                    fieldNames={{
                      chartSituation: 'chartSituation',
                      degreeDistributionList: 'degreeDistributionList',
                      levelSuggest: 'suggestDesc',
                    }}
                    labelNames={{
                      levelSuggest: '★ 整体建议说明',
                    }}
                    chartProps={{
                      pieProps: {
                        height: 360,
                        customOption: renderPieOptions(
                          v.degreeDistributionList?.map(
                            (v: TGradeDegreeDistributionItem) => ({
                              name: v.latitudeLevelTitle,
                              num: v.num,
                            })
                          )
                        ),
                      },
                      barProps: {
                        label: '★  各班级预警人数情况',
                        height: 120,
                        customOption: renderBarOptions(
                          v?.classLatitudeWarningList?.map(
                            (v: TGradeClassWarningItem) => ({
                              name: v.className,
                              value: v.num,
                            })
                          )
                        ),
                      },
                    }}
                  />
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}

        {relation?.length > 0 && (
          <ModuleTitle
            title="年级整体分析"
            id={BLOCK_F_STRING.Analysis}
            description="Analysis"
          >
            {relation.map((v: any, i: number) => (
              <div className={`${styles['card-wrapper']} ${styles['analysis-wrapper']}`} key={i}>
                {v.latitudeId && renderFirstLevel(v)}
                <div className={styles.analysis}>
                  {v.overallAnalysis?.length > 0 ? (
                    <>
                      <ModuleSubtitle title="维度得分情况" />
                      <ol>
                        {overallAnalysisList.map((item, index) => (
                          <ScoreSituationItem
                            key={`scoreSituationItem_key_${index}`}
                            data={item}
                            color={getColorValue(item, latitudeLevelList).color}
                            resultLabelAfter="，该年级的学生"
                          />
                        ))}
                      </ol>
                    </>
                  ) : null}
                  <ModuleSubtitle title="预警情况" />
                  <div className={styles['warning-box']}>
                    <p className={styles['warning-desc']}>
                      预警情况是指在某方面得分比较异常，需要特别关注的学生情况
                    </p>
                    <p className={styles.title}>★ 年级总体预警情况</p>
                    <ReportResultTable
                      rowKey="latitudeId"
                      columns={[
                        {
                          title: '维度',
                          dataIndex: 'latitudeTitle',
                          align: 'center',
                        },
                        {
                          title: '人数',
                          dataIndex: 'num',
                          align: 'center',
                        },
                        {
                          title: '占比',
                          dataIndex: 'percentage',
                          align: 'center',
                          render: (value: any) => `${value}%`,
                        },
                      ]}
                      dataSource={v.latitudeWarning}
                    />
                  </div>
                  <ModuleSubtitle title="具体情况分析" />
                  {v.caseAnalysis.map((item: any, index: number) => (
                    <CaseAnalysisCard
                      type="grade"
                      key={item.latitudeTitle}
                      color={getColorValue(item, latitudeLevelList).color}
                      data={item}
                      fieldNames={{
                        latitudeDescription: 'latitudeDescription',
                        chartSituation: 'chartSituation',
                        levelList: 'levelList',
                      }}
                      chartProps={{
                        pieProps: {
                          height: 400,
                          customOption: renderPieOptions(item.levelList),
                        },
                        barProps: {
                          height: 220,
                          label: '★ 各班级在该项得分情况',
                          customOption: renderBarOptions(
                            item.classScoreList.map(
                              (v: { name: string; score: number }) => ({
                                name: v.name,
                                value: v.score,
                              })
                            )
                          ),
                        },
                      }}
                    />
                  ))}
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}

        {identify?.length > 0 && (
          <ModuleTitle
            title="测评鉴别分析"
            id={BLOCK_F_STRING.identify}
            description="identify"
          >
            {identify.map((v, index) => (
              <div key={index} className={`${styles['card-wrapper']}`}>
                {renderFirstLevel(v)}
                <div className={styles['result-info']}>
                  <p>从测评结果来看，你的年级：</p>
                  <div
                    className={styles.content}
                    dangerouslySetInnerHTML={{ __html: v.result! }}
                  />
                </div>
                <div className={styles['chart-wrapper']}>
                  <ChartCard
                    type="grade"
                    data={v}
                    fieldNames={{
                      latitudeDescription: 'latitudeDescription',
                      chartSituation: 'chartSituation',
                      levelList: 'levelList',
                    }}
                    chartProps={{
                      pieProps: {
                        height: 400,
                        customOption: renderPieOptions(v.levelList),
                      },
                      barProps: {
                        height: 220,
                        label: '★ 各班级在该项得分情况',
                        customOption: renderBarOptions(
                          v.classScoreList!.map((v: any) => ({
                            name: v.name,
                            value: v.score,
                          }))
                        ),
                      },
                    }}
                  />
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}
        <ModuleTitle
          title="预警名单"
          id={BLOCK_F_STRING['Warning List']}
          description="Warning List"
          wrapperClass={printMode ? 'typePrint' : ''}
        >
          <div className={styles['card-wrapper']}>
            <p className={styles.title}>★ 预警名单</p>
            <WarningRoster
              queryParams={queryParams}
              printMode={printMode}
              reportType={3}
              clickPvParams={{
                eventCode:
                  'ewt_pc_teacher_psychology_manage_special_assess_grade_report_warning_list_download_button_click',
                school_name: basicData?.schoolName,
              }}
              sortDescription={
                config?.latitudeSimple
                  ? `* 默认按主维度${config?.latitudeSimple.latitudeTitle}进行排序`
                  : ''
              }
            />
          </div>
        </ModuleTitle>

        <ModuleTitle
          title="附加说明"
          id={BLOCK_F_STRING.additional}
          description="additional"
        >
          <div className={styles['card-wrapper']}>
            <div className={styles.additional}>
              <div
                className={styles.desc}
                dangerouslySetInnerHTML={{
                  __html: (render && commonData?.extDesc) || '',
                }}
              />
            </div>
          </div>
        </ModuleTitle>
      </Layout>

      {!printMode && (
        <SideNavigation
          id="navigation"
          offset={45}
          className={styles['side-navigation']}
          list={getNavList(
            {
              overallAnalysis: gradeLatitudeOverallAnalysisList,
              relation,
              identify,
            },
            { type: 'grade' }
          )}
          renderBottom={() => {
            return (
              <DownloadPDF
                wrapperClassName={styles.download}
                label="报告下载"
              />
            );
          }}
        />
      )}
      <img className={styles.footer} src={FooterBg} />
    </div>
  );
};

export const getServerSideProps = async (ctx: any) => {
  const {
    recordId,
    evaluationRecordId,
    gradeId,
    classGroupNum,
    initRecordId,
    ff,
    isPrint,
  } = ctx.query;
  const printMode = !!(ff || isPrint);
  const queryParams = filterInvalidValues({
    evaluationTaskId: recordId || evaluationRecordId,
    gradeId,
    classGroupNum,
    initRecordId,
  });
  try {
    const [overallRes, basicRes, caseRes, configRes] = await Promise.all([
      api.getGradeOverallAnalysis(queryParams, ctx),
      api.getGradeBasicAnalysis(queryParams, ctx),
      api.getGradeCaseAnalysis(queryParams, ctx),
      api.getReportConfig(queryParams, ctx),
    ]);

    let commonData = {};
    if (basicRes?.success) {
      const { templateVersionId } = basicRes.data;
      const commonRes = await api.getUserCommonModule(
        {
          templateVersionId,
          reportType: 3, // 个人报告为 1, 班级报告：2，年级报告：3
          initRecordId,
        },
        ctx
      );
      if (commonRes?.success) {
        commonData = commonRes.data;
      }
    }
    return {
      props: {
        basicData: basicRes.data || {},
        overallData: overallRes.data || {},
        caseData: caseRes.data || [],
        commonData,
        printMode,
        config: configRes.data || {},
        queryParams: { ...ctx.query, ...queryParams },
        error: null,
      },
    };
  } catch (error) {
    return {
      props: {
        basicData: {},
        overallData: [],
        caseData: [],
        commonData: {},
        printMode,
        config: {},
        queryParams: { ...ctx.query, ...queryParams },
        error,
      },
    };
  }
};

export default GradeReport;
