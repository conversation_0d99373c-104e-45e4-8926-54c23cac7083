import { NextPage } from 'next';
import React, { useEffect } from 'react';
import { message } from 'antd';
import moment from 'moment';
import styles from './index.module.less';
import api from '@/service/api/special-report';
import ModuleTitle from '~/businessComponents/ModuleTitle';
import Layout from '~/businessComponents/Layout';
import CaseAnalysisCard, {
  ChartCard,
} from '@/lib/special-assess/shared/components/analysis-card';
import {
  TClassOverallResponse,
  TClassCaseResponse,
  TClassBasicResponse,
  TUserCommonResponse,
  TClassBasicParams,
} from '@/service/api/special-report/types';
import Cover from '@/lib/special-assess/shared/components/cover';
import ModuleSubtitle from '~/businessComponents/ModuleSubtitle';
import ScoreSituationItem from '~/businessComponents/ScoreItem';
import { useClient } from '@/hooks';
import ReportResultTable from '~/businessComponents/ReportResultTable';
import DownloadPDF from '~/components/DownloadPDF';
import {
  filterInvalidValues,
  mountPSTScreenshot,
  stopPSTScreenshotHook,
} from '~/utils/tools';
import WarningRoster from '@/lib/special-assess/shared/components/warning-roster';
import {
  transformLatitudeRelationToView,
  renderPieOptions,
  getColorValue,
  getNavList,
} from '@/lib/special-assess/shared/logic/index';
import SideNavigation from '@/components/career/SideNavigation';
import FooterBg from '@/assets/special-assess/footer.png';
import { BLOCK_F_STRING } from '@/lib/special-assess/shared/types';

type IClassReportProps = {
  basicData: TClassBasicResponse;
  overallData: TClassOverallResponse;
  caseData: TClassCaseResponse[];
  commonData: TUserCommonResponse;
  queryParams: TClassBasicParams;
  printMode: boolean;
  error: any;
  config: any;
};

const ClassReport: NextPage<IClassReportProps> = ({
  basicData,
  commonData,
  overallData,
  caseData,
  queryParams,
  printMode,
  config,
  error,
}: IClassReportProps) => {
  const [render] = useClient();
  const { classLatitudeOverallAnalysisList } = overallData;
  const coverTimeStrToDate = (startTime: any, endTime: any) => {
    const formatStr = 'YYYY-MM-DD';
    if (startTime && endTime) {
      return `${moment(startTime).format(formatStr)}-${moment(endTime).format(
        formatStr
      )}`;
    }
    return '';
  };

  const { identify, relation } = transformLatitudeRelationToView(
    {
      caseAnalysis: caseData,
      overallAnalysis: overallData,
    },
    {
      ...config,
      firstLevelKey: 'classLatitudeOverallAnalysisList',
    }
  );
  const { latitudeLevelList } = config;

  const renderFirstLevel = (v: any) => {
    return (
      <div
        className={styles['level-top-info']}
        style={{
          backgroundImage: `linear-gradient(90deg, ${
            getColorValue(v, latitudeLevelList).color
          } 13%, #ffffff 85%)`,
        }}
      >
        <div className={styles['main-box']}>
          <div className={styles.left}>{v.latitudeTitle}</div>
          <div className={styles.right}>
            得分说明
            <br />
            {v.description}
          </div>
        </div>
        <div
          className={styles['score-box']}
          style={{
            backgroundColor: getColorValue(v, latitudeLevelList).secondColor,
          }}
        >
          <div
            className={styles.before}
            style={{
              borderColor: `#fff ${
                getColorValue(v, latitudeLevelList).secondColor
              }`,
            }}
          />
          <div>{v.latitudeLevelTitle}</div>
          <div className={styles.score}>
            {v.score} / {v.totalScore}
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    console.log(
      `🚀 ~ {
      basicData,
      commonData,
      overallData,
      caseData,
      queryParams,
      printMode,
      config,
      error,
    }:`,
      {
        basicData,
        commonData,
        overallData,
        caseData,
        queryParams,
        printMode,
        config,
        error,
      }
    );
    if (error) {
      const errorCode = Number(error?.code || 0);
      const errorMessage =
        errorCode !== 200 ? error?.msg || '服务异常,请稍后重试！' : '';
      message.error(errorMessage);

      if (printMode) {
        stopPSTScreenshotHook({
          errorCode,
          errorMessage,
        });
      }
      return;
    }

    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: 3000,
      });
    }
  }, []);
  return (
    <div className={styles.hbs}>
      <Cover
        isPrint={printMode}
        reportTitle={commonData?.title}
        userBaseInfo={basicData}
        reportTypeTitle="班级分析报告"
      />
      <Layout>
        <ModuleTitle
          title="报告说明"
          id={BLOCK_F_STRING['Report Description']}
          description="Report Description"
        >
          <div className={styles['card-wrapper']}>
            <div className={styles.description}>
              <p className={styles.title}>★ 报告导读</p>
              <div
                className={styles.desc}
                dangerouslySetInnerHTML={{
                  __html: (render && commonData?.reportGuide) || '',
                }}
              />
              <p className={styles.title}>★ 测评目的</p>
              <div className={styles.desc}>{commonData.evaluationAim}</div>
              <p className={styles.title}>★ 测评工具</p>
              <div className={styles.desc}>{commonData.evaluationTool}</div>
              <p className={styles.title}>★ 测评时间</p>
              <div className={styles.desc}>
                {coverTimeStrToDate(basicData.startTime, basicData.endTime)}
              </div>
              <p className={styles.title}>★ 测评群体</p>
              <div className={styles.desc}>
                人数：完成测评 {basicData.finishedNum}人
              </div>
            </div>
          </div>
        </ModuleTitle>
        {classLatitudeOverallAnalysisList?.length > 0 && (
          <ModuleTitle
            title="班级整体概况"
            wrapperClass={printMode ? 'typePrint' : ''} 
            id={BLOCK_F_STRING['Overall Overview']}
            description="Overall Overview"
          >
            <div className={styles['card-wrapper']}>
              <div className={styles.overall}>
                {classLatitudeOverallAnalysisList.map((v, i) => (
                  <div className={styles['overall-list-item']} key={i}>
                    <div className={styles.left}>
                      <div className={styles.round} />
                      <div className={styles.label}>
                        {i + 1 >= 10 ? i + 1 : `0${i + 1}`} {v.latitudeTitle}
                      </div>
                      <div className={styles.line} />
                    </div>
                    <div className={styles.right}>
                      <div className={styles.score}>
                        {v.score} / {v.totalScore}
                      </div>
                      <div>{v.latitudeLevelTitle}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {classLatitudeOverallAnalysisList.map((v, index) => (
              <div
                key={index}
                className={`${styles['card-wrapper']} ${styles.overview}`}
              >
                {renderFirstLevel(v)}
                <div className={styles['result-info']}>
                  <p>从测评结果来看，你的班级：</p>
                  <div
                    className={styles.content}
                    dangerouslySetInnerHTML={{ __html: v.result }}
                  />
                </div>
                <div className={styles['chart-wrapper']}>
                  <ChartCard
                    type="class"
                    data={v}
                    fieldNames={{
                      chartSituation: 'chartSituation',
                      degreeDistributionList: 'degreeDistributionList',
                      levelSuggest: 'suggestDesc',
                    }}
                    labelNames={{
                      levelSuggest: '★ 整体建议说明',
                    }}
                    chartProps={{
                      pieProps: {
                        height: 360,
                        customOption: renderPieOptions(
                          v.degreeDistributionList?.map((v) => ({
                            name: v.latitudeLevelTitle,
                            num: v.num,
                          }))
                        ),
                      },
                    }}
                  />
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}

        {relation?.length > 0 && (
          <ModuleTitle
            title="班级整体分析"
            id={BLOCK_F_STRING.Analysis}
            wrapperClass={printMode ? 'typePrint' : ''} 
            description="Analysis"
          >
            {relation.map((v: any, i: number) => (
              <div className={`${styles['card-wrapper']} ${styles['analysis-wrapper']}`} key={i}>
                {v.latitudeId && renderFirstLevel(v)}
                <div className={styles.analysis}>
                  {v.overallAnalysis?.length > 0 ? (
                    <>
                      <ModuleSubtitle title="维度得分情况" />
                      <ol>
                        {v.overallAnalysis.map((item: any, index: number) => (
                          <ScoreSituationItem
                            color={getColorValue(item, latitudeLevelList).color}
                            key={`scoreSituationItem_key_${index}`}
                            data={item}
                            resultLabelAfter="，您的班级"
                          />
                        ))}
                      </ol>
                    </>
                  ) : null}
                  <ModuleSubtitle title="预警情况" />
                  <div className={styles['warning-box']}>
                    <p className={styles['warning-desc']}>
                      预警情况是指在某方面得分比较异常，需要特别关注的学生情况
                    </p>
                    <p className={styles.title}>★ 班级总体预警情况</p>
                    <ReportResultTable
                      rowKey="latitudeId"
                      columns={[
                        {
                          title: '维度',
                          dataIndex: 'latitudeTitle',
                        },
                        {
                          title: '人数',
                          dataIndex: 'num',
                        },
                        {
                          title: '占比',
                          dataIndex: 'percentage',
                          render: (value: any) => `${value}%`,
                        },
                      ]}
                      dataSource={v.latitudeWarning}
                    />
                  </div>
                  <ModuleSubtitle title="具体情况分析" />
                  {v.caseAnalysis.map((item: any, index: number) => (
                    <CaseAnalysisCard
                      color={getColorValue(item, latitudeLevelList).color}
                      type="class"
                      key={item.latitudeTitle}
                      fieldNames={{
                        latitudeDescription: 'latitudeDescription',
                        chartSituation: 'chartSituation',
                        waringStudentsList: 'waringStudentsList',
                        levelSuggest: 'levelSuggest',
                        levelList: 'levelList',
                      }}
                      data={item}
                      chartProps={{
                        pieProps: {
                          height: 400,
                          customOption: renderPieOptions(item.levelList),
                        },
                      }}
                    />
                  ))}
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}

        {identify?.length > 0 && (
          <ModuleTitle
            title="测评鉴别分析"
            id={BLOCK_F_STRING.identify}
            wrapperClass={printMode ? 'typePrint' : ''} 
            description="identify"
          >
            {identify.map((v, index) => (
              <div key={index} className={`${styles['card-wrapper']}`}>
                {renderFirstLevel(v)}
                <div className={styles['result-info']}>
                  <p>从测评结果来看，你的班级：</p>
                  <div
                    className={styles.content}
                    dangerouslySetInnerHTML={{ __html: v.result! }}
                  />
                </div>
                <div className={styles['chart-wrapper']}>
                  <ChartCard
                    type="class"
                    data={v}
                    fieldNames={{
                      chartSituation: 'chartSituation',
                      levelList: 'levelList',
                      levelSuggest: 'levelSuggest',
                    }}
                    labelNames={{
                      levelSuggest: '★ 整体建议说明',
                    }}
                    chartProps={{
                      pieProps: {
                        height: 360,
                        customOption: renderPieOptions(
                          v.levelList?.map((v: any) => ({
                            name: v.latitudeLevelTitle,
                            num: v.num,
                          })) as any
                        ),
                      },
                    }}
                  />
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}

        <ModuleTitle
          title="预警名单"
          description="Warning List"
          id={BLOCK_F_STRING['Warning List']}
          wrapperClass={printMode ? 'typePrint' : ''}
        >
          <div className={styles['card-wrapper']}>
            <p className={styles.title}>★ 预警名单</p>
            <WarningRoster
              queryParams={queryParams}
              printMode={printMode}
              reportType={2}
              clickPvParams={{
                eventCode:
                  'ewt_pc_teacher_psychology_manage_special_assess_class_report_warning_list_download_button_click',
                school_name: basicData?.schoolName,
              }}
              sortDescription={
                config?.latitudeSimple
                  ? `默认按主维度${config?.latitudeSimple.latitudeTitle}进行排序`
                  : ''
              }
            />
          </div>
        </ModuleTitle>

        <ModuleTitle
          title="附加说明"
          wrapperClass={printMode ? 'typePrint' : ''} 
          id={BLOCK_F_STRING.additional}
          description="additional"
        >
          <div className={styles['card-wrapper']}>
            <div className={styles.additional}>
              <div
                className={styles.desc}
                dangerouslySetInnerHTML={{
                  __html: (render && commonData?.extDesc) || '',
                }}
              />
            </div>
          </div>
        </ModuleTitle>
      </Layout>
      {!printMode && (
        <SideNavigation
          id="navigation"
          offset={45}
          className={styles['side-navigation']}
          list={getNavList(
            {
              overallAnalysis: classLatitudeOverallAnalysisList,
              relation,
              identify,
            },
            { type: 'class' }
          )}
          renderBottom={() => {
            return (
              <DownloadPDF
                wrapperClassName={styles.download}
                label="报告下载"
              />
            );
          }}
        />
      )}
      <img className={styles.footer} src={FooterBg} />
    </div>
  );
};

export const getServerSideProps = async (ctx: any) => {
  const {
    recordId: evaluationTaskId,
    gradeId,
    classId,
    initRecordId,
    ff,
    isPrint,
  } = ctx.query;
  const printMode = !!(ff || isPrint);
  const queryParams = filterInvalidValues({
    evaluationTaskId,
    gradeId,
    classId,
    initRecordId,
  });
  try {
    const [overallRes, caseRes, basicRes, configRes] = await Promise.all([
      api.getClassOverallAnalysis(queryParams, ctx),
      api.getClassCaseAnalysis(queryParams, ctx),
      api.getClassBasicAnalysis(queryParams, ctx),
      api.getReportConfig(queryParams, ctx),
    ]);
    let commonData = {};
    if (basicRes && basicRes.success) {
      const { templateVersionId } = basicRes.data;
      const userCommonRes = await api.getUserCommonModule(
        {
          templateVersionId,
          reportType: 2, // 个人报告为 1,班级报告：2，年级报告：3
          initRecordId,
        },
        ctx
      );

      if (userCommonRes && userCommonRes.success) {
        commonData = userCommonRes.data;
      }
    }
    return {
      props: {
        basicData: basicRes.data || {},
        overallData: overallRes.data || [],
        caseData: caseRes.data || [],
        commonData,
        queryParams: { ...ctx.query, ...queryParams },
        printMode,
        config: configRes.data || {},
        error: null,
      },
    };
  } catch (error: any) {
    return {
      props: {
        basicData: {},
        overallData: [],
        caseData: [],
        commonData: {},
        config: {},
        queryParams: { ...ctx.query, ...queryParams },
        printMode,
        error,
      },
    };
  }
};

export default ClassReport;
