.hbs {
  font-size: 14px;
  background-color: #f9fbfc;
  min-width: 1100px;
  .desc {
    font-weight: 400;
    font-size: 18px;
    color: #666666;
    letter-spacing: 0;
    line-height: 26px;
    padding-left: 36px;
  }
}

.card-wrapper {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0 0 20px 0 #e6ece9;
  border-radius: 8px;
  .title {
    font-weight: bold;
    font-size: 18px;
    color: #475370;
    margin-bottom: 20px;
  }
}

.analysis-wrapper {
  margin-bottom: 60px;
  &:last-child {
    margin-bottom: 0;
  }
}

.description,
.additional {
  padding: 10px 20px 20px;
  .desc {
    font-weight: 400;
    font-size: 18px;
    color: #666666;
    line-height: 30px;
    padding-left: 25px;
    margin-bottom: 40px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.overall {
  .overall-list-item {
    position: relative;
    background-color: #fafafb;
    margin-bottom: 20px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    height: 54px;
    &:last-child {
      margin-bottom: 0;
    }
    .left {
      display: flex;
      align-items: center;
      flex: 1;
      .round {
        width: 16px;
        height: 16px;
        margin-top: 14px;
        opacity: 0.4;
        margin-right: 4px;
        background-image: linear-gradient(109deg, #4b80ff 21%, #ffffff 100%);
        border-radius: 8px;
      }
      .line {
        flex: 1;
        content: '';
        margin-left: 14px;
        margin-right: 10px;
        height: 1px;
        border-bottom: 1px dashed #cccccc;
      }
      .label {
        position: relative;
        display: inline;
        font-weight: bold;
        font-size: 20px;
        color: rgba(0, 17, 57, 0.85);
        line-height: 34px;
        z-index: 10;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 32px;
        white-space: nowrap;
        max-width: 663px;
        &::after {
          position: absolute;
          display: block;
          top: 16px;
          flex: 1;
          content: '';
          height: 16px;
          width: 100%;
          opacity: 0.4;
          background-image: linear-gradient(91deg, #4b80ff 21%, #ffffff 100%);
          border-radius: 8px;
        }
      }
    }
    .right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 150px;
      font-size: 18px;
      font-weight: bold;
      color: rgba(0, 17, 57, 0.85);
      .score {
        color: rgba(75, 128, 255, 1);
      }
    }
  }
}

.level-top-info {
  margin-bottom: 20px;
  display: flex;
  position: relative;
  height: 80px;
  align-items: center;
  justify-content: space-between;
  background-image: linear-gradient(90deg, #4b80ff 13%, #ffffff 85%);
  border-radius: 8px;
  font-weight: bold;
  font-size: 18px;
  overflow: hidden;
  color: rgba(0, 17, 57, 0.85);
  &::before {
    position: absolute;
    content: '';
    left: 0;
    width: 10px;
    height: 30px;
    background-image: linear-gradient(
      269deg,
      #ffffff 0%,
      rgba(255, 255, 255, 0) 100%
    );
    border-radius: 0 6px 6px 0;
  }
  .main-box {
    padding-left: 30px;
    display: flex;
    height: 100%;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    .left {
      flex: 1;
      color: #fff;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .right {
      white-space: nowrap;
      text-align: right;
      font-size: 16px;
      font-weight: normal;
      color: rgba(0, 17, 57, 0.85);
    }
  }
  .score-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    padding-right: 20px;
    height: 100%;
    margin-left: 20px;
    width: 160px;
    text-align: right;
    mix-blend-mode: multiply;
    background-color: rgba(219, 230, 255, 1);
    .before {
      height: 100%;
      width: 10px;
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      border-width: 80px 30px 0 0;
      border-style: solid;
      border-color: #fff rgba(219, 230, 255, 1);
    }
  }
}

.result-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 20px;
  min-height: 110px;
  background: #ffffff;
  border: 1px dashed rgba(0, 17, 57, 0.85);
  border-radius: 8px;
  p:first-child {
    font-family: MicrosoftYaHeiUI;
    font-size: 18px;
    color: rgba(0, 17, 57, 0.85);
  }
  .content {
    margin-top: 10px;
    font-weight: bold;
  }
}
.chart-wrapper {
  margin-top: 20px;
  padding: 20px;
  background: #fafafb;
  border-radius: 8px;
}
.overview {
  margin-top: 60px;
}

.analysis {
  background: #fafafb;
  border-radius: 8px;
  padding-bottom: 20px;
}

.side-navigation {
  margin: 0;
  top: 0;
  width: 168px !important;
  margin: 350px -700px 0 !important;
  border-radius: 12px !important;
  :global {
    .ant-timeline-item-content {
      font-size: 15px;
      margin-left: 18px;
      min-height: auto;
    }
  }
}
.footer {
  display: block;
  width: 100%;
  margin-top: 60px;
}

.download {
  position: static !important;
}
