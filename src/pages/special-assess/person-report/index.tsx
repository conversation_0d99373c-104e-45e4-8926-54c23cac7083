import { NextPage } from 'next';
import React, { useEffect } from 'react';
import { message } from 'antd';
import styles from './index.module.less';
import api from '@/service/api/special-report';
import ModuleTitle from '~/businessComponents/ModuleTitle';
import Layout from '~/businessComponents/Layout';
import {
  TUserOverallResponse,
  TUserCaseResponse,
  TUserCommonResponse,
} from '@/service/api/special-report/types';
import { IUserBaseInfo } from '~/businessComponents/BaseUserInfo';
import Cover from '@/lib/special-assess/shared/components/cover';
import ModuleSubtitle from '~/businessComponents/ModuleSubtitle';
import ScoreSituationItem from '~/businessComponents/ScoreItem';
import CaseAnalysisCard, {
  ChartCard,
} from '@/lib/special-assess/shared/components/analysis-card';
import { useClient } from '@/hooks';
import DownloadPDF from '~/components/DownloadPDF';
import SideNavigation from '@/components/career/SideNavigation';
import FooterBg from '@/assets/special-assess/footer.png';
import { mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';
import {
  transformLatitudeRelationToView,
  getColorValue,
  getNavList,
} from '@/lib/special-assess/shared/logic/index';

import { BLOCK_F_STRING } from '@/lib/special-assess/shared/types';

type IPersonReportProps = {
  basicData: IUserBaseInfo;
  overallData: TUserOverallResponse;
  caseData: TUserCaseResponse[];
  commonData: TUserCommonResponse;
  printMode: boolean;
  error: any;
  config: any;
};

const PersonReport: NextPage<IPersonReportProps> = ({
  basicData,
  commonData,
  overallData,
  caseData,
  printMode,
  error,
  config,
}: IPersonReportProps) => {
  const { latitudeLevelList } = config;
  const [render] = useClient();
  const { personLatitudeOverallAnalysisList } = overallData;
  useEffect(() => {
    console.log(`🚀 ~ {
      basicData,
      commonData,
      overallData,
      caseData,
      printMode,
      error,
      config,
    }:`, {
      basicData,
      commonData,
      overallData,
      caseData,
      printMode,
      error,
      config,
    })
    if (error) {
      const errorCode = Number(error?.code || 0);
      const errorMessage =
        errorCode !== 200 ? error?.msg || '服务异常,请稍后重试！' : '';
      message.error(errorMessage);

      if (printMode) {
        stopPSTScreenshotHook({
          errorCode,
          errorMessage,
        });
      }
      return;
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: 3000,
      });
    }
  }, []);

  const { identify, relation } = transformLatitudeRelationToView(
    {
      caseAnalysis: caseData,
      overallAnalysis: {
        overallAnalysisList: overallData.latitudeOverallAnalysisList,
        personLatitudeOverallAnalysisList:
          overallData.personLatitudeOverallAnalysisList,
      },
    },
    {
      ...config,
      firstLevelKey: 'personLatitudeOverallAnalysisList',
    }
  );

  const renderFirstLevel = (v: any) => {
    return (
      <div
        className={styles['level-top-info']}
        style={{
          backgroundImage: `linear-gradient(90deg, ${
            getColorValue(v, latitudeLevelList).color
          } 13%, #ffffff 85%)`,
        }}
      >
        <div className={styles['main-box']}>
          <div className={styles.left}>{v.latitudeTitle}</div>
          <div className={styles.right}>
            得分说明
            <br />
            {v.description}
          </div>
        </div>
        <div
          className={styles['score-box']}
          style={{
            backgroundColor: getColorValue(v, latitudeLevelList).secondColor,
          }}
        >
          <div
            className={styles.before}
            style={{
              borderColor: `#fff ${
                getColorValue(v, latitudeLevelList).secondColor
              }`,
            }}
          />
          <div>{v.latitudeLevelTitle}</div>
          <div className={styles.score}>
            {v.score} / {v.totalScore}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.hbs}>
      <Cover
        isPrint={printMode}
        reportTitle={commonData?.title}
        userBaseInfo={basicData}
        reportTypeTitle="个人分析报告"
      />
      <Layout>
        <ModuleTitle
          title="报告说明"
          id={BLOCK_F_STRING['Report Description']}
          description="Report Description"
        >
          <div className={styles['card-wrapper']}>
            <div className={styles.description}>
              <p className={styles.title}>★ 报告导读</p>
              <div
                className={styles.desc}
                dangerouslySetInnerHTML={{
                  __html: (render && commonData?.reportGuide) || '',
                }}
              />
            </div>
          </div>
        </ModuleTitle>

        {personLatitudeOverallAnalysisList?.length > 0 && (
          <ModuleTitle
            title="个人整体概况"
            wrapperClass={printMode ? 'typePrint' : ''}
            id={BLOCK_F_STRING['Overall Overview']}
            description="Overall Overview"
          >
            <div className={styles['card-wrapper']}>
              <div className={styles.overall}>
                {personLatitudeOverallAnalysisList.map((v, i) => (
                  <div className={styles['overall-list-item']} key={i}>
                    <div className={styles.left}>
                      <div className={styles.round} />
                      <div className={styles.label}>
                        {i + 1 >= 10 ? i + 1 : `0${i + 1}`} {v.latitudeTitle}
                      </div>
                      <div className={styles.line} />
                    </div>
                    <div className={styles.right}>
                      <div className={styles.score}>
                        {v.score} / {v.totalScore}
                      </div>
                      <div>{v.latitudeLevelTitle}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {personLatitudeOverallAnalysisList.map((v, index) => (
              <div
                key={index}
                className={`${styles['card-wrapper']} ${styles.overview}`}
              >
                {renderFirstLevel(v)}
                <div className={styles['result-info']}>
                  <p>从测评结果来看：</p>
                  <div
                    className={styles.content}
                    dangerouslySetInnerHTML={{ __html: v.result }}
                  />
                </div>
                <div className={styles['chart-wrapper']}>
                  <ChartCard
                    type="person"
                    data={v}
                    fieldNames={{
                      degreeDistributionList: 'degreeDistributionList',
                      levelSuggest: 'suggestDesc',
                    }}
                    labelNames={{
                      levelSuggest: '★ 整体建议说明',
                    }}
                  />
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}

        {relation?.length > 0 && (
          <ModuleTitle
            title="个人整体分析"
            wrapperClass={printMode ? 'typePrint' : ''}
            id={BLOCK_F_STRING['Analysis']}
            description="Analysis"
          >
            {relation?.map((v: any, i: number) => (
              <div className={`${styles['card-wrapper']} ${styles['analysis-wrapper']}`} key={i}>
                {v.latitudeId && renderFirstLevel(v)}
                <div className={styles.analysis}>
                  {v.overallAnalysis?.length > 0 ? (
                    <>
                      <ModuleSubtitle title="维度得分情况" />
                      <ol>
                        {v.overallAnalysis.map((item: any, index: number) => (
                          <ScoreSituationItem
                            color={getColorValue(item, latitudeLevelList).color}
                            key={`scoreSituationItem_key_${index}`}
                            data={item}
                          />
                        ))}
                      </ol>
                    </>
                  ) : null}
                  <ModuleSubtitle title="具体情况分析" />
                  {v.caseAnalysis.map((item: any) => (
                    <CaseAnalysisCard
                      key={item.latitudeId}
                      color={getColorValue(item, latitudeLevelList).color}
                      data={item}
                      type="person"
                      fieldNames={{
                        latitudeDescription: 'latitudeDescription',
                        levelDescription: 'levelDescription',
                        chartSituation: 'chartSituation',
                        levelSuggest: 'levelSuggest',
                      }}
                    />
                  ))}
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}

        {identify?.length > 0 && (
          <ModuleTitle
            title="测评鉴别分析"
            id={BLOCK_F_STRING['identify']}
            description="identify"
          >
            {identify.map((v, index) => (
              <div key={index} className={`${styles['card-wrapper']}`}>
                {renderFirstLevel(v)}
                <div className={styles['result-info']}>
                  <p>从测评结果来看：</p>
                  <div
                    className={styles.content}
                    dangerouslySetInnerHTML={{ __html: v.result! }}
                  />
                </div>
                <div className={styles['chart-wrapper']}>
                  <ChartCard
                    type="person"
                    data={v}
                    fieldNames={{
                      degreeDistributionList: 'degreeDistributionList',
                      levelSuggest: 'levelSuggest',
                    }}
                    labelNames={{
                      levelSuggest: '★ 整体建议说明',
                    }}
                  />
                </div>
              </div>
            ))}
          </ModuleTitle>
        )}

        <ModuleTitle
          title="附加说明"
          id={BLOCK_F_STRING.additional}
          description="additional"
        >
          <div className={styles['card-wrapper']}>
            <div className={styles.additional}>
              <div
                className={styles.desc}
                dangerouslySetInnerHTML={{
                  __html: (render && commonData?.extDesc) || '',
                }}
              />
            </div>
          </div>
        </ModuleTitle>
      </Layout>
      {!printMode && (
        <SideNavigation
          id="navigation"
          offset={45}
          className={styles['side-navigation']}
          list={getNavList(
            {
              overallAnalysis: personLatitudeOverallAnalysisList,
              relation,
              identify,
            },
            { type: 'person' }
          )}
          renderBottom={() => {
            return (
              <DownloadPDF
                wrapperClassName={styles.download}
                label="报告下载"
              />
            );
          }}
        />
      )}
      <img className={styles.footer} src={FooterBg} />
    </div>
  );
};

export const getServerSideProps = async (ctx: any) => {
  const { recordId: evaluationRecordId, initRecordId, ff, isPrint } = ctx.query;
  const printMode = !!(ff || isPrint);
  try {
    const params = {
      evaluationRecordId,
      initRecordId,
    };
    const [overallOverviewRes, basicRes, caseRes, configRes] =
      await Promise.all([
        api.getUserOverallAnalysis(params, ctx),
        api.getUserBasicAnalysis(params, ctx),
        api.getUserCaseAnalysis(params, ctx),
        api.getReportConfigByRecord(params, ctx),
      ]);

    let commonData = {};
    if (basicRes && basicRes.success) {
      const { templateVersionId } = basicRes.data;
      const commonRes = await api.getUserCommonModule(
        { templateVersionId, reportType: 1, initRecordId },
        ctx
      );
      if (commonRes && commonRes.success) {
        commonData = commonRes.data;
      }
    }

    return {
      props: {
        basicData: basicRes.data || {},
        overallData: overallOverviewRes.data || {},
        caseData: caseRes.data || [],
        commonData,
        printMode,
        config: configRes.data || {},
        error: null,
      },
    };
  } catch (error) {
    return {
      props: {
        basicData: {},
        overallData: {},
        caseData: [],
        commonData: {},
        config: {},
        printMode,
        error,
      },
    };
  }
};

export default PersonReport;
