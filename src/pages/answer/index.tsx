import React, { useEffect, useState, useRef, useMemo } from 'react';
import { useRouter } from 'next/router';
import { useRequest, useSetState } from 'ahooks';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import type {
  GetEvaluationInfo,
  GetEvaluationByType,
} from '~/service/psychology/evaluation/types';
import api from '~/service/psychology/evaluation';
import commonApi from '@/service/common/index';
import { UserBaseInfo } from '@/service/common/types';
import AnswerPC from '@/lib/answer/platforms/pc';
import AnswerH5 from '@/lib/answer/platforms/h5';

import { CLIENT_TYPE } from '~/lib/answer/shared/types';

interface IAnswerProps {
  basicData: GetEvaluationInfo.Response['data'] & GetEvaluationByType.Response['data'];
}

const Answer: React.FC<IAnswerProps> = (props) => {
  const router = useRouter();

  const { basicData } = props || {};
  const { evaluationTaskId, answerTaskId, clientType = CLIENT_TYPE.pc, evaluationTemplateType } :any =
    router?.query || {};
  const oApi = !!evaluationTemplateType ? api.getEvaluationByTypeClient : api.getEvaluationInfoClient;

  const { data: { data: userInfo } = {}, error } = useRequest<
    UserBaseInfo.Response,
    any
  >(commonApi.getUCBaseInfo);

  const { data: { data: basicState } = {}, run: fetchEvaluationInfo } =
    useRequest<any, any>(oApi, {
      manual: true,
    });

  
  const basic = basicState || basicData || {};
  const { answerInfo } = basic || {};
  const { userRecordId } = answerInfo || {};

  if(typeof window !== 'undefined'){
    console.log('basic:', basic)
  }
  const onReStart = async () => {
    await api.cleanUserRecord({
      userRecordId,
    });
    fetchEvaluationInfo({
      evaluationTemplateType,
      answerTaskId,
      evaluationTaskId,
      clientType,
    });
  };


  if (clientType === CLIENT_TYPE.h5 && userInfo?.userId) {
    return <AnswerH5 basic={basic} onReStart={onReStart} userInfo={userInfo}  clientType={clientType}/>;
  }
  return userInfo?.userId ? <AnswerPC basic={basic} onReStart={onReStart} userInfo={userInfo} clientType={clientType} /> : <></>;
};

export const getServerSideProps: GetServerSideProps<{
  basicData: any;
}> = async (ctx: any) => {
  try {
    const {
      answerTaskId,
      evaluationTaskId,
      clientType = '1',
      evaluationTemplateType,
    } = ctx.query;
    const oApi = !!evaluationTemplateType ? api.getEvaluationByType : api.getEvaluationInfo;

    const { data: basicData } = await oApi(
      {
        evaluationTemplateType,
        answerTaskId,
        evaluationTaskId,
        clientType,
      },
      ctx
    );
    return { props: { basicData } };
  } catch (error) {
    return {
      props: { basicData: {} },
    };
  }
};

export default Answer;
