// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import type { NextApiRequest, NextApiResponse } from 'next';

type IResponse = {
  msg: string;
  success: boolean;
  code: number;
};

export default function handler(
  _req: NextApiRequest,
  res: NextApiResponse<IResponse>
) {
  res.statusCode = 200;
  res.setHeader("Content-Type", "application/json");
  res.status(200).json({code: 200, msg: 'ok',success: true})
  res.end(JSON.stringify({ status: "ok" }));
  // res.status(200).end('ok');
}