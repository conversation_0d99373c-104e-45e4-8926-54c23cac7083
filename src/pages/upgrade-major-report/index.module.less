@viewportMWidth: 375px;

.layout-wrapper {
  background-color: #fff;
  .body{
    position: relative;
    background-color: #f3f4f8;
  }
  :global {
    .ant-tabs-nav {
      margin-bottom: 13px !important;
      &::before {
        display: none;
      }
      .ant-tabs-tab{
        height: auto;
      }
    }
    .ant-tabs-tab-btn {
      line-height: 1.5;
      transition: none !important;
      text-shadow: none !important;
    }
    .ant-tabs-tab {
      transition: none !important;
      padding: 0 !important;
      background-color: transparent !important;
      margin-right: 24px !important;
      height: 14px;
      font-weight: 400;
      font-size: 14px;
      color: #5b6f8c !important;
      line-height: 14px;
      border: none !important;
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn{
      font-weight: 600;
      font-size: 15px;
      color: #5a8bff !important;
      line-height: 15px;
    }
    
    .ant-table {
      border-radius: 4px;
      overflow: hidden;
      .ant-table-thead {
        .ant-table-cell {
          background-color: #f9fafc;
          color: #8b97b6;
          &::before {
            display: none;
          }
        }
      }
      .ant-table-tbody {
        .ant-table-cell {
          color: #50596f;
          background-color: #fff;
          &:hover {
            background-color: #fff;
          }
        }
      }
    }
  }
}

.pc-wrapper {
  :global{
    .ant-table {
      border-top: 1px solid #e1e6f2;
      border-left: 1px solid #e1e6f2;
      border-right: 1px solid #e1e6f2;
    }
    .ant-table-tbody > tr > td,
    .ant-table-thead > tr > th{
      border-bottom: 1px solid #e1e6f2 !important;
    }
    .ant-pagination-item-active{
      border: none;
      background: #5A8BFF;
      > a{
        color: #fff;
      }
      &:hover{
        > a{
         color: #fff !important;
        }
       }
    }
    .ant-pagination-item{
      &:hover{
        > a{
         color: #5A8BFF;
        }
       }
    }
    .ant-pagination-prev > button,
    .ant-pagination-item,
    .ant-pagination-next > button{
      border-radius: 4px;
      border-color: #E1E6F2;
    }

  }
  .error{
    width: 100vw;
    height: calc(100vh - 171px);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .error-pic{
      width: 128px;
    }
    .error-text{
      font-weight: 400;
      font-size: 14px;
      color: #6b7884cc;
      margin-top: 2px;
      margin-bottom: 8px;
    }
    .error-btn{
      cursor: pointer;
      width: 116px;
      height: 32px;
      background: #2E86FF;
      border-radius: 100px;
      border-radius: 16px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
  }
  .header{
    width: 100%;
    height: 240px;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  .header-info{
    position: relative;
    z-index: 1;
    width: 1000px;
    margin: 0 auto;
    padding: 48px 0 40px;
    .title{
      height: 60px;
      font-weight: 700;
      font-size: 60px;
      color: #FFFFFF;
      line-height: 60px;
    }
    .info{
      height: 20px;
      opacity: 0.8;
      font-weight: 400;
      font-size: 20px;
      color: #FFFFFF;
      line-height: 20px;
      margin-top: 20px;
    }
  }
  
  .block-wrapper {
    position: relative;
    z-index: 5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .side-navigation{
    position: absolute;
    margin: 0;
    top: 0;
    width: 180px;
    margin: 0 -700px 0;
    border-radius: 12px;
    :global{
      .ant-timeline-item-content{
        font-size: 15px;
        margin-left: 18px;
        min-height: auto;
      }
    }
  }
}
.h5-wrapper {
  min-height: 100vh;
  width: 100vw;
  .error{
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .error-pic{
      width: 116px;
    }
    .error-text{
      font-weight: 400;
      font-size: (14px / @viewportMWidth) * 100vw;
      color: #6B7884;
      margin-top: (12px / @viewportMWidth) * 100vw;
      margin-bottom: (16px / @viewportMWidth) * 100vw;
    }
    .error-btn{
      cursor: pointer;
      font-size:(13px / @viewportMWidth) * 100vw ;
      width: (108px / @viewportMWidth) * 100vw;
      height: (32px / @viewportMWidth) * 100vw;
      background: #2E86FF;
      border-radius: (16px / @viewportMWidth) * 100vw;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
  }
  :global {
    .adm-nav-bar{
      width: 100vw;
      color: inherit;
    }
    .adm-nav-bar-left {
      flex: 0 1 auto;
      .adm-nav-bar-back {
        margin-right: 0;
      }
    }
    .adm-nav-bar-title {
      text-align: left;
    }
    .ant-table.ant-table-middle{
      font-size: 13px;
    }
  }
  .body{
    .h5-header-nav{
      position: relative;
      z-index: 101;
      color: #fff;
      transition: .5s;
      &.fixed{
        position: fixed;
        top: 0;
        left: 0;
        z-index: 101;
        color: #000;
        background-color: #fff;
      }
    }
  }
  .header{
    width: 100vw;
    min-height: max-content;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  .header-info{
    position: relative;
    z-index: 1;
    margin: 0 auto;
    padding: (12px / @viewportMWidth) * 100vw (16px / @viewportMWidth) * 100vw (20px / @viewportMWidth) * 100vw;
    .title{
      font-weight: 700;
      font-size: 23px;
      color: #FFFFFF;
    }
    .info{
      height: (20px / @viewportMWidth) * 100vw;
      font-weight: 400;
      font-size: 12px;
      color: #FFFFFF;
      margin-top: (12px / @viewportMWidth) * 100vw;
    }
  }
  .block-wrapper {
    position: relative;
    z-index: 5;
    margin-top: (12px / @viewportMWidth) * 100vw;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 (12px / @viewportMWidth) * 100vw;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}
