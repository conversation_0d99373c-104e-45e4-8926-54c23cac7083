import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import { NavBar } from 'antd-mobile';
import moment from 'moment';
import { CLIENT_TYPE } from '~/lib/answer/shared/types';
import PcTopBg from '@/assets/upgrade-major-report/pc-top-bg.png';
import H5TopBg from '@/assets/upgrade-major-report/h5-top-bg.png';
import ErrorPic from '@/assets/upgrade-major-report/error.png';

import { useSafeHeight } from '@/hooks';
import type { GetBaseReportInfo } from '~/service/psychology/upgradeMajorReport/types';
import api from '~/service/psychology/upgradeMajorReport';
import Header from '@/components/career/Header';
import TabNavBar from '@/components/TabNavBar';
import SideNavigation from '@/components/career/SideNavigation';
import useReport from '@/lib/upgrade-major-report/shared/hooks/useReport';
import { BLOCK_F_STRING } from '@/lib/upgrade-major-report/shared/types';

import Overview from '@/lib/upgrade-major-report/shared/components/Overview';
import SuitableProfession from '@/lib/upgrade-major-report/shared/components/SuitableProfession';
import ProfessionalPotential from '@/lib/upgrade-major-report/shared/components/ProfessionalPotential';
import VocationalInterest from '@/lib/upgrade-major-report/shared/components/VocationalInterest';
import ProfessionalValues from '@/lib/upgrade-major-report/shared/components/ProfessionalValues';
import ProfessionalDecisionMakingEfficacy from '@/lib/upgrade-major-report/shared/components/ProfessionalDecisionMakingEfficacy';
import Conclusion from '@/lib/upgrade-major-report/shared/components/Conclusion';


import styles from './index.module.less';

interface IUpgradeMajorReportProps {
  basicData: GetBaseReportInfo.Data;
  error: boolean;
  code: string | number;
}

const UpgradeMajorReport: React.FC<IUpgradeMajorReportProps> = (props) => {
  const router = useRouter();

  const [safeHeight = 0] = useSafeHeight({ position: 'top' });

  const { basicData, error = false, code } = props;

  const { clientType = CLIENT_TYPE.pc }: any = router?.query || {};

  

  const { state, methods } = useReport(basicData, { clientType, safeHeight });

  const onScroll = () => {
    if(clientType === CLIENT_TYPE.pc){
      const offsetN = 40
      const pcO = document.getElementById(BLOCK_F_STRING.overview)!;
      const pcNavigation = document.getElementById('navigation')!;
      if (!pcNavigation) return;
      if(pcO.getBoundingClientRect().top <= offsetN){
        pcNavigation.style.position = 'fixed';
        pcNavigation.style.top = '40px';
      }else{
        pcNavigation.style.position = 'absolute';
        pcNavigation.style.top = '0px';
      }
    }
    if(clientType === CLIENT_TYPE.h5){
      const element = document.getElementById('tabBar');
      const headerNav = document.getElementById('header-nav')!;
      if (!element) return;
      const rect = element.getBoundingClientRect();
      const stickyThreshold = headerNav.offsetHeight + safeHeight;
      const cIsFixed = rect.top <= stickyThreshold ? true : false;
      if (cIsFixed) {
        headerNav.classList.add(`${styles['fixed']}`)
      } else {
        headerNav.classList.remove(`${styles['fixed']}`)
      }
    }
  };


  useEffect(() => {
    if(typeof window !== 'undefined'){
      console.log('props--: ', props)
      console.log('basicData--:', basicData);
    }
    window.addEventListener('scroll', onScroll);
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, [safeHeight]);

  const {
    hasSuitableProfession,
    list,
  } = state;

  const block = (
    <div className={styles['block-wrapper']}>
      {
         clientType === CLIENT_TYPE.pc &&  <SideNavigation id="navigation" offset={45} className={styles['side-navigation']} list={list} />
      }
      <Overview clientType={clientType} methods={methods} state={state} />
      {hasSuitableProfession && (
        <SuitableProfession
          state={state}
          methods={methods}
          clientType={clientType}
          basicData={basicData}
        />
      )}
      {Object.keys(basicData?.academicPotentialRecommend || {}).length > 0 && (
        <ProfessionalPotential
          state={state}
          data={basicData?.academicPotentialRecommend}
          clientType={clientType}
          basicData={basicData}
        />
      )}
      {Object.keys(basicData?.categoryMajorRecommend || {}).length > 0 && (
        <VocationalInterest
          state={state}
          data={basicData.categoryMajorRecommend}
          clientType={clientType}
          basicData={basicData}
        />
      )}
      {Object.keys(basicData?.occupationalValuesRecommend || {}).length > 0 && (
        <ProfessionalValues
          state={state}
          data={basicData?.occupationalValuesRecommend}
          clientType={clientType}
          basicData={basicData}
        />
      )}
      {Object.keys(basicData?.decisionMakingEfficacyRecommend || {}).length > 0 && (
        <ProfessionalDecisionMakingEfficacy
          state={state}
          data={basicData?.decisionMakingEfficacyRecommend}
          clientType={clientType}
          basicData={basicData}
        />
      )}
      <Conclusion
        state={state}
        clientType={clientType}
        data={state}
        basicData={basicData}
      />
    </div>
  );

  const onBack = () => {
    if (window?.mstJsBridge.isInMstApp()) {
      window?.mstJsBridge.closeWebview();
    } else {
      history.back();
    }
  };

  const codeToMassage:any = {
    1: {
      desc: '报告正在生成中，可以点击按钮获取报告',
      btnText: '获取报告',
    },
    3: {
      desc: '报告生成失败，可以点击按钮重新生成',
      btnText: '重新生成',
    },
    '-1': {
      desc: '报告生成失败，可以点击按钮重新生成',
      btnText: '重新生成',
    }
  }
  /** H5 */
  if (clientType === CLIENT_TYPE.h5) {
    return (
      <div className={`${styles['h5-wrapper']} ${styles['layout-wrapper']}`}>
        {error ? (
          <div className={styles.body}>
            {/* 初始化导航栏高度 45 */}
            <div style={{ height: safeHeight + 45 }}>
              <div
                id="header-nav"
                className={`${styles['h5-header-nav']} ${styles['fixed']}`}
              >
                <div style={{ height: safeHeight }} />
                <NavBar onBack={onBack}>测评报告</NavBar>
              </div>
            </div>
            <div className={styles.error} style={{
              height: `calc(100vh - 45px - ${safeHeight}px)`
            }}>
              <img
                src={ErrorPic}
                alt="top-bg"
                className={styles['error-pic']}
              />
              <div className={styles['error-text']}>
                {codeToMassage[code].desc}
              </div>
              <div
                className={styles['error-btn']}
                onClick={() => window.location.reload()}
              >
                 {codeToMassage[code].btnText}
              </div>
            </div>
          </div>
        ) : (
          <div className={styles.body}>
            <div style={{ height: safeHeight + 45 }}>
              <div id="header-nav" className={`${styles['h5-header-nav']}`}>
                <div style={{ height: safeHeight }} />
                <NavBar onBack={onBack}>测评报告</NavBar>
              </div>
            </div>
            <img
              style={{ height: safeHeight + 175 }}
              src={H5TopBg}
              alt="top-bg"
              className={styles.header}
            />
            <div className={styles['header-info']}>
              <div className={styles.title}>升学专业选择测评</div>
              <div className={styles.info}>
                {basicData?.realName ? (
                  <>姓名：{basicData?.realName} &nbsp; &nbsp;</>
                ) : (
                  ''
                )}
                {
                  basicData.finishTime ? <>
                   测评时间：
                   {moment(+basicData.finishTime).format('YYYY-MM-DD HH:mm')}
                  </> : ''
                }
              </div>
            </div>
            <TabNavBar list={list} />
            {block}
          </div>
        )}
      </div>
    );
  }
  return (
    <div className={`${styles['pc-wrapper']} ${styles['layout-wrapper']}`}>
      <Header />
      {error ? (
        <div className={styles.body}>
          <div className={styles.error}>
            <img src={ErrorPic} alt="top-bg" className={styles['error-pic']} />
            <div className={styles['error-text']}> {codeToMassage[code].desc}</div>
            <div
              className={styles['error-btn']}
              onClick={() => window.location.reload()}
            >
             {codeToMassage[code].btnText}
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className={styles.body}>
            <img src={PcTopBg} alt="top-bg" className={styles.header} />
            <div className={styles['header-info']}>
              <div className={styles.title}>升学专业选择测评</div>
              <div className={styles.info}>
                {basicData?.realName ? (
                  <>姓名：{basicData?.realName} &nbsp; &nbsp;</>
                ) : (
                  ''
                )}
                {
                  basicData.finishTime ? <>
                   测评时间：
                   {moment(+basicData.finishTime).format('YYYY-MM-DD HH:mm')}
                  </> : ''
                }
              </div>
            </div>
            {block}
          </div>
        </>
      )}
    </div>
  );
};

export const getServerSideProps: GetServerSideProps<{
  basicData: any;
}> = async (ctx: any) => {
  try {
    const {
      userRecordId,
      clientType = CLIENT_TYPE.pc,
    } = ctx.query;

    const { data: basicData } = await api.getBaseReportInfo(
      {
        userRecordId,
        clientType,
      },
      ctx
    );
    const { reportStatus } = (basicData || {}) as GetBaseReportInfo.Data;

    return { props: { basicData: basicData || {}, error: [1, 3].includes(reportStatus),  code: reportStatus || 0 } };
  } catch (error) {
    console.log(error)
    return {
      props: { basicData: {}, error: true, code: -1 },
    };
  }
};

export default UpgradeMajorReport;
