import type { NextPage } from 'next';
import Head from 'next/head';
import styles from '../styles/Home.module.css';

const Home: NextPage = () => {
	return (
		<div className={styles.container}>
			<Head>
				<title>升学e网通,升学助考一网通</title>
				<meta name="keywords" content="升学e网通,升学助考一网通" />
				<meta name="description" content="升学e网通,升学助考一网通" />
				<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" />
				<meta name="renderer" content="webkit" />
				<link rel="icon" href="//web.ewt360.com/favicon.ico" type="image/ico"/>
			</Head>
			<main className={styles.main}>测评报告</main>
		</div>
	);
};

export default Home;
