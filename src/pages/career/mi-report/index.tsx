import { useEffect, useState } from 'react';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { Tooltip, message } from 'antd';
import moment from 'moment';
import { TQueryParams, MIReportResponse, majorItem } from '@/service/career/types';
import API from '@/service/career';
import Header from '~/components/career/Header';
import Banner from '~/components/career/Banner';
import Card from '~/components/career/Card';
import CardIconTitle from '~/components/career/CardIconTitle';
import ChartBar from '~/components/career/ChartBar';
import ProfessionalTable from '~/components/career/ProfessionalTable';
import VideoBar from '~/components/career/VideoBar';
import SideNavigation from '~/components/career/SideNavigation';
import headerBanner from "~/assets/career/MI-head-banner.png";
import footerBanner from "~/assets/career/MI-footer-banner.png";
import { goToPermissionPage } from '~/utils/tools';
import { web } from '~/utils/hosts';
import styles from './index.module.less';

export const getServerSideProps: GetServerSideProps<{
  baseInfo: MIReportResponse;
}> = async (ctx: any) => {
  const { userRecordId, clientType, secretSign } = ctx.query as TQueryParams;
  try {
    const reportResponse = await API.getMIReport({ userRecordId, clientType, secretSign }, ctx)
    return { props: { baseInfo: { ...reportResponse } } };
  } catch (error: any) {
    console.log('MI-report', error);
    return { props: { baseInfo: { ...error } } };
  }
}

const Page = ({
  baseInfo,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const navPageList = [
    { label: "报告详情", key: "detail" },
    { label: "专业推荐", key: "recommend" },
    { label: "关于测评", key: "about" },
  ];
  const [reportInfo] = useState({ ...baseInfo.data });
  const [latitudeList, setLatitudeList] = useState<any[]>([]);
  const [majorList, setMajorList] = useState<any[]>([]);

  // 专业列表
  const getMajorList = async (majorIdList: number[]) => {
    try {
      const { data } = await API.getMajorList({ majorIdList });
      const list = data.map((v) => ({
        id: v.majorId,
        name: v.majorName,
        categoryName: v.categoryName,
        hasCollect: v.hasCollect,
      }));
      setMajorList(list);
    } catch (e) {
      console.log("getMajorList", e);
    }
  };

  // 获取报告
  const initReprot = async () => {
    const { code, msg, success } = baseInfo;
    if (code && ['9091401', '9091402', '9091403'].indexOf(code) !== -1) {
      goToPermissionPage({
        code,
        msg
      })
      return
    }

    if (!success) {
      message.error(msg)
      return
    }

    const {
      recommendMajorIdList = [],
      veryGoodAts = [],
      relativelyGoodAts = [],
      notVeryGoodAts = [],
      basicallyIncompetents = [],
    } = reportInfo;

    veryGoodAts?.map((v) => (v.result = '十分擅长'));
    relativelyGoodAts?.map((v) => (v.result = '比较擅长'));
    notVeryGoodAts?.map((v) => (v.result = '不太擅长'));
    basicallyIncompetents?.map((v) => (v.result = '基本不擅长'));

    const list = veryGoodAts?.concat(
      relativelyGoodAts,
      notVeryGoodAts,
      basicallyIncompetents
    );
    setLatitudeList(list);
    getMajorList(recommendMajorIdList);
  };

  // 收藏专业/职业
  // 内容类型: 2、院校 3、职业精选 4、专业 5 职业宝库
  const goAddCollect = async (item: { id: string; }) => {
    try {
      await API.addCollect({
        contentId: item.id,
        contentType: 4,
      });
      const list = majorList.map((v) =>
        v.id === item.id ? { ...v, hasCollect: true } : v
      );
      setMajorList(list);
      message.success("收藏成功", 1);
    } catch (e) {
      console.log("goAddCollect", e);
    }
  };

  // 取消收藏专业/职业
  // 内容类型: 专业 majorId, 院校 collegeId，职业精选 occupationPerusalId
  const goCancelCollect = async (item: { id: string; }) => {
    try {
      await API.cancelCollect({
        contentId: item.id,
        contentType: 4,
      });
      const list = majorList.map((v) =>
        v.id === item.id ? { ...v, hasCollect: false } : v
      );
      setMajorList(list);
      message.success("已取消收藏", 1);
    } catch (e) {
      console.log("goCancelCollect", e);
    }
  };

  // 收藏 取消收藏专业/职业
  const handleToggleCollect = (item: any) => {
    if (item.hasCollect) {
      goCancelCollect(item);
    } else {
      goAddCollect(item);
    }
  };

  const columns = [
    { title: "维度", dataIndex: "latitudeName" },
    { title: "结果", dataIndex: "result" },
    { title: "特征", dataIndex: "desc" },
  ];

  const majorColumns = [
    {
      title: "专业名称",
      dataIndex: "name",
      width: 390,
      render: (_: any, record: { id: string; name: string; }) => (
        <span key={record.id}>
          <a
            href={`${web}/career/careerlibs/index.html#/allLibs/majorDetail?majorId=${record.id}`}
            target="_blank"
          >
            {record.name}
          </a>
        </span>
      ),
    },
    {
      title: "专业大类",
      dataIndex: "categoryName",
      width: 390,
      render: (_: any, record: { categoryName: string; }) => (record.categoryName && record.categoryName.length >= 26 ?
        <Tooltip placement="top" title={record.categoryName}>
          <span>{record.categoryName}</span>
        </Tooltip> : record.categoryName
      ),
    },
    {
      title: "操作",
      dataIndex: "options",
      render: (_: any, record: { hasCollect: boolean; }) => (
        <a onClick={() => handleToggleCollect(record)}>
          {record.hasCollect && "已"}收藏
        </a>
      ),
    },
  ];

  useEffect(() => {
    initReprot();
  }, []);

  return reportInfo && Object.keys(reportInfo).length > 0 ?
    <div className={styles['mi-report-page']}>
      <Header title={`${reportInfo.evaluationEnglishName}-${reportInfo.reportTitle}`} />
      <Banner src={headerBanner} height="240" />
      <div className={styles.container} id={navPageList[0].key}>
        <div className={styles.head}>
          <div className={styles.title}>{`${reportInfo.evaluationEnglishName}-${reportInfo.reportTitle}`}</div>
          <div className={styles.time}>
            <span className={styles.name}>个人报告</span>
            <span>
              报告时间：
              {moment(reportInfo.reportTime).format("YYYY-MM-DD HH:mm")}
            </span>
          </div>
        </div>
        <Card>
          <CardIconTitle icon iconType="half" title="多元智能概况" />
          <div className={styles["content-box"]}>
            <ChartBar
              list={reportInfo.latitudeInfoList}
              barStyle={[
                {
                  backgroundImage:
                    "linear-gradient(-44deg, #77D4FF 0%, #0081FD 100%)",
                  boxShadow: "0 8px 16px 0 rgba(0,129,253,0.40)",
                },
                {
                  backgroundImage:
                    "linear-gradient(-44deg, #77D4FF 0%, #0081FD 100%)",
                  boxShadow: "0 8px 16px 0 rgba(0,129,253,0.40)",
                },
              ]}
            />
          </div>
          <ProfessionalTable
            rowKey="latitudeCode"
            columns={columns}
            dataSource={latitudeList}
            pagination={false}
            bordered
          />
        </Card>
        <Card>
          <CardIconTitle icon iconType="half" title="优势智能" />
          { reportInfo.advantageIntelligences?.map((v, i) => (
              <div key={i} className={styles["advantage-box"]}>
                <div
                  className={styles["icon-img"]}
                  style={{
                    background: `url(${v.pcImageUrl}) center no-repeat`,
                    backgroundSize: "100%",
                  }}
                />
                <div className={styles["advantage-content"]}>
                  <div className={styles.title}>
                    {v.name}({v.englishName})
                  </div>
                  <div className={styles.description} dangerouslySetInnerHTML={{ __html: v.desc }} />
                  <div className={styles.subtitle}>优势分析：</div>
                  <div
                    className={styles.description}
                    dangerouslySetInnerHTML={{
                      __html: v.advantageAnalysis,
                    }}
                  />
                  <div className={styles.subtitle}>训练秘诀：</div>
                  <div className={styles.description} dangerouslySetInnerHTML={{ __html: v.trainingSecret }} />
                  <div className={styles.subtitle}>职业启示：</div>
                  <div className={styles.description} dangerouslySetInnerHTML={{ __html: v.jonRevelation }} />
                </div>
              </div>
            ))}
        </Card>
        <Card id={navPageList[1].key}>
          <CardIconTitle icon iconType="half" title="专业推荐" />
          <ProfessionalTable
            rowKey="id"
            columns={majorColumns}
            dataSource={majorList}
            pagination={false}
            bordered
          />
        </Card>
        <Card id={navPageList[2].key}>
          <CardIconTitle icon iconType="half" title="测评介绍" />
          <div className={styles["content-box"]}>
            <div
              className={styles.description}
              dangerouslySetInnerHTML={{ __html: reportInfo?.introduction }}
            />
          </div>
        </Card>
        <Card wrapperStyle={{ marginBottom: 40 }}>
          <CardIconTitle icon iconType="half" title="解释说明" />
          <div className={styles["content-box"]}>
            <div
              className={styles.description}
              dangerouslySetInnerHTML={{ __html: reportInfo.explain }}
            />
          </div>
        </Card>
        <Banner src={footerBanner} height="80" />
        <VideoBar data={{ courseId: reportInfo.courseId, lessonId: reportInfo.lessonId }} />
        <SideNavigation list={navPageList} />
      </div>
    </div> : null
}

export default Page;