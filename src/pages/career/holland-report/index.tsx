import { useEffect, useState } from 'react';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { Row, Col, message, Tooltip } from 'antd';
import moment from 'moment';
import { TQueryParams, hollandReportResponse, majorItem, occupationItem } from '@/service/career/types';
import API from '@/service/career';
import headerBanner from "@/assets/career/Holland-head-banner.png";
import footerBanner from "@/assets/career/Holland-footer-banner.png";
import Banner from "@/components/career/Banner";
import Header from "@/components/career/Header";
import Card from "@/components/career/Card";
import CardIconTitle from "@/components/career/CardIconTitle";
import ChartRadar from "@/components/career/ChartRadar";
import CapsuleTabs from "@/components/career/CapsuleTabs";
import ProfessionalTable from "@/components/career/ProfessionalTable";
import SideNavigation from "@/components/career/SideNavigation";
import VideoBar from "@/components/career/VideoBar";
import { web } from "@/utils/hosts";
import { goToPermissionPage } from '@/utils/tools'
import styles from './index.module.less'

export const getServerSideProps: GetServerSideProps<{
  baseInfo: hollandReportResponse;
}> = async (ctx: any) => {
  const { userRecordId, clientType, secretSign } = ctx.query as TQueryParams;
  try {
    const reportResponse = await API.getHollandReport({ userRecordId, clientType, secretSign }, ctx)
    return { props: { baseInfo: { ...reportResponse }} };
  } catch (error: any) {
    console.log('cvt-report', error);
    return { props: { baseInfo: { ...error }} };
  }
}

const Page = ({
  baseInfo,
} : InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const [reportInfo] = useState({...baseInfo.data});
  const [currentTabsIndex, setCurrentTabsIndex] = useState(0);
  const [majorList, setMajorList] = useState<any[]>([]);
  const [occupationList, setOccupationList] = useState<any[]>([]);
  const navPageList = [
    { label: "报告详情", key: "detail" },
    { label: "专业职业推荐", key: "recommend" },
    { label: "关于测评", key: "about" },
  ];

  // 专业列表
  const getMajorList = async (majorIdList: number[] ) => {
    try {
      const { data } = await API.getMajorList({ majorIdList });
      const list = data.map((v: majorItem) => ({
        id: v.majorId,
        name: v.majorName,
        categoryName: v.categoryName,
        hasCollect: v.hasCollect,
      }));
      setMajorList(list);
    } catch (e) {
      console.log("getMajorList", e);
    }
  };

  // 职业列表
  const getCollectOccupationList = async (occupationIdList: number[]) => {
    try {
      const { data } = await API.getOccupationList({ occupationIdList });
      const list = data.map((v: occupationItem) => ({
          id: v.occupationId,
          name: v.occupationName,
          categoryName: v.categoryName,
          hasCollect: v.hasCollect,
      }));
      setOccupationList(list);
    } catch (e) {
      console.log("getCollectOccupationList", e);
    }
  };

  // 收藏专业/职业
  // 内容类型: 2、院校 3、职业精选 4、专业 5 职业宝库
  const goAddCollect = async (item: { id: any; key: string; }) => {
    try {
      const params = {
        contentId: item.id,
        contentType: item.key === "major" ? 4 : 3,
      };
      await API.addCollect(params);
      const list = (item.key === "major" ? majorList : occupationList).map(
        (v) => (v.id === item.id ? { ...v, hasCollect: true } : v)
      );
      if (item.key === "major") {
        setMajorList(list);
      } else {
        setOccupationList(list);
      }
      message.success("收藏成功", 1);
    } catch (e) {
      console.log("goAddCollect", e);
    }
  };

  // 取消收藏专业/职业
  // 内容类型: 专业 majorId, 院校 collegeId，职业精选 occupationPerusalId
  const goCancelCollect = async (item: { id: any; key: string; }) => {
    try {
      const params = {
        contentId: item.id,
        contentType: item.key === "major" ? 4 : 3,
      };
      await API.cancelCollect(params);
      const list = (item.key === "major" ? majorList : occupationList).map(
        (v) => (v.id === item.id ? { ...v, hasCollect: false } : v)
      );

      if (item.key === "major") {
        setMajorList(list);
      } else {
        setOccupationList(list);
      }
      message.success("已取消收藏", 1);
    } catch (e) {
      console.log("goCancelCollect", e);
    }
  };

  const initReprot = () => {
    const { data, code, msg, success } = baseInfo;
    if (code && ['9091401', '9091402', '9091403'].indexOf(code) !== -1) {
      goToPermissionPage({
        code,
        msg
      })
      return
    }

    if (!success) {
      message.error(msg)
      return
    }

    const { recommendMajorIdList, occupationInfoList } = data;
    getMajorList(recommendMajorIdList);
    getCollectOccupationList(occupationInfoList);
  }

  // 收藏 取消收藏专业/职业
  const handleToggleCollect = (item: any) => {
    if (item.hasCollect) {
      goCancelCollect(item);
    } else {
      goAddCollect(item);
    }
  };

  const handleCapsuleTabChange = (index: number) => {
    setCurrentTabsIndex(index);
  };

  const majorColumns = [
    {
      title: "专业名称",
      dataIndex: "name",
      width: 400,
      render: (_: any, record: { id: string; name: string;}) => (
        <span key={record.id}>
          <a
            href={`${web}/career/careerlibs/index.html#/allLibs/majorDetail?majorId=${record.id}`}
            target="_blank"
          >
            {record.name}
          </a>
        </span>
      ),
    },
    {
      title: "专业大类",
      dataIndex: "categoryName",
      width: 400,
      render: (_: any, record: { categoryName: string; }) =>
        record.categoryName && record.categoryName.length >= 26 ? (
          <Tooltip placement="top" title={record.categoryName}>
            <span>{record.categoryName}</span>
          </Tooltip>
        ) : (
          record.categoryName
        ),
    },
    {
      title: "操作",
      dataIndex: "action",
      render: (_: any, record: { id: string; hasCollect: boolean; }) => (
        <a
          key={record.id}
          onClick={() => handleToggleCollect({ ...record, key: "major" })}
        >
          {record.hasCollect && "已"}收藏
        </a>
      ),
    },
  ];

  const occupationColumns = [
    {
      title: "职业名称",
      dataIndex: "name",
      width: 400,
      render: (_: any, record: { id: string; name: string; }) => (
        <span key={record.id}>
          <a
            href={`${web}/career/careerlibs/index.html#/allLibs/occupationDetail?occupationId=${record.id}`}
            target="_blank"
          >
            {record.name}
          </a>
        </span>
      ),
    },
    {
      title: "职业大类",
      dataIndex: "categoryName",
      width: 400,
      render: (_: any, record: { categoryName: string; }) =>
        record.categoryName && record.categoryName.length >= 26 ? (
          <Tooltip placement="top" title={record.categoryName}>
            <span>{record.categoryName}</span>
          </Tooltip>
        ) : (
          record.categoryName
        ),
    },
    {
      title: "操作",
      dataIndex: "action",
      render: (_: any, record: { id:string; hasCollect: boolean; }) => (
        <a
          key={record.id}
          onClick={() => handleToggleCollect({ ...record, key: "occupation" })}
        >
          {record.hasCollect && "已"}收藏
        </a>
      ),
    },
  ];

  useEffect(() => {
    initReprot();
  }, []);

  return reportInfo && Object.keys(reportInfo).length > 0 ?
  <div className={styles['holland-report-page']}>
    <Header title={`${reportInfo.evaluationEnglishName}-${reportInfo.reportTitle}`} />
    <Banner src={headerBanner} height="240" />
    <div className={styles.container}>
      <div className={styles.head}>
        <div className={styles.title}>{`${reportInfo.evaluationEnglishName}-${reportInfo.reportTitle}`}</div>
        <div className={styles.time}>
          <span className={styles.name}>个人报告</span>
          <span>
            报告时间：
            {moment(reportInfo.reportTime).format("YYYY-MM-DD HH:mm")}
          </span>
        </div>
      </div>
      <Card id={navPageList[0].key}>
        <CardIconTitle icon iconType="half" title="职业兴趣的整体概况" />
        {reportInfo.hollandScoreList.length > 0 && (
          <Row>
            <Col span={14} style={{ paddingLeft: "36px" }}>
              {reportInfo.hollandScoreList.map((v) => (
                <div key={v.hollandCode} className={styles.twitedfate}>
                  <span className={styles.score}>{v.score}</span>
                  <span className={styles.name}>{v.hollandName}</span>
                </div>
              ))}
              {reportInfo.hollandCodeGroup.length > 0 && (
                <div className={styles['profile-box']}>
                  {reportInfo.hollandCodeGroup.map((v) => v.hollandCode)}:
                  {reportInfo.hollandCodeGroup.map(
                    (v, i) =>
                      `${
                        i && i !== reportInfo.hollandCodeGroup.length
                          ? " + "
                          : ""
                      } ${v.hollandName}`
                  )}
                </div>
              )}
            </Col>
            <Col span={10}>
              <ChartRadar
                areaColor="#A0BCFF"
                list={reportInfo.hollandScoreList.map((v) => ({
                  name: `${v.hollandName} (${v.hollandCode})`,
                  value: Number(v.score),
                  max: 100,
                }))}
                fullDisplay={false}
                height={300}
                wrapperStyle={{
                  margin: "0 auto",
                }}
              />
            </Col>
          </Row>
        )}
      </Card>
      <Card>
        <CardIconTitle icon iconType="half" title="你的职业兴趣类型为" />
        <CapsuleTabs
          list={
            reportInfo.hollandCodeGroup &&
            reportInfo.hollandCodeGroup.map((v) => ({
                key: v.hollandCode,
                name: `${v.hollandName}(${v.hollandCode})`,
            }))
          }
          activeIndex={currentTabsIndex}
          onChange={handleCapsuleTabChange}
          wrapperStyle={{
            borderRadius: "20px",
            padding: "6px",
          }}
        />
        <div className={styles['content-box']}>
          <div className={styles['content-title']}>基本特点：</div>
          <div
            className={styles.content}
            dangerouslySetInnerHTML={{
              __html:
                reportInfo.hollandCodeGroup[currentTabsIndex].basicFeatures,
            }}
          />
          <div className={styles['content-title']}>职业特点：</div>
          <div
            className={styles.content}
            dangerouslySetInnerHTML={{
              __html:
                reportInfo.hollandCodeGroup[currentTabsIndex]
                  .occupationFeatures,
            }}
          />
          <div className={styles['content-title']}>职业发展建议：</div>
          <div
            className={styles.content}
            dangerouslySetInnerHTML={{
              __html:
                reportInfo.hollandCodeGroup[currentTabsIndex]
                  .careerDevelopmentAdvice,
            }}
          />
          <div className="content-title">适合职业：</div>
          <div
            className={styles.content}
            dangerouslySetInnerHTML={{
              __html:
                reportInfo.hollandCodeGroup[currentTabsIndex]
                  .suitableOccupation,
            }}
          />
        </div>
      </Card>
      <Card id={navPageList[1].key}>
        <CardIconTitle icon iconType="half" title="专业推荐" />
        <ProfessionalTable
          rowKey="id"
          columns={majorColumns}
          dataSource={majorList}
          pagination={false}
          bordered
        />
      </Card>
      <Card>
        <CardIconTitle icon iconType="half" title="职业推荐" />
        <ProfessionalTable
          rowKey="id"
          columns={occupationColumns}
          dataSource={occupationList}
          pagination={false}
          bordered
        />
      </Card>
      <Card id={navPageList[2].key}>
        <CardIconTitle icon iconType="half" title="测评介绍" />
        <div className={styles['content-box']}>
          <div className={styles.description}
          dangerouslySetInnerHTML={{ __html: reportInfo.introduction }} />
        </div>
      </Card>
      <Card wrapperStyle={{ marginBottom: 40 }}>
        <CardIconTitle icon iconType="half" title="解释说明" />
        <div className={styles['content-box']}>
        <div
          className={styles.description}
          dangerouslySetInnerHTML={{ __html: reportInfo.explain }}
        />
        </div>
      </Card>
      <Banner src={footerBanner} height="80" />
      <VideoBar
        data={{
          courseId: reportInfo.courseId,
          lessonId: reportInfo.lessonId,
        }}
      />
      <SideNavigation list={navPageList} />
    </div>
  </div> : null;
}

export default Page;