.holland-report-page {
  .container {
    position: relative;
    width: 1000px;
    margin: -140px auto 0;
  }
  .head {
    .title {
      display: inline-block;
      font-weight: 600;
      font-size: 60px;
      color: #FFFFFF;
      line-height: 64px;
    }
    .time {
      display: inline-block;
      margin-left: 24px;
      padding: 8px 16px;
      background: rgba(80, 89, 111, 0.4);
      border-radius: 8px;
      color: #FFF;
      font-size: 14px;
      .name {
        display:block;
        font-size: 20px;
      }
    }
  }
  .twitedfate {
    display: inline-block;
    margin: 60px auto 24px;
    padding-top: 10px;
    width: 66px;
    height: 75px;
    background: #F9FAFC;
    border-radius: 4px;
    margin-right: 16px;
    text-align: center;
    .score {
      display: block;
      font-weight: 700;
      font-size: 24px;
      color: #50596F;
      text-align: center;
    }
    .name {
      font-size: 14px;
      color: #96A2BE;
    }
  }
  .profile-box {
    width: 476px;
    height: 80px;
    line-height: 80px;
    text-align: center;
    background: #F9FAFC;
    border-radius: 4px;
    font-weight: 700;
    font-size: 24px;
    color: #50596F;
  }

  .content-box {
    margin-top: 16px;
    background: #F9FAFC;
    border-radius: 8px;
    padding: 16px;
    color: #5B6F8C;
    font-size: 14px;
    .content-title {
      color: #50596F;
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      margin: 16px 0 12px;
    }
    .description,
    .content {
      display: block;
      margin-top: 6px;
      line-height: 22px;
      br {
        display: block;
        margin: 4px 0;
        content: "";
      }
    }
    & > div:first-child {
      .content-title {
        margin-top: 0;
      }
    }
    & > div.content-title:first-child {
      margin-top: 0;
    }
  }
}
