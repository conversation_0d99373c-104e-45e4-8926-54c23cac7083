import { useEffect, useState } from 'react';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { Tooltip, message } from 'antd';
import moment from 'moment';
import { TQueryParams, cvtReportResponse, majorItem } from '@/service/career/types';
import API from '@/service/career';
import Banner from "@/components/career/Banner";
import Header from "@/components/career/Header";
import Card from "@/components/career/Card";
import CardIconTitle from "@/components/career/CardIconTitle";
import ChartBar from "@/components/career/ChartBar";
import CapsuleTabs from "@/components/career/CapsuleTabs";
import ProfessionalTable from "@/components/career/ProfessionalTable";
import SideNavigation from "@/components/career/SideNavigation";
import VideoBar from "@/components/career/VideoBar";
import headerBanner from "~/assets/career/CVT-head-banner.png";
import footerBanner from "~/assets/career/CVT-footer-banner.png";
import { web } from "~/utils/hosts";
import { goToPermissionPage } from "@/utils/tools";
import styles from "./index.module.less";

export const getServerSideProps: GetServerSideProps<{
  baseInfo: cvtReportResponse;
}> = async (ctx: any) => {
  const { userRecordId, clientType, secretSign } = ctx.query as TQueryParams;
  try {
    const reportResponse = await API.getCVTReport({ userRecordId, clientType, secretSign }, ctx)
    return { props: { baseInfo: { ...reportResponse }}};
  } catch (error: any) {
    console.log('cvt-report', error);
    return { props: { baseInfo: { ...error }} };
  }
}

const Page = ({
  baseInfo,
} : InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const [reportInfo] = useState({ ...baseInfo.data});
  const [majorList, setMajorList] = useState<any[]>([]);
  const [currentTabsIndex, setCurrentTabsIndex] = useState(0);
  const navPageList = [
    { label: "报告详情", key: "detail" },
    { label: "专业推荐", key: "recommend" },
    { label: "关于测评", key: "about" },
  ];
  // 获取专业
  const getMajorList = async (majorIdList: number[]) => {
    try {
      const { data } = await API.getMajorList({ majorIdList });
      const list = data.map((v: majorItem) => ({
        id: v.majorId,
        name: v.majorName,
        categoryName: v.categoryName,
        hasCollect: v.hasCollect,
      }));
      setMajorList(list);
    } catch (e) {
      console.log("getMajorList", e);
    }
  };

  // 获取报告
  const initReprot = async () => {
    const { data, code, msg, success } = baseInfo;
    if (code && ["9091401", "9091402", "9091403"].indexOf(code) !== -1) {
      goToPermissionPage({
        code,
        msg,
      });
      return
    }
    if (!success) {
      message.error(msg)
      return
    }
    const { recommendMajorIdList } = data;
    getMajorList(recommendMajorIdList);
  };

  // 收藏专业/职业
  // 内容类型: 2、院校 3、职业精选 4、专业 5 职业宝库
  const goAddCollect = async (item: { id: any; }) => {
    try {
      const params = {
        contentId: item.id,
        contentType: 4,
      };
      await API.addCollect(params);
      const list = majorList.map((v) =>
        v.id === item.id ? { ...v, hasCollect: true } : v
      );
      setMajorList(list);
      message.success("收藏成功", 1);
    } catch (e) {
      console.log("goAddCollect", e);
    }
  };

  // 取消收藏专业/职业
  // 内容类型: 专业 majorId, 院校 collegeId，职业精选 occupationPerusalId
  const goCancelCollect = async (item: { id: any; key: string; }) => {
    try {
      const params = {
        contentId: item.id,
        contentType: item.key === "major" ? 4 : 3,
      };
      await API.cancelCollect(params);
      const list = majorList.map((v) =>
        v.id === item.id ? { ...v, hasCollect: false } : v
      );
      setMajorList(list);
      message.success("已取消收藏", 1);
    } catch (e) {
      console.log("goCancelCollect", e);
    }
  };

  // 收藏 取消收藏专业/职业
  const handleToggleCollect = (item: any) => {
    if (item.hasCollect) {
      goCancelCollect(item);
    } else {
      goAddCollect(item);
    }
  };

  const columns = [
    {
      title: "专业名称",
      dataIndex: "name",
      key: "name",
      width: 390,
      render: (_: any, record: { id: string; name: string; }) => (
        <span key={record.id}>
          <a
            href={`${web}/career/careerlibs/index.html#/allLibs/majorDetail?majorId=${record.id}`}
            target="_blank"
          >
            {record.name}
          </a>
        </span>
      ),
    },
    {
      title: "专业大类",
      dataIndex: "categoryName",
      key: "categoryName",
      width: 390,
      render: (_: any, record: { categoryName: string; }) =>
        record.categoryName && record.categoryName.length >= 26 ? (
          <Tooltip placement="top" title={record.categoryName}>
            <span>{record.categoryName}</span>
          </Tooltip>
        ) : (
          record.categoryName
        ),
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: { hasCollect: any; }) => (
        <a onClick={() => handleToggleCollect(record)}>
          {record.hasCollect && "已"}收藏
        </a>
      ),
    },
  ];

  useEffect(() => {
    initReprot();
  }, []);

  return reportInfo && Object.keys(reportInfo).length > 0 ? (
    <div className={styles['cvt-report-page']}>
      <Header title={`${reportInfo.evaluationEnglishName}-${reportInfo.reportTitle}`}/>
      <Banner src={headerBanner} height="240" />
      <div className={styles.container}>
        <div className={styles.head}>
          <div className={styles.title}>{`${reportInfo.evaluationEnglishName}-${reportInfo.reportTitle}`}</div>
          <div className={styles.time}>
            <span className={styles.name}>个人报告</span>
            <span>
              报告时间：
              {moment(reportInfo.reportTime).format("YYYY-MM-DD HH:mm")}
            </span>
          </div>
        </div>
        <Card id={navPageList[0].key}>
          <CardIconTitle icon iconType="half" title="结果解读" />
          <div className={styles['content-box']}>
            <ChartBar list={reportInfo.miScoreInfoList} />
          </div>
          <div className={styles['card-vocational-box']}>
            {reportInfo.miInfoList.length > 0 &&
              reportInfo.miInfoList.map((v, i) => (
                <div
                  key={i}
                  className={`${styles['card-vocational']} ${
                    i ? styles['vocational-minor'] : styles['vocational-major']
                  }`}
                >
                  <div className={styles['card-vocational-title']}>
                    {`${i ? "辅助" : "主导"}`}职业锚的典型特征：
                  </div>
                  <div className={styles['card-vocational-type']}>{v.name}</div>
                  <div className={styles['card-vocational-description']}>
                    {v.typicalCharacteristic}
                  </div>
                </div>
              ))}
          </div>
        </Card>
        <Card>
          <CardIconTitle icon iconType="half" title="你的职业兴趣类型为" />
          <CapsuleTabs
            list={reportInfo.miInfoList?.map((v, i) => ({
                key: v.code,
                name: `${v.name}${i ? `(辅助)` : `(主导)`}`,
              }))
            }
            activeIndex={currentTabsIndex}
            onChange={(index) => setCurrentTabsIndex(index)}
          />
          <div className={styles['content-box']}>
            <div className={styles.title}>基本特点：</div>
            <div className={styles.description}>
              {reportInfo.miInfoList[currentTabsIndex].basicInfo}
            </div>
            <div className={styles.title}>你期望的工作类型：</div>
            <div className={styles.description}>
              {reportInfo.miInfoList[currentTabsIndex].desiredJobType}
            </div>
            <div className={styles.title}>你期望的薪酬福利：</div>
            <div className={styles.description}>
              {reportInfo.miInfoList[currentTabsIndex].desiredSalaryWelfare}
            </div>
            <div className={styles.title}>你期望的工作晋升：</div>
            <div className={styles.description}>
              {reportInfo.miInfoList[currentTabsIndex].desiredJobPromotion}
            </div>
            <div className={styles.title}>你期望的认可方式：</div>
            <div className={styles.description}>
              {reportInfo.miInfoList[currentTabsIndex].desiredApprovedMeans}
            </div>
          </div>
        </Card>
        <Card id={navPageList[1].key}>
          <CardIconTitle icon iconType="half" title="专业推荐" />
          <ProfessionalTable
            rowKey="id"
            columns={columns}
            dataSource={majorList}
            pagination={false}
            bordered
          />
        </Card>
        <Card id={navPageList[2].key}>
          <CardIconTitle icon iconType="half" title="测评介绍" />
          <div className={styles['content-box']}>
            <p
              className={styles.description}
              dangerouslySetInnerHTML={{ __html: reportInfo?.introduction }}
            />
          </div>
        </Card>
        <Card wrapperStyle={{ marginBottom: 40 }}>
          <CardIconTitle icon iconType="half" title="解释说明" />
          <div className={styles['content-box']}>
            {reportInfo.explain.map((v, key) => (
              <div key={key}>
                <p className={styles.title}>{v.title}：</p>
                <p
                  className={styles.description}
                  dangerouslySetInnerHTML={{ __html: v.text }}
                />
              </div>
            ))}
          </div>
        </Card>
        <Banner src={footerBanner} height="80" />
        <VideoBar
          data={{
            courseId: reportInfo.courseId,
            lessonId: reportInfo.lessonId,
          }}
        />
        <SideNavigation list={navPageList} />
      </div>
    </div>
  ) : null;
}

export default Page;