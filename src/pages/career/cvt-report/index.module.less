.cvt-report-page {
  .container {
    position: relative;
    width: 1000px;
    margin: -140px auto 0;
  }
  .head {
    .title {
      display: inline-block;
      font-weight: 600;
      font-size: 60px;
      color: #FFFFFF;
      line-height: 64px;
    }
    .time {
      display: inline-block;
      margin-left: 24px;
      padding: 8px 16px;
      background: rgba(80, 89, 111, 0.4);
      border-radius: 8px;
      color: #FFF;
      font-size: 14px;
      .name {
        display:block;
        font-size: 20px;
      }
    }
  }
  .card-vocational-box {
    display: flex;
    margin:24px auto 0;
  }
  .card-vocational {
    flex: 1;
    width: 50%;
    padding: 16px;
    color: #5B6F8C;
    background-color: #F1FDFB;
    border-radius: 8px;
    font-size: 14px;
    vertical-align: top;
    .card-vocational-type {
      font-weight: 600;
      font-size: 16px;
      line-height: 44px;
      color: #1E9A97;
    }
    .card-vocational-title {
      font-size: 16px;
      color: #1E9A97;
    }
  }
  .vocational-major {
    font-size: 14px;
  }
  .vocational-minor {
    background-color:#FFFAF3;
    margin-left: 16px;
    .card-vocational-type,
    .card-vocational-title {
      color:#FFA61A;
    }
  }
  .card-vocational-description {
    display: block;
  }

  .customTable {
    margin-top: 16px;
  }
  .content-box {
    margin-top: 16px;
    background: #F9FAFC;
    border-radius: 8px;
    padding: 16px;
    color: #5B6F8C;
    font-size: 14px;
    .title {
      color: #50596F;
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      margin: 16px 0 12px;
    }
    .description {
      display: block;
      line-height: 22px;
      br {
        display: block;
        margin: 4px 0;
        content: "";
      }
    }
    & > div:first-child {
      .title {
        margin-top: 0;
      }
    }
    & > div.title:first-child {
      margin-top: 0;
    }
  }
}
