.mbti-report-page {
  .container {
    position: relative;
    width: 1000px;
    margin: -140px auto 0;
  }
  .head {
    .title {
      display: inline-block;
      font-weight: 600;
      font-size: 60px;
      color: #FFFFFF;
      line-height: 64px;
    }
    .time {
      display: inline-block;
      margin-left: 24px;
      padding: 8px 16px;
      background: rgba(80, 89, 111, 0.4);
      border-radius: 8px;
      color: #FFF;
      font-size: 14px;
      .name {
        display:block;
        font-size: 20px;
      }
    }
  }
  .personality-type-box {
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: stretch;
    .first-item {
      margin-right: 24px;
      width: 40%;
      .title {
        display: inline-block;
        line-height: 60px;
        background: #FFFFFF;
        padding: 0 20px;
        margin-bottom: 16px;
        border-radius: 16px;
        font-weight: 700;
        font-size: 24px;
        color: #50596F;
      }
    }
    .second-item {
      width: 60%;
    }
  }
  .career-advice-box {
    display: flex;
    align-items: stretch;
    flex-wrap: wrap;
    .advice-box {
      display: inline-block;
      position: relative;
      width: 49%;
      background: #F9FAFC;
      border-radius: 8px;
      margin-top: 16px;
      padding: 16px;
      vertical-align: top;
      .icon-item {
        display: inline-block;
        width: 44px;
        vertical-align: top;
      }
      .content-item {
        display: inline-block;
        width: 88%;
        .content-title {
          line-height: 32px;
          font-weight: 700;
          font-size: 16px;
          color: #50596F;
        }
        .content {
          line-height: 30px;
          font-size: 14px;
          color: #5B6F8C;
        }
      }
    }
    .advice-box:nth-child(even) {
      margin-left: 16px;
    }
    .advice-full {
      display: block;
      width: 100%;
      .content-item {
        width: 95%;
      }
    }
  }
  .content-box {
    margin-top: 16px;
    background: #F9FAFC;
    border-radius: 8px;
    padding: 16px;
    color: #5B6F8C;
    font-size: 14px;
    .explanation {
      color: #5B6F8C;
    }
    .content-title {
      color: #50596F;
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      margin: 16px 0 12px;
    }
    .description {
      display: block;
      margin-top: 6px;
      line-height: 22px;
      br {
        display: block;
        margin: 4px 0;
        content: "";
      }
    }
    & > div:first-child {
      .content-title {
        margin-top: 0;
      }
    }
    & > div.content-title:first-child {
      margin-top: 0;
    }
  }
}
