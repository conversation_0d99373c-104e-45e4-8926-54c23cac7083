import { useEffect, useState } from 'react';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { Tooltip, message } from 'antd';
import moment from 'moment';
import { TQueryParams, MBTIReportResponse } from '@/service/career/types';
import API from '@/service/career';
import Header from '~/components/career/Header';
import Banner from '~/components/career/Banner';
import Card from '~/components/career/Card';
import CardIconTitle from '~/components/career/CardIconTitle';
import ProfessionalTable from '~/components/career/ProfessionalTable';
import PositiveNegativeBarChart from '~/components/career/PositiveNegativeBarChart';
import SideNavigation from '~/components/career/SideNavigation';
import VideoBar from '~/components/career/VideoBar';
import headerBanner from "~/assets/career/MBTI-head-banner.png";
import footerBanner from "~/assets/career/MBTI-footer-banner.png";
import illustrationImg from "~/assets/career/MBTI_growthPotentialAreas_illustration.png";
import IconFavour from "~/assets/career/icon_favour.png";
import IconImperfection from "~/assets/career/icon_imperfection.png";
import IconEnvironment from "~/assets/career/icon_environment.png";
import IconProposal from "~/assets/career/icon_proposal.png";
import IconTerritory from "~/assets/career/icon_territory.png";
import { goToPermissionPage } from '~/utils/tools';
import { web } from '~/utils/hosts';
import styles from './index.module.less'

export const getServerSideProps: GetServerSideProps<{
  baseInfo: MBTIReportResponse;
}> = async (ctx: any) => {
  const { userRecordId, clientType, secretSign } = ctx.query as TQueryParams;
  try {
    const reportResponse = await API.getMBTIReport({ userRecordId, clientType, secretSign }, ctx)
    return { props: { baseInfo: { ...reportResponse }} };
  } catch (error: any) {
    console.log('cvt-report', error);
    return { props: { baseInfo: { ...error }} };
  }
}

const Page = ({
  baseInfo,
} : InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const navPageList = [
    { label: "报告详情", key: "detail" },
    { label: "专业职业推荐", key: "recommend" },
    { label: "关于测评", key: "about" },
  ];
  const levelDict = ["", "轻微", "一般", "较明显", "明显"];
  const dimensionDict = ["总体层面", "信息获取", "决策方式", "生活方式"];
  const [reportInfo, setReportInfo] = useState({...baseInfo.data});
  const [majorList, setMajorList] = useState<any[]>([]);
  const [occupationList, setOccupationList] = useState<any[]>([]);
  const [careerAdviceList, setCareerAdviceList] = useState<any[]>([]);

  // 获取报告
  const initReprot = async () => {
    const { data, code, msg, success } = baseInfo;
    if (code && ['9091401', '9091402', '9091403'].indexOf(code) !== -1) {
      goToPermissionPage({
        code,
        msg
      })
      return
    }

    if (!success) {
      message.error(msg)
      return
    }

    const {
      recommendMajorIdList,
      occupationInfoList,
      workAdvantages,
      potentialDefects,
      preferredWorkEnvironment,
      developmentProposals,
      advantageAreas
    } = data;
    getMajorList(recommendMajorIdList);
    getCollectOccupationList(occupationInfoList);
    setCareerAdviceList([
      { icon: IconFavour, title: "工作中的优点", content: workAdvantages,},
      { icon: IconImperfection, title: "潜在的缺陷", content: potentialDefects,},
      { icon: IconEnvironment, title: "偏好的工作环境", content: preferredWorkEnvironment,},
      { icon: IconProposal, title: "发展建议", content: developmentProposals,},
      { icon: IconTerritory, title: "优势领域", content: advantageAreas },
    ]);
  };

  // 专业列表
  const getMajorList = async (majorIdList: number[]) => {
    try {
      const { data } = await API.getMajorList({ majorIdList });
      const list = data.map((v) => ({
          id: v.majorId,
          name: v.majorName,
          categoryName: v.categoryName,
          hasCollect: v.hasCollect,
      }));
      setMajorList(list);
    } catch (e) {
      console.log("getMajorList", e);
    }
  };

  // 职业列表
  const getCollectOccupationList = async (occupationIdList: number[]) => {
    try {
      const { data } = await API.getOccupationList({ occupationIdList });
      const list = data.map((v) => ({
          id: v.occupationId,
          name: v.occupationName,
          categoryName: v.categoryName,
          hasCollect: v.hasCollect,
      }));
      setOccupationList(list);
    } catch (e) {
      console.log("getCollectOccupationList", e);
    }
  };

  // 收藏专业/职业
  // 内容类型: 2、院校 3、职业精选 4、专业 5 职业宝库
  const goAddCollect = async (item: { id: any; key: string; }) => {
    try {
      const params = {
        contentId: item.id,
        contentType: item.key === "major" ? 4 : 3,
      };
      await API.addCollect(params);
      const list = (item.key === "major" ? majorList : occupationList).map(
        (v) => (v.id === item.id ? { ...v, hasCollect: true } : v)
      );
      if (item.key === "major") {
        setMajorList(list);
      } else {
        setOccupationList(list);
      }

      message.info("收藏成功", 1);
    } catch (e) {
      console.log("goAddCollect", e);
    }
  };

  // 取消收藏专业/职业
  // 内容类型: 专业 majorId, 院校 collegeId，职业精选 occupationPerusalId
  const goCancelCollect = async (item: { id: string; key: string; }) => {
    try {
      const params = {
        contentId: item.id,
        contentType: item.key === "major" ? 4 : 3,
      };
      await API.cancelCollect(params);
      const list = (item.key === "major" ? majorList : occupationList).map(
        (v) => (v.id === item.id ? { ...v, hasCollect: false } : v)
      );

      if (item.key === "major") {
        setMajorList(list);
      } else {
        setOccupationList(list);
      }
      message.info("已取消收藏", 1);
    } catch (e) {
      console.log("goCancelCollect", e);
    }
  };

  // 收藏 取消收藏专业/职业
  const handleToggleCollect = (item: any) => {
    if (item.hasCollect) {
      goCancelCollect(item);
    } else {
      goAddCollect(item);
    }
  };

  const personalityColumns = [
    {
      title: "性格类型",
      dataIndex: "code",
      width: 100,
      render: (_: any, record: { code: string; name: string; }) => (
        <span>
          {record.code}({record.name})
        </span>
      ),
    },
    {
      title: "性格层面",
      dataIndex: "name",
      width: 100,
      render: (_: any, record: any, index: number) => <span>{dimensionDict[index]}</span>,
    },
    {
      title: "明显程度",
      dataIndex: "level",
      width: 100,
      render: (_: any, record: { level: number; }) => <span>{levelDict[record.level]}</span>,
    },
    { title: "主要特征", dataIndex: "desc", render: (text: string) => <p style={{ whiteSpace: 'break-spaces' }}>{text}</p> },
  ];

  const majorColumns = [
    {
      title: "专业名称",
      dataIndex: "name",
      width: 400,
      render: (_: any, record: { id: string; name: string; }) => (
        <span key={record.id}>
          <a
            href={`${web}/career/careerlibs/index.html#/allLibs/majorDetail?majorId=${record.id}`}
            target="_blank"
          >
            {record.name}
          </a>
        </span>
      ),
    },
    {
      title: "专业大类",
      dataIndex: "categoryName",
      width: 400,
      render: (_: any, record: { categoryName: string; }) =>
        record.categoryName && record.categoryName.length >= 26 ? (
          <Tooltip placement="top" title={record.categoryName}>
            <span>{record.categoryName}</span>
          </Tooltip>
        ) : (
          record.categoryName
        ),
    },
    {
      title: "操作",
      dataIndex: "action",
      render: (_: any, record: { id: string; hasCollect: boolean; }) => (
        <a
          key={record.id}
          onClick={() => handleToggleCollect({ ...record, key: "major" })}
        >
          {record.hasCollect && "已"}收藏
        </a>
      ),
    },
  ];

  const occupationColumns = [
    {
      title: "职业名称",
      dataIndex: "name",
      width: 400,
      render: (_: any, record: { id: string; name: string; }) => (
        <span key={record.id}>
          <a
            href={`${web}/career/careerlibs/index.html#/allLibs/occupationDetail?occupationId=${record.id}`}
            target="_blank"
          >
            {record.name}
          </a>
        </span>
      ),
    },
    {
      title: "职业大类",
      dataIndex: "categoryName",
      width: 400,
      render: (_: any, record: { categoryName: string; }) =>
        record.categoryName && record.categoryName.length >= 26 ? (
          <Tooltip placement="top" title={record.categoryName}>
            <span>{record.categoryName}</span>
          </Tooltip>
        ) : (
          record.categoryName
        ),
    },
    {
      title: "操作",
      dataIndex: "action",
      render: (_: any, record: { id: string; hasCollect: boolean; }) => (
        <a
          key={record.id}
          onClick={() => handleToggleCollect({ ...record, key: "occupation" })}
        >
          {record.hasCollect && "已"}收藏
        </a>
      ),
    },
  ];

  useEffect(() => {
    initReprot();
  }, []);

  return reportInfo && Object.keys(reportInfo).length > 0 ? (
    <div className={styles['mbti-report-page']}>
      <Header title={`${reportInfo.evaluationEnglishName}-${reportInfo.reportTitle}`} />
      <Banner src={headerBanner} height="240" />
      <div className={styles.container}>
        <div className={styles.head}>
          <div className={styles.title}>{`${reportInfo.evaluationEnglishName}-${reportInfo.reportTitle}`}</div>
          <div className={styles.time}>
            <span className={styles.name}>个人报告</span>
            <span>
              报告时间：
              {moment(reportInfo.reportTime).format("YYYY-MM-DD HH:mm")}
            </span>
          </div>
        </div>
        <Card id={navPageList[0].key}>
          <CardIconTitle icon iconType="half" title="你的人格类型是" />
          <div className={styles['personality-type-box']}>
            <div className={`${styles['content-box']} ${styles['first-item']}`}>
              <div className={styles.title}>{reportInfo.personalityType}</div>
              <div
                dangerouslySetInnerHTML={{
                  __html: reportInfo.personalityTypeDesc,
                }}
              />
            </div>
            <div className={`${styles['content-box']} ${styles['second-item']}`}>
              <div className={styles['content-title']}>
                {reportInfo.personalityType}个性特征
              </div>
              <div
                className={styles.description}
                dangerouslySetInnerHTML={{
                  __html: reportInfo.personalityTraits,
                }}
              />
            </div>
          </div>
        </Card>
        <Card>
          <CardIconTitle
            icon
            iconType="half"
            title="你的关键词"
            specialTitle={reportInfo.personalityType}
            wrapperStyle={{ marginBottom: 24 }}
          />
          <img src={reportInfo.pcKeywordImgUrl} width="100%" height="270" />
        </Card>
        <Card>
          <CardIconTitle
            icon
            iconType="half"
            title="性格倾向"
            specialTitle={reportInfo.personalityType}
          />
          {reportInfo.personalityTendencies &&
            reportInfo.personalityTendencies.length > 0 && (
              <PositiveNegativeBarChart
                wrapperStyle={{ marginTop: 24 }}
                contentStyle={{ width: 600 }}
                list={reportInfo.personalityTendencies}
              />
            )}
          <ProfessionalTable
            rowKey="code"
            columns={personalityColumns}
            dataSource={
              reportInfo.personalityTendencies.length > 0 &&
              reportInfo.personalityTendencies.filter((v) => v.hitFlag)
            }
            pagination={false}
            bordered
          />
        </Card>
        <Card>
          <CardIconTitle
            icon
            iconType="half"
            title="人格优势"
            specialTitle={reportInfo.personalityType}
          />
          <div className={styles['content-box']}>
            {reportInfo.personalityAdvantages.length > 0 &&
              reportInfo.personalityAdvantages.map((v, i) => (
                <div key={i}>
                  <div className={styles['content-title']}>{v.title}:</div>
                  <div
                    className={styles.description}
                    dangerouslySetInnerHTML={{ __html: v.desc }}
                  />
                </div>
              ))}
          </div>
        </Card>
        <Card>
          <CardIconTitle
            icon
            iconType="half"
            title="成长的潜在区域"
            specialTitle={reportInfo.personalityType}
          />
          <div className={styles['content-box']}>
            <div
              style={{
                width: "535px",
                display: "inline-block",
                lineHeight: "26px"
              }}
              className={styles.description}
              dangerouslySetInnerHTML={{
                __html: reportInfo.growthPotentialAreas,
              }}
            />
            <img
              src={illustrationImg}
              width="350"
              height="270"
              style={{ verticalAlign: "top" }}
            />
          </div>
        </Card>
        <Card>
          <CardIconTitle
            icon
            iconType="half"
            title="职业建议"
            specialTitle={reportInfo.personalityType}
          />
          <div className={styles['career-advice-box']}>
            {careerAdviceList.length > 0 &&
              careerAdviceList.map((v, i) => (
                <div
                  key={i}
                  className={`${styles['advice-box']} ${i === 4 ? styles['advice-full'] : ""}`}
                >
                  <div className={styles['icon-item']}>
                    <img src={v.icon} />
                  </div>
                  <div className={styles['content-item']}>
                    <div className={styles['content-title']}>{v.title}</div>
                    <div
                      className={styles.content}
                      dangerouslySetInnerHTML={{ __html: v.content }}
                    />
                  </div>
                </div>
              ))}
          </div>
        </Card>
        <Card id={navPageList[1].key}>
          <CardIconTitle
            icon
            iconType="half"
            title="专业推荐"
            specialTitle={reportInfo.personalityType}
          />
          <ProfessionalTable
            rowKey="id"
            columns={majorColumns}
            dataSource={majorList}
            pagination={false}
            bordered
          />
        </Card>
        <Card>
          <CardIconTitle
            icon
            iconType="half"
            title="职业推荐"
            specialTitle={reportInfo.personalityType}
          />
          <ProfessionalTable
            rowKey="id"
            columns={occupationColumns}
            dataSource={occupationList}
            pagination={false}
            bordered
          />
        </Card>
        <Card id={navPageList[2].key}>
          <CardIconTitle icon iconType="half" title="测评介绍" />
          {reportInfo.introduction && (
            <div className={styles['content-box']}>
              <div
                className={styles.description}
                dangerouslySetInnerHTML={{
                  __html: reportInfo.introduction,
                }}
              />
            </div>
          )}
        </Card>
        <Card wrapperStyle={{ marginBottom: 40 }}>
          <CardIconTitle icon iconType="half" title="解释说明" />
          {reportInfo.explains && (
            <div className={styles['content-box']}>
              {reportInfo.explains.map((v, key) => (
                <div key={key} className={styles.explanation}>
                  <p className={styles["content-title"]}>{v.title}：</p>
                  <p
                    className={styles.description}
                    dangerouslySetInnerHTML={{ __html: v.desc }}
                  />
                </div>
              ))}
            </div>
          )}
        </Card>
        <Banner src={footerBanner} height="80" />
        <VideoBar
          data={{
            courseId: reportInfo.courseId,
            lessonId: reportInfo.lessonId,
          }}
        />
        <SideNavigation list={navPageList} />
      </div>
    </div>
  ) : null;
}

export default Page;