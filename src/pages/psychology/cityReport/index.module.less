.psychology-city {
  font-size: 14px;
  background: #000;
  .container {
    width: 1000px;
    background-color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0 auto;
    padding-bottom: 40px;
  }
  .countyCover {
    height: 1450px;
    width: 1000px;
    margin: 0 auto;
  }
  .countyArea {
    display: inline-block;
    font-size: 36px;
    color: #ffffff;
    line-height: 43.2px;
    padding: 18px 40px;
    background-image: linear-gradient(88deg, #00b8c1 0%, #00e8a0 100%);
    border-radius: 47.52px;
    margin: 460px 0 0 50px;
  }
  .reportGrade {
    margin-top: 432px;
    .gradeBox {
      background: rgba(1, 119, 119, 0.04);
      border-radius: 0 10px 10px 0;
      border-radius: 0 10px 10px 0;
      padding: 42px 320px 42px 65px;
      display: inline-block;
      p {
        font-size: 28px;
        color: #233155;
        letter-spacing: 0;
        margin-top: 30px;
        font-weight: 300;
      }
      p:first-child {
        margin: 0;
      }
    }
    .provenience {
      margin-top: 60px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -ms-flex-pack: justify;
      justify-content: space-between;
      padding: 0 33px;
      span {
        font-size: 22px;
        color: rgba(35,49,85,0.6);
        line-height: 18px;
      }
    }
  }
  .textBox {
    padding: 0 30px 20px;
    color:rgba(0, 17, 57, 0.85);
    p {
      line-height: 28px;
      font-size: 18px;
      margin-top: 20px;
      span {
        color: #162755;
        font-weight: 700;
      }
    }
    .center {
      text-align: center;
    }
  }
  .riskDistribution {
    li {
      padding-top: 20px;
      color: rgba(0, 17, 57, 0.85);
      font-size: 18px;
      list-style: disc;
    }
  }
  .focusOnSchoolsBox{
    padding:0 40px 20px;
    position: relative;
    .itemCard {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 280px;
      margin-right: 40px;
      margin-top: 20px;
      border: 1px solid #f2f6ff;
      border-radius: 9px;
      color: #001139;
      vertical-align: top;
      .circleSerial {
        display: inline-block;
        width: 40px;
        height: 40px;
        line-height: 40px;
        background: -webkit-gradient(linear, left top, left bottom, color-stop(1%, #ffa46a), to(#ff7a65));
        background: linear-gradient(180deg, #ffa46a 1%, #ff7a65 100%);
        border-radius: 50%;
        font-size: 24px;
        font-weight: 700;
        text-align: center;
        color: #ffffff;
      }
      .itemContent {
        display: inline-block;
        width: 180px;
        padding: 12px 10px 15px 0;
        margin-left: 20px;
        .schoolName {
          max-width: 180px;
          font-size: 16px;
          line-height: 22px;
        }
      }
    }
    .itemCard:nth-child(3n+3) {
      margin-right: 0;
    }
    .explain {
      margin-top: 20px;
      font-size: 16px;
    }
  }
  .stateDistributionBox {
    padding:20px 40px 40px;
    color: rgba(0, 17, 57, 0.85);
    .stateTitle {
      font-size: 18px;
      font-weight: bold;
      font-family: MicrosoftYaHeiUI;
      text-align: left;
    }
    li {
      padding-top: 20px;
      font-size: 18px;
      list-style: disc;
    }
  }
  .customTable,
  .earlyWarningTable {
    display: block;
    position: relative;
    text-align: left;
    margin-bottom: 30px;
    border-radius: 8px;
    thead > tr > th {
      background: rgba(0, 200, 101, 0.1);
      font-size: 18px;
      padding: 16px 10px;
      text-align: center;
    }
    tbody {
      tr {
        &:nth-child(even) {
          background: #00c8650d;
        }
        td {
          font-size: 18px;
          padding: 16px 4px;
          text-align: center;
          color: rgba(0,0,0,.65);
        }
        .empty {
          color: #ccc;
        }
      }
    }
    p {
      line-height: 24px;
      font-size: 18px;
      color: #001139;
      margin-top: 15px;
    }
  }
  .earlyWarningTable {
    thead > tr > th {
      background: rgba(0, 200, 101, 0.1);
      font-size: 18px;
      padding: 2px 10px;
    }
    tbody > tr {
      td {
        font-size: 18px;
        padding: 6px 10px;
        border-bottom: 1px solid #f2f7ff;
        color: rgba(0,0,0,.65);
      }
    }
  }
  .downloadBox {
    display:block;
    margin-bottom: 30px;
    text-align: right;
    button {
      margin-left: 30px;
      height: 36px;
      background: #38d486;
      border-radius: 3px;
      font-size: 16px;
      border:0;
      color: #fff;
      .iconDownload {
        display: inline-block;
        vertical-align: middle;
        margin-top: -3px;
      }
    }
  }
}
