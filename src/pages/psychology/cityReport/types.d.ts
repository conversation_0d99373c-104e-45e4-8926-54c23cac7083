export interface areaWarningItem {
  areaCode: number;
  areaName: string;
  score: number;
}

export interface areaHealthWarningProps{
  areaWarning: areaWarningItem[];
  areaWarningPoints: number;
  conclusion: string;
}

export interface schoolIndexAnalysisItem {
  index: number;
  schoolName: string;
}

export interface areaIndexAnalysisItem {
  areaCode: number;
  areaName: string;
  schoolIndexAnalysis: schoolIndexAnalysisItem[];
}

export interface areaPercentageItem {
  areaCode: number;
  areaName: string;
  healthPeople: number;
  healthPercentage: number;
  higherRiskPeople: number;
  higherRiskPercentage: number;
  highestRiskPeople: number;
  highestRiskPercentage: number;
  minorRiskPeople: number;
  minorRiskPercentage: number;
  totlePeople?: number;
}

export interface latitudeItem {
  latitudeId: string;
  latitudeName: string;
  referenceScore: number;
  score: number;
  warnningScore: number;
}

export interface factorAnalysisProps {
  inConclusion: string;
  latitudeList: latitudeItem[];
}

export interface focusOnSchoolsItem {
  highRiskPeople: number;
  percentage: number;
  schoolName: string;
}

export interface factorAnalysisDetailsItem {
  areaFactorComparison: areaWarningItem[];
  areaStateDistribution: areaPercentageItem[];
  focusOnSchools: focusOnSchoolsItem[];
  latitudeId: number;
  latitudeName: string;
  referenceScore: number;
}

export interface riskIndexProps {
  conclusion: string;
  remarks: string[];
  riskIndexScore: number;
  riskLevel: number;
  riskLevelName: string;
  isUser?: boolean
}

export interface stateDistributionItem {
  number: number;
  percentage: number;
  riskId: number;
  riskName: string;
}

export interface queryParamsProps {
  cityCode?: string;
  areaCode?: string;
  gradeId?: string;
  gradeCode?: string;
  graduationYear?: string;
  reportDateStr?: string;
  type?: string;
}

export interface cityReportProps {
  queryParams: any;
  areaHealthWarning: areaHealthWarningProps;
  areaIndexAnalysis: areaIndexAnalysisItem[];
  areaName: string;
  areaPercentage: areaPercentageItem[];
  endTime: number;
  evaluatedPeople: number;
  factorAnalysis: factorAnalysisProps;
  factorAnalysisDetails: factorAnalysisDetailsItem[];
  focusOnSchools: focusOnSchoolsItem[];
  gradeName: string;
  mainFindings: string[];
  overSchoolCnt: number;
  queryParams: queryParamsProps;
  reportCode: string;
  reportTime: number;
  riskIndex: riskIndexProps;
  startTime: number;
  stateDistribution: stateDistributionItem[];
  totalPeople: number;
  warningSummary: areaPercentageItem[];
}

export interface cityReportResponse {
  success?: boolean;
  code?: string | number;
  msg?: string;
  data?: cityReportProps;
  printMode?: boolean
}