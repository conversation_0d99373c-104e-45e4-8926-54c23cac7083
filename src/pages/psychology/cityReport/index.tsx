import { useEffect } from 'react';
import { message } from 'antd';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import dynamic from 'next/dynamic';
import moment from 'moment';
import treeList from '@/lib/psychology/cityReport';
import API from '@/service/psychology/cityReport'
import CountyCover from '@/assets/psychology/countyCover.png'
import { cityReportResponse } from './types';
import styles from './index.module.less';
import { TQueryParams } from '~/service/psychology/cityReport/types';
import { mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';
import commonApi from '@/service/common/index';
const ComplexComponents = dynamic(() => import('~/complexComponents'));

export const getServerSideProps: GetServerSideProps<{
  baseInfo: cityReportResponse;
  queryParams: TQueryParams;
}> = async (ctx: any) => {
  const { cityCode, gradeId, reportDateStr, ff, isPrint, isDetailReport, sourceType } = ctx.query as TQueryParams;
  const printMode = !!(ff || isPrint);
  const detailReportMode = isDetailReport ? Boolean(isDetailReport) : false;
  try {
    // 强制处理直接通过链接进来的历史数据
    if (ctx.query.sourceType !== '1') {
      return {
        redirect: {
          destination: `/psychology-service/common/permission?edu=1`,
          permanent: false,
        },
      };
    }
    let isVaild = false;
    if (cityCode && +cityCode) {
      const authResponse = await commonApi.getUserViewAuth({
        areaCode: +cityCode,
      }, ctx);
      isVaild = !!authResponse.data;
    }
    if (!isVaild) {
      // 无权限
      return {
        redirect: {
          destination: '/psychology-service/common/permission',
          permanent: false,
        }
      }
    }
    const cityReportResponse = await API.getCityReport({
      clientType: 1,
      cityCode,
      gradeId,
      reportDateStr,
      sourceType,
    }, ctx)
    return {
      props: {
        baseInfo: {
          ...cityReportResponse
        },
        queryParams: {
          ...ctx.query,
          printMode,
          detailReportMode
        }
      }
    };
  } catch (error: any) {
    console.log('getCityReport', error);
    const { code, msg } = error;
    return {
      props: {
        baseInfo: {
          success: false,
          code,
          msg
        },
        queryParams: {
          ...ctx.query,
          printMode,
          detailReportMode
        }
      }
    };
  }
};

const Page = ({
  baseInfo,
  queryParams
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { data } = baseInfo;
  const { printMode, detailReportMode } = queryParams;

  useEffect(() => {
    if (baseInfo && !baseInfo.success) {
      message.error(baseInfo.msg)
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: baseInfo.code || '500',
          errorMessage: baseInfo.msg || '接口异常',
        })
      }
      return
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: detailReportMode ? 6000 : 3000
      })
    }
  }, [])

  return (
    <div {...{ className: styles['psychology-city'] }}>
      <div className={styles.container}>
        <div className={styles.countyCover} style={{background: `url(${CountyCover}) center center no-repeat`}}>
          <div className={styles.countyArea}>
            { data?.areaName }
          </div>
          <div className={styles.reportGrade}>
            <div className={styles.gradeBox}>
              <p>年级：<b>{ data?.gradeName}</b></p>
              <p>覆盖人数/布置总人数：<b>{data?.evaluatedPeople}/{data?.totalPeople}</b></p>
              <p>覆盖学校数：参与学校<b>{data?.overSchoolCnt}</b>个</p>
              <p>报告起止时间：<b>{ moment(data?.startTime).format('YYYY.MM.DD')} ~ { moment(data?.endTime).format('YYYY.MM.DD')}</b></p>
            </div>
            <div className={styles.provenience}>
              <span>报告编号：{data?.reportCode}</span>
              <span>杭州铭师堂心理教育研究院</span>
            </div>
          </div>
        </div>
        { baseInfo.success &&
          <ComplexComponents {...{ tree: treeList, data: { ...baseInfo.data, printMode, queryParams }}} />
        }
      </div>
    </div>
  );
};
export default Page;
