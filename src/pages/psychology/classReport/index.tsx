import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import dynamic from 'next/dynamic';
import moment from 'moment';
import { classReportProps } from './types';
import { TClassReportParams } from '~/service/psychology/classReport/types';
import API from '~/service/psychology/classReport';
import treeList from '@/lib/psychology/classReport';
import EBanner from '~/components/CommonBanner';
import bannerImg from '@/assets/psychology/headerClass.png';
import bannerImg370 from '@/assets/psychology/headerClass370.png';
import footerImg from '@/assets/psychology/footerClass.png';
import ECard from '~/components/ECard';
import styles from './index.module.less';
import { useEffect } from 'react';
import { mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';
import { message } from 'antd';
const ComplexComponents = dynamic(() => import('~/complexComponents'));

export const getServerSideProps: GetServerSideProps<{
  baseInfo: classReportProps;
  queryParams: TClassReportParams;
}> = async (ctx: any) => {
  const { evaluationTaskId, scoolId, gradeId, classId, type, ff, isPrint, isDetailReport, initRecordId } = ctx.query as unknown as TClassReportParams;
  const printMode = !!(ff || isPrint);
  const detailReportMode = isDetailReport ? Boolean(isDetailReport) : false;
  try {
    const dataRes = await API.getClassReport({ evaluationTaskId, scoolId, gradeId, classId, type, initRecordId }, ctx);
    return { props: { baseInfo: dataRes, queryParams: { ...ctx.query, printMode, detailReportMode }} };
  } catch (error: any) {
    console.error('getClassReport', error);
    const { code, msg } = error;
    return {
      props: {
        baseInfo: {
          success: false,
          code,
          msg
        },
        queryParams: {
          ...ctx.query,
          printMode,
          detailReportMode
        }
      }
    };
  }
};

const Page = ({
  baseInfo,
  queryParams,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { printMode, detailReportMode } = queryParams;

  useEffect(() => {
    if (baseInfo && !baseInfo.success) {
      message.error(baseInfo.msg)
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: baseInfo.code || '500',
          errorMessage: baseInfo.msg || '接口异常',
        })
      }
      return
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: detailReportMode ? 6000 : 3000
      })
    }
  },[])

  return (
    <div {...{ className: styles['psychology-class'] }}>
      <EBanner imgSrc={ printMode ? bannerImg370 : bannerImg} height={ printMode ? 320 : 470 } />
      <div className={styles.container}>
        <ECard wrapperstyle={{ marginBottom: printMode ? 10 : 30, padding: printMode ? '6px 14px' : 24 }}>
          <div className={styles.overView}>
            <ul>
              <li>
                <b>班级:</b> {baseInfo.data?.className}
              </li>
              <li>
                <b>毕业年份:</b> {baseInfo.data?.gradeName}
              </li>
              <li>
                <b>学校:</b> {baseInfo.data?.schoolName}
              </li>
              <li>
                <b>地区:</b> {baseInfo.data?.areaName}
              </li>
              <li>
                <b>报告日期:</b>{' '}
                {moment(baseInfo.data?.reportTime).format('YYYY-MM-DD')}
              </li>
              <li>
                <b>报告编码:</b> {baseInfo.data?.reportCode}
              </li>
            </ul>
          </div>
        </ECard>
        { baseInfo && baseInfo.data &&
          <ComplexComponents {...{ tree: treeList, data: { ...baseInfo.data, queryParams}}} />
        }
      </div>
      <EBanner imgSrc={footerImg} height={90} />
    </div>
  );
};
export default Page;
