// 查看市区县与权限
export interface userViewAuthorityRes {
  areaCode: number | string | null;
  areaName: string;
  cityCode: number | string;
  cityName: string;
  hasViewAuthority: boolean;
}

export interface latitudeItem {
  latitudeId: number;
  latitudeName: string;
  referenceScore: number;
  score: number;
  warnningScore: number;
  totalScore: number;
}

export interface riskStatusDistributionItem {
  number: number;
  percentage: number;
  riskId: number;
  riskName: string;
}

export interface factorAnalysisDetailsItem {
  higherRiskStudents: string;
  highestRiskStudents: string;
  latitudeId: number;
  latitudeName: string;
  minorRiskStudents: string;
  riskLevel: number;
  riskStatusDistribution: riskStatusDistributionItem[];
  score: number;
  totalScore: number;
}

export interface factorAnalysisProps {
  factorAnalysisDetails: factorAnalysisDetailsItem[];
  inConclusion: string;
  latitudeList: latitudeItem[];
}

export interface latitudeListItem {
  higherRiskPeople: number;
  higherRiskPercentage: number;
  highestRiskPeople: number;
  highestRiskPercentage: number;
  latitudeId: number;
  latitudeName: string;
  minorRiskPeople: number;
  minorRiskPercentage: number;
  percentage: number;
  warningPeople: number;
}

export interface factorWarningProps {
  latitudeList: latitudeListItem[];
  totalPeople: number;
}

export interface riskIndexProps {
  inConclusion: string;
  remarks: string[];
  riskIndexScore: number;
  riskLevel: number;
  riskLevelName: string;
  isUser?: boolean;
}

// 班级报告
export interface classReportDataProps {
  queryParams?: any;
  sidePageNavList: any[];
  areaName: string;
  className: string;
  endTime: number;
  evaluatedPeople: number;
  factorAnalysis: factorAnalysisProps;
  factorWarning: factorWarningProps;
  gradeName: string;
  mainFindings: string[];
  reportCode: string;
  reportTime: number;
  riskIndex: riskIndexProps;
  schoolName: string;
  startTime: number;
  stateDistribution: riskStatusDistributionItem[];
  totalPeople: number;
}

export interface classReportProps {
  code?: string | number;
  msg?: string;
  success?: boolean;
  data?: classReportDataProps;
  queryParams?: any;
}