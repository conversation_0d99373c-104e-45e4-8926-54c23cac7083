
// 查看市区县与权限
export interface viewAuthorityProps {
  areaCode?: string | number | null;
  areaName?: string | null;
  cityCode?: string | number;
  cityName?: string;
  hasViewAuthority?: boolean;
}

export interface gradeTermItem {
  gradeCode: number;
  graduationYear: number;
  name: string;
  semester: number;
}

export interface areaSelectItem {
  areaId: number;
  areaName: string;
}

export interface schoolStaticsItem {
  answerTaskId: string;
  areaCode: number;
  areaName: string;
  assignDate: number;
  endDate: number;
  finishedNum: number;
  finishedPercentage: number;
  gradeFlag: number;
  gradeName: string;
  schoolCode: number;
  schoolName: string;
  totalNum: number;
}

export interface areaCountyCoverEntranceProps {
  schoolCount?: number;
  schoolList?: schoolStaticsItem[];
}

export interface schoolItem {
  answerTaskId: string | null;
  areaCode: number;
  areaName: string;
  assignDate: number | null;
  endDate: number | null;
  finishedNum: number;
  finishedPercentage: number;
  gradeFlag: number | null;
  gradeName: string;
  schoolCode: number;
  schoolName: string;
  totalNum: number;
}

export interface areaCitySchoolEntranceProps {
  schoolCount?: number | null;
  schoolList?: schoolItem[];
}
