
export interface latitudeItem {
    latitudeId: number;
    latitudeName: string;
    referenceScore: number;
    score: number;
    warnningScore: number;
}

export interface factorAnalysisProps {
    inConclusion: string;
    latitudeList: latitudeItem[]
}

export interface schoolFactorComparisonItem {
    schoolCode: number;
    schoolName: string;
    score: number;
}

export interface schoolPercentageBeansItem {
    healthPeople: number;
    healthPercentage: number;
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    schoolCode: number;
    schoolName: string;
}

export interface factorAnalysisDetailsItem {
    score: number;
    totalScore: number;
    inConclusion: string;
    latitudeId: number;
    latitudeName: string;
    referenceScore: number;
    schoolFactorComparison: schoolFactorComparisonItem[];
    schoolPercentageBeans: schoolPercentageBeansItem[];
}

export interface focusOnSchoolsItem {
    highRiskPeople: number;
    percentage: number;
    schoolName: string;
}

export interface riskIndexProps {
    conclusion: string;
    remarks: string[];
    riskIndexScore: number;
    riskLevel: number;
    riskLevelName: string;
}

export interface schoolHealthWarningProps {
    conclusion: string;
    schoolWarningBean: schoolFactorComparisonItem[];
    warningPoints: number;
}

export interface schoolIndexAnalysisItem {
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    schoolCode: number;
    schoolName: string;
    warningPeople: number;
    warningPeoplePercentage: number;
}

export interface schoolPercentageItem {
    healthPeople: number;
    healthPercentage: number;
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    schoolCode: number;
    schoolName: string;
}

export interface riskStatusDistributionItem {
    number: number;
    percentage: number;
    riskId: number;
    riskName: string;
}

export interface areaReportDataProps {
    queryParams?: any;
    areaName: string;
    districtName: string;
    endTime: number;
    evaluatedPeople: number;
    factorAnalysis: factorAnalysisProps;
    factorAnalysisDetails: factorAnalysisDetailsItem[];
    focusOnSchools: focusOnSchoolsItem[];
    gradeName: string;
    mainFindings: string[];
    overSchoolCnt: number;
    reportCode: string;
    reportTime: number;
    riskIndex: riskIndexProps;
    schoolHealthWarning: schoolHealthWarningProps;
    schoolIndexAnalysis: schoolIndexAnalysisItem[];
    schoolPercentage: schoolPercentageItem[];
    startTime: number;
    stateDistribution: riskStatusDistributionItem[];
    totalPeople: number;
}

export interface areaReportProps {
    code?: string | number;
    msg?: string;
    success?: boolean;
    data?: areaReportDataProps;
    queryParams?: any;
}

export interface areaWarningItem {
    classId: number;
    className: string;
    highRiskCount: number;
    highRiskPercentage: number;
    higherRiskCount: number;
    higherRiskPercentage: number;
    minorRiskCount: number;
    minorRiskPercentage: number;
    noRiskCount: number;
    noRiskPercentage: number;
    schoolId: number;
    schoolName: string;
    totalCount: number;
}

export interface pageAreaWarningDetailProps {
    data: areaWarningItem[];
    haveNextPage: boolean;
    havePrePage: boolean;
    pageIndex: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
}