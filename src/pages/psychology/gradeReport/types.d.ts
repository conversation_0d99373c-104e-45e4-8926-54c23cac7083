
export interface userViewAuthorityRes {
  areaCode: number | string | null;
  areaName: string;
  cityCode: number | string;
  cityName: string;
  hasViewAuthority: boolean;
}



export interface latitudeItem {
  latitudeId: number;
  latitudeName: string;
  referenceScore: number;
  score: number;
  warnningScore: number;
}

export interface riskStatusDistributionItem {
  number: number;
  percentage: number;
  riskId: number;
  riskName: string;
}

export interface classWarningItem {
  classId: string;
  className: string;
  higherRiskPeople: number;
  higherRiskPercentage: number;
  highestRiskPeople: number;
  highestRiskPercentage: number;
  minorRiskPeople: number;
  minorRiskPercentage: number;
  noRiskPeople: number;
  percentage: number;
  warningPeople: number;
}

export interface classComparisonAnalysisItem {
  classId: string;
  className: string;
  riskLevel: number;
  score: number;
}



export interface factorAnalysisProps {
  factorAnalysisDetails: classFactorAnalysisDetailsItem[];
  inConclusion: string;
  latitudeList: latitudeItem[];
}

export interface latitudeListItem {
  higherRiskPeople: number;
  higherRiskPercentage: number;
  highestRiskPeople: number;
  highestRiskPercentage: number;
  latitudeId: number;
  latitudeName: string;
  minorRiskPeople: number;
  minorRiskPercentage: number;
  percentage: number;
  warningPeople: number;
}


export interface riskIndexProps {
  inConclusion: string;
  isUser?: boolean,
  remarks: string[];
  riskIndexScore: number;
  riskLevel: number;
  riskLevelName: string;
}

export interface warningStudentItem {
  anxietyTendency?: number;
  anxietyTendencyLevel?: number;
  anxietyTendencyLevelName?: string;
  classId?: string;
  className?: string;
  depressionTendency?: number;
  depressionTendencyLevel?: number;
  depressionTendencyLevelName?: string;
  fearTendency?: number;
  fearTendencyLevel?: number;
  fearTendencyLevelName?: string;
  hostileTendency?: number;
  hostileTendencyLevel?: number;
  hostileTendencyLevelName?: string;
  interpersonalSensitivity?: number;
  interpersonalSensitivityLevel?: number;
  interpersonalSensitivityLevelName?: string;
  obsessive?: number;
  obsessiveLevel?: number;
  obsessiveLevelName?: string;
  paranoidTendency?: number;
  paranoidTendencyLevel?: number;
  paranoidTendencyLevelName?: string;
  physicalSymptoms?: number;
  physicalSymptomsLevel?: number;
  physicalSymptomsLevelName?: string;
  psychotic?: number;
  psychoticLevel?: number;
  psychoticLevelName?: string;
  riskIndex?: number;
  riskLevelName?: string;
  userId?: string;
  userName?: string;
  userRecordId?: string;
  userRecordIdUrl?: string;
  warningMark?: number;
}

export interface classWarningStudentProps {
  data: warningStudentItem[];
  haveNextPage: boolean;
  havePrePage: boolean;
  pageIndex: number;
  pageSize: number;
  totalPages: number;
  totalRecords: number;
}

export interface mainFindingsProps {
  characterTraits:string[];
  growthEnvironment: string[];
  mentalHealth:string[];
  studyReadiness: string[];
}

export interface factorWarningVOSItem {
  classId: number;
  highRisk: number;
  highRiskPercentage: string;
  higherRisk: number;
  higherRiskPercentage: string;
  minorRisk: number;
  minorRiskPercentage: string;
  name: string;
  percentage: string;
  total: number;
  warningNum: number;
}

export interface latitudeListBeansItem {
  id: number;
  name: string;
  num: number;
  percentage: string;
}

export interface pieChartVOSProps {
  inConclusion: string;
  latitudeListBeans: latitudeListBeansItem[];
}
export interface factorAnalysisDetailsItem {
  counselingAdvice: string[];
  factorDescription: string[];
  factorWarningVOS: factorWarningVOSItem[];
  id: number;
  level: number;
  levelName: string;
  name: string;
  pieChartVOS: pieChartVOSProps;
  score: number;
  studentPerformed: string[];
  totalScore: string;
}

export interface classFactorAnalysisDetailsItem {
  classComparisonAnalysis: classComparisonAnalysisItem[];
  classWarning: classWarningItem[];
  latitudeId: number;
  latitudeName: string;
  riskLevel: number;
  riskStatusDistribution: riskStatusDistributionItem[];
  score: number;
  totalScore: number;
  warningReferenceScore: number;
}


export interface factorWarningLatitudeItem {
  highRisk: number;
  highRiskPercentage: string;
  higherRisk: number;
  higherRiskPercentage: string;
  id: number;
  minorRisk: number;
  minorRiskPercentage: string;
  name: string;
  num: number;
  percentage: string;
}
export interface factorWarningProps {
  latitudeList: factorWarningLatitudeItem[];
  totalPeople: number;
}

// export interface factorWarningProps {
//   latitudeList: latitudeListItem[];
//   totalPeople: number;
// }

export interface resultAnalysisLatitudeItem {
  id: number;
  name: string;
  referenceScore: number;
  score: number;
  warnningScore: number;
}
export interface resultAnalysisProps {
  inConclusion: string;
  latitudeList: resultAnalysisLatitudeItem[];
}

export interface statusAnalysisRiskIndexProps {
  inConclusion: string;
  level: number;
  name: string;
  score: number;
}

export interface stateDistributionProps {
  inConclusion: string;
  latitudeListBeans: latitudeListBeansItem[];
}

// export interface statusAnalysisProps {
//   factorAnalysisDetails: factorAnalysisDetailsItem[];
//   factorWarning: factorWarningProps;
//   factorWarningVOS: factorWarningVOSItem[];
//   resultAnalysis: resultAnalysisProps;
//   riskIndex: statusAnalysisRiskIndexProps;
//   stateDistribution: stateDistributionProps;
// }

export interface fatherEducationItem {
  id: number;
  name: string;
  num: number;
}


export interface parentingStyleItem {
  description: string[];
  id: number;
  name: string;
  num: number;
  percentage: string;
}

export interface familyUpbringingAnalysisProps {
  fatherEducation: fatherEducationItem[];
  life: fatherEducationItem[];
  motherEducation: fatherEducationItem[];
  onlyChild: fatherEducationItem[];
  parentingStyle: parentingStyleItem[];
}

export interface learningStatusFactorAnalysisDetailsItem {
  counselingAdvice: string[];
  factorDescription: string[];
  id: number;
  level: number;
  levelName: string;
  low: string[];
  lower: string[];
  name: string;
  pieChartVOS: pieChartVOSProps;
  score: number;
  studentPerformed: string[];
  totalScore: string;
}

// export interface learningStatusProps {
//   classStudyReadiness: factorWarningVOSItem[];
//   factorAnalysisDetails: learningStatusFactorAnalysisDetailsItem[];
//   factorWarning: factorWarningProps
//   pieResultAnalysis: pieChartVOSProps;
//   resultAnalysis: resultAnalysisProps;
//   riskIndex: statusAnalysisRiskIndexProps;
// }

export interface personalityAnalysisDetailsItem {
  characterTraits: string[];
  counselingAdvice: string[];
  id: number;
  name: string;
  percentage: string;
}

// export interface personalityAnalysisProps {
//   analysisDetails: personalityAnalysisDetailsItem[];
//   pieChartVO: pieChartVOSProps;
// }

export interface stateDistributionItem {
  number: number;
  percentage: number;
  riskId: number;
  riskName: string;
}

// 年级报告
 export interface gradeReportDataProps {
  queryParams?: any;
  areaName: string;
  classWarning: classWarningItem[];
  endTime: number;
  evaluatedPeople: number;
  factorAnalysis: factorAnalysisProps;
  factorWarning: factorWarningProps;
  gradeName: string;
  mainFindings: string[];
  reportCode: string;
  reportTime: number;
  riskIndex :riskIndexProps;
  schoolName: string;
  startTime: number;
  stateDistribution: stateDistributionItem[];
  totalPeople: number;

  // learningStatus: learningStatusProps;
  // personalityAnalysis: personalityAnalysisProps;
  // statusAnalysis: statusAnalysisProps;
  version: number;
}


export interface gradeReportProps {
  code?: string | number;
  msg?: string;
  success?: boolean;
  data?: gradeReportDataProps;
  queryParams?: any;
}


