.psychology-grade {
  font-size: 14px;
  box-sizing: border-box;
  margin: 0 auto;
  color: rgba(0, 17, 57, 0.85);
  .container {
    width: 1000px;
    box-sizing: border-box;
    margin: -80px auto 0;
  }
  .overView {
    li {
      display: inline-block;
      min-width: 25%;
      font-size: 14px;
      line-height: 36px;
      b {
        padding-right: 6px;
      }
    }
  }
  .rightTitle {
    font-size: 20px;
    font-weight: 700;
    color: #475370;
    span {
      margin-right: 4px;
    }
  }
  .riskDistribution {
    padding-left: 20px;
  }
  .textBox {
    p {
      line-height: 32px;
      font-size: 18px;
      margin-top: 20px;
      span {
        color: #162755;
        font-weight: 700;
      }
    }
  }
  .optionBox {
    display: inline-block;
    position: absolute;
    top: -60px;
    right: 0;
    margin-bottom: 30px;
    text-align: right;
    button {
      margin-left: 30px;
      height: 36px;
      background: #00c865;
      border-radius: 4px;
      font-size: 16px;
      border:0;
      color: #fff;
      .iconDownload {
        display: inline-block;
      }
    }
  }
  .searchForm {
    position: relative;
    margin: 10px 0px 90px;
    .buttonStyle {
      position: absolute;
      right: -16px;
      button[type="submit"] {
        background: #00c865;
        border-radius: 4px;
        font-size: 16px;
        border:0;
        color: #fff;
      }
    }
  }

  .customTable,
  .factorTable {
    display: block;
    position: relative;
    text-align: left;
    margin-bottom: 30px;
    border-radius: 8px;
    color: rgba(0,0,0,.65);
    thead > tr > th {
      background: rgba(0, 200, 101, 0.1);
      font-size: 16px;
      padding: 10px;
    }
    tbody > tr {
      &:nth-child(even) {
        background: rgba(0, 200, 101, 0.05)
      }
      td {
        font-size: 16px;
        padding: 12px 6px;
        color: rgba(0,0,0,.65);
      }
      a {
        color: rgb(250, 140, 22);
        text-decoration: underline;
      }
      .empty {
        color: #ccc;
      }
    }
    :global {
      .ant-pagination li:first-child {
        float: left;
      }
      .ant-pagination-item:hover,
      .ant-pagination-item-link:hover {
        border:1px solid #38d486;
        color: #38d486;
        a {
          color: #38d486;
        }
      }
      .ant-pagination-item-active {
        background: #38d486;
        border: none;
        a, a:hover {
          color: #fff;
        }
      }
    }
    p {
      line-height: 24px;
      font-size: 18px;
      color: #001139;
      margin-top: 15px;
    }
    .explain {
      text-align: center;
      margin-top: 16px;
      p {
        margin: 0;
        line-height: 32px;
        font-size: 14px;
        color: rgba(0, 17, 57, 0.65);
        .explainItem {
          margin: 0 10px;
          .colorItem {
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 6px;
          }
        }
      }
    }
  }
  .factorTable {
    thead > tr > th {
      background: rgba(0, 200, 101, 0.1);
      font-size: 18px;
      padding:2px 10px;
    }
    tbody > tr {
      td {
        padding: 16px 10px;
        color: #243355;
        font-size: 18px;
      }
    }
  }
}

