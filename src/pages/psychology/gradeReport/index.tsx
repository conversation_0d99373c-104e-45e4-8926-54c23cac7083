import { useEffect, useState } from 'react';
import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import dynamic from 'next/dynamic';
import moment from 'moment';
import { gradeReportProps } from './types'
import { TGradeReportParams } from '~/service/psychology/gradeReport/types'
import API from '~/service/psychology/gradeReport'
import styles from './index.module.less';
import treeList from '@/lib/psychology/gradeReport';
import EBanner from '~/components/CommonBanner';
import bannerImg from '@/assets/psychology/headerGrade.png';
import bannerImg370 from '@/assets/psychology/headerGrade370.png';
import footerImg from '@/assets/psychology/footerGrade.png';
import ECard from '~/components/ECard';
import { mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';
import { message } from 'antd';

const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  baseInfo: gradeReportProps;
  queryParams: TGradeReportParams
}> = async (ctx: any) => {
  const { evaluationTaskId, scoolId, gradeId, bizType, classGroupNum, type, ff, isPrint, isDetailReport, initRecordId } = ctx.query as unknown as TGradeReportParams;
  const printMode = !!(ff || isPrint);
  const detailReportMode = isDetailReport ? Boolean(isDetailReport) : false;
  try {
    const reportRes = await API.getGradeReport(
      { evaluationTaskId, scoolId, gradeId, type, classGroupNum, bizType, clientType: 1, initRecordId },
      ctx
    );
    return { props: { baseInfo: reportRes, queryParams: { ...ctx.query, printMode, detailReportMode } } };
  } catch (error: any) {
    console.error('getGradeReport', error);
    const { code, msg } = error;
    return {
      props: {
        baseInfo: {
          success: false,
          code,
          msg,
        },
        queryParams: {
          ...ctx.query,
          printMode,
          detailReportMode
        }
      }
    };
  }
};

const Page = ({
  baseInfo,
  queryParams,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { printMode, detailReportMode } = queryParams;
  useEffect(() => {
    if (baseInfo && !baseInfo.success) {
      message.error(baseInfo.msg)
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: baseInfo.code || '500',
          errorMessage: baseInfo.msg || '接口异常',
        })
      }
      return
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: detailReportMode ? 6000 : 3000
      })
    }
  }, [])

  return (
    <div {...{ className: styles['psychology-grade'] }}>
      <EBanner imgSrc={printMode ? bannerImg370 : bannerImg} height={printMode ? 320 : 470} />
      <div className={styles.container}>
        <ECard wrapperstyle={{ marginBottom: printMode ? 10 : 30, padding: printMode ? '6px 14px' : 24 }}>
          <div className={styles.overView}>
            <ul>
              <li>
                <b>毕业年份:</b> {baseInfo.data?.gradeName}
              </li>
              <li>
                <b>地区:</b> {baseInfo.data?.areaName}
              </li>
              <li>
                <b>报告日期:</b>
                {moment(baseInfo.data?.reportTime).format('YYYY-MM-DD')}
              </li>
              <li>
                <b>学校:</b> {baseInfo.data?.schoolName}
              </li>
              <li>
                <b>报告编码:</b> {baseInfo.data?.reportCode}
              </li>
            </ul>
          </div>
        </ECard>
        { baseInfo && baseInfo.data &&
          <ComplexComponents {...{ tree: treeList, data: { ...baseInfo.data, queryParams } }} /> 
        }
      </div>
      <EBanner imgSrc={footerImg} height={90} />
    </div>
  );
};
export default Page;

