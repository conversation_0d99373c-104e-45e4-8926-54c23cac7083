import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import dynamic from 'next/dynamic';
import moment from 'moment';
import { personReportProps } from './types';
import { TPersonReportParams } from '~/service/psychology/personReport/types';
import API from '~/service/psychology/personReport';
import treeList from '@/lib/psychology/personReport';
import EBanner from '~/components/CommonBanner';
import ECard from '~/components/ECard';
import { useEffect } from 'react';
import { message } from 'antd';
import { mountPSTScreenshot, stopPSTScreenshotHook } from '~/utils/tools';
import bannerImg from '@/assets/psychology/headerPersonal.png';
import footerImg from '@/assets/psychology/footerPersonal.png';
import styles from './index.module.less';

const ComplexComponents = dynamic(() => import('~/complexComponents'));

export const getServerSideProps: GetServerSideProps<{
  baseInfo: personReportProps;
  queryParams: TPersonReportParams
}> = async (ctx: any) => {
  const { evaluationRecordId, initRecordId, ff, isPrint, isDetailReport } = ctx.query as unknown as TPersonReportParams;
  const printMode = !!(ff || isPrint);
  const detailReportMode = isDetailReport ? Boolean(isDetailReport) : false;
  try {
    const response = await API.getUserReport({ evaluationRecordId, clientType: 1, initRecordId },ctx);
    return {
      props: {
        baseInfo: response,
        queryParams: {...ctx.query, printMode, detailReportMode }
      }
    };
  } catch (error: any) {
    
    console.error('getUserReport', error);
    const { code, msg } = error;
    return {
      props: {
        baseInfo: { success: false, code, msg },
        queryParams: {...ctx.query, printMode, detailReportMode }
      }
    };
  }
};

const Page = ({
  baseInfo,
  queryParams
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const { printMode, detailReportMode } = queryParams;

  useEffect(() => {
    if (baseInfo && !baseInfo.success) {
      message.error(baseInfo.msg)
      if (printMode) {
        stopPSTScreenshotHook({
          errorCode: baseInfo.code || '500',
          errorMessage: baseInfo.msg || '接口异常',
        })
      }
      return
    }
    if (printMode) {
      mountPSTScreenshot({
        currentUrl: location.href,
        delay: detailReportMode ? 6000 : 3000
      })
    }
  }, [])
  return (
    <div {...{ className: styles['psychology-person'] }}>
      <EBanner imgSrc={bannerImg} height={470} />
      <div className={styles.container} style={{ marginTop: printMode ? -100 : -80}}>
        <ECard wrapperstyle={{ marginBottom: printMode ? 10 : 30, padding: printMode ? '6px 14px' : 24 }}>
          <div className={styles.overView}>
            <ul>
              <li>
                <b>姓名:</b> {baseInfo.data?.userName}
              </li>
              <li>
                <b>班级:</b> {baseInfo.data?.className}
              </li>
              <li>
                <b>年级:</b> {baseInfo.data?.gradeName}
              </li>
              <li>
                <b>学校:</b> {baseInfo.data?.schoolName}
              </li>
              <li>
                <b>地区:</b> {baseInfo.data?.areaName}
              </li>
              <li>
                <b>报告日期:</b>
                {moment(baseInfo.data?.reportTime).format('YYYY-MM-DD')}
              </li>
              <li>
                <b>报告编码:</b> {baseInfo.data?.reportCode}
              </li>
            </ul>
          </div>
        </ECard>
        { baseInfo && baseInfo.data &&
          <ComplexComponents {...{ tree: treeList, data: { ...baseInfo.data, queryParams } }} />
        }
      </div>
      <EBanner imgSrc={footerImg} height={90} />
    </div>
  );
};
export default Page;
