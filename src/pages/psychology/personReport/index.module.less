.psychology-person {
  font-size: 14px;
  box-sizing: border-box;
  margin: 0 auto;
  .container {
    width: 1000px;
    box-sizing: border-box;
    margin: -80px auto 0;
  }
  .overView {
    li {
      display: inline-block;
      min-width: 25%;
      font-size: 14px;
      line-height: 36px;
      b {
        padding-right: 6px;
      }
    }
  }
  .reportDescription {
    h3 {
      font-size: 18px;
      font-weight: 700;
      text-align: left;
      color: rgba(0, 17, 57, 0.85);
      line-height: 23px;
      padding-bottom: 30px;
    }
    p {
      font-size: 16px;
      text-align: left;
      color: rgba(0, 17, 57, 0.85);
      line-height: 24px;
      padding: 0 40px 40px 0;
    }
  }

  .customTable {
    display: block;
    position: relative;
    text-align: left;
    margin-bottom: 30px;
    border-radius: 8px;
    thead > tr > th {
      background: rgba(0, 200, 101, 0.1);
      font-size: 16px;
      padding: 10px;
      color: #243355;
    }
    tbody > tr {
      &:nth-child(even) {
        background: rgba(0, 200, 101, 0.05)
      }
      td {
        font-size: 16px;
        padding: 12px 6px;
        color: #243355;
      }
      a {
        color: rgb(250, 140, 22);
        text-decoration: underline;
      }
      .empty {
        color: #ccc;
      }
    }
  }

}
