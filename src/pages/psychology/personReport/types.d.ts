
// 查看市区县与权限
export interface userViewAuthorityRes {
  areaCode: number | string | null;
  areaName: string;
  cityCode: number | string;
  cityName: string;
  hasViewAuthority: boolean;
}

export interface latitudeItem {
  latitudeId: number;
  latitudeName: string;
  referenceScore: number;
  score: number;
  warnningScore: number;
  totalScore:number;
}

export interface latitudeList {
  latitudeId: number;
  latitudeName: string;
  referenceScore: number;
  score: number;
  warnningScore: number;
}

export interface factorAnalysisProps {
  inConclusion: string;
  latitudeList: latitudeItem[]
}

export interface factorAnalysisDetailsItem {
  counselingAdvice: string;
  factorDescription: string;
  latitudeId: number;
  latitudeName: string;
  riskLevel: number;
  score: number;
  studentSituation: string;
  totalScore: number;
}

export interface riskIndexProps {
  inConclusion: string;
  remarks: string[];
  riskIndexScore: number;
  riskLevel: number;
  riskLevelName: string;
  isUser?: boolean;
}

// 个人报告
export interface personReportDataProps {
  queryParams?: any;
  areaName: string;
  className: string;
  factorAnalysis: factorAnalysisProps;
  factorAnalysisDetails: factorAnalysisDetailsItem[];
  gradeName: string;
  reportCode: string;
  reportTime: number;
  riskIndex: riskIndexProps;
  schoolName: string;
  userName: string;
}

export interface personReportProps {
  code?: string | number;
  msg?: string;
  success?: boolean;
  data?: personReportDataProps;
  queryParams?: any;
}