.psychology-city {
  font-size: 14px;
  box-sizing: border-box;
  margin: 0 auto;
  background-color: #f5f5f5;
  color: rgba(0, 17, 57, 0.85);
  .container {
    position: relative;
    width: 1000px;
    box-sizing: border-box;
    margin: -30px auto 0;
    min-height: 550px;
  }
  .downloadBox {
    display: inline-block;
    position: absolute;
    top: -60px;
    right: 0;
    margin-bottom: 30px;
    text-align: right;
    button {
      margin-left: 30px;
      height: 36px;
      background: #00c865;
      border-radius: 8px;
      font-size: 16px;
      border:0;
      color: #fff;
      .iconDownload {
        display: inline-block;
      }
    }
  }
  .customTable {
    display: block;
    position: relative;
    text-align: left;
    margin-bottom: 30px;
    border-radius: 8px;
    color: rgba(0,0,0,.65);
    thead > tr > th {
      background: rgba(0, 200, 101, 0.1);
      font-size: 16px;
      padding: 15px;
      text-align: center;
    }
    tbody > tr {
      &:nth-child(even) {
        background: rgba(0, 200, 101, 0.05);
      }
      td {
        font-size: 16px;
        padding: 15px 4px;
        text-align: center;
        color: rgba(0,0,0,.65);
      }
      a {
        color: #fa8c16;
        // text-decoration: underline;
      }
      .empty {
        color: #ccc;
      }
    }

    p {
      line-height: 24px;
      font-size: 18px;
      color: #001139;
      margin-top: 15px;
    }
  }
  .watching {
    margin:20px 0 60px 40px;
    p {
      line-height: 28px;
      font-size: 16px;
    }
  }
  .buttonCityEntrance {
    margin:0 auto;
    text-align: center;
    img {
      cursor: pointer;
    }
  }
  .multiSchool {
    :global {
      .ant-modal-header {
        border-bottom: 0;
        .ant-modal-title {
          padding-top: 20px;
          font-size: 24px;
          color: #475370;
        }
      }
    }
    p {
      font-size: 18px;
      color: rgba(0, 17, 57, 0.85);
    }
  }
  .emptyBlock {
    width: 100%;
    height: 550px;
  }
  .footerCover {
    height: 120px;
    margin: 0 auto;
    min-width: 1000px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }
}
