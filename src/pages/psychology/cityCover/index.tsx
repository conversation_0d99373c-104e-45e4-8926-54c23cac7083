import { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import { useEffect, useState } from 'react';
import { Modal, Table, message } from 'antd';
import moment from 'moment';
import dynamic from 'next/dynamic';
import EBanner from '~/components/CommonBanner';
import treeList from '@/lib/psychology/cityCover';
import headerImg from '@/assets/psychology/entry-header.png';
import footerImg from '@/assets/psychology/entry-footer.png';
import buttonCityEntrance from '@/assets/psychology/buttonCityEntrance.png';
import EntryHeader from '~/components/EntryHeader';
import styles from './index.module.less';
import API from '@/service/psychology/cityCover';
import { useSyncState } from '@/utils/hooks';
import { areaSelectItem, areaCityEntranceProps } from './types'
import { filterAddressItem, getSourceType, objectToUrlParams } from '@/utils/tools'
import commonApi from '@/service/common/index';
import { IAddressItem, IGradeItem } from '~/@types';


const ComplexComponents = dynamic(() =>
  import('~/complexComponents')
);

export const getServerSideProps: GetServerSideProps<{
  addressList: IAddressItem[];
  defaultAddress: number[];
}> = async (ctx: any) => {
  let code = 0;
  let msg = '';
  try {
    // 强制处理直接通过链接进来的历史数据
    if (ctx.query.sourceType !== '1') {
      return {
        redirect: {
          destination: `/psychology-service/common/permission?edu=1`,
          permanent: false,
        },
      };
    }
    const { data } = await commonApi.getUserAccessAddress({ depth: 1 }, ctx);
    const addressList = filterAddressItem(data.adminDataAccessNodeVO);
    // 判断是否有市级权限
    if (addressList?.[0]?.children?.[0]?.areaCode) {
      const defaultAddress = [
        addressList[0].areaCode,
        addressList[0].children[0].areaCode,
      ];
      return {
        props: {
          addressList,
          defaultAddress,
        },
      };
    }
  } catch (error) {
    console.error('getUserAccessAddress', error);
    if (error) {
      code = (error as any).code || 0;
      msg = (error as any).msg || '';
    }
  }
  return {
    redirect: {
      destination: `/psychology-service/common/permission?code=${code}&msg=${encodeURIComponent(msg)}`,
      permanent: false,
    },
  };
};

const Page = ({
  addressList,
  defaultAddress,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const [currentData, setCurrentData] = useSyncState<any>({
    cityCode: defaultAddress[1]
  })
  const [gradeInfoList, setGradeInfoList] = useState<IGradeItem[]>([]);
  const [currentAddress, setCurrentAddress] = useState<number[]>(defaultAddress);
  const [currentGrade, setCurrentGrade] = useState<any[]>([])
  const [areaOptions, setAreaOptions] = useState<areaSelectItem[]>([])
  const [areaCityEntrance, setAreaCityEntrance] = useState<areaCityEntranceProps>({})

  const [multiSchoolInfo, setMultiSchoolInfo] = useState<any>({
    visible: false,
    schoolList: []
  });

  // 获取年级-学期列表
  const getGradeInfoList = async () => {
    try {
      const { cityCode } = currentData.current;
      const { data } = await API.getGradeInfoList({
        cityCode,
      });
      // 检查是否有数据
      if (data?.[0]?.children?.[0]?.value) {
        setGradeInfoList(data);
        handleGrade([data[0].value, data[0].children[0].value]);
        return
      }
    } catch (error) {
      console.log('getGradeInfoList', error);
    }
    message.info('暂无数据，请切换省、市查看');
  }

  // 获取地区
  const getAreaSelectOptions = async () => {
    try {
      const { cityCode, graduationYear, reportDateStr } = currentData.current;
      const { data } = await API.getAreaSelect({ cityCode, graduationYear, reportDateStr, sourceType: getSourceType() })
      setAreaOptions(data)
    } catch (error) {
      console.log('getAreaSelectOptions', error)
    }
  }

  // 获取区县报告
  const getAreaCityEntranceList = async () => {
    try {
      const { cityCode, graduationYear, reportDateStr } = currentData.current;
      const { data } = await API.getAreaCityEntranceList({ type: 1, cityCode, graduationYear, reportDateStr })
      setAreaCityEntrance(data)
    } catch (error) {
      console.log('getAreaCityEntranceList', error)
    }
  }

  // 切换省、市
  const handleAddress = (values: number[]) => {
    setCurrentAddress([...values]);
    setCurrentData({
      ...currentData.current,
      cityCode: values[1],
      graduationYear: 0,
      reportDateStr: '',
    });
    setGradeInfoList([]);
    setCurrentGrade([]);
    getGradeInfoList();
  }

  // 切换 年级 学期
  const handleGrade = (values: any[]) => {
    setCurrentGrade([...values])
    setCurrentData({
      ...currentData.current,
      graduationYear: values[0],
      reportDateStr: values[1],
    })
    getAreaSelectOptions()
    getAreaCityEntranceList()
  };

  const handleJumpLink = () => {
    const { cityCode, areaCode, graduationYear, reportDateStr } = currentData.current;
    const params = {
      cityCode,
      areaCode,
      gradeId: graduationYear,
      reportDateStr,
      sourceType: getSourceType()
    }
    window.open(`${location.origin}/psychology-service/psychology/cityReport?${objectToUrlParams(params)}`, '_blank')
  }

  // 跳转到年级
  const JumpToGradeReport = (record: any) => {
    if (!record.gradeFlag) return;
    const { cityCode, graduationYear, reportDateStr } = currentData.current;
    const params: any = {
      type: 2,
      cityCode,
      schoolCode: record.schoolCode,
      areaCode: record.areaCode,
      evaluationTaskId: record.answerTaskId,
      gradeId: graduationYear,
      reportDateStr
    }
    const link = `${location.origin}/psychology-service/psychology/gradeReport?${objectToUrlParams(params)}`
    window.open(link)
  }

  // 查下单个学校多份报告
  const handleSearch = async(params: any, record:any) => {
    try {
      const { data } = await API.getMultiAnswerTaskInfoBySchoolId(params)
      setMultiSchoolInfo({
        ...record,
        schoolList: data || [],
        visible: true,
      })
    } catch (error) {
      console.error('getMultiAnswerTaskInfoBySchoolId', error)
    }
  }

  const columns: any[] = [
    { title: '学校', dataIndex: 'schoolName', align: 'center' },
    { title: '年级', dataIndex: 'gradeName', align: 'center' },
    { title: '布置人数', dataIndex: 'totalNum', align: 'center' },
    { title: '完成测评人数', dataIndex: 'finishedNum', align: 'center' },
    { title: '完成率', dataIndex: 'finishedPercentage', align: 'center', render: (value: number) => <span>{value}%</span> },
    { title: '布置日期', dataIndex: 'assignDate', align: 'center', render: (value: number) => <span>{value ? `${moment(value).format('YYYY-MM-DD')}` : ''}</span> },
    { title: '截止日期', dataIndex: 'endDate', align: 'center', render: (value: number) => <span>{value ? `${moment(value).format('YYYY-MM-DD')}` : ''}</span> },
    {
      title: '集体报告', dataIndex: 'operation', align: 'center', render: (_: any, record: any, key: any) => {
        return (
          <span key={record.areaCode}>
              <a
                onClick={() => {
                  JumpToGradeReport(record);
                }}
                style={{
                  textDecoration: record.gradeFlag ? 'underline' : '',
                  color: record.gradeFlag ? 'rgba(250,140,22,1)' : 'rgba(0,17,57,0.85)',
                  cursor: record.gradeFlag ? 'pointer' : 'text',
                }}
              >
                查看集体报告
              </a>
          </span>)
      }
  }]

  useEffect(() => {
    getGradeInfoList()
  }, [])
  return (
    <div {...{ className: styles['psychology-city'] }}>
      <EntryHeader 
        bg={headerImg}
        schoolCount={areaCityEntrance?.schoolCount}
        gradeInfoList={gradeInfoList}
        currentGrade={currentGrade}
        addressList={addressList}
        currentAddress={currentAddress}
        handleChangeAddress={handleAddress}
        handleGradeChange={handleGrade}
      />
      <div className={styles.container}>
        { (currentData.current?.graduationYear && currentData.current?.reportDateStr) ?
          <ComplexComponents {...{ tree: treeList, data: { currentData: currentData.current, areaOptions, areaCityEntrance, onSearch: handleSearch } }} /> :
          <div className={styles.emptyBlock}></div>
        }
        <div className={styles.watching}>
          <p>注：</p>
          <p>市级报告生成条件：至少2所学校生成校级报告，且每所学校完成测评人数≥100人</p>
          <p>区县报告生成条件：至少2所学校生成校级报告，且每所学校完成测评人数≥100人</p>
          <p>集体报告生成条件：每所学校完成人数≥ 100人，且学校布置的测评作业至少有一个班级完成人数≥10人</p>
        </div>
        {currentData.current?.graduationYear && currentData.current?.reportDateStr ? (
          <div className={styles.buttonCityEntrance}>
            <EBanner imgSrc={buttonCityEntrance} width={305} height={114} contentStyle={{cursor: 'pointer', display: 'inline-block' }} onClick={() => handleJumpLink()}/>
          </div>
        ) : null}
        { multiSchoolInfo && multiSchoolInfo.visible &&
          <Modal
            title={`${multiSchoolInfo.schoolName}集体报告`}
            getContainer={false}
            open={multiSchoolInfo.visible}
            footer={null}
            destroyOnClose={true}
            onCancel={()=> setMultiSchoolInfo({ ...multiSchoolInfo, visible: false})}
            width={1000}
            bodyStyle={{padding:'6px 24px' }}
            className={styles.multiSchool}
          >
            <p>1、该学校被布置多次测评，且生成多份可查看报告</p>
            <p style={{marginBottom: 20}}>2、该校多份测评中可能存在重复作答数据，所以进入区县报告的学校数据会有去重处理</p>
            <Table
              columns={columns}
              dataSource={multiSchoolInfo.schoolList}
              rowKey="answerTaskId"
              pagination={false}
              className={styles.customTable}
            />
          </Modal>
        }
      </div>
      <div className={styles.footerCover} style={{ backgroundImage: `url(${footerImg})`}}></div>
    </div>
  );
};
export default Page;
