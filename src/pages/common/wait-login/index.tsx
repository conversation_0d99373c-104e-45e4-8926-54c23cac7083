import React, { useEffect } from 'react';
import { Spin } from 'antd';
import { useRouter } from 'next/router';
import styles from './index.module.less';
import { web, IS_SIT, edu } from '~/utils/hosts';
import { isInIframe } from '~/utils/tools';

const Page = () => {
  const router = useRouter();
  useEffect(() => {
    // 路由信息未准备好
    if (!router.isReady) {
      return;
    }
    const { sourceType, fromurl = '' } = router.query;
    const url = Array.isArray(fromurl) ? fromurl[0] : fromurl;
    let loginUrl = '';
    if (sourceType === '1') {
      // 表示行政端跳转
      if (isInIframe()) {
        // 在 iframe 则通知主页面进行登录
        window.parent.postMessage(
          {
            type: 'edu',
            loginState: 0,
          },
          '*'
        );
        return;
      } else {
        // 不在行政端iframe中，则自行跳转到行政端登录页
        loginUrl = `${edu}/bend-administration/user/login?redirect-url=${encodeURIComponent(
          url
        )}`;
      }
    } else {
      // 兜底跳转e网通登录页
      loginUrl = `${web}/register/#/login?sid=${
        IS_SIT ? 14 : 16
      }&fromurl=${encodeURIComponent(url)}`;
    }
    window.location.href = loginUrl;
  }, [router]);
  return (
    <div className={styles.login}>
      <Spin tip="登录中..." />
    </div>
  );
};

export default Page;
