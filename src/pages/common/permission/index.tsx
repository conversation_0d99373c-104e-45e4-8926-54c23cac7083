import React, { useEffect } from 'react';
import { Empty } from 'antd';
import { useRouter } from 'next/router';
import { stopPSTScreenshotHook } from '~/utils/tools';
import emptyImg from '@/assets/common/empty.png';
import styles from './index.module.less';

const abnormalStatus: Record<string, string> = {
  '7771020': '您暂无查看该报告的权限，请联系学校管理员',
}

interface IQueryProps {
  description?: string;
  code?: string;
  edu?: string;
  printMode?: string;
}


const Page = () => {
  const router = useRouter();
  const { description = '', code = '', edu, printMode }: IQueryProps = router.query;
  const isPrintMode = printMode && printMode === 'true' || false;
  const errorMessage = decodeURIComponent(description || '暂无访问权限，请联系管理员')
  let info: React.ReactElement | string = errorMessage;
  let customizeTheme = false;
  if (code) {
    customizeTheme = !!abnormalStatus[code];
    if (!customizeTheme) {
      info += `（${code}）`;
    }
  }
  if (edu === '1') {
    info = (
      <span>
        您好，为提升数据安全性，行政端报告已迁移至新地址：<br/>
        <a href="https://edu.ewt360.com/bend-administration/user/login">
          https://edu.ewt360.com/bend-administration/user/login
        </a><br/>
        权限申请请联系当地营销经理
      </span>
    );
  }

  useEffect(() => {
    if (isPrintMode) {
      stopPSTScreenshotHook({
        errorCode: code,
        errorMessage,
      });
    }
  },[])

  return (
    <div className={ customizeTheme ? styles.customize_permission_theme : styles.permission}>
      <Empty
        {...{
          image: customizeTheme ? emptyImg : Empty.PRESENTED_IMAGE_DEFAULT,
          description: info
        }}
      />
    </div>
  );
};

export default Page;
