import { createContext, useContext } from 'react';
import { observable } from 'mobx';

export const storeContext = createContext(Object.create(null));

export const useStore = () => {
  const store = useContext(storeContext);
  if (!store) {
    throw new Error('useStore must be used within a StoreProvider.');
  }
  return store;
};

// 所有组件共享的 store对象
export const observableStore = observable({
  rootStore: {
    lang: 'en'
  }
});