import React, { createContext, useContext, ReactElement } from 'react';
import { useLocalObservable, enableStaticRendering } from 'mobx-react-lite';
// import createStore, { IStore } from './store';

interface IProps {
  initialValue: Record<any, any>;
  children: ReactElement;
}

enableStaticRendering(typeof window === "undefined");

const StoreContext = createContext<{} | undefined>(undefined);

// export const StoreProvider = ({ initialValue, children }: IProps) => {
//   const store: IStore = useLocalObservable(createStore(initialValue));
//   return (
//     <StoreContext.Provider value={store}>
//     {children}
//     </StoreContext.Provider>
//   )
// }

// export const useStore = () => {
//   const store: IStore = useContext(StoreContext) as IStore;
//   if (!store) {
//     throw new Error('数据不存在');
//   }
//   return store;
// };
