html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

.page {
  background-color: #fff;
  max-width: 1000px;
  margin: 0 auto;
  overflow-x: auto;
}
.mt20 {
  margin-top: 20px
}
.mt30 {
  margin-top: 30px
}
.defaultColor {
  color: #373A44;
}
.primaryColor {
  color: #4B80FF;
}
.warningColor {
  color: #FF9631
}
.dangerColor {
  color: #FF5858;
}
.center {
  text-align: center;
}
.errorPage {
  margin: 0 auto;
}

.errorPage .errorBox {
  text-align: center;
}

.errorPage .number {
  background: #fff;
  position: relative;
  font: 900 30vmin 'Consolas';
  letter-spacing: 5vmin;
  text-shadow: 2px -1px 0 #000, 4px -2px 0 #0a0a0a, 6px -3px 0 #0f0f0f, 8px -4px 0 #141414, 10px -5px 0 #1a1a1a, 12px -6px 0 #1f1f1f, 14px -7px 0 #242424, 16px -8px 0 #292929;
}

.errorPage .number::before {
  background-color: #673ab7;
    background-image: radial-gradient(closest-side at 50% 50%, #ffc107 100%, rgba(0, 0, 0, 0)), radial-gradient(closest-side at 50% 50%, #e91e63 100%, rgba(0, 0, 0, 0));
    background-repeat: repeat-x;
    background-size: 40vmin 40vmin;
    background-position: -100vmin 20vmin, 100vmin -25vmin;
    width: 100%;
    height: 100%;
    mix-blend-mode: screen;
    -webkit-animation: moving 10s linear infinite both;
    animation: moving 10s linear infinite both;
    display: block;
    position: absolute;
    content: "";
}

.errorPage .description {
  font-weight: 700;
  font-size: 20px;
}

@-webkit-keyframes moving {
  to {
    background-position: 100vmin 20vmin, -100vmin -25vmin;
  }
}
@keyframes moving {
  to {
    background-position: 100vmin 20vmin, -100vmin -25vmin;
  }
}


@media print {
  canvas {
    position: static !important;
    display: block;
    break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  @page {
    margin: 0;
  }

  .mental-module-title {
    margin-top: 0;
  }

  .typePrint {
    page-break-before: always !important;
    /* page-break-after: avoid; */
    display: block;
    position: relative;
  }

  .singlePrint {
    page-break-before: always !important;
  }

  .typePrintAuto {
    page-break-before: left !important;
    /* page-break-after: avoid; */
    display: block;
    position: relative;
  }

  tr {
    page-break-after: avoid;
    page-break-inside: avoid;
    page-break-before: avoid;
  }
}

.typePrint {
  page-break-before: always !important;
  /* page-break-after: avoid; */
  display: block;
  position: relative;
}

.singlePrint {
  page-break-before: always !important;
}

@media screen {
  .typePrint {
    page-break-before: always !important;
    /* page-break-after: avoid; */
    display: block;
    position: relative;
  }

  .typePrintAuto {
    page-break-before: left !important;
    /* page-break-after: avoid; */
    display: block;
    position: relative;
  }
}

.typePrintAuto {
  page-break-before: left !important;
  /* page-break-after: avoid; */
  display: block;
  position: relative;
}

.typePrintTop {
  padding: 0 0 30px 0;
}
