// 风险等级-健康
.riskLevelBg1 {
  background-color: #5CDBD3;
}

// 风险等级-轻微
.riskLevelBg2 {
  background-color: #40A9FF;
}

// 风险等级-较高
.riskLevelBg3 {
  background-color: #FFA940;
}

// 风险等级-高
.riskLevelBg4 {
  background-color: #FF4D4F;
}

// 渐变色 - 风险等级-健康
.riskLevelImgBg1 {
  background: linear-gradient(to right, #5CDBD3, #CEF4F2) !important;
}

// 渐变色 - 风险等级-轻微
.riskLevelImgBg2 {
  background: linear-gradient(to right, #40A9FF, #C6E5FF) !important;
}

// 渐变色 - 风险等级-较高
.riskLevelImgBg3 {
  background: linear-gradient(to right, #FFA940, #FFE5C5) !important;
}

// 渐变色 - 风险等级-高
.riskLevelImgBg4 {
  background: linear-gradient(to right, #FF4D4F, #FFCACA) !important;
}

// 居中对齐
.text-center {
  text-align: center;
}

// 表格的深蓝色样式。类似thead
.table-head {
  background-color: #5B6F8C;
  color: #fff;
  font-size: 16px;
  font-weight: normal;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

.font-size-12 {
  font-size: 12px !important;
}

.font-size-14 {
  font-size: 14px !important;
}

.font-size-16 {
  font-size: 16px !important;
}

.thead-dark-blue {
  table th {
    background-color: #5B6F8C;
    color: #fff;
    font-size: 16px;
    text-align: center;
  }
}