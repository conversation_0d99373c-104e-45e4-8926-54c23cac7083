body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  margin: 0;
  padding: 0;
}

html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  overflow-y: scroll;
  -webkit-tap-highlight-color: transparent;
  font-size: 62.5%;
}

body {
  background-color: #fff;
  font: 12px/1.5 "PingFang SC", "Hiragino Sans GB", BlinkMacSystemFont,
    "Seogoe UI", Roboto, "Helvetica Neue", Helvetica, Arial,
    "Microsoft YaHei UI", "Microsoft YaHei", "Source Han Sans CN", sans-serif;
  color: #333;
}

html,
body {
  -webkit-overflow-scrolling: touch;
  height: 100%;
}

button,
input,
select,
textarea {
  font: inherit;
  outline: 0 none;
  vertical-align: middle;
}

button,
select {
  text-transform: none;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"],
.fmSubmit {
  -webkit-appearance: button;
  cursor: pointer;
  *overflow: visible;
}

input[type="search"] {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  -webkit-appearance: textfield;
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

button[disabled],
input[disabled] {
  cursor: default;
}

input[type="checkbox"],
input[type="radio"],
.fmRadio,
.fmCheckbox {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  *height: 13px;
  *width: 13px;
}

textarea {
  overflow: auto;
  resize: vertical;
}

input::-ms-clear,
input::-ms-reveal {
  display: none;
}

textarea,
input {
  -webkit-user-modify: read-write-plaintext-only;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #999;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #999;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #999;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999;
}

.placeholder {
  color: #666;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
  *display: inline;
  *zoom: 1;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

[hidden],
template {
  display: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

img {
  border: 0;
  -ms-interpolation-mode: bicubic;
}

svg:not(:root) {
  overflow: hidden;
}

pre {
  overflow: auto;
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

a {
  background: transparent;
  text-decoration: none;
  color: #08c;
}

a:active,
a:focus,
a:hover {
  outline: 0 none;
}

a:hover {
  color: #069;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: 400;
}

ul,
ol {
  list-style: none;
  list-style-image: none;
}
i,
em {
  font-style: normal;
}
* {
  box-sizing: border-box;
}
