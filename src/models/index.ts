import { createContext, useContext } from 'react';
import { GlobalModel } from './global';


const createModels = () => {
  return {
    globalModel: new GlobalModel(),

  };
};

export const rootModels = createModels();
export const modelsContext = createContext(rootModels);
export const ModelsProvider = modelsContext.Provider;

export const useModels = () => {
  return {
    ...useContext(modelsContext),
  };
};
export const useModel = <T extends keyof typeof rootModels>(store: T):
  typeof rootModels[T] => useContext(modelsContext)[store];
