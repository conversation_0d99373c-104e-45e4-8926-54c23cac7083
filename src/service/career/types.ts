export type TQueryParams = {
  userRecordId?: string;
  clientType?: string;
  secretSign?: string;
};

// CVT Report Start
export interface explainItem {
  text: string;
  title: string;
}

export interface miInfoListItem {
  basicInfo: string;
  code: string;
  desiredApprovedMeans: string;
  desiredJobPromotion: string;
  desiredJobType: string;
  desiredSalaryWelfare: string;
  name: string;
  typicalCharacteristic: string;
}

export interface miScoreInfoListItem {
  code: string;
  name: string;
  score: string;
}

export interface cvtReportResponseData {
  courseId: string;
  evaluationEnglishName: string;
  explain: explainItem[];
  introduction: string;
  lessonId: string;
  miInfoList: miInfoListItem[];
  miScoreInfoList: miScoreInfoListItem[];
  recommendMajorIdList: number[];
  reportTime: number;
  reportTitle: string;
}

export interface cvtReportResponse {
  code: string;
  data: cvtReportResponseData;
  msg: string;
  success: boolean;
}

// CVT Report End

// Holland Report Start
export interface hollandCodeGroupItem {
  basicFeatures: string;
  careerDevelopmentAdvice: string;
  hollandCode: string;
  hollandName: string;
  occupationFeatures: string;
  suitableOccupation: string;
}

export interface hollandScoreItem {
  hollandCode: string;
  hollandName: string;
  score: string;
}

export interface hollandReportResponseData {
  courseId: string;
  evaluationEnglishName: string;
  explain: string;
  hollandCodeGroup: hollandCodeGroupItem[];
  hollandScoreList: hollandScoreItem[];
  introduction: string;
  lessonId: string;
  occupationInfoList: number[];
  recommendMajorIdList: number[];
  reportTime: number;
  reportTitle: string;
}

export interface hollandReportResponse {
  code: string;
  data: hollandReportResponseData;
  msg: string;
  success: boolean;
}

// Holland Report End


// MI Report Start

export interface advantageIntelligencesItem {
  advantageAnalysis: string;
  code: string;
  desc: string;
  englishName: string;
  h5ImageUrl: string;
  jonRevelation: string;
  name: string;
  pcImageUrl: string;
  trainingSecret: string;
}

export interface latitudeItem {
  latitudeName:string;
  latitudeCode: string;
  desc: string;
  result?: string;
}

export interface latitudeInfoItem {
  code: string;
  level: number;
  name: string;
  score: string;
}

export interface MIReportResponseData {
  advantageIntelligences: advantageIntelligencesItem[];
  basicallyIncompetents: latitudeItem[];
  courseId: string;
  evaluationEnglishName: string;
  explain: string;
  introduction: string;
  latitudeInfoList: latitudeInfoItem[];
  lessonId: string;
  notVeryGoodAts: latitudeItem[];
  recommendMajorIdList: number[];
  relativelyGoodAts:latitudeItem[];
  reportTime: number;
  reportTitle: string;
  veryGoodAts: latitudeItem[];
}

export interface MIReportResponse {
  code: string;
  data: MIReportResponseData;
  msg: string;
  success: boolean;
}

// MI Report End

// MBTI Report Start

export interface explainsItem {
  desc: string;
  title: string;
}

export interface personalityTendenciesItem {
  code: string;
  desc: string;
  group: string;
  hitFlag: string;
  level: number;
  name: string;
}

export interface MBTIReportResponseData {
  advantageAreas: string;
  courseId: string;
  developmentProposals: string;
  evaluationEnglishName: string;
  explains: explainsItem[];
  growthPotentialAreas: string;
  h5KeywordImgUrl: string;
  introduction: string;
  lessonId: string;
  occupationInfoList: number[];
  pcKeywordImgUrl: string;
  personalityAdvantages: explainsItem[];
  personalityTendencies: personalityTendenciesItem[];
  personalityTraits: string;
  personalityType: string;
  personalityTypeDesc: string;
  potentialDefects: string;
  preferredWorkEnvironment: string;
  recommendMajorIdList: number[];
  reportTime: number;
  reportTitle: string;
  workAdvantages: string;
}

export interface MBTIReportResponse {
  code: string;
  data: MBTIReportResponseData;
  msg: string;
  success: boolean;
}

// MBTI Report End


// major Start

export interface majorItem {
  categoryCode: string;
  categoryName: string;
  hasCollect: boolean;
  majorId: number;
  majorName: string;
}

export interface majorListResponse {
  code: string;
  data: majorItem[];
  msg: string;
  success: boolean;
}

// major End

// occupation Start

export interface occupationItem {
  categoryCode: string;
  categoryName: string;
  hasCollect: boolean;
  occupationId: number;
  occupationName: string;
}

export interface occupationListResponse {
  code: string;
  data: occupationItem[];
  msg: string;
  success: boolean;
}

// Career Menu Start
export interface careerMenuItem {
  applyaction: string;
  childmenu?: careerMenuItem[];
  classname: string;
  id: number;
  ishot: false;
  isnew: false;
  parid: number;
  position: number;
  remark: string;
  routeurl: string;
}

export interface careerMenuResponse {
  code: string;
  data?: careerMenuItem[];
  msg: string;
  success: boolean;
}
// Career Menu End

// collect/ cancel collect
export interface collectResponse {
  code: string;
  data: boolean;
  msg: string;
  success: boolean;
}
// Career Menu End

