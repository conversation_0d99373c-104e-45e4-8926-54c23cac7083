import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import clientRequest from '@/service/clientRequest';
import type { IResponse } from '@/service/request';
import {
  TQueryParams,
  careerMenuResponse,
  cvtReportResponse,
  hollandReportResponse,
  MIReportResponse,
  MBTIReportResponse,
  majorListResponse,
  majorItem,
  occupationListResponse,
  collectResponse,
  occupationItem
} from './types';
import { web } from '@/utils/hosts';

interface IReportApi {
  getCVTReport: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<cvtReportResponse>;
  getHollandReport: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<hollandReportResponse>;
  getMBTIReport: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<MBTIReportResponse>;
  getMIReport: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<MIReportResponse>;
  getMajorList: (
    data: any,
  ) => IResponse<majorItem[]>;
  getOccupationList: (
    data:any,
  ) => IResponse<occupationItem[]>;
  addCollect: (
    data: any,
  ) => IResponse<collectResponse>;
  cancelCollect: (
    data: any,
  ) => IResponse<collectResponse>;
}

const API: IReportApi = {
  // CVT Report
  getCVTReport: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: `/api/psychology/career/evaluation/report/getCVTReportInfo`,
        params,
      },
      ctx
    ),
  // Holland Report
  getHollandReport: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: `/api/psychology/career/evaluation/report/getHollandBaseReportInfo`,
        params,
      },
      ctx
    ),
  // MI Report
  getMIReport: (params, ctx) =>
    serviceRequest({
      method: 'get',
      url: `/api/psychology/career/evaluation/report/getMIBaseReportInfo`,
      params,
    },
    ctx
  ),
  // MBTI Report
  getMBTIReport: (params, ctx) =>
    serviceRequest({
      method: 'get',
      url: `/api/psychology/career/evaluation/report/getMBTIBaseReportInfo`,
      params,
    },
    ctx
  ),
  // Major list
  getMajorList: (data) =>
    clientRequest(
      {
        method: 'post',
        url: `${web}/api/careerprod/library/major/user/collect/list/get`,
        data,
      }
    ),
  // Occupation list
  getOccupationList: (data) =>
    clientRequest(
      {
        method: 'post',
        url: `${web}/api/careerprod/library/occupation/user/collect/list/get`,
        data,
      }
    ),
  // 三库添加收藏
  addCollect: (data) =>
    clientRequest(
      {
        method: 'post',
        url: `${web}/api/careerprod/my/library/collect/add`,
        data,
      }
    ),
  // 年级报告
  cancelCollect: (data) =>
    clientRequest(
      {
        method: 'post',
        url: `${web}/api/careerprod/my/library/collect/cancel`,
        data,
      }
    ),
};

export default API;
