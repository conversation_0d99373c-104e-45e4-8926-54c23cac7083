import axios, { AxiosRequestConfig } from 'axios';
const instance = axios.create({
  baseURL: 'http://127.0.0.1:8769',
  withCredentials: false,
  timeout: 5000,
  headers: {
    'Access-Control-Allow-Origin': '*',
    'Content-Type': 'application/json',
  },
  proxy: false,
});
// const codeMessage = {
//   200: '服务器成功返回请求的数据。',
//   201: '新建或修改数据成功。',
//   202: '一个请求已经进入后台排队（异步任务）。',
//   204: '删除数据成功。',
//   400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
//   401: '用户没有权限（令牌、用户名、密码错误）。',
//   403: '用户得到授权，但是访问是被禁止的。',
//   404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
//   406: '请求的格式不可得。',
//   410: '请求的资源被永久删除，且不会再得到的。',
//   422: '当创建一个对象时，发生一个验证错误。',
//   500: '服务器发生错误，请检查服务器。',
//   502: '网关错误。',
//   503: '服务不可用，服务器暂时过载或维护。',
//   504: '网关超时。',
// };

export type TConfig = AxiosRequestConfig & {
  isNotMoveEmptyString?: boolean;
  mockUrl?: string;
  timestamp?: number;
  sign?: string;
  token?: string;
  host?: string;
};
// 添加请求拦截器
instance.interceptors.request.use(
  (config: TConfig) => {
    const { params = {}, mockUrl } = config;
    const _ = Date.now();
    config.params = { _, ...params };
    const env = process.env.DEPLOYMENT_ENV || 'prod';
    // 根据isNotMoveEmptyString是否删除 [key]='' 的参数
    if (config.params && !config.isNotMoveEmptyString) {
      for (const key in config.params) {
        if (config.params[key] === '') delete config.params[key];
      }
      // mock环境 优先取mockUrl， mockUrl不在则gateway使用mock前缀的地址
      if (env === 'mock' && mockUrl && typeof mockUrl === 'string') {
        config.url = mockUrl.trim();
      }
      return config;
    }
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

export interface IPageParams {
  pageIndex: number;
  pageSize: number;
}
export type IResponseData<T> = {
  code: number;
  data: T;
  success: boolean;
  msg?: string;
  needLogin: boolean;
  hashId?: string;    // pdf 接口格式
  filePath?: string; // pdf 接口格式
  status?: string; // pdf 接口格式
};

export type IResponse<T> = Promise<IResponseData<T>>;

/** 默认使用http状态码的请求的泛型  要自定义响应状态码可用注释掉的那一行 */
const request = async <T>(config: TConfig) => {
  return instance.request<IResponse<T>>({ ...config }).then((res) => res.data);
};

export default request;
