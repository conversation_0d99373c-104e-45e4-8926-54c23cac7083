import type { IResponse } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import {
  TQueryParams,
  IResponseData,
  IUserAccessAddressResponse,
  IUserAccessAddressParams,
  IUserViewAuthData,
  TCheckStatusParams,
  UserBaseInfo
} from './types';
import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import { pst, web } from '~/utils/hosts';

interface ICommonReportDApi {
  generatePDFByPage: (data: TQueryParams) => IResponse<IResponseData>;
  asyncPDFByPage: (data: TQueryParams) => IResponse<IResponseData>;
  queryPDFStatus: (data: TCheckStatusParams) => IResponse<IResponseData>;
  getUserAccessAddress: (
    params: IUserAccessAddressParams,
    ctx: ContextProps
  ) => IResponse<IUserAccessAddressResponse>;
  getUserViewAuth: (
    data: IUserViewAuthData,
    ctx: ContextProps
  ) => IResponse<boolean>;
  getUCBaseInfo: () => IResponse<UserBaseInfo.Data>;
  getJoinedClassesAndExtendedInfo:  () => IResponse<any>
}

const api: ICommonReportDApi = {
  // 下载pdf
  generatePDFByPage: (data) =>
    clientRequest({
      method: 'POST',
      url: `${pst}/api/print`,
      data,
    }),
    // 下载pdf异步
    asyncPDFByPage: (data) =>
    clientRequest({
      method: 'POST',
      url: `${pst}/api/print/async`,
      data,
    }),
    // PST查询文件状态
    queryPDFStatus: (data) =>
      clientRequest({
        method: 'POST',
        url: `${pst}/api/query/list/ids`,
        data,
      }),
  getUserAccessAddress: (params, ctx) =>
    serviceRequest(
      {
        method: 'GET',
        url: '/api/psychology/evaluationReportCommon/areaList',
        params,
        // url:
        //   'http://yapi.235.mistong.com/mock/1199/evaluationReportCommon/areaList',
        // headers: {
        //   host: 'yapi.235.mistong.com'
        // }
      },
      ctx
    ),
  getUserViewAuth: (params, ctx) =>
    serviceRequest(
      {
        method: 'GET',
        url: '/api/psychology/evaluationReportCommon/getUserViewAuth',
        params,
        // url:
        //   'http://yapi.235.mistong.com/mock/1199/evaluationReportCommon/getUserViewAuth',
        // headers: {
        //   host: 'yapi.235.mistong.com'
        // }
      },
      ctx
    ),
  getUCBaseInfo: () =>
    clientRequest({
      method: 'GET',
      url: `${web}/api/usercenter/user/baseinfo`,
      params: {},
    }),
    // 获取学生已加入的班级和学校信息
    getJoinedClassesAndExtendedInfo: () =>
      clientRequest({
        method: 'GET',
        url: `${web}/api/eteacherproduct/studentManage/getJoinedClassesAndExtendedInfo`,
        params: {},
      }),
};

export default api;
