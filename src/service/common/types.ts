import { IAddressItem } from '~/@types';

export interface TQueryParams {
  type: string;
  modal: string;
  waitUntil: string;
  name: string;
  width: number;
  timeout: number;
  url: string;
  activeTriggerFuncName: string;
}

export interface TCheckStatusParams {
  ids: string[];
}

export interface IResponseData {
  hashId: string;
  filePath: string;
  status: string;
}

export interface IUserAccessAddressParams {
  depth: number;
}

export interface IUserAccessAddressResponse {
  adminDataAccessNodeVO: IAddressItem[];
}

export interface IUserViewAuthData {
  areaCode: number;
}

export declare namespace UserBaseInfo {
  export interface Request {
    /** 用户id */
    userId: string;
  }

  export interface Response {
    code: number;
    success: boolean;
    msg?: string;
    /** 注释 */
    data: Data;
  }

  export interface Data {
    userId: string;
    /** 用户姓名 */
    realName?: string;
    /** 性别：0表示男，1表示女，默认为0 */
    sex?: number;
    /** 头像地址 */
    photoUrl?: string;
    /** 地区代码 */
    areaCode?: string;
    /** 地区名称 */
    areaName?: string;
    /** qq */
    qq?: string;
    /** 邮箱 */
    email?: string;
    /** 学校ID */
    schoolId?: number;
    /** 学校名称 */
    schoolName?: string;
    /** 文理科 1,文科2理科 */
    departmentalType?: number;
    /** 会员等级（类型）会员类型如果是注册会员，返回的是试用会员的会员类型(0, 普通卡；4, 教师；7, VIP；13, 励志卡；14, 励志VIP；15, 至尊VIP"；99, 注册未激活) */
    memberType?: number;
    /** 会员等级(名称) */
    memberTypeName?: string;
    /** 会员高考省份code */
    memberProvinceCode?: string;
    /** 会员所属省份名称 */
    memberProvinceName?: string;
    /** 会员过期时间 */
    expireTime?: number;
    /** 会员过期年份 */
    expireYear?: number;
    /** 是否老师 */
    isTeacher?: boolean;
    /** 昵称 */
    nickName?: string;
    /** 用户来源 */
    source?: number;
    /** 年级（0,新高一；1，高一；2，高二；3，高三）只给学生用 */
    grade?: number;
    /** 权限等级 */
    accessType?: number;
    /** 是否过期会员 */
    isExpire?: boolean;
    /** 毕业年份 */
    graduationYear?: number;
    /** 角色id（1，学生；2，教师） */
    roleId?: number;
    /** 是否在试用中 */
    isOnTrial?: boolean;
    /** 试用过期时间 */
    trialExpireTime?: string;
  }
}
