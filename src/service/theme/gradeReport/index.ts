import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import { TGradeReportParams, gradeBasicProps, gradeOverallAnalysisProps, gradeCaseAnalysisProps } from './types';

const hostApi = 'http://yapi.235.mistong.com/mock/1199';

interface IGradeReportApi {
  getGradeBasic: (
    data: TGradeReportParams,
    ctx: ContextProps
  ) => IResponse<gradeBasicProps>;
  getGradeOverallAnalysis: (
    data: TGradeReportParams,
    ctx: ContextProps
  ) => IResponse<gradeOverallAnalysisProps>;
  getGradeCaseAnalysis: (
    data: TGradeReportParams,
    ctx: ContextProps
  ) => IResponse<gradeCaseAnalysisProps>;
}

const api: IGradeReportApi = {
  getGradeBasic: (params, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `${hostApi}/special/screening/report/grade/basic/module`,
        params,
      },
      ctx
    ),
  getGradeOverallAnalysis: (params, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `${hostApi}/special/screening/report/grade/overall/analysis/module`,
        params,
      },
      ctx
    ),
  getGradeCaseAnalysis: (params, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `${hostApi}/special/screening/report/grade/case/analysis/module`,
        params,
      },
      ctx
    ),
};

export default api;
