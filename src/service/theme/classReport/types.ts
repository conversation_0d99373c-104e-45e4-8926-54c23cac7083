export interface TClassReportParams {
    evaluationTaskId?: string;
    scoolId?: string;
    gradeId?: string;
    classId?: string;
    type?: string;
    sort?: string;
    hide?: boolean;
}

export interface classBasicModuleItem {
    /** 班级 */
    className?: string
    /** 年级 */
    gradeName?: string
    /** 学校 */
    schoolName?: string
    /** 报告编号 */
    reportCode?: string
    /** 报告时间：报告生成时间 */
    reportTime?: string
    /** 测评布置开始时间 */
    startTime?: string
    /** 测评布置结束时间 */
    endTime?: string
    /** 测评群体：参与测试人数，报告的学生数 */
    finishedNum?: number
    /** 报告样式 */
    reportStyle?: number
    /** 测评快照ID,用于报告公共模块的查询 */
    templateVersionId?: number
}
export interface classReportProps {
    /** 注释 */
  success?: boolean
  /** 注释 */
  code?: string
  /** 注释 */
  msg?: string
  /** ClassReportBasicModuleVO */
  data?: classBasicModuleItem[]
}