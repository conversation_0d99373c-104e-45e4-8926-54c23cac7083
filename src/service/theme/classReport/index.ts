import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request'
import { TClassReportParams, classReportProps } from './types'

const hostApi = 'http://yapi.235.mistong.com/mock/1199'

interface IClassReportApi {
    getClassBasic: (
        data: TClassReportParams,
        ctx: ContextProps
    ) => IResponse<classReportProps>;
    getClassOverallAnalysis: (
        data: TClassReportParams,
        ctx: ContextProps
    ) => IResponse<classReportProps>;
    getClassCaseAnalysis: (
        data: TClassReportParams,
        ctx: ContextProps
    ) => IResponse<classReportProps>;
}

const api: IClassReportApi = {
    getClassBasic: (params, ctx) =>
        serviceRequest(
            {
                method: "post",
                url: `${hostApi}/special/screening/report/class/basic/module`,
                params,
            },
        ctx,
    ),
    getClassOverallAnalysis: (params, ctx) =>
        serviceRequest(
            {
                method: "post",
                url: `${hostApi}/special/screening/report/class/overall/analysis/module`,
                params,
            },
        ctx,
    ),
    getClassCaseAnalysis: (params, ctx) =>
        serviceRequest(
            {
                method: "post",
                url: `${hostApi}/special/screening/report/class/case/analysis/module`,
                params,
            },
        ctx,
    ),
}

export default api;