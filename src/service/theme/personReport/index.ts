import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import { TPersonReportParams, personBasicProps, personOverallAnalysisProps, personCaseAnalysisProps } from './types';

const hostApi = 'http://yapi.235.mistong.com/mock/1199';

interface IPersonReportApi {
  getPersonBasic: (
    data: TPersonReportParams,
    ctx: ContextProps
  ) => IResponse<personBasicProps>;
  getPersonOverallAnalysis: (
    data: TPersonReportParams,
    ctx: ContextProps
  ) => IResponse<personOverallAnalysisProps>;
  getPersonCaseAnalysis: (
    data: TPersonReportParams,
    ctx: ContextProps
  ) => IResponse<personCaseAnalysisProps>;
}

const api: IPersonReportApi = {
  getPersonBasic: (params, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `${hostApi}/special/screening/report/grade/basic/module`,
        params,
      },
      ctx
    ),
  getPersonOverallAnalysis: (params, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `${hostApi}/special/screening/report/grade/overall/analysis/module`,
        params,
      },
      ctx
    ),
  getPersonCaseAnalysis: (params, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `${hostApi}/special/screening/report/grade/case/analysis/module`,
        params,
      },
      ctx
    ),
};

export default api;
