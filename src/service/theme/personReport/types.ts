export interface TPersonReportParams {
  evaluationRecordId: string;
}

export interface basicModelItem {
  /** 姓名 */
  userName?: string;
  /** 班级 */
  className?: string;
  /** 年级 */
  gradeName?: string;
  /** 报告编号 */
  reportCode?: string;
  /** 报告时间：报告生成时间 */
  reportTime?: string;
  /** 报告样式 */
  reportStyle?: number;
  /** 测评快照ID,用于报告公共模块的查询 */
  templateVersionId?: number;
}
export interface personBasicProps {
  /** 注释 */
  success?: boolean;
  /** 注释 */
  code?: string;
  /** 注释 */
  msg?: string;
  /** UserReportBasicModuleVO */
  data?: basicModelItem[];
}

export interface personOverallAnalysisProps {}

export interface personCaseAnalysisProps {}
