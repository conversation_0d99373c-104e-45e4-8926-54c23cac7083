export interface TQueryParams {
    type?: string | number;
    clientType?: string | number;
    cityCode?: string | number;
    gradeCode?: string | number;
    gradeId?: number;
    areaCode?: string | number;
    semester?: number;
    schoolName?: string;
    pageIndex?: number;
    pageSize?: number;
}

export interface schoolDistributionBeanItem {
    healthPeople: number;
    healthPercentage: number;
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    id: string;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    name: string;
    warningPeople: number;
    warningPeoplePercentage: number;
}

export interface statusDistributionBeanItem {
    number: number;
    percentage: number;
    riskId: number;
    riskName: string;
}
export interface inwardAttackProps {
    description: string;
    distributionRemarks: string;
    inConclusion: string;
    remarks: string;
    riskIndexScore: number
    riskLevel: number;
    riskLevelName: string;
    schoolDistributionBeanList: schoolDistributionBeanItem[];
    schoolStatusDistributionBean: schoolDistributionBeanItem[];
    statusDistributionBean: statusDistributionBeanItem[];
}

export interface schoolItem {
    areaName: string;
    classNum: number;
    finishPeople: number;
    finishPercentage: number;
    schoolName: string;
    totalPeople: number;
}
export interface areaBaseInfoProps {
    areaName: string;
    districtName: string;
    endTime: number;
    evaluatedPeople: number;
    gradeName: string;
    inwardAttack: inwardAttackProps;
    outwardAttack: inwardAttackProps;
    overSchoolCnt: number;
    overallRisk: inwardAttackProps;
    principleVideoURL:string;
    reportCode: string;
    reportTime: number;
    schoolList: schoolItem[];
    startTime: number;
    totalPeople: number
}

export interface comparativeAnalysisBeanItem {
    avgRiskIndex: number;
    id: string;
    name: string;
    riskLevel: number;
    riskLevelName: string;
}

export interface statusRiskDistributionBeanItem {
    healthPeople: number;
    healthPercentage: number;
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    id: string;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    name: string;
}

export interface factorAnalysisItem {
    counselingAdvice: string;
    factorDescription: string;
    highScorePerformance: string;
    latitudeId: number;
    latitudeName: string;
    lowScorePerformance: string;
    referenceScore: number;
    remarks: string;
    riskLevel: number;
    riskLevelName: string;
    score: number;
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
}

export interface factorScoreBeanItem {
    latitudeId: number;
    latitudeName: string;
    positiveFlag: boolean;
    referenceScore: number;
    score: number;
    warnningScore: number;
}

export interface peopleRiskItem {
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    id: string;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    name: string;
    warningPeople: number;
    warningPeoplePercentage: number;
}

export interface areaCurrentPerformanceProps {
    comparativeAnalysisBean: comparativeAnalysisBeanItem[];
    comparativeAnalysisRemarks: string;
    comparativeScore: number;
    factorAnalysis: factorAnalysisItem[];
    factorDescription: string;
    factorScoreBean: factorScoreBeanItem[];
    factorScoreRemarks: string;
    highScorePerformance: string;
    lowScorePerformance: string;
    peopleRiskList: peopleRiskItem[];
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
    statusRiskDistributionRemarks: string;
    transitionParagraph: string;
}

export interface areaPersonalityReasonsProps {
    comparativeAnalysisBean: comparativeAnalysisBeanItem[];
    comparativeAnalysisRemarks: string;
    comparativeScore: number;
    factorAnalysis: factorAnalysisItem[];
    factorDescription: string;
    factorScoreBean: factorScoreBeanItem[];
    factorScoreRemarks: string;
    highScorePerformance: string;
    lowScorePerformance: string;
    peopleRiskList: peopleRiskItem[];
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
    statusRiskDistributionRemarks: string;
    transitionParagraph: string;
}

export interface areaEnvironmentalReasonsProps {
    comparativeAnalysisBean: comparativeAnalysisBeanItem[];
    comparativeAnalysisRemarks: string;
    comparativeScore: number;
    factorAnalysis: factorAnalysisItem[];
    factorDescription: string;
    factorScoreBean: factorScoreBeanItem[];
    factorScoreRemarks: string;
    highScorePerformance: string;
    lowScorePerformance: string;
    peopleRiskList: peopleRiskItem[];
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
    statusRiskDistributionRemarks: string;
    transitionParagraph: string;
}

export interface areaCognitiveReasonsProps {
    comparativeAnalysisBean: comparativeAnalysisBeanItem[];
    comparativeAnalysisRemarks: string;
    comparativeScore: number;
    factorAnalysis: factorAnalysisItem[];
    factorDescription: string;
    factorScoreBean: factorScoreBeanItem[];
    factorScoreRemarks: string;
    highScorePerformance: string;
    lowScorePerformance: string;
    peopleRiskList: peopleRiskItem[];
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
    statusRiskDistributionRemarks: string;
    transitionParagraph: string;
}

export interface cityFocusSchoolItem {
    areaCode: number;
    areaName: string;
    highRiskNum: number;
    highRiskNumPercentage: number;
    schoolCode: number;
    schoolName: string;
}

export interface warningSchoolItem {
    classId: number;
    className: string;
    highRiskCount: number;
    highRiskPercentage: number;
    higherRiskCount: number;
    higherRiskPercentage: number;
    minorRiskCount: number;
    minorRiskPercentage: number;
    noRiskCount: number;
    noRiskPercentage: number;
    schoolId: number;
    schoolName: string;
    totalCount: number;
}

export interface pageAreaWarningDetailProps {
    data: warningSchoolItem[];
    haveNextPage: boolean;
    havePrePage: boolean;
    pageIndex: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
}

export interface baseInfoProps {
    areaBaseInfo?: areaBaseInfoProps;
}