import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import {
    TQueryParams,
    areaBaseInfoProps,
    areaCurrentPerformanceProps,
    areaPersonalityReasonsProps,
    areaEnvironmentalReasonsProps,
    areaCognitiveReasonsProps,
    pageAreaWarningDetailProps,
} from './types';

const hostApi = 'http://yapi.235.mistong.com/mock/1199'

interface IAreaReportApi {
    getAreaBaseInfo: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<areaBaseInfoProps>;
    getAreaCurrentPerformance: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<areaCurrentPerformanceProps>;
    getAreaPersonalityReasons: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<areaPersonalityReasonsProps>;
    getAreaEnvironmentalReasons: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<areaEnvironmentalReasonsProps>;
    getAreaCognitiveReasons: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<areaCognitiveReasonsProps>;
    getPageAreaWarningDetail: (
        data: TQueryParams,
    ) => IResponse<pageAreaWarningDetailProps>;
}

export interface IGetAreaWarningSchoolList {
    params: TQueryParams;
    data: IResponseData<pageAreaWarningDetailProps>;
}

const api: IAreaReportApi = {
    getAreaBaseInfo: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `${hostApi}/mentalHealthAreaV2Report/getAreaBaseInfo`,
                params,
            },
        ctx,
    ),
    getAreaCurrentPerformance: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `${hostApi}/mentalHealthAreaV2Report/getAreaCurrentPerformance`,
                params,
            },
        ctx,
    ),
    getAreaPersonalityReasons: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `${hostApi}/mentalHealthAreaV2Report/getAreaPersonalityReasons`,
                params,
            },
        ctx,
    ),
    getAreaEnvironmentalReasons: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `${hostApi}/mentalHealthAreaV2Report/getAreaEnvironmentalReasons`,
                params,
            },
        ctx
    ),
    getAreaCognitiveReasons: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `${hostApi}/mentalHealthAreaV2Report/getAreaCognitiveReasons`,
                params,
            },
        ctx
    ),
    getPageAreaWarningDetail: (params) =>
        clientRequest(
            {
                method: "POST",
                url: `${hostApi}/mentalHealthAreaV2Report/pageAreaWarningDetail`,
                params,
            }
    )
}

export default api;