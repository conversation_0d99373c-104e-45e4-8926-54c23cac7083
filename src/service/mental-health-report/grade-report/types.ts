// 年级报告的必备参数
export interface TQueryParams {
    evaluationTaskId: string;
    gradeId: string;
    classGroupNum?: string;
    clientType?: number;
    ff?: string;
    isPrint?: string;
    printMode?: boolean;
    positiveFlag?: boolean;
    initRecordId?: string;
}


// 年级报告基本信息
export interface IGradeBaseInfoProps {
    /** 年级 */
    gradeName: string;
    /** 学校 */
    schoolName: string;
    /** 地区 */
    areaName: string;
    /** 报告编号 */
    reportCode: string;
    /** 报告日期 */
    reportTime: number;
    /** 测评开始时间 */
    startTime: number;
    /** 测评结束时间 */
    endTime: number;
    /** 测评视频原理url */
    principleVideoURL: string;
    /** 完成人数 */
    evaluatedPeople: number;
    /** 总体风险 ,RiskIndexBean */
    overallRisk: IInwardAttackProps;
    /** 向内攻击 ,RiskIndexBean */
    inwardAttack: IInwardAttackProps;
    /** 向外攻击 ,RiskIndexBean */
    outwardAttack: IInwardAttackProps;
}

// 年级报告基本信息 - 结果、向内外信息
export interface IInwardAttackProps {
    /** 攻击说明 */
    description: string;
    /** 结论从测评结果来看，该班级总体风险为：高风险 */
    inConclusion: string;
    /** 风险指数，得分 */
    riskIndexScore: number;
    /** 风险级别 */
    riskLevel: number;
    /** 风险级别名称 */
    riskLevelName: string;
    /** 备注 */
    remarks: string;
    /** 风险状态分布 ,RiskStatusDistribution2Bean */
    statusDistributionBean: IStatusDistributionBean[];
    /** 各班风险分布情况 ,RiskStatusDistributionBean */
    classStatusDistributionBean: IClassStatusDistributionBean[];
}

// 年级报告基本信息 - 各班风险分布情况
export interface IClassStatusDistributionBean {
    /** 班级id */
    classId: string;
    /** 班级名称 */
    className: string;
    /** 高风险人数 */
    highestRiskPeople: number;
    /** 高风险人数占比 */
    highestRiskPercentage: number;
    /** 较高风险人数 */
    higherRiskPeople: number;
    /** 较高风险百分比 */
    higherRiskPercentage: number;
    /** 轻微风险人数 */
    minorRiskPeople: number;
    /** 轻微风险百分比 */
    minorRiskPercentage: number;
    /** 健康人数 */
    healthPeople: number;
    /** 健康人数占比 */
    healthPercentage: number;
}
// 年级报告基本信息 - 风险状态分布
export interface IStatusDistributionBean {
    /** 风险id */
    riskId: number;
    /** 风险名称 */
    riskName: string;
    /** 人数 */
    number: number;
    /** 百分比 */
    percentage: number;
}

// 年级总体概况中的因子情况，分为保护因子和危险因子
export interface IGradeResultFactors {
    /** 正向标志：true为正向，false为负向 */
    positiveFlag: boolean;
    /** 因子id */
    latitudeId: number;
    /** 因子名称 */
    latitudeName: string;
    /** 维度id */
    latitudeGroupId: number;
    /** 纬度名称 */
    latitudeGroupName: string;
    /** 得分人均得分 */
    score: number;
    /** 参考分均分 */
    referenceScore: number;
    /** 预警分 */
    warnningScore: number;
    /** 总体风险级别 */
    riskLevel: number;
}

// 年级结果分析
export interface IGradeFactorAnalysis {
    /** 正向标志：true为正向，false为负向 */
    positiveFlag: boolean;
    // 分析数据集合
    latitudeListBeanGroups: ILatitudeListBeanGroups[];
}

// 年级结果分析 - 分析数据集合
export interface ILatitudeListBeanGroups {
    /** 编码 - 暂时忽略了 */
    latitudeCode: number;
    /** 注释 */
    message: string;
    /** 维度下的各个因子的信息 ,LatitudeListBeanV2 */
    latitudeListBeanList: ILatitudeListBeanList[];
}

// 年级结果分析 - 分析数据集合 - 维度下因子信息
export interface ILatitudeListBeanList {
    /** 维度id */
    latitudeId: number;
    /** 纬度名称 */
    latitudeName: string;
    /** 得分 */
    score: number;
    /** 参考分均分 */
    referenceScore: number;
    /** 预警分 */
    warnningScore: number;
    /** 风险级别 */
    riskLevel: number;
    /** 风险级别名称 */
    riskLevelName: string;
    /** 学生情况(年级情况) */
    studentSituation: string;
    /** 注释 */
    healthPeople: number;
    /** 注释 */
    healthPercentage: number;
    /** 年级分险分布 ,RiskDistributionVO */
    riskDistributionVO: IRiskDistributionVO[];
    /** 各班风险分布情况（集体报告） ,EachClassRiskDistributionVO */
    eachClassRiskDistributionBOS: IEachClassRiskDistributionBOS[];
}

// 年级结果分析 - 分析数据集合 - 维度下因子信息 - 年级分险分布
export interface IEachClassRiskDistributionBOS {
    /** 班级id */
    classId: string;
    /** 班级姓名 */
    className: string;
    /** 分险分布 ,RiskDistributionVO */
    riskDistributionVO: IRiskDistributionVO[];
}

// 年级结果分析 - 分析数据集合 - 维度下因子信息 - 各班风险分布情况（集体报告）
export interface IRiskDistributionVO {
    /** 高分险人数 */
    highestRiskPeople: number;
    /** 高分险比例 */
    highestRiskPercentage: number;
    /** 较高分险人数 */
    higherRiskPeople: number;
    /** 较高分险比例 */
    higherRiskPercentage: number;
    /** 较低分险人数 */
    minorRiskPeople: number;
    /** 较低分险比例 */
    minorRiskPercentage: number;
    /** 健康人数 */
    healthPeople: number;
    /** 健康人比例 */
    healthPercentage: number;
    /** 风险人数占比排序时使用不对外暴露 */
    riskPercentage: number;
}

// 年级辅导建议 - 因子层级列表
export interface IGradeSuggestFactors {
    /** 维度的id */
    latitudeGroupId: number;
    /** 维度的名称 */
    latitudeGroupName: string;
    /** 各个因子的辅导建议 ,LatitudeResultAnalysisFactorSuggestion */
    analysisFactorSuggestionList: IAnalysisFactorSuggestionList[];
}

// 年级辅导建议 - 建议列表
export interface IAnalysisFactorSuggestionList {
    /** 维度的id */
    latitudeId: number;
    /** 维度的名称 */
    latitudeName: string;
    /** 辅导建议 */
    counselingAdvice: string;
}

// 因子说明 - 目录层级
export interface IFactorCatalogRemark {
    /** 正向标志：true为正向，false为负向 */
    positiveFlag: boolean;
    /** 一级维度的数据 ,ParentLatitudeInfoVO */
    parentLatitudeInfoList: IParentLatitudeInfo[];
}

// 因子说明 - 一级维度
export interface IParentLatitudeInfo {
    /** 一级维度id */
    latitudeGroupId: number;
    /** 一级纬度名称 */
    latitudeGroupName: string;
    /** 二级因维度的数据 ,LatitudeInfoVO */
    latitudeInfoList: ILatitudeInfoList[];
}

// 因子说明 - 二级维度
export interface ILatitudeInfoList {
    /** 维度id */
    latitudeId: number;
    /** 纬度名称 */
    latitudeName: string;
    /** 因子说明 */
    factorDescription: string;
    /** 高分表现 */
    highScorePerformance: string;
    /** 低分表现 */
    lowScorePerformance: string;
}

export interface IFactorDetailParams {
    evaluationRecordId: string;
    latitudeId: number;
}

// 因子详情
export interface IFactorDetail {
    /** 因子id */
    id: number;
    // 因子名称
    latitudeName?: string;
    /** 因子说明 */
    factorDescription: string;
    /** 高分表现 */
    highScorePerformance: string;
    /** 低分表现 */
    lowScorePerformance: string;
    /** 健康等级 */
    riskLevel: number;
    /** 辅导建议 */
    counselingAdvice: string;
}

// 预警名单参数
export interface ISearchWarningListParams {
    evaluationTaskId: string;
    gradeId: string;
    classId: string;
    classGroupNum?: string;
    userName?: string;
    sort: number;
    hide?: boolean;
    pageIndex: number;
    pageSize: number;
    start?: number;
}

export interface IWarningListRes {
    /** 总页数 */
    totalPages?: number;
    /** 当前页码 */
    pageIndex?: number;
    /** 每页条数 */
    pageSize?: number;
    /** 总记录数 */
    totalRecords?: number;
    /** 分页结果集 ,T */
    data?: IWarningList[];
    /** 注释 */
    havePrePage?: boolean;
    /** 注释 */
    haveNextPage?: boolean;
}

export interface IWarningList {
    /** 班级id */
    classId?: string;
    /** 班级名称 */
    className?: string;
    /** 学生id */
    userId?: string;
    /** 学生姓名 */
    userName?: string;
    /** 总体风险级别 */
    riskLevel?: number;
    /** 注释 */
    riskName?: string;
    /** 个人评测id查询个人报告需要 */
    userRecordId?: number;
    /** 向内攻击风险 */
    inwardAttackRisk?: number;
    /** 级别 */
    inwardAttackRiskLevel?: number;
    /** 注释 */
    inwardAttackRiskName?: string;
    /** 向外攻击风险 */
    outwardAttackRisk?: number;
    /** 级别 */
    outwardAttackRiskLevel?: number;
    /** 注释 */
    outwardAttackRiskName?: string;
    /** 人格原因 */
    personalityReasons?: number;
    /** 级别 */
    personalityReasonsLevel?: number;
    /** 注释 */
    personalityReasonsName?: string;
    /** 环境原因 */
    environmentalReasons?: number;
    /** 级别 */
    environmentalReasonsLevel?: number;
    /** 注释 */
    environmentalReasonsName?: string;
    /** 认知原因 */
    cognitiveReasons?: number;
    /** 级别 */
    cognitiveReasonsLevel?: number;
    /** 注释 */
    cognitiveReasonsName?: string;
    /** 当前表现 */
    currentPerformance?: number;
    /** 当前表现级别 */
    currentPerformanceLevel?: number;
    /** 注释 */
    currentPerformanceName?: string;
    /** 消极情绪管理 */
    cogNegativeEmotions?: number;
    /** 级别 */
    cogNegativeEmotionsLevel?: number;
    /** 注释 */
    cogNegativeEmotionsName?: string;
    /** 消极认知 */
    cogNegativeCognition?: number;
    /** 级别 */
    cogNegativeCognitionLevel?: number;
    /** 注释 */
    cogNegativeCognitionName?: string;
    /** 积极应对 */
    cogPositiveResponse?: number;
    /** 级别 */
    cogPositiveResponseLevel?: number;
    /** 注释 */
    cogPositiveResponseName?: string;
    /** 注意缺陷 */
    cogAttentionDefects?: number;
    /** 级别 */
    cogAttentionDefectsLevel?: number;
    /** 注释 */
    cogAttentionDefectsName?: string;
    /** 复原力 */
    cogResilience?: number;
    /** 级别 */
    cogResilienceLevel?: number;
    /** 注释 */
    cogResilienceName?: string;
    /** 抑郁 */
    curDepression?: number;
    /** 级别 */
    curDepressionLevel?: number;
    /** 注释 */
    curDepressionName?: string;
    /** 焦虑 */
    curAnxiety?: number;
    /** 级别 */
    curAnxietyLevel?: number;
    /** 注释 */
    curAnxietyName?: string;
    /** 精神病性 */
    curPsychotic?: number;
    /** 级别 */
    curPsychoticLevel?: number;
    /** 注释 */
    curPsychoticName?: string;
    /** 对立违抗 */
    curDefiance?: number;
    /** 级别 */
    curDefianceLevel?: number;
    /** 注释 */
    curDefianceName?: string;
    /** 攻击行为 */
    curBehavior?: number;
    /** 级别 */
    curBehaviorLevel?: number;
    /** 注释 */
    curBehaviorName?: string;
    /** 欺负行为 */
    curBullying?: number;
    /** 级别 */
    curBullyingLevel?: number;
    /** 注释 */
    curBullyingName?: string;
    /** 绝望 */
    curDespair?: number;
    /** 级别 */
    curDespairLevel?: number;
    /** 注释 */
    curDespairName?: string;
    /** 累赘感知 */
    curPerception?: number;
    /** 级别 */
    curPerceptionLevel?: number;
    /** 注释 */
    curPerceptionName?: string;
    /** 归属感受挫 */
    curBelonging?: number;
    /** 级别 */
    curBelongingLevel?: number;
    /** 注释 */
    curBelongingName?: string;
    /** 自我攻击行为 */
    curSelfAggressive?: number;
    /** 级别 */
    curSelfAggressiveLevel?: number;
    /** 注释 */
    curSelfAggressiveName?: string;
    /** 家庭压力 */
    envFamilyStress?: number;
    /** 级别 */
    envFamilyStressLevel?: number;
    /** 注释 */
    envFamilyStressName?: string;
    /** 学业压力 */
    envStudyingPressure?: number;
    /** 级别 */
    envStudyingPressureLevel?: number;
    /** 注释 */
    envStudyingPressureName?: string;
    /** 同伴接受 */
    envPeersAccept?: number;
    /** 级别 */
    envPeersAcceptLevel?: number;
    /** 注释 */
    envPeersAcceptName?: string;
    /** 同伴拒绝 */
    envPeerRejection?: number;
    /** 级别 */
    envPeerRejectionLevel?: number;
    /** 注释 */
    envPeerRejectionName?: string;
    /** 教师压力 */
    envTeacherPressure?: number;
    /** 级别 */
    envTeacherPressureLevel?: number;
    /** 注释 */
    envTeacherPressureName?: string;
    /** 网络欺凌 */
    envCyberbullying?: number;
    /** 级别 */
    envCyberbullyingLevel?: number;
    /** 注释 */
    envCyberbullyingName?: string;
    /** 社会支持 */
    envSocialSupport?: number;
    /** 级别 */
    envSocialSupportLevel?: number;
    /** 注释 */
    envSocialSupportName?: string;
    /** 外向性 */
    perExtroversion?: number;
    /** 级别 */
    perExtroversionLevel?: number;
    /** 注释 */
    perExtroversionName?: string;
    /** 神经质 */
    perNeuroticism?: number;
    /** 级别 */
    perNeuroticismLevel?: number;
    /** 注释 */
    perNeuroticismName?: string;
    /** 完美主义 */
    perPerfectionism?: number;
    /** 级别 */
    perPerfectionismLevel?: number;
    /** 注释 */
    perPerfectionismName?: string;
    /** 冲动性 */
    perImpulsive?: number;
    /** 级别 */
    perImpulsiveLevel?: number;
    /** 注释 */
    perImpulsiveName?: string;
    /** 自尊 */
    perSelfEsteem?: number;
    /** 级别 */
    perSelfEsteemLevel?: number;
    /** 注释 */
    perSelfEsteemName?: string;
    /** 自我效能感 */
    perSelfEfficacy?: number;
    /** 级别 */
    perSelfEfficacyLevel?: number;
    /** 注释 */
    perSelfEfficacyName?: string;
    /** 操纵性 */
    perManipulative?: number;
    /** 级别 */
    perManipulativeLevel?: number;
    /** 注释 */
    perManipulativeName?: string;
    /** 精神病态 */
    perPsychopath?: number;
    /** 级别 */
    perPsychopathLevel?: number;
    /** 注释 */
    perPsychopathName?: string;
    /** 自恋 */
    perNarcissism?: number;
    /** 级别 */
    perNarcissismLevel?: number;
    /** 注释 */
    perNarcissismName?: string;
}
