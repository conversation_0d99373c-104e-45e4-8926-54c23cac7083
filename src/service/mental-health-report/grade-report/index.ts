import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import {
    TQueryParams,
    IGradeBaseInfoProps,
    IFactorCatalogRemark,
    ILatitudeInfoList,
    IFactorDetail,
    IFactorDetailParams,
    IWarningListRes,
    ISearchWarningListParams,
    IGradeResultFactors,
    IGradeSuggestFactors,
    IGradeFactorAnalysis,
} from './types';
import clientRequest from '~/service/clientRequest';
import { web } from '@/utils/hosts';

interface IUserReportApi {
    getGradeBaseInfo: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IGradeBaseInfoProps>; // 年级基础信息
    getGradeProtectiveFactors: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IGradeResultFactors[]>; // 年级保护因子
    getGradeRiskFactors: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IGradeResultFactors[]>; // 年级风险因子
    getGradeResultAnalysis: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IGradeFactorAnalysis[]>; // 年级结果分析
    getGradeSuggestion: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IGradeSuggestFactors[]>; // 年级辅导建议
    getFactorRemarkList: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<ILatitudeInfoList[]>; // 因子说明
    getFactorCatalogRemarkList: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IFactorCatalogRemark[]>; // 因子的目录说明
    getFactorRemarkDetail: (
        data: IFactorDetailParams,
    ) => IResponse<IFactorDetail>; // 因子的详情
    getWarningList: (
        data: ISearchWarningListParams,
    ) => IResponse<IWarningListRes>; // 预警名单
}

const api: IUserReportApi = {
    getGradeBaseInfo: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: '/api/psychology/mentalHealthV2Report/getGradeBaseInfo',
                data: params,
            },
            ctx,
        ),
    getGradeProtectiveFactors: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getGradeProtectiveFactors`,
                data: params,
            },
            ctx,
        ),
    getGradeRiskFactors: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getGradeRiskFactors`,
                data: params,
            },
            ctx,
        ),
    getGradeResultAnalysis: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getGradeAnalyzed`,
                data: params,
            },
            ctx
        ),
    getGradeSuggestion: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getGradeLatitudeAnalysisSuggestion`,
                data: params,
            },
            ctx
        ),
    getFactorRemarkList: (params, ctx) =>
        serviceRequest(
            {
                method: "GET",
                url: `/api/psychology/mentalHealthV2Report/getLatitudeInfoList`,
                params,
            },
            ctx
        ),
    getFactorCatalogRemarkList: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getFactorInfoList`,
                data: params,
            },
            ctx
        ),
    getWarningList: (params) =>
        clientRequest(
            {
                method: "POST",
                url: `${web}/api/psychology/mentalHealthV2Report/pageMentalHealthWarning`,
                data: params,
            }
        ),
    getFactorRemarkDetail: (params) =>
        clientRequest(
            {
                method: "GET",
                url: `${web}/api/psychology/mentalHealthV2Report/getFactorsDetail`,
                params,
            }
        )
};

export default api;


