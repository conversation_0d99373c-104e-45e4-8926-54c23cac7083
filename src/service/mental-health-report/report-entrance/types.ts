
// 查询参数
export interface TQueryParams {
    type?: string | number;
    areaCode?: string | number;
    cityCode?: string | number;
    clientType?: number | string;
    gradeCode?: string | number;
    gradeId?: string | number;
    semester?: number;
}

// 查看报告权限
export interface viewAuthorityProps {
    cityCode?: number;
    cityName?: string;
    areaCode?: number | string | null,
    areaName?: string | null,
    hasViewAuthority?: boolean;
}

// 年级
export interface gradeTermItem {
    gradeCode: number;
    graduationYear: number;
    name: string;
    semester: number;
}

// 城市概览
export interface cityAreaEntrySummaryInfoProps {
    allFinishCnt: number;
    allFinishRate: number;
    allTotalCnt: number;
    joinSchoolCnt: number;
    reportFlag: boolean;
    schoolCnt: number;
}

// 城市 区县列表
export interface cityAreaEntryItem{
    allFinishCnt: number;
    allFinishRate: number;
    allTotalCnt: number;
    areaCode: number;
    areaFlag: boolean;
    areaName: string;
    schoolCount: number;
}

// 区县列表
export interface areaSelectItem {
    areaId: number;
    areaName: string;
}

// 区县下的学校列表
export interface entrySchoolInfoItem {
    allFinishCnt: number;
    allFinishRate: number;
    allTotalCnt: number;
    answerTaskId: string;
    endDate: number;
    reportFlag: number;
    schoolCode: number;
    schoolName: string;
    startDate: number;
}

export interface baseInfoProps {
    authority?: viewAuthorityProps;
    gradeTermList?: gradeTermItem[];
    cityAreaEntrySummaryInfo?: cityAreaEntrySummaryInfoProps;
    cityAreaEntryList?: cityAreaEntryItem[];
    areaSelectList?: areaSelectItem[];
    entrySchoolInfoList?: entrySchoolInfoItem[];
}