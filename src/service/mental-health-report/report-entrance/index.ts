import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import { TQueryParams,
    viewAuthorityProps,
    gradeTermItem,
    cityAreaEntrySummaryInfoProps,
    cityAreaEntryItem,
    areaSelectItem,
    entrySchoolInfoItem
} from './types'
import { areaCitySchoolEntranceProps } from '~/pages/psychology/cityCover/types'

const hostApi = 'http://yapi.235.mistong.com/mock/1199'

interface ICityCoverReportApi {
    getUserViewAuthority: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<viewAuthorityProps>;
    getGradeTermList: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<gradeTermItem[]>;
    getCityAreaEntrySummaryInfo: (
        data: TQueryParams,
    ) => IResponse<cityAreaEntrySummaryInfoProps>;
    getCityAreaEntryList: (
        data: TQueryParams,
    ) => IResponse<cityAreaEntryItem[]>;
    getAreaSelect: (
        data: TQueryParams,
    ) => IResponse<areaSelectItem[]>;
    getEntrySchoolInfoList: (
        data: TQueryParams,
    ) => IResponse<entrySchoolInfoItem[]>;
    getMultiAnswerTaskInfoBySchoolId: (
        data: TQueryParams,
    ) => IResponse<entrySchoolInfoItem[]>;
}

export interface IGetAreaCitySchoolEntranceListInfo {
    params: TQueryParams;
    data: IResponseData<areaCitySchoolEntranceProps>;
}

const api: ICityCoverReportApi = {
    getUserViewAuthority: (params, ctx) =>
        serviceRequest(
            {
                method: "GET",
                url: `${hostApi}/mentalHealthAreaV2Report/getUserViewAuthority`,
                params,
            },
        ctx,
    ),
    getGradeTermList: (params, ctx) =>
        serviceRequest(
            {
                method: "GET",
                url: `${hostApi}/mentalHealthAreaV2Report/getGradeTermList`,
                params,
            },
        ctx,
    ),
    getCityAreaEntrySummaryInfo: (params) =>
        clientRequest(
            {
                method: "POST",
                url: `${hostApi}/mentalHealthAreaV2Report/getCityAreaEntrySummaryInfo`,
                params,
            },
    ),
    getCityAreaEntryList: (params) =>
        clientRequest(
            {
            method: "POST",
            url: `${hostApi}/mentalHealthAreaV2Report/getCityAreaEntryList`,
            params,
        },
    ),
    getAreaSelect: (params) =>
        clientRequest({
            method: "POST",
            url: `${hostApi}/mentalHealthAreaV2Report/getAreaSelect`,
            params,
        }
    ),
    getEntrySchoolInfoList: (params) =>
        clientRequest({
            method: "POST",
            url: `${hostApi}/mentalHealthAreaV2Report/getEntrySchoolInfoList`,
            params,
        }
    ),
    getMultiAnswerTaskInfoBySchoolId: (params) =>
        clientRequest({
            method: "POST",
            url: `${hostApi}/mentalHealthAreaV2Report/getMultiAnswerTaskInfoBySchoolId`,
            params,
        }
    ),
}

export default api;