export interface TQueryParams {
    evaluationRecordId?: string;
    evaluationTaskId?: string;
    classId?: string;
    gradeId?: string;
    clientType?: string | number;
    ff?: string;
    isPrint?: string;
    printMode?: boolean;
    initRecordId?: string;
}

// 结果、向内外信息
export interface inwardAttackProps {
    description: string;
    inConclusion: string;
    remarks: string;
    riskIndexScore: number;
    riskLevel: number;
    riskLevelName: string;
}

// 报告基本信息
export interface IUserBaseInfoProps {
    areaName: string;
    className: string;
    gradeName: string;
    inwardAttack: inwardAttackProps;
    outwardAttack: inwardAttackProps;
    overallRisk: inwardAttackProps;
    principleVideoURL: string;
    reportCode: string;
    reportTime: number;
    schoolName: string;
    userName: string;
}

// 用户总体概况中的因子情况，分为保护因子和危险因子
export interface IUserResultFactors {
    /** 正向标志：true为正向，false为负向 */
    positiveFlag: boolean;
    /** 维度id */
    latitudeId: number;
    /** 纬度名称 */
    latitudeName: string;
    /** 得分 */
    score: number;
    /** 参考分均分 */
    referenceScore: number;
    /** 预警分 */
    warnningScore: number;
    /** 总体风险级别 */
    riskLevel: number;
}

export interface ILatitudeListBeanGroups {
    latitudeCode: number;
    /** 注释 */
    message: string;
    /** 维度下的各个因子的信息 ,LatitudeListBeanV2 */
    latitudeListBeanList: ILatitudeListBeanList[];
}

export interface ILatitudeListBeanList {
    /** 维度id */
    latitudeId: number;
    /** 纬度名称 */
    latitudeName: string;
    /** 得分 */
    score: number;
    /** 参考分均分 */
    referenceScore: number;
    /** 预警分 */
    warnningScore: number;
    /** 风险级别 */
    riskLevel: number;
    /** 风险级别名称 */
    riskLevelName: string;
    /** 学生情况 */
    factorDescription: string;
}

// 结果分析
export interface IUserFactorAnalysis {
    /** 正向标志：true为正向，fasle为负向 */
    positiveFlag: boolean;
    /** 按照因子的维度分组MentalhealthTotalV2Enum ,LatitudeListBeanGroup */
    latitudeListBeanGroups: ILatitudeListBeanGroups[];
}

export interface ISuggestionFactorInfo {
    /** 维度的id */
    latitudeId: number;
    /** 维度的名称 */
    latitudeName: string;
    /** 辅导建议 */
    counselingAdvice: string;
}

// 辅导建议
export interface ICounselingSuggest {
    /** 维度的id */
    latitudeGroupId: number;
    /** 维度的名称 */
    latitudeGroupName: string;
    /** 各个因子的辅导建议 */
    analysisFactorSuggestionList?: ISuggestionFactorInfo[];
}

// 因子说明 - 目录层级
export interface IFactorCatalogRemark {
    /** 正向标志：true为正向，false为负向 */
    positiveFlag: boolean;
    /** 一级维度的数据 ,ParentLatitudeInfoVO */
    parentLatitudeInfoList: IParentLatitudeInfo[];
}

// 因子说明 - 一级维度
export interface IParentLatitudeInfo {
    /** 一级维度id */
    latitudeGroupId: number;
    /** 一级纬度名称 */
    latitudeGroupName: string;
    /** 二级因维度的数据 ,LatitudeInfoVO */
    latitudeInfoList: ILatitudeInfoList[];
}

// 因子说明 - 二级维度
export interface ILatitudeInfoList {
    /** 维度id */
    latitudeId: number;
    /** 纬度名称 */
    latitudeName: string;
    /** 因子说明 */
    factorDescription: string;
    /** 高分表现 */
    highScorePerformance: string;
    /** 低分表现 */
    lowScorePerformance: string;
}

export interface IFactorDetailParams {
    latitudeId: number;
}

// 因子详情
export interface IFactorDetail {
    /** 因子id */
    id: number;
    // 因子名称
    latitudeName?: string;
    /** 因子说明 */
    factorDescription: string;
    /** 高分表现 */
    highScorePerformance: string;
    /** 低分表现 */
    lowScorePerformance: string;
    /** 健康等级 */
    riskLevel: number;
    /** 辅导建议 */
    counselingAdvice: string;
}
