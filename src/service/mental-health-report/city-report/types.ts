export interface TQueryParams {
    type?: string | number;
    clientType?: string | number;
    cityCode?: string | number;
    gradeCode?: string | number;
    gradeId?: string | number;
    areaCode?: string | number;
    semester?:  string | number;
}

export interface schoolDistributionBeanItem {
    healthPeople: number;
    healthPercentage: number;
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    id: string;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    name: string;
    warningPeople: number;
    warningPeoplePercentage: number;
}

export interface statusDistributionBeanItem {
    number: number;
    percentage: number;
    riskId: number;
    riskName: string;
}
export interface inwardAttackProps {
    description: string;
    distributionRemarks: string;
    inConclusion: string;
    remarks: string;
    riskIndexScore: number
    riskLevel: number;
    riskLevelName: string;
    schoolDistributionBeanList: schoolDistributionBeanItem[];
    schoolStatusDistributionBean: schoolDistributionBeanItem[];
    statusDistributionBean: statusDistributionBeanItem[];
}

export interface schoolItem {
    areaName: string;
    classNum: number;
    finishPeople: number;
    finishPercentage: number;
    schoolName: string;
    totalPeople: number;
}
export interface cityBaseInfoProps {
    areaName: string;
    endTime: number;
    evaluatedPeople: number;
    gradeName: string;
    inwardAttack: inwardAttackProps;
    outwardAttack: inwardAttackProps;
    overSchoolCnt: number;
    overallRisk: inwardAttackProps;
    principleVideoURL:string;
    reportCode: string;
    reportTime: number;
    schoolList: schoolItem[];
    startTime: number;
    totalPeople: number
}

export interface comparativeAnalysisBeanItem {
    avgRiskIndex: number;
    id: string;
    name: string;
    riskLevel: number;
    riskLevelName: string;
}

export interface statusRiskDistributionBeanItem {
    healthPeople: number;
    healthPercentage: number;
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    id: string;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    name: string;
}

export interface factorAnalysisItem {
    counselingAdvice: string;
    factorDescription: string;
    highScorePerformance: string;
    latitudeId: number;
    latitudeName: string;
    lowScorePerformance: string;
    referenceScore: number;
    remarks: string;
    riskLevel: number;
    riskLevelName: string;
    score: number;
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
}

export interface factorScoreBeanItem {
    latitudeId: number;
    latitudeName: string;
    positiveFlag: boolean;
    referenceScore: number;
    score: number;
    warnningScore: number;
}

export interface peopleRiskItem {
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    id: string;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    name: string;
    warningPeople: number;
    warningPeoplePercentage: number;
}

export interface cityCurrentPerformanceProps {
    comparativeAnalysisBean: comparativeAnalysisBeanItem[];
    comparativeAnalysisRemarks: string;
    comparativeScore: number;
    factorAnalysis: factorAnalysisItem[];
    factorDescription: string;
    factorScoreBean: factorScoreBeanItem[];
    factorScoreRemarks: string;
    highScorePerformance: string;
    lowScorePerformance: string;
    peopleRiskList: peopleRiskItem[];
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
    statusRiskDistributionRemarks: string;
    transitionParagraph: string;
}

export interface cityPersonalityReasonsProps {
    comparativeAnalysisBean: comparativeAnalysisBeanItem[];
    comparativeAnalysisRemarks: string;
    comparativeScore: number;
    factorAnalysis: factorAnalysisItem[];
    factorDescription: string;
    factorScoreBean: factorScoreBeanItem[];
    factorScoreRemarks: string;
    highScorePerformance: string;
    lowScorePerformance: string;
    peopleRiskList: peopleRiskItem[];
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
    statusRiskDistributionRemarks: string;
    transitionParagraph: string;
}


export interface cityEnvironmentalReasonsProps {
    comparativeAnalysisBean: comparativeAnalysisBeanItem[];
    comparativeAnalysisRemarks: string;
    comparativeScore: number;
    factorAnalysis: factorAnalysisItem[];
    factorDescription: string;
    factorScoreBean: factorScoreBeanItem[];
    factorScoreRemarks: string;
    highScorePerformance: string;
    lowScorePerformance: string;
    peopleRiskList: peopleRiskItem[];
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
    statusRiskDistributionRemarks: string;
    transitionParagraph: string;
}

export interface pageCityWarningDetailItem {
    areaCode: number;
    areaName:string;
    healthPeople: number;
    healthPercentage: number;
    higherRiskPeople: number;
    higherRiskPercentage: number;
    highestRiskPeople: number;
    highestRiskPercentage: number;
    minorRiskPeople: number;
    minorRiskPercentage: number;
    range: string;
    totlePeople: number;
}

export interface cityCognitiveReasonsProps {
    comparativeAnalysisBean: comparativeAnalysisBeanItem[];
    comparativeAnalysisRemarks: string;
    comparativeScore: number;
    factorAnalysis: factorAnalysisItem[];
    factorDescription: string;
    factorScoreBean: factorScoreBeanItem[];
    factorScoreRemarks: string;
    highScorePerformance: string;
    lowScorePerformance: string;
    peopleRiskList: peopleRiskItem[];
    statusDistributionBean: statusDistributionBeanItem[];
    statusRiskDistributionBean: statusRiskDistributionBeanItem[];
    statusRiskDistributionRemarks: string;
    transitionParagraph: string;
}

export interface cityFocusSchoolItem {
    areaCode: number;
    areaName: string;
    highRiskNum: number;
    highRiskNumPercentage: number;
    schoolCode: number;
    schoolName: string;
}
