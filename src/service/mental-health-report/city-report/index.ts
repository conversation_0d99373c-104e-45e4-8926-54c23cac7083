import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import {
  TQueryParams,
  cityBaseInfoProps,
  cityCurrentPerformanceProps,
  cityPersonalityReasonsProps,
  cityEnvironmentalReasonsProps,
  pageCityWarningDetailItem,
  cityCognitiveReasonsProps,
  cityFocusSchoolItem,
} from './types';

const hostApi = 'http://yapi.235.mistong.com/mock/1199';

interface ICityReportApi {
  getCityBaseInfo: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<cityBaseInfoProps>;
  getCityCurrentPerformance: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<cityCurrentPerformanceProps>;
  getCityPersonalityReasons: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<cityPersonalityReasonsProps>;
  getCityEnvironmentalReasons: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<cityEnvironmentalReasonsProps>;
  getPageCityWarningDetail: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<pageCityWarningDetailItem[]>;
  getCityCognitiveReasons: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<cityCognitiveReasonsProps>;
  getCityFocusSchoolList: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<cityFocusSchoolItem[]>;
}

const api: ICityReportApi = {
  getCityBaseInfo: (data, ctx) =>
    serviceRequest(
      {
        method: 'POST',
        url: `/mentalHealthAreaV2Report/getCityBaseInfo`,
        data,
      },
      ctx
    ),
  getCityCurrentPerformance: (data, ctx) =>
    serviceRequest(
      {
        method: 'POST',
        url: `/mentalHealthAreaV2Report/getCityCurrentPerformance`,
        data,
      },
      ctx
    ),
  getCityPersonalityReasons: (data, ctx) =>
    serviceRequest(
      {
        method: 'POST',
        url: `/mentalHealthAreaV2Report/getCityPersonalityReasons`,
        data,
      },
      ctx
    ),
  getCityEnvironmentalReasons: (data, ctx) =>
    serviceRequest(
      {
        method: 'POST',
        url: `/mentalHealthAreaV2Report/getCityEnvironmentalReasons`,
        data,
      },
      ctx
    ),
  getPageCityWarningDetail: (data, ctx) =>
    serviceRequest(
      {
        method: 'POST',
        url: `/mentalHealthAreaV2Report/pageCityWarningDetail`,
        data,
      },
      ctx
    ),
  getCityCognitiveReasons: (data, ctx) =>
    serviceRequest(
      {
        method: 'POST',
        url: `/mentalHealthAreaV2Report/getCityCognitiveReasons`,
        data,
      },
      ctx
    ),
  getCityFocusSchoolList: (data, ctx) =>
    serviceRequest(
      {
        method: 'POST',
        url: `/mentalHealthAreaV2Report/getCityFocusSchoolList`,
        data,
      },
      ctx
    ),
};

export default api;
