import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import {
    IUserResultFactors,
    TQueryParams,
    IUserBaseInfoProps,
    IUserFactorAnalysis,
    ICounselingSuggest,
    IFactorCatalogRemark,
    ILatitudeInfoList,
    IFactorDetail,
    IFactorDetailParams,
    IWarningListRes,
    ISearchWarningListParams,
} from './types';
import clientRequest from '~/service/clientRequest';
import { web } from '@/utils/hosts';

interface IUserReportApi {
    getUserBaseInfo: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IUserBaseInfoProps>; // 基础信息
    getUserProtectiveFactors: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IUserResultFactors[]>; // 保护因子
    getUserRiskFactors: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IUserResultFactors[]>; // 风险因子
    getClassFactorAnalysis: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IUserFactorAnalysis[]>; // 结果分析
    getLatitudeAnalysisSuggestion: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<ICounselingSuggest[]>; // 辅导建议
    getFactorRemarkList: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<ILatitudeInfoList[]>; // 因子说明
    getFactorCatalogRemarkList: (
        data: TQueryParams,
        ctx: ContextProps
    ) => IResponse<IFactorCatalogRemark[]>; // 因子的目录说明
    getFactorRemarkDetail: (
        data: IFactorDetailParams,
    ) => IResponse<IFactorDetail>; // 因子的目录说明
    getWarningList: (
        data: ISearchWarningListParams,
    ) => IResponse<IWarningListRes>; // 因子的目录说明
}

const api: IUserReportApi = {
    getUserBaseInfo: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: '/api/psychology/mentalHealthV2Report/getClassBaseInfo',
                data: params,
            },
            ctx,
        ),
    getUserProtectiveFactors: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getClassProtectiveFactors`,
                data: params,
            },
            ctx,
        ),
    getUserRiskFactors: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getClassRiskFactors`,
                data: params,
            },
            ctx,
        ),
    getClassFactorAnalysis: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getClassAnalyzed`,
                data: params,
            },
            ctx
        ),
    getLatitudeAnalysisSuggestion: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getClassLatitudeAnalysisSuggestion`,
                data: params,
            },
            ctx
        ),
    getFactorRemarkList: (params, ctx) =>
        serviceRequest(
            {
                method: "GET",
                url: `/api/psychology/mentalHealthV2Report/getLatitudeInfoList`,
                params,
            },
            ctx
        ),
    getFactorCatalogRemarkList: (params, ctx) =>
        serviceRequest(
            {
                method: "POST",
                url: `/api/psychology/mentalHealthV2Report/getFactorInfoList`,
                data: params,
            },
            ctx
        ),
    getWarningList: (params) =>
        clientRequest(
            {
                method: "POST",
                url: `${web}/api/psychology/mentalHealthV2Report/pageMentalHealthWarning`,
                data: params,
            }
        ),
    getFactorRemarkDetail: (params) =>
        clientRequest(
            {
                method: "GET",
                url: `${web}/api/psychology/mentalHealthV2Report/getFactorsDetail`,
                params,
            }
        )
};

export default api;


