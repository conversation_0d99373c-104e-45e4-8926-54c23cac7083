export interface IMetadataProps {
  spring_application_gateway_type: string;
  spring_application_protocol: string;
  spring_application_register_control_enabled: string;
  spring_application_discovery_version: string;
  version: string;
  spring_application_config_rest_control_enabled: string;
  spring_application_name: string;
  spring_application_discovery_agent_version: string;
  'spring_application_context-path': string;spring_application_group_key: string;
  spring_application_discovery_plugin: string;
  spring_application_discovery_control_enabled: string;
  spring_application_uuid: string;
  'preserved.register.source': string;
  region: string;
  'preserved.vs.app.name': string;
  spring_application_type: string;
  group: string;
  spring_boot_version: string;
}

export interface INacosConfig {
  instanceId: string;
  ip: string;
  port: number;
  weight: number;
  healthy: boolean;
  enabled: boolean;
  ephemeral: boolean;
  clusterName: string;
  serviceName: string;
  metadata: IMetadataProps;
  instanceHeartBeatInterval: number;
  instanceHeartBeatTimeOut: number;
  ipDeleteTimeout: number;
}
