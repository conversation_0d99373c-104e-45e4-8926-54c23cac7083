import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import { TUserReportParams, TUserReportResponse } from './types';

interface IMentalAdmissionReportApi {
  // 个人报告
  getUserReport: (
    data: TUserReportParams,
    ctx: ContextProps
  ) => IResponse<TUserReportResponse[]>;
}

const api: IMentalAdmissionReportApi = {
  // 个人报告
  getUserReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/mentalAdmissionReport/getUserReport',
        data,
      },
      ctx
    ),
};

export default api;
