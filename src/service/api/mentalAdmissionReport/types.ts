export interface TUserReportParams {
  evaluationRecordId: number;
};

export interface latitudeListItem {
  id: number;
  name: string;
  score: number;
  referenceScore: number;
  warnningScore: number;
}

export interface factorAnalysisProps {
  inConclusion?: string;
  latitudeList?: latitudeListItem[];
}

export interface TUserReportResponse {
  /** 评测版本目前只有入学测存在多版本，默认不传值为1 */
  version ?: number
  /** 报告编号 */
  reportCode ?: string
  /** 姓名 */
  userName ?: string
  /** 班级 */
  className ?: string
  /** 年级 */
  gradeName ?: string
  /** 学校名称 */
  schoolName ?: string
  /** 浙江省杭州市 */
  areaName ?: string
  /** 报告时间1596102128693报告生成时间 */
  reportTime ?: string
  /** 主要发现01 ,String */
  mainFindings ?: string[]
  /** 心理健康风险指数信息02 ,RiskAnalysisBean */
  riskAnalysis ?: RiskAnalysis
  /** 心理健康因子分析02 ,FactorAnalysisBean */
  factorAnalysis ?: factorAnalysisProps
  /** 心理健康因子分析详情02 ,FactorAnalysisDetailsBean */
  factorAnalysisDetails ?: FactorAnalysisDetails
  /** 学习准备度信息03 ,LearningReadinessBean */
  learningReadiness ?: LearningReadiness
  /** 学习准备度分析03 ,StudyReadinessAnalysisBean */
  studyReadinessAnalysis ?: StudyReadinessAnalysis
  /** 学习准备度分析详情03 ,StudyReadinessDetailsBean */
  studyReadinessDetails ?: FactorAnalysisDetails
  /** 性格类型分析04 ,PersonalityTypeAnalysisBean */
  personalityTypeAnalysis ?: PersonalityTypeAnalysis
  /** 家庭教养风格分析05 ,FamilyEducationBean */
  familyEducation ?: FamilyEducation
}

export interface FamilyEducation {
  /** 风格类型 */
  type ?: number
  /** 风格类型名称 */
  name ?: string
  /** 独生子女是，否 */
  onlyChild ?: string
  /** 父亲文化程度 */
  fatherEducation ?: string
  /** 母亲文化程度 */
  motherEducation ?: string
  /** 日常家庭环境 */
  familyEnvironment ?: string
  /** 家庭教养风格 */
  parentingStyleName ?: string
  /** 辅导建议 */
  counselingAdvice ?: string
}

export interface AnalysisList {
  /** id */
  id ?: number
  /** 名称 */
  name ?: string
  /** 分数 */
  score ?: number
}
export interface PersonalityTypeAnalysis {
  /** 结论：胆汁忧郁质 */
  name ?: string
  /** 特点分析 */
  characteristicAnalysis ?: string
  /** 辅导建议 */
  counselingAdvice ?: string
  /** 性格类型分析列表 ,AnalysisListBean */
  analysisList ?: AnalysisList
}


export interface LatitudeList2 {
  /** id */
  id ?: number
  /** 名称 */
  name ?: string
  /** 得分 */
  score ?: number
  /** 参考分 */
  referenceScore ?: number
}
export interface StudyReadinessAnalysis {
  /** inConclusion:从测评结果来看，该生在偏执倾向、人际敏感和恐惧倾向等维度上的得分超过参考值，提示该生在以上维度方面可能存在心理困扰。且超出的分值越多，表示对应因子对学生的影响越大。 */
  inConclusion ?: string
  /** 纬度列表 ,LatitudeListBean */
  latitudeList ?: LatitudeList2
}

export interface LearningReadiness {
  /** 级别 */
  level ?: number
  /** 名称 */
  name ?: string
  /** 分数 */
  score ?: number
}

export interface FactorAnalysisDetails {
  /** id */
  id ?: number
  /** 名称 */
  name ?: string
  /** 分数 */
  score ?: number
  /** 总分 */
  totalScore ?: number
  /** 级别 */
  level ?: number
  /** 级别名称 */
  levelName ?: string
  /** 因子说明 */
  factorDescription ?: string
  /** 学生情况 */
  studentSituation ?: string
  /** 辅导建议 */
  counselingAdvice ?: string
}

export interface LatitudeList {
  /** id */
  id ?: number
  /** 名称 */
  name ?: string
  /** 得分 */
  score ?: number
  /** 参考分 */
  referenceScore ?: number
  /** 预警分 */
  warnningScore ?: number
}

export interface RiskAnalysis {
  /** 新增结论 */
  inConclusion ?: string
  /** 风险级别1:无风险2:轻微风险3:较高风险4:高风险 */
  level ?: number
  /** 风险级别名称 */
  name ?: string
  /** 分数 */
  score ?: number
}