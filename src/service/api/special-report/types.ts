export type TGradeReportModuleParams = {
  evaluationTaskId: number;
  gradeId?: string;
  classGroupNum?: string;
  initRecordId?: string;
  printMode?: boolean;
};

// 预警名单参数
export interface ISearchWarningListParams {
  evaluationTaskId: string;
  gradeId?: string;
  classId?: string;
  classGroupNum?: string;
  userName?: string;
  hide?: boolean;
  pageIndex: number;
  pageSize: number;
  start?: number;
}

export type TGradeReportModuleRes = {
  /** 年级  */
  gradeName: string;
  /** 学校  */
  schoolName: string;
  /** 报告编号  */
  reportCode: string;
  /** 报告时间：报告生成时间  */
  reportTime: string;
  /** 测评布置开始时间  */
  startTime: string;
  /** 测评布置结束时间  */
  endTime: string;
  /** 测评群体：参与测试人数，报告的学生数  */
  finishedNum: number;
  /** 报告样式  */
  reportStyle: number;
  /** 测评快照ID,用于报告公共模块的查询  */
  templateVersionId: number;
};


export type TBuildItem = {
  appName: string;
  realName: string;
  buildTime: number;
  platform: string;
  id: number;
};

export type TGetBuildListParams = Partial<Omit<TBuildItem, "buildTime" | "id">>;

export interface TUserBasicParams {
  evaluationRecordId?: number;
  recordId?: string;
  initRecordId?: string;
}

export interface TUserBasicResponse {
  userName: string;
  className: string;
  gradeName: string;
  reportCode: string;
  reportTime: number;
  templateVersionId: string;
}

export interface TUserCommonResponse {
  /** 报告标题 */
  title: string;
  /** 报告导读 */
  reportGuide: string;
  /** 附加说明 */
  extDesc: string;
  /** 测评目的,备注：个人报告无此项 */
  evaluationAim: string;
  /** 测评工具,备注：个人报告无此项 */
  evaluationTool: string;
}

export interface TUserCommonParams {
  templateVersionId: string;
  reportType: number;
  initRecordId?: string;
}

export interface TUserCaseResponse {
  /** 纬度id */
  latitudeId: number;
  /** 纬度名称 */
  latitudeTitle: string;
  /** 内容(维度)说明 */
  latitudeDescription: string;
  /** 程度名称 */
  latitudeLevelTitle: string;
  /** 维度得分说明 */
  latitudeScoreDescription: string;
  /** 你的情况 */
  levelDescription: string;
  /** 心理建议 */
  levelSuggest: string;
}
export interface ConfigResponse {
  primaryLatitude: PrimaryLatitude
  latitudeLevelList: LatitudeLevelList
  latitudeRelation: LatitudeRelation
}

export interface LatitudeRelation {
  latitudeRelationList: LatitudeRelationList
  noParentLatitude: NoParentLatitude
}

export interface NoParentLatitude {
  /** 维度id */
  latitudeId ?: number
  /** 维度等级 */
  latitudeLevel ?: number
}

export interface LatitudeRelationList {
  /** 维度id */
  latitudeId: string
  /** 维度等级 */
  latitudeLevel: string
  /** 子维度，可能为空，返回空数组 */
  childLatitudeList ?: string
}

export interface LatitudeLevelList {
  /** 纬度id */
  latitudeId: number
  /** 程度id */
  latitudeLevelId: number
  /** 程度名称 */
  latitudeLevelTitle: number
  /** 程度颜色 ，前端定义  1，2，3，4，5~10即可，后端不用，前端自己保存自己用 */
  color: number
}

export interface PrimaryLatitude {
  /** 维度Id */
  latitudeId: string
  /** 维度名称 */
  latitudeTitle: string
  /** 排序1：升序  2：降序 */
  primaryOrder: string
}

export interface TLatitudeOverallAnalysisItem {
  /** 纬度id */
  latitudeId: number;
  /** 纬度名称 */
  latitudeTitle: string;
  /** 维度分数 */
  score: number;
  /** 程度id */
  latitudeLevelId: number;
  /** 程度名称 */
  latitudeLevelTitle: string;
  /** 分数对应的级别1，2，3，4 */
  latitudeScoreLevel: number;
  /** 总分 */
  totalScore: number;
  /** 测评结果 */
  result: string;
  /** 得分说明 */
  description: string;
  /** 建议 */
  suggest: string;
}

export interface TUserOverallAnalysisItem {
  /** 纬度id */
  latitudeId: number
  /** 纬度名称 */
  latitudeTitle: string
  /** 维度分数 */
  score: number
  /** 程度id */
  latitudeLevelId: number
  /** 程度名称 */
  latitudeLevelTitle: string
  /** 分数对应的级别1，2，3，4 */
  latitudeScoreLevel: number
  /** 总分 */
  totalScore: number
  /** 测评结果说明 */
  result: string
  /** 得分说明 */
  description: string
  /** 建议说明 */
  suggestDesc: string
}

export interface TUserOverallResponse {
  latitudeOverallAnalysisList: TLatitudeOverallAnalysisItem[];
  personLatitudeOverallAnalysisList: TUserOverallAnalysisItem[];
}

// 班级报告
export interface TClassBasicParams {
  evaluationTaskId: string;
  gradeId: string;
  classId: string;
  initRecordId?: string;
}
export interface TClassBasicResponse {
  className: string
  /** 年级 */
  gradeName: string
  /** 学校 */
  schoolName: string;
  userName: string;
  /** 报告编号 */
  reportCode: string
  /** 报告时间：报告生成时间 */
  reportTime: string
  /** 测评布置开始时间 */
  startTime: string
  /** 测评布置结束时间 */
  endTime: string
  /** 测评群体：参与测试人数，报告的学生数 */
  finishedNum: number
  /** 报告样式 */
  reportStyle: string;
  /** 测评快照ID,用于报告公共模块的查询 */
  templateVersionId: string;
}

export interface TClassCaseParams {
  evaluationTaskId: string;
  gradeId: string;
  classId: string;
  initRecordId?: string;
}

export interface LevelItem {
  /** id */
  id: number
  /** 名称,良好，轻微 */
  name: string
  /** 人数 */
  num: number
}
export interface TClassCaseResponse {
  /** 纬度id */
  latitudeId: number
  /** 纬度名称 */
  latitudeTitle: string
  /** 内容(维度)说明 */
  latitudeDescription: string
  /** 心理建议 */
  levelSuggest: string
  /** 班级情况 ,ReportPieChartVO */
  levelList: LevelItem[];
  /** 预警学生, String */
  waringStudentsList: string[]
}

export interface TClassOverallParams {
  evaluationTaskId: string;
  gradeId: string;
  classId: string;
  initRecordId?: string;
}

export interface OverallAnalysisList {
  /** 纬度id */
  latitudeId: number
  /** 纬度名称 */
  latitudeTitle: string
  /** 维度分数 */
  score: number
  /** 程度id */
  latitudeLevelId: number
  /** 程度名称 */
  latitudeLevelTitle: string
  /** 分数对应的级别1，2，3，4 */
  latitudeScoreLevel: number
  /** 总分 */
  totalScore: number
  /** 测评结果 */
  result: string
  /** 得分说明 */
  description: string;
  /** 建议 */
  suggest: string;
}
export interface LatitudeWarningList {
  /** 纬度id */
  latitudeId: number
  /** 纬度名称 */
  latitudeTitle: string
  /** 预警人数 */
  num: number
  /** 占比,预警人数总人数 */
  percentage: number
}
export interface TClassOverallResponse {
  /** 注释 */
  overallAnalysisList: OverallAnalysisList[];
  /** 注释 */
  latitudeWarningList: LatitudeWarningList[];
  /** 一级维度 */
  classLatitudeOverallAnalysisList: TClassLatitudeOverallAnalysisItem[];
}


export interface IDegreeDistributionList {
  /** 程度id	 */
  latitudeLevelId: string
  /** 程度名称	 */
  latitudeLevelTitle: string
  /** 人数 */
  num: number
  /** 百分比 */
  percentage: number
}
export interface TClassLatitudeOverallAnalysisItem {
  /** 纬度id	 */
  latitudeId: number
  /** 纬度名称	 */
  latitudeTitle: string
  /** 维度分数	 */
  score: number
  /** 程度id	 */
  latitudeLevelId: number
  /** 程度名称	 */
  latitudeLevelTitle: string
  /** 分数对应的级别1，2，3，4	 */
  latitudeScoreLevel: number
  /** 总分	 */
  totalScore: number
  /** 测评结果说明	 */
  result: string
  /** 得分说明	 */
  description: string
  /** 建议说明	 */
  suggestDesc: string
  /** 各个程度的分布数据 */
  degreeDistributionList: IDegreeDistributionList[];
}


// 年级报告
export interface TGradeBasicParams {
  evaluationTaskId: string;
  gradeId: string;
  classGroupNum?: string;
  initRecordId?: string;
}
export interface TGradeBasicResponse {
  gradeName: string;
  schoolName: string;
  className: string;
  userName: string;
  reportCode: string;
  reportTime: string;
  startTime: string;
  endTime: string;
  finishedNum: number;
  reportStyle: string;
  templateVersionId: string;
}

export interface TGradeOverallParams {
  evaluationTaskId: string;
  gradeId: string;
  classGroupNum: string;
  initRecordId?: string;
}

export interface ClassWarningList {
  length: ClassWarningList;
  /** 班级名称 */
  name: string
  /** 预警人数,有预警情况的学生数（多个维度预警算一个） */
  num: number
  /** 占比,预警的人数参加测评的人数 */
  percentage: number
}

export interface TGradeOverallResponse {
  /** 年级概况 */
  gradeLatitudeOverallAnalysisList: TGradeOverallAnalysisItem[];
  /** 注释 */
  overallAnalysisList: OverallAnalysisList[];
  /** 注释 */
  latitudeWarningList: LatitudeWarningList[];
  /** 注释 */
  classWarningList: ClassWarningList[];
}
export interface TGradeCaseParams {
  evaluationTaskId: string;
  gradeId: string;
  classGroupNum: string;
  initRecordId?: string;
}

export interface LevelList {
  /** id */
  id: number
  /** 名称,良好，轻微 */
  name: string
  /** 人数 */
  num: number
}

export interface ClassScoreList {
  /** 班级名称 */
  name?: string
  /** 班级分数 */
  score?: number
}

export interface TGradeCaseResponse {
  map(arg0: (i: any) => JSX.Element): import("react").ReactNode;
  /** 纬度id */
  latitudeId?: number
  /** 纬度名称 */
  latitudeTitle?: string
  /** 内容(维度)说明 */
  latitudeDescription?: string
  /** 维度得分说明 */
  latitudeScoreDescription?: string
  /** 年级整体情况 ,ReportPieChartVO */
  levelList?: LevelList[]
  /** 各班级在该项得分情况 ,ClassScoreVO */
  classScoreList?: ClassScoreList[]
  /** 年级平均分,按每个人的得分算出均分 */
  gradeAverageScore?: number
}



export interface TGradeDegreeDistributionItem {
  /** 程度id */
  latitudeLevelId?: string
  /** 程度名称	 */
  latitudeLevelTitle?: string
  /** 人数	 */
  num?: number
  /** 百分比 */
  percentage?: string
}

export interface TGradeClassWarningItem {
  /** 班级id */
  classId?: number
  /** 班级名称	 */
  className?: string
  /** 人数	 */
  num?: string
  /** 百分比 */
  percentage?: string
}


export interface TGradeOverallAnalysisItem {
  /** 纬度id */
  latitudeId: number
  /** 纬度名称 */
  latitudeTitle: string
  /** 维度分数 */
  score: number
  /** 程度id */
  latitudeLevelId: number
  /** 程度名称 */
  latitudeLevelTitle: string
  /** 分数对应的级别1，2，3，4 */
  latitudeScoreLevel: number
  /** 总分 */
  totalScore: number
  /** 测评结果 */
  result: string
  /** 得分说明 */
  description: string
  /** 建议说明		 */
  suggestDesc: string
  /** 年级各个程度的分布数据 */
  degreeDistributionList?: TGradeDegreeDistributionItem[]
  /** 各班级预警人数,业务场景：班级维度比率的表格 ,ClassWarningVO */
  classLatitudeWarningList?: TGradeClassWarningItem[];
}


export interface IWarningUserLatitudeItem {
  /** 维度Id */
  latitudeId: string;
  /** 维度名称 */
  latitudeTitle: string;
  /** 维度等级 */
  latitudeLevelType: number;
  /** 级别 */
  level: number;
  /** 维度得分 */
  score: number;
}

export interface IWarningRosterItem {
  userId: number;
  /** 注释 */
  userName: string;
  /** 注释 */
  classId: string;
  /** 注释 */
  className: string;
  /** 一级维度的数据 */
  warningUserLatitudeList: IWarningUserLatitudeItem[];
}

export interface IWarningListRes {
  /** 总页数 */
  totalPages?: number;
  /** 当前页码 */
  pageIndex?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 总记录数 */
  totalRecords?: number;
  /** 分页结果集 ,T */
  data?: IWarningRosterItem[];
  /** 注释 */
  havePrePage?: boolean;
  /** 注释 */
  haveNextPage?: boolean;
}

// 根据测评任务获取模版设置的主维度信息
export interface IWarningLatitudeRes {
  latitudeId: number; // 维度Id
  latitudeTitle: string; // 维度名称
  primaryOrder: number // 1：升序2：降序
}