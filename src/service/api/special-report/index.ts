import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import clientRequest from '~/service/clientRequest';
import type { IResponse } from '@/service/request';
import { web } from '@/utils/hosts';
import {
  // 个人
  TUserBasicParams,
  TUserBasicResponse,
  TUserCommonParams,
  TUserCommonResponse,
  TUserCaseResponse,
  TUserOverallResponse,
  // 班级
  TClassBasicParams,
  TClassBasicResponse,
  TClassCaseParams,
  TClassCaseResponse,
  TClassOverallParams,
  TClassOverallResponse,
  // 年级
  TGradeBasicParams,
  TGradeBasicResponse,
  TGradeCaseParams,
  TGradeCaseResponse,
  TGradeOverallParams,
  TGradeOverallResponse,
  ISearchWarningListParams,
  IWarningListRes,
  IWarningLatitudeRes,
  ConfigResponse,
} from './types';

interface IReportApi {
  // 专项测评
  // 个人报告
  getUserOverallAnalysis: (
    data: TUserBasicParams,
    ctx: ContextProps
  ) => IResponse<TUserOverallResponse[]>;

  getUserCaseAnalysis: (
    data: TUserBasicParams,
    ctx: ContextProps
  ) => IResponse<TUserCaseResponse[]>;

  getReportConfigByRecord: (
    data: TUserBasicParams,
    ctx: ContextProps
  ) => IResponse<ConfigResponse>;

  getUserBasicAnalysis: (
    data: TUserBasicParams,
    ctx: ContextProps
  ) => IResponse<TUserBasicResponse>;

  getUserCommonModule: (
    data: TUserCommonParams,
    ctx: ContextProps
  ) => IResponse<TUserCommonResponse>;

  // 班级报告
  getClassOverallAnalysis: (
    data: TClassOverallParams,
    ctx: ContextProps
  ) => IResponse<TClassOverallResponse[]>;
  // 班级和年级共用
  getReportConfig: (
    data: TUserBasicParams,
    ctx: ContextProps
  ) => IResponse<ConfigResponse>;
  getClassBasicAnalysis: (
    data: TClassBasicParams,
    ctx: ContextProps
  ) => IResponse<TClassBasicResponse>;
  getClassCaseAnalysis: (
    data: TClassCaseParams,
    ctx: ContextProps
  ) => IResponse<TClassCaseResponse[]>;
  exportClassWarningRosterExcel: (
    data: ISearchWarningListParams
  ) => IResponse<Blob>;

  // 年级
  getGradeOverallAnalysis: (
    data: TGradeOverallParams,
    ctx: ContextProps
  ) => IResponse<TGradeOverallResponse[]>;

  getGradeBasicAnalysis: (
    data: TGradeBasicParams,
    ctx: ContextProps
  ) => IResponse<TGradeBasicResponse>;

  getGradeCaseAnalysis: (
    data: TGradeCaseParams,
    ctx: ContextProps
  ) => IResponse<TGradeCaseResponse[]>;
  getClassWarningRosterList: (
    data: ISearchWarningListParams
  ) => IResponse<IWarningListRes>;
  getGradeWarningRosterList: (
    data: ISearchWarningListParams
  ) => IResponse<IWarningListRes>;
  exportGradeWarningRosterExcel: (
    data: ISearchWarningListParams
  ) => IResponse<Blob>;
  getLatitudePrimary: (
    data: TGradeCaseParams,
    ctx: ContextProps
  ) => IResponse<IWarningLatitudeRes>;
}

const api: IReportApi = {
  // 专项测 个人报告
  getUserOverallAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/user/overall/analysis/module/new',
        data,
      },
      ctx
    ),

  getUserCaseAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/user/case/analysis/module',
        data,
      },
      ctx
    ),

  getUserBasicAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/user/basic/module',
        data,
      },
      ctx
    ),
  getUserCommonModule: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/special/screening/report/common/module',
        params,
      },
      ctx
    ),
  // 班级报告
  getClassOverallAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/class/overall/analysis/module',
        data,
      },
      ctx
    ),
  getClassBasicAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/class/basic/module',
        data,
      },
      ctx
    ),
  getClassCaseAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/class/case/analysis/module',
        data,
      },
      ctx
    ),
  getClassWarningRosterList: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/evaluationReportCommon/getCLassWarningUser`,
      params,
    }),
  exportClassWarningRosterExcel: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/evaluationReportCommon/exportCLassWarningUser`,
      params,
      responseType: 'blob',
    }),
  // 年级报告
  getGradeOverallAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/grade/overall/analysis/module',
        data,
      },
      ctx
    ),
  getGradeBasicAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/grade/basic/module',
        data,
      },
      ctx
    ),
  getGradeCaseAnalysis: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/special/screening/report/grade/case/analysis/module',
        data,
      },
      ctx
    ),
  getGradeWarningRosterList: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/evaluationReportCommon/getGradeWarningUser`,
      params,
    }),
  exportGradeWarningRosterExcel: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/evaluationReportCommon/exportGradeWarningUser`,
      params,
      responseType: 'blob',
    }),
  // 根据测评任务获取模版设置的主维度信息
  getLatitudePrimary: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/evaluationReportCommon/getLatitudePrimary',
        params,
      },
      ctx
    ),
  getReportConfig: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/special/screening/report/config',
        params,
      },
      ctx
    ),
  getReportConfigByRecord: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/special/screening/report/getConfigByRecord',
        params,
      },
      ctx
    ),
};

export default api;
