import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request'
import { TGradeReportModuleParams, TGradeReportModuleRes, TOverallAnalysisModuleParams, TOverallAnalysisModuleRes } from "./types";

interface IYearReportApi {
  /**
   * 年级报告-基础信息模块
   * @see http://yapi.235.mistong.com/project/1199/interface/api/81344
   */
  gradeReportModule: (data: TGradeReportModuleParams, ctx: ContextProps) => IResponse<TGradeReportModuleRes>;

  /**
   * 年级报告-总体得分情况模块
  * @see http://yapi.235.mistong.com/project/1199/interface/api/81345
  */
  overallAnalysisModule: (data: TOverallAnalysisModuleParams, ctx: ContextProps) => IResponse<TOverallAnalysisModuleRes>;
}

const api: IYearReportApi = {

  gradeReportModule: (data, ctx) => serviceRequest({
    url: '/special/screening/report/grade/basic/module',
    data,
    method: 'post',
  }, ctx),

  overallAnalysisModule: (data, ctx) => serviceRequest({
    url: '/special/screening/report/user/overall/analysis/module',
    data,
    method: 'post',
  }, ctx),

};

export default api;