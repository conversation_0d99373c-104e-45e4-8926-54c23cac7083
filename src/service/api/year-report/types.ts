export type TGradeReportModuleParams = {
  evaluationTaskId: number | string;
  gradeId?: string;
  classGroupNum?: string;
}

export type TGradeReportModuleRes = {
  /** 年级  */
  gradeName: string;
  /** 学校  */
  schoolName: string;
  /** 报告编号  */
  reportCode: string;
  /** 报告时间：报告生成时间  */
  reportTime: string;
  /** 测评布置开始时间  */
  startTime: string;
  /** 测评布置结束时间  */
  endTime: string;
  /** 测评群体：参与测试人数，报告的学生数  */
  finishedNum: number;
  /** 报告样式  */
  reportStyle: number;
  /** 测评快照ID,用于报告公共模块的查询  */
  templateVersionId: number;
}

// 自己修改
export type TOverallAnalysisModuleRes = {
  overallAnalysisList: [];
  latitudeWarningList: [];
  classWarningList: [];
}

export type TOverallAnalysisModuleParams = {
  evaluationRecordId:number | string;
  gradeId?: string;
  classGroupNum?: string;
}