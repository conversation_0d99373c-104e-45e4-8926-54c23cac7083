import axios, { AxiosRequestConfig } from 'axios';
import md5 from "md5";
import { getSourceType, getToken, objectToUrlParams, reLaunchToLogin, UrlParamsToObject, fileToJson, stopPSTScreenshotHook } from '@/utils/tools';
import { web } from '@/utils/hosts';
import moment from 'moment';

// const codeMessage = {
//   200: '服务器成功返回请求的数据。',
//   201: '新建或修改数据成功。',
//   202: '一个请求已经进入后台排队（异步任务）。',
//   204: '删除数据成功。',
//   400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
//   401: '用户没有权限（令牌、用户名、密码错误）。',
//   403: '用户得到授权，但是访问是被禁止的。',
//   404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
//   406: '请求的格式不可得。',
//   410: '请求的资源被永久删除，且不会再得到的。',
//   422: '当创建一个对象时，发生一个验证错误。',
//   500: '服务器发生错误，请检查服务器。',
//   502: '网关错误。',
//   503: '服务不可用，服务器暂时过载或维护。',
//   504: '网关超时。',
// };


const instance = axios.create({
  baseURL: 'http://127.0.0.1:10086',
  withCredentials: false,
  headers: {
    'Access-Control-Allow-Origin': '*',
    'Content-Type': 'application/json',
  },
});

const errorHandler = async (error: any, config?: any) => {
  // 这里做一些其他错误的处理
  if (error) {
    console.error('api-error', {
      method: config?.method,
      apiUrl: config?.url,
      params: config?.params,
      data: config?.data,
      token: config?.headers?.token,
      error: error,
      time: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
      url: `${location.href}`
    })
  }
  return Promise.reject(error);
};

instance.interceptors.response.use(
  async(response) => {
    const { status, data, config } = response;
    if (status >= 200 && response.status < 300) {
      const { code, success, msg } = data;
      const { ff, isPrint } = UrlParamsToObject(window.location.href);
      const printMode = !!(ff || isPrint);
      if ([117001, 2001106, 2001109].includes(Number(code))) {
        if (printMode) {
          stopPSTScreenshotHook({
            errorCode: code,
            errorMessage: msg || 'token失效'
          })
        }
        window.location.href = reLaunchToLogin(location.href)
        return errorHandler(response, config);
      } else if (Number(code) !== 200 && !success) {
        if ([7771020].includes(Number(code))) {
          if (printMode) {
            stopPSTScreenshotHook({
              errorCode: code,
              errorMessage: msg || '您暂无查看该报告的权限，请联系学校管理员'
            })
          }
          window.location.replace(`${web}/psychology-service/common/permission?${objectToUrlParams({
            code: 7771020,
            description: encodeURIComponent(msg || '您暂无查看该报告的权限，请联系学校管理员'),
            printMode
          })}`);
          return errorHandler(response, config);
        }
        // 数据流
        const contentType = response.headers['content-type'];
        if (!contentType || contentType.indexOf('application/json') === -1) {
          return response;
        }
        // 导出数据流报错
        if (data && toString.call(data) === '[object Blob]') {
          const JSONResult = await fileToJson(data);
          return errorHandler(JSONResult, config);
        }
        return errorHandler(response, config);
      }
      return response;
    } else {
      return errorHandler(response, config);
    }
  },
  (error) => errorHandler(error),
);

type TConfig = AxiosRequestConfig & {
  isNotMoveEmptyString?: boolean;
  mockUrl?: string;
  useDefaultErrorMsg?: boolean;
};
// 添加请求拦截器
instance.interceptors.request.use((config: TConfig) => {
  const { headers = {}, params = {} } = config;
  const _ = Date.now();
  const token = getToken() || '';
  config.headers = {
    timestamp: _,
    sign: md5(_ + "bdc739ff2dcf").toString().toLocaleUpperCase(),
    token,
    ...headers
  };
  config.params = { _, ...params };
  // 对于特殊场景，所有API都通过query加上场景标识
  const sourceType = getSourceType();
  if (sourceType) {
    config.params.sourceType = sourceType;
  }
  // 根据isNotMoveEmptyString是否删除 [key]='' 的参数
  if (config.params && !config.isNotMoveEmptyString) {
    for (const key in config.params) {
      if (config.params[key] === '') delete config.params[key];
    }
  }
  return config;
}, (error: any) => {
  return Promise.reject(error);
});

export interface IPageParams {
  pageIndex: number;
  pageSize: number;
}
/** 弹消息的队里 */
const queueMaker = ((timer: number = 1500) => {
  let last = Promise.resolve(null);
  return (fn: () => void) => {
    const newJob = () => new Promise(res => {
      fn();
      setTimeout(() => res(null), timer);
    });
    // @ts-ignore
    last = last.then(newJob);
  };
})();

/** 默认使用http状态码的请求的泛型  要自定义响应状态码可用注释掉的那一行 */
const request = async <T>(config: TConfig) => {
  return instance.request<IResponse<T>>({ ...config })
    .then((res) => res.data).catch(error => {
      let shouldAlert = true;
      if (error?.data) {
        const { code, msg } = error.data;
        if ([117001, 2001106, 2001109].includes(Number(code))) {
          shouldAlert = false;
        }
      }
      if (shouldAlert && config.useDefaultErrorMsg !== false) {
        // const msg = error.data?.msg || codeMessage[error.response?.status || 404];
        // msg && queueMaker(() => message.error(msg));
      }
      if (error?.response?.data) {
        return error?.response?.data
      }
      throw error;
    });
};

export default request;

export interface IPageData<T> {
  data: T[];
  totalPages: number;
  pageIndex: number;
  pageSize: number;
  totalRecords: number;
  havePrePage: boolean;
  haveNextPage: boolean;
}