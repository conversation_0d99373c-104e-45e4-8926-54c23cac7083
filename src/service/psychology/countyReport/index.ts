import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import { TQueryParams } from './types';
import {
  areaReportDataProps,
  pageAreaWarningDetailProps,
} from '~/pages/psychology/countyReport/types';
import { web } from '@/utils/hosts';

interface ICountyReportApi {
  getAreaReport: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<areaReportDataProps>;
  getPageAreaWarningDetail: (
    data: TQueryParams
  ) => IResponse<pageAreaWarningDetailProps>;
}

export interface IGetPageAreaWarningDetail {
  params: TQueryParams;
  data: IResponseData<pageAreaWarningDetailProps>;
}

const api: ICountyReportApi = {
  getAreaReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `/api/psychology/mentalHealthReport/getAreaReport`,
        data,
      },
      ctx
    ),
  getPageAreaWarningDetail: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/mentalHealthReport/pageAreaWarningDetail`,
      params,
    }),
};

export default api;
