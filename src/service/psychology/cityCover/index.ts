import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import { TQueryParams } from './types';
import {
  viewAuthorityProps,
  areaSelectItem,
  areaCityEntranceProps,
  areaCitySchoolEntranceProps,
  schoolItem,
} from '~/pages/psychology/cityCover/types';
import { web } from '@/utils/hosts';
import { IGradeItem } from '~/@types';

interface ICityCoverReportApi {
  getViewAuthority: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<viewAuthorityProps>;
  getGradeInfoList: (
    data: TQueryParams
  ) => IResponse<IGradeItem[]>;
  getAreaSelect: (
    data: TQueryParams
  ) => IResponse<areaSelectItem[]>;
  getAreaCityEntranceList: (
    data: TQueryParams
  ) => IResponse<areaCityEntranceProps>;
  getAreaCitySchoolEntranceListInfo: (
    data: TQueryParams
  ) => IResponse<areaCitySchoolEntranceProps>;
  getMultiAnswerTaskInfoBySchoolId: (
    data: TQueryParams
  ) => IResponse<schoolItem[]>;
}

export interface IGetAreaCitySchoolEntranceListInfo {
  params: TQueryParams;
  data: IResponseData<areaCitySchoolEntranceProps>;
}

const api: ICityCoverReportApi = {
  getViewAuthority: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: `/api/psychology/mentalHealthReport/getUserViewAuthority`,
        params,
      },
      ctx
    ),
  getGradeInfoList: (data) =>
    clientRequest(
      {
        method: 'post',
        url: `${web}/api/psychology/mentalHealthReport/getGradeInfoList`,
        data,
      }
    ),
  getAreaSelect: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/mentalHealthReport/getAreaSelect`,
      data,
    }),
  getAreaCityEntranceList: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/mentalHealthReport/getAreaCityEntranceList`,
      params,
    }),
  getAreaCitySchoolEntranceListInfo: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/mentalHealthReport/getAreaCitySchoolEntranceListInfo`,
      params,
    }),
  getMultiAnswerTaskInfoBySchoolId: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/mentalHealthReport/getMultiAnswerTaskInfoBySchoolId`,
      params,
  }),
};

export default api;
