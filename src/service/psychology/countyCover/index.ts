import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import { TQueryParams } from './types';
import {
  viewAuthorityProps,
  areaCountyCoverEntranceProps,
  schoolItem
} from '~/pages/psychology/countyCover/types';
import { web } from '@/utils/hosts';
import { IGradeItem } from '~/@types';

interface ICoverReportApi {
  getUserViewAuthority: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<viewAuthorityProps>;
  getGradeInfoList: (
    data: TQueryParams
  ) => IResponse<IGradeItem[]>;
  getAreaCitySchoolEntranceListInfo: (
    data: TQueryParams
  ) => IResponse<areaCountyCoverEntranceProps>;
  getMultiAnswerTaskInfoBySchoolId: (
    data: TQueryParams
  ) => IResponse<schoolItem[]>;
}

export interface IGetAreaCitySchoolEntranceListInfo {
  params: TQueryParams;
  data: IResponseData<areaCountyCoverEntranceProps>;
}

const api: ICoverReportApi = {
  getUserViewAuthority: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: `/api/psychology/mentalHealthReport/getUserViewAuthority`,
        params,
      },
      ctx
    ),
  getGradeInfoList: (data) =>
    clientRequest(
      {
        method: 'post',
        url: `${web}/api/psychology/mentalHealthReport/getGradeInfoList`,
        data,
      }
    ),
  getAreaCitySchoolEntranceListInfo: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/mentalHealthReport/getAreaCitySchoolEntranceListInfo`,
      params,
    }),
  getMultiAnswerTaskInfoBySchoolId: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/mentalHealthReport/getMultiAnswerTaskInfoBySchoolId`,
      params,
    }),
};

export default api;
