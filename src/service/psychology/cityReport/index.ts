import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import { TQueryParams } from './types';
import { cityReportProps } from '~/pages/psychology/cityReport/types';
interface ICityReportApi {
  getCityReport: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<cityReportProps>;
}

const api: ICityReportApi = {
  getCityReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/mentalHealthReport/getCityReport',
        data,
      },
      ctx
    ),
};

export default api;
