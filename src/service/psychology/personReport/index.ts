import serviceRequest, { ContextProps } from '../../serviceRequest';
import type { IResponse } from '@/service/request';
import { TPersonReportParams } from './types';
import { personReportDataProps } from '~/pages/psychology/personReport/types';
import { web } from '@/utils/hosts';

interface IPersonReportApi {
  getUserReport: (
    data: TPersonReportParams,
    ctx: ContextProps
  ) => IResponse<personReportDataProps>;
}

const api: IPersonReportApi = {
  getUserReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/mentalHealthReport/getUserReport',
        data,
      },
      ctx
    ),
};

export default api;
