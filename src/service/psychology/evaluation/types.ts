export declare namespace GetEvaluationInfo {
  export interface Request {
    /** 测评作答任务id */
    evaluationTaskId: string
    /** 客户端类型1:web2:android3:ios4:h5 */
    clientType: string
    /** 此用户信息用来判断特殊类型的测评是否对学生开放 */
    userId ?: string
    /** 如果是后端服务器直接调用，为了好排查问题，把服务器ip或者唯一识别号传过来 */
    serverIP ?: string
    /** 调用者服务名称,b端服务，中后台服务，其他等 */
    serverName ?: string
    /** 扩展字段，根据业务来定义该字段 */
    ext ?: string
    answerTaskId?: string
  }
  
  export interface Response {
    /** 注释 */
    success ?: boolean
    /** 注释 */
    code ?: string
    /** 注释 */
    msg ?: string
    /** EvaluationAndAnswerVO */
    data ?: Data
  }
  
  export interface Data {
    /** 用户答题信息 */
    answerInfo ?: AnswerInfo
    /** 注释 */
    id ?: number
    /** 测评模板id */
    evaluationTemplateId ?: number
    /** 测评模板标题 */
    evaluationTemplateTitle ?: string
    /** 测评简介 */
    descript ?: string
    /** 测评简介详情 */
    descriptDetail ?: string
    /** 测评导读 */
    guideContent ?: string
    /** 测评封面图url */
    coverImgUrl ?: string
    /** 测评列表图url */
    listImgUrl ?: string
    /** 测评背景图url */
    backgroundImgUrl ?: string
    /** 测评url集合 ,EvaluationUrl */
    evaluationUrl ?: EvaluationUrl
    /** 新增字段答题用时单位:分钟 */
    duration ?: number
  }
  
  export interface EvaluationUrl {
    /** 预览地址 */
    previewUrl ?: string
    /** 班级报告地址 */
    classReportUrl ?: string
    /** 集体报告地址 */
    groupReportUrl ?: string
    /** pc,个人报告地址 */
    PcPersonReportUrl ?: string
    /** h5,个人报告地址 */
    H5PersonReportUrl ?: string
    /** pc详报 */
    pcPersonDetailReportUrl ?: string
    /** h5详报 */
    h5PersonDetailReportUrl ?: string
    /** pc答题地址 */
    PcDoAnswerUrl ?: string
    /** h5答题地址 */
    H5DoAnswerUrl ?: string
    /** 市级报告地址 */
    cityReportUrl ?: string
  }
  
  export interface AnswerInfo {
    /** 用户评测状态0待测评:1评测中；2已结束3：伪答 */
    answerStatus ?: number
    /** 当前题目数 */
    currentQuestions ?: number
    /** 一共题目数 */
    totalQuestions ?: number
    /** 用户答题id */
    userRecordId ?: number
    /** 是否伪答true:伪答；false:没有伪答 */
    feignAnswer ?: boolean
    /** 伪答次数 */
    feignAnswerTimes ?: number
    /** 个人报告跳转链接 */
    personalReportUrl ?: string
    /** 测评模版快照id */
    templateVersionId ?: number
  }
}

export declare namespace GetEvaluationByType {
  export interface Request {
    /** 测评模板类型 */
    evaluationTemplateType: string
  }
  
  export interface Response {
    /** 注释 */
    success ?: boolean
    /** 注释 */
    code ?: string
    /** 注释 */
    msg ?: string
    /** EvaluationInfo */
    data ?: Data
  }
  
  export interface Data {
    /** 测评模板id */
    evaluationTemplate: string
    /** 测评快照id */
    evaluationTemplateVersion: string
    /** 测评任务 */
    evaluationTaskId: string
    /** 标题	 */
    title: string
    /** 测评简介	 */
    describe: string
    /** 测评简介详情	 */
    descriptDetail: string
    /** 测评模板类型	 */
    templateType: string
  }
}


export declare namespace PostCleanUserRecord {
  export interface Request {
  }
  
  export interface Response {
    /** 注释 */
    success ?: boolean
    /** 注释 */
    code ?: string
    /** 注释 */
    msg ?: string
    /** data */
    data ?: boolean
  }
}

export declare namespace PostCleanUserRecord {
  export interface Request {
  }
  
  export interface Response {
    /** 注释 */
    success ?: boolean
    /** 注释 */
    code ?: string
    /** 注释 */
    msg ?: string
    /** data */
    data ?: boolean
  }
}

export declare namespace PostEvaluationAnswer {
  export interface Request {
  }
  
  export interface Response {
    /** 注释 */
    success ?: boolean
    /** 注释 */
    code ?: string
    /** 注释 */
    msg ?: string
    /** data */
    data ?: boolean
  }
}

export declare namespace GetEvaluationQuestion {
  export interface Request {
    /** 题目序号比如90道题，题目序号为1~90 */
    pageIndex: string
    /** 测评版本号，用于取题 */
    templateVersionId: string
    /** 废弃 */
    pageSize: string
    /** 注释 */
    userRecordId: string
    /** 客户端类型1:web2:android3:ios4:h5 */
    clientType: string
    /** 如果是后端服务器直接调用，为了好排查问题，把服务器ip或者唯一识别号传过来 */
    serverIP: string
    /** 调用者服务名称,b端服务，中后台服务，其他等 */
    serverName: string
    /** 扩展字段，根据业务来定义该字段 */
    ext: string
  }
  
  export interface Response {
    /** 注释 */
    success ?: boolean
    /** 注释 */
    code ?: string
    /** 注释 */
    msg ?: string
    /** ElementDetailVO */
    data ?: Data
  }
  
  export interface Data {
    /** 题目序号id1，2，3~90questionId提交答案的时候需要传入 */
    id ?: number
    /** 当前所处的题目index */
    elementIndex: number
    /** 真实题目id */
    elementId: string
    /** 选项数类型：1-至少选；2-必须选 */
    selectNumType: string
    /** 总题目数 */
    totalQuestion: string
    /** 题目类型,RADIO("radio","单选题"),,CHECKBOX("checkbox","多选题"),,CHECKBOXORDER("checkboxOrder","多选排序题"),,SCALE("scale","量表题"),,SLIDER("slider","滑块题"),,TEXT("text","文本描述") */
    type ?: string
    /** 题目标题 */
    title ?: string
    /** 题目描述,TEXT("text","文本描述")文本题描述取该字段 */
    description ?: string
    /** 多选题需要，选几项 */
    selectNum ?: number
    /** 新增字段元素内容,不同题目类型content结构不一样,RADIO("radio","单选题"),,CHECKBOX("checkbox","多选题"),,CHECKBOXORDER("checkboxOrder","多选排序题"),,SCALE("scale","量表题"),,SLIDER("slider","滑块题"),,TEXT("text","文本描述") */
    elementContent ?: string
    /** 新增字段RADIO("radio","单选题"),SCALE("scale","量表题"),答案,如果已经答过，则返回单选已经选择的id */
    selectId ?: string
    /** 新增字段SLIDER("slider","滑块题"),答案 */
    inputNumber ?: number
    /** 新增字段,CHECKBOX("checkbox","多选题"),,CHECKBOXORDER("checkboxOrder","多选排序题"),答案 ,CheckboxOrderModel */
    checkboxOrder ?: CheckboxOrder[]
    /** previousQuestionNum */
    previousQuestionNum ?: string
    /** 是否完成组题 */
    generateQuestionGroupFlag ?: string
    /** 子题的列表 */
    childElementDetailList?: ChildElementDetailList[]
    /** 是否重组题 */
    reorganizeFlag ?: string
    totalIndex: number | string
  }
  
  export interface ChildElementDetailList {
    id ?: number
    /** 当前所处的题目index */
    elementIndex: number
    /** 真实题目id */
    elementId: string
    /** 选项数类型：1-至少选；2-必须选 */
    selectNumType: string
    /** 总题目数 */
    totalQuestion: string
    /** 题目类型,RADIO("radio","单选题"),,CHECKBOX("checkbox","多选题"),,CHECKBOXORDER("checkboxOrder","多选排序题"),,SCALE("scale","量表题"),,SLIDER("slider","滑块题"),,TEXT("text","文本描述") */
    type ?: string
    /** 题目标题 */
    title ?: string
    /** 题目描述,TEXT("text","文本描述")文本题描述取该字段 */
    description ?: string
    /** 多选题需要，选几项 */
    selectNum ?: number
    /** 新增字段元素内容,不同题目类型content结构不一样,RADIO("radio","单选题"),,CHECKBOX("checkbox","多选题"),,CHECKBOXORDER("checkboxOrder","多选排序题"),,SCALE("scale","量表题"),,SLIDER("slider","滑块题"),,TEXT("text","文本描述") */
    elementContent ?: string
    /** 新增字段RADIO("radio","单选题"),SCALE("scale","量表题"),答案,如果已经答过，则返回单选已经选择的id */
    selectId ?: string
    /** 新增字段SLIDER("slider","滑块题"),答案 */
    inputNumber ?: number
    /** 新增字段,CHECKBOX("checkbox","多选题"),,CHECKBOXORDER("checkboxOrder","多选排序题"),答案 ,CheckboxOrderModel */
    checkboxOrder ?: CheckboxOrder[]
    /** previousQuestionNum */
    previousQuestionNum ?: string
    /** 是否完成组题 */
    generateQuestionGroupFlag ?: string
    reorganizeFlag ?: string
    totalIndex: number | string
  }
  
  export interface CheckboxOrder2 {
    /** 多选题选项id */
    id: string
    /** 多选题选项顺序 */
    selectOrder: string
    /** 后端使用，前端无需关注多选题选项纬度 */
    elementLatitudeId: string
    /** 后端使用，前端无需关注多选题选项分数 */
    selectScore: string
  }
  
  export interface CheckboxOrder {
    /** 多选题选项id */
    id: string
    /** 多选题选项顺序 */
    selectOrder: number
    /** 后端使用，前端无需关注多选题选项纬度 */
    elementLatitudeId: number
    /** 后端使用，前端无需关注多选题选项分数 */
    selectScore: number
  }
}