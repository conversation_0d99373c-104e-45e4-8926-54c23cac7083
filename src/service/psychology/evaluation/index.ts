import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import { web } from '@/utils/hosts';
import {
  GetEvaluationInfo,
  GetEvaluationByType,
  GetEvaluationQuestion,
} from './types';

interface IEvaluationTemplate {
  getEvaluationInfo: (
    data: GetEvaluationInfo.Request,
    ctx: ContextProps
  ) => IResponse<GetEvaluationInfo.Response>;
  getEvaluationByType: (
    data: GetEvaluationByType.Request,
    ctx: ContextProps
  ) => IResponse<GetEvaluationByType.Response>;
  getEvaluationByTypeClient: (
    data: GetEvaluationByType.Request
  ) => IResponse<GetEvaluationInfo.Response>;
  getEvaluationInfoClient: (
    data: GetEvaluationInfo.Request
  ) => IResponse<GetEvaluationByType.Response>;
  cleanUserRecord: (data: any) => IResponse<any>;
  cleanUserFeignAnswerDetail: (data: any) => IResponse<any>;
  postEvaluationAnswer: (data: any) => IResponse<any>;
  postBatchEvaluationAnswer: (data: any) => IResponse<any>;
  getEvaluationQuestion: (
    data: GetEvaluationQuestion.Request
  ) => IResponse<GetEvaluationQuestion.Data>;
  refreshUserBeginTime: (data: any) => IResponse<any>;
  unAnswerCheck: (data: any) => IResponse<any>;
  refreshEvaluationCompletedTime: (data: any) => IResponse<any>;
}

const api: IEvaluationTemplate = {
  getEvaluationByType: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: `/api/psychology/evaluationTemplate/getEvaluationByType`,
        params,
      },
      ctx
    ),
  getEvaluationInfo: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: `/api/psychology/evaluationTemplate/getEvaluationInfo`,
        params,
      },
      ctx
    ),
  refreshUserBeginTime: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationAnswer/refreshUserBeginTime`,
      data,
    }),
  getEvaluationByTypeClient: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/evaluationTemplate/getEvaluationByType`,
      params,
    }),
  getEvaluationInfoClient: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/evaluationTemplate/getEvaluationInfo`,
      params,
    }),
  cleanUserRecord: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationAnswer/cleanUserRecord`,
      data,
    }),
  cleanUserFeignAnswerDetail: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationAnswer/cleanUserFeignAnswerDetail`,
      data,
    }),
  getEvaluationQuestion: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/evaluationAnswer/getEvaluationQuestion`,
      params,
    }),
  postEvaluationAnswer: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationAnswer/postEvaluationAnswer`,
      data,
    }),
  postBatchEvaluationAnswer: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationAnswer/postBatchEvaluationAnswer`,
      data,
    }),
  unAnswerCheck: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationAnswer/unAnswerCheck`,
      data,
    }),
  refreshEvaluationCompletedTime: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationAnswer/refreshEvaluationCompletedTime`,
      data,
    }),
};

export default api;
