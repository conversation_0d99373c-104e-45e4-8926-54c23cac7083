import serviceRequest, { ContextProps } from '../../serviceRequest';
import clientRequest from '../../clientRequest';
import { TGradeReportParams, TClassWarningParams } from './types';
import {
  gradeReportDataProps,
  classWarningStudentProps,
} from '~/pages/psychology/gradeReport/types';
import type { IResponse, IResponseData } from '@/service/request';
import { web } from '@/utils/hosts';

export interface IGradeReportApi {
  getGradeReport: (
    data: TGradeReportParams,
    ctx: ContextProps
  ) => IResponse<gradeReportDataProps>;
  getClassWarningStudentList: (
    data: TClassWarningParams
  ) => IResponse<classWarningStudentProps>;
}

export interface IGetClassWarningStudentList {
  params: TClassWarningParams;
  data: IResponseData<classWarningStudentProps>;
}

const api: IGradeReportApi = {
  getGradeReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/mentalHealthReport/getGradeReport',
        data,
      },
      ctx
    ),
  getClassWarningStudentList: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/mentalHealthReport/pageClassWarning`,
      data,
    }),
};

export default api;
