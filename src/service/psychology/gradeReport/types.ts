export interface TGradeReportParams {
    evaluationTaskId: string;
    scoolId: string;
    gradeId: string;
    type: string;
    classGroupNum?: string;
    initRecordId?: string;
    bizType?: string;
    clientType?: number;
    ff?:string;
    isPrint?: string;
    printMode?: boolean;
    isDetailReport?: boolean | string;
    detailReportMode?: boolean;
}

export interface TClassWarningParams {
    evaluationTaskId?: string;
    gradeId?: string;
    classId?: string;
    pageIndex?: number;
    pageSize?: number;
    sort?: number;
    type?: string | number;
    userName?: string;
}