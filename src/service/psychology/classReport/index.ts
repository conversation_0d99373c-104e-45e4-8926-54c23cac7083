import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import { TClassReportParams } from './types';
import { classReportDataProps } from '~/pages/psychology/classReport/types';
interface IClassReportApi {
  getClassReport: (
    data: TClassReportParams,
    ctx: ContextProps
  ) => IResponse<classReportDataProps>;
}

const api: IClassReportApi = {
  getClassReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/mentalHealthReport/getClassReport',
        data,
      },
      ctx
    ),
};

export default api;
