import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import type { GetBaseReportInfo } from './types'
import clientRequest from '@/service/clientRequest';
import { web } from '@/utils/hosts';

interface IUpgradeMajorReport {
  getBaseReportInfo: (data: GetBaseReportInfo.Request, ctx: ContextProps) => IResponse<GetBaseReportInfo.Response>;
  getCollectList: (data: any, ctx?: ContextProps) => IResponse<any>;
  addCollect: (data: any, ctx?: ContextProps) => IResponse<any>;
  cancelCollect: (data: any, ctx?: ContextProps) => IResponse<any>;
  getCount: (data: any, ctx?: ContextProps) => IResponse<any>;
}

const api: IUpgradeMajorReport = {
  getBaseReportInfo: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: `/api/psychology/user/report/upgrade/major/getBaseReportInfo`,
        params,
      },
      ctx
    ),

  getCollectList: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/careerprod/my/library/collect/getList`,
      data,
    }),
  addCollect: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/careerprod/my/library/collect/add`,
      data,
    }),
  cancelCollect: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/careerprod/my/library/collect/cancel`,
      data,
    }),
  getCount: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/careerprod/my/library/collect/getCount`,
      params,
    }),
};

export default api;
