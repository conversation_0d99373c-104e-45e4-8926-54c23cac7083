
export declare namespace GetBaseReportInfo {
  export interface Request {
    /** (Long) */
    userRecordId: string
    /** (String) */
    clientType: string
  }
  
  export interface Response {
    /** 注释 */
    success ?: boolean
    /** 注释 */
    code ?: string
    /** 注释 */
    msg ?: string
    /** AlgorithmRecommendVO */
    data ?: Data
  }
  
  export interface Data {
    reportStatus: number;
    realName: string;
    finishTime: number;
    /** 注释 */
    majorList: MajorList
    /** 注释 */
    academicPotentialRecommend: AcademicPotentialRecommend
    /** 注释 */
    categoryMajorRecommend: CategoryMajorRecommend
    /** 注释 */
    occupationalValuesRecommend: OccupationalValuesRecommend
    /** 注释 */
    decisionMakingEfficacyRecommend: DecisionMakingEfficacyRecommend
  }
  
  export interface DecisionMakingEfficacyRecommend {
    /** 决策效能计算结果 ,LatitudeScoreVO */
    decisionMakingEfficacyList ?: MajorLatitudeScoreList
    /** 当前的总体等级：1、维度等级全部为高2、维度等级存在为中3、维度等级全部为低 */
    currentTotalLevel ?: number
    /** 反思 ,String */
    reflectionList ?: string[]
  }
  
  export interface OccupationalValuesRecommend {
    /** 手段性价值观职业 ,LatitudeScoreVO */
    tacticalOccupationalList ?: MajorLatitudeScoreList
    /** 目的性价值观职业 ,LatitudeScoreVO */
    intentionOccupationalList ?: MajorLatitudeScoreList
    /** 职业选择思路 ,String */
    adviceList ?: string[]
    /** 推荐的职业 */
    occupationals ?: string
    /** 当前的总体等级：1、维度等级全部为高2、维度等级存在为中3、维度等级全部为低 */
    currentTotalLevel ?: number
  }
  
  export interface CategoryMajorRecommend {
    /** 注释 */
    categoryMajorInfoList: CategoryMajorList
    /** 注释 */
    careerInterestScores: CareerInterestScores
  }
  
  export interface CareerInterestScores {
    /** 维度id */
    latitudeId ?: number
    /** 维度名称 */
    latitudeName ?: string
    /** 维度标签 */
    latitudeLabel ?: string
    /** 维度标签 */
    latitudeScore ?: number
  }
  
  export interface AcademicPotentialRecommend {
    /** 注释 */
    majorList: MajorList
    /** 注释 */
    majorLatitudeScoreList: MajorLatitudeScoreList
    /** 注释 */
    categoryMajorList: CategoryMajorList
  }
  
  export interface CategoryMajorList {
    /** 专业大类Id */
    categoryId ?: number
    /** 专业大类名称 */
    categoryName ?: string
    /** 专业列表 ,MajorVO */
    majorList ?: MajorList
    /** 维度分数 ,LatitudeScoreVO */
    categoryMajorLatitudeScoreList ?: MajorLatitudeScoreList
  }
  
  export interface MajorLatitudeScoreList {
    /** 维度id */
    latitudeId ?: number
    /** 维度名称 */
    latitudeName ?: string
    /** 维度标签 */
    latitudeLabel ?: string
    /** 维度得分 */
    score ?: number
    /** 程度情况描述 */
    description ?: string
    /** 当前的维度等级id */
    latitudeLevelId ?: number
    /** 当前的维度等级名称 */
    latitudeLevelName ?: string
    /** 当前的维度等级 */
    level ?: number
    /** 当前的维度等级文案 */
    levelName ?: string
  }
  
  export interface MajorList {
    /** 专业id */
    majorId ?: number
    /** 专业名称 */
    majorName ?: string
    /** 专业大类Id */
    categoryId ?: string
    /** 专业大类名称 */
    categoryName ?: string
  }
}
