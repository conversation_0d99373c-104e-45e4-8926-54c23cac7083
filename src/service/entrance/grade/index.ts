import serviceRequest, { ContextProps } from '../../serviceRequest';
import type { IResponse } from '@/service/request';
import {
  TGradeReportParams,
  gradeReportDataProps,
  TGradeListParams,
  TGradeListResponse,
} from './types';

interface IEntraceReportApi {
  // 年级报告
  getGradeReport: (
    data: TGradeReportParams,
    ctx: ContextProps
  ) => IResponse<gradeReportDataProps>;
  getGradeList: (
    data: TGradeListParams,
    ctx: ContextProps
  ) => IResponse<TGradeListResponse[]>
}

const api: IEntraceReportApi = {
  // 年级报告
  getGradeReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url:"/api/psychology/mentalAdmissionReport/getGradeReport",
        data,
      },
      ctx
    ),
  getGradeList: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: "/api/psychology/mentalAdmissionReport/getClassList",
        params,
      },
      ctx
    ),
};

export default api;
