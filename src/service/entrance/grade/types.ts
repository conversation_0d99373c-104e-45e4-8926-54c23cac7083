// 年级报告
export interface TGradeReportParams{
  evaluationTaskId:number;
  gradeId?:string;
  classId?:string;
  isPrint?: string;
  ff?:string;
  isDetailReport?: string;
  printMode?:boolean;
  detailReportMode?:boolean;
}

export interface MainFindings {
  /** 注释 */
  mentalHealth: string[]
  /** 注释 */
  studyReadiness: string[]
  /** 注释 */
  characterTraits: string[]
  /** 注释 */
  growthEnvironment: string[]
}

export interface RiskIndex {
  /** 说明有的地方不用返回null */
  inConclusion?: string
  /** 学习准备度枚举类,LOW(1,"低准备度"),,LOWER(2,"较低准备度"),,HIGHER(3,"较高准备度"),,HIGH(4,"高准备度") */
  level?: number
  /** 名称 */
  name?: string
  /** 分数 */
  score?: number
}
export interface LatitudeListBeans {
  /** id */
  id?: number
  /** 名称 */
  name?: string
  /** 人数 */
  num?: number
  /** 占比如果是班级性格类型，则不需要 */
  percentage?: string
}

export interface StateDistribution {
  /** 说明或者结论有的地方不用默认null */
  inConclusion?: string
  /** 饼状图数据 ,LatitudeListBean */
  latitudeListBeans?: LatitudeListBeans
}


export interface LatitudeList {
  /** 纬度id */
  id?: number
  /** 纬度名称 */
  name?: string
  /** 预警人数待提升 */
  num?: number
  /** 占比 */
  percentage?: string
  /** 高风险人数低准备度 */
  highRisk?: number
  /** 占比 */
  highRiskPercentage?: string
  /** 较高风险较低准备度 */
  higherRisk?: number
  /** 占比 */
  higherRiskPercentage?: string
  /** 轻微风险 */
  minorRisk?: number
  /** 占比 */
  minorRiskPercentage?: string
}
export interface FactorWarning {
  /** 总人数 */
  totalPeople?: number
  /** 列表 ,LatitudeListBean */
  latitudeList?: LatitudeList
}

export interface LatitudeList2 {
  /** 级别id */
  id?: number
  /** 级别名称 */
  name?: string
  /** 得分 */
  score?: number
  /** 参考分有的地方不用返回null */
  referenceScore?: number
  /** 预警分有的地方不用返回null */
  warnningScore?: number
}
export interface ResultAnalysis {
  /** 测评结论 */
  inConclusion?: string
  /** 列表 ,LatitudeListBean */
  latitudeList?: LatitudeList2
}

export interface FactorAnalysisDetails {
  /** 因子id */
  id?: number
  /** 名称 */
  name?: string
  /** 分数 */
  score?: number
  /** 总分不用 */
  totalScore?: string
  /** 级别 */
  level?: number
  /** 级别名称 */
  levelName?: string
  /** 因子说明 ,String */
  factorDescription?: string[]
  /** 学生情况 ,String */
  studentPerformed?: string[]
  /** 辅导建议 ,String */
  counselingAdvice?: string[]
  /** 饼图+文案 ,PieChartVO */
  pieChartVOS?: StateDistribution
  /** 高风险学生 ,String */
  highRisk?: string[]
  /** 较高风险学生 ,String */
  higherRisk?: string[]
  /** 轻微风险学生 ,String */
  minorRisk?: string[]
}
export interface StatusAnalysis {
  /** 注释 */
  riskIndex: RiskIndex
  /** 注释 */
  stateDistribution: StateDistribution
  /** 注释 */
  factorWarning: FactorWarning
  /** 注释 */
  resultAnalysis: ResultAnalysis
  /** 注释 */
  factorAnalysisDetails: FactorAnalysisDetails
}


export interface ReadinessDetailsBeans {
  /** 因子id */
  id?: number
  /** 名称 */
  name?: string
  /** 分数 */
  score?: number
  /** 总分或标准分部分列表不用 */
  totalScore?: number
  /** 级别 */
  level?: number
  /** 级别名称 */
  levelName?: string
  /** 因子说明 ,String */
  factorDesc?: string[]
  /** 学生情况 ,String */
  studentPerformed?: string[]
  /** 辅导建议 ,String */
  counselingAdvice?: string[]
  /** 饼图+文案 ,PieChartVO */
  pieChartVOS?: StateDistribution
  /** 低考试准备度学生 ,String */
  low?: string[]
  /** 较低考试准备度学生 ,String */
  lower?: string[]
}
export interface LearningStatus {
  /** 注释 */
  riskIndex: RiskIndex
  /** 注释 */
  pieResultAnalysis: StateDistribution
  /** 注释 */
  factorWarning: FactorWarning
  /** 注释 */
  resultAnalysis: ResultAnalysis
  /** 注释 */
  readinessDetailsBeans: ReadinessDetailsBeans
}

export interface AnalysisDetails {
  /** id */
  id?: number
  /** 名称 */
  name?: string
  /** 占比 */
  percentage?: string
  /** 性格特征 ,String */
  characterTraits?: string[]
  /** 辅导建议 ,String */
  counselingAdvice?: string[]
}export interface PersonalityAnalysis {
  /** 注释 */
  pieChartVO: StateDistribution
  /** 注释 */
  analysisDetails: AnalysisDetails
}

export interface OnlyChild {
  /** id */
  id?: number
  /** 名称 */
  name?: string
  /** 人数 */
  num?: number
}

export interface ParentingStyle {
  /** id */
  id?: number
  /** 名称 */
  name?: string
  /** 人数 */
  num?: number
  /** 占比 */
  percentage?: string
  /** 文案 ,String */
  description?: string[]
}

export interface FamilyUpbringingAnalysis {
  /** 注释 */
  onlyChild: OnlyChild
  /** 注释 */
  life: OnlyChild
  /** 注释 */
  fatherEducation: OnlyChild
  /** 注释 */
  motherEducation: OnlyChild
  /** 注释 */
  parentingStyle: ParentingStyle
}

export interface gradeReportDataProps {
  queryParams?: any;
  /** 评测版本目前只有入学测存在多版本，默认不传值为1 */
  version?: number
  /** 班级名称 */
  className?: string
  /** 年级名称 */
  gradeName?: string
  /** 学校名称 */
  schoolName?: string
  /** 地区 */
  areaName?: string
  /** 报告编号 */
  reportCode?: string
  /** 报告日期 */
  reportTime?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
  /** 完成人数 */
  evaluatedPeople?: number
  /** 总人数 */
  totalPeople?: number
  /** 主要发现1 ,MainFindingVO */
  mainFindings?: MainFindings
  /** 心里健康状态分析2 ,StatusAnalysisBean */
  statusAnalysis?: StatusAnalysis
  /** 学习准备度分析3 ,LearningStatusBean */
  learningStatus?: LearningStatus
  /** 学生性格类型分布图4 ,PersonalityAnalysisBean */
  personalityAnalysis?: PersonalityAnalysis
  /** 家庭环境分析5 ,FamilyUpbringingAnalysisBean */
  familyUpbringingAnalysis?: FamilyUpbringingAnalysis
}

export interface TGradeReportResponse{
  /** 注释 */
  success?: boolean
  /** 注释 */
  code?: string | number;
  /** 注释 */
  msg?: string
  /** ClassMentalAdmissionReportResp */
  data?: gradeReportDataProps;
  queryParams?: TGradeReportParams;
}

export interface TGradeListParams{
  /** 布置id(Long) */
  evaluationTaskId: string
  /** 年级id(String) */
  gradeId: string
}

export interface gradeItem {
  /** 班级id */
  classId?: string
  /** 班级名称 */
  className?: string
}
export interface TGradeListResponse{
     /** 注释 */
  success?: boolean
  /** 注释 */
  code?: string
  /** 注释 */
  msg?: string
  /** ClassContentVO */
  data?: gradeItem[]
}

