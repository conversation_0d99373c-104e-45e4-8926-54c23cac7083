
export interface TCityReportParams {
    templateId: number
    cityCode: number
    gradeId: number
}


export interface Data {
    /** 年级：高一（2021级） */
    gradeName: string
    /** 所属地区：浙江省杭州市 */
    areaName: string
    /** 学校名称 */
    schoolName: string
    /** 布置班级数 */
    classNum: number
    /** 布置学生数 */
    studentNum: number
    /** 完成数 */
    completeNum: number
    /** 完成率 */
    completionRate: string
}
export interface MainFindings {
    /** 注释 */
    mentalHealth: string[]
    /** 注释 */
    studyReadiness: string[]
    /** 注释 */
    characterTraits: string[]
    /** 注释 */
    growthEnvironment: string[]
}

export interface StateDistribution {
    /** 说明或者结论有的地方不用默认null */
    inConclusion: string[]
    /** 饼状图数据 ,LatitudeListBean */
    latitudeListBeans: LatitudeListBeans
}
export interface AreaLevelVO2 {
    /** 地区id */
    id: number
    /** 地区名称 */
    name: string
    /** 学习准备度枚举类,LOW(1,"低准备度"),,LOWER(2,"较低准备度"),,HIGHER(3,"较高准备度"),,HIGH(4,"高准备度") */
    level: number
    /** 名称 */
    levelName: string
    /** 分数 */
    score: number
}
export interface RiskIndex {
    /** 说明有的地方不用返回null */
    inConclusion: string
    /** 学习准备度枚举类,LOW(1,"低准备度"),,LOWER(2,"较低准备度"),,HIGHER(3,"较高准备度"),,HIGH(4,"高准备度") */
    level: number
    /** 名称 */
    name: string
    /** 分数 */
    score: number
}
export interface AreaLevelVO {
    /** 地区id */
    id: number
    /** 地区名称 */
    name: string
    /** 心里健康级别名称 */
    level: number
    /** 级别名称 */
    levelName: string
    /** 较高风险占比20 */
    higherRiskPercentage: string
    /** 高风险占比30 */
    highRiskPercentage: string
}
export interface StatusAnalysis {
    /** 注释 */
    riskIndex: RiskIndex
    /** 注释 */
    areaLevelVO: AreaLevelVO
    /** 注释 */
    stateDistribution: StateDistribution
    /** 注释 */
    areaWarningVOS: AreaWarningVOS
    /** 注释 */
    areaFactorWarningVOS: AreaWarningVOS
    /** 注释 */
    resultAnalysis: ResultAnalysis
    /** 注释 */
    factorWarning: FactorWarning
    /** 注释 */
    factorAnalysisDetails: FactorAnalysisDetails
}


export interface AnalysisDetails {
    /** id */
    id: number
    /** 名称 */
    name: string
    /** 占比 */
    percentage: string
    /** 性格特征 ,String */
    characterTraits: string[]
    /** 辅导建议 ,String */
    counselingAdvice: string[]
}

export interface LatitudeListBeans {
    /** id */
    id: number
    /** 名称 */
    name: string
    /** 人数 */
    num: number
    /** 占比如果是班级性格类型，则不需要 */
    percentage: string
}
export interface PieChartVOS {
    /** 说明或者结论有的地方不用默认null */
    inConclusion: string
    /** 饼状图数据 ,LatitudeListBean */
    latitudeListBeans: LatitudeListBeans
}

export interface PersonalityAnalysis {
    /** 注释 */
    pieChartVO: PieChartVOS
    /** 注释 */
    analysisDetails: AnalysisDetails
}
export interface OnlyChild {
    /** id */
    id: number
    /** 名称 */
    name: string
    /** 人数 */
    num: number
}

export interface ParentingStyle {
    /** id */
    id: number
    /** 名称 */
    name: string
    /** 人数 */
    num: number
    /** 占比 */
    percentage: string
    /** 文案 ,String */
    description: string[]
}
export interface Life {
    id: number,
    num: number,
    name: string
}
export interface FatherEducation {
    id: number,
    num: number,
    name: string
}
export interface MotherEducation {
    id: number,
    num: number,
    name: string
}
export interface FamilyUpbringingAnalysis {
    /** 注释 */
    onlyChild: OnlyChild
    /** 注释 */
    life: Life
    /** 注释 */
    fatherEducation: FatherEducation
    /** 注释 */
    motherEducation: MotherEducation
    /** 注释 */
    parentingStyle: ParentingStyle
}



export interface AreaWarningVOS {
    /** 区域id */
    areaId: number
    /** 区域名称 */
    areaName: string
    /** 学校id */
    schoolId: number
    /** 学校名称 */
    schoolName: string
    /** 预警人数待提升 */
    num: number
    /** 占比 */
    percentage: number
    /** 无风险高准备度 */
    norisk: number
    /** 占比 */
    noriskPercentage: number
    /** 轻微风险较高准备度 */
    minorRisk: number
    /** 占比 */
    minorRiskPercentage: number
    /** 较高风险较低准备度 */
    higherRisk: number
    /** 占比 */
    higherRiskPercentage: number
    /** 高风险人数低准备度 */
    highRisk: number
    /** 占比 */
    highRiskPercentage: number
}

export interface LatitudeList {
    /** 级别id */
    id: number
    /** 级别名称 */
    name: string
    /** 得分 */
    score: number
    /** 参考分有的地方不用返回null */
    referenceScore: number
    /** 预警分有的地方不用返回null */
    warnningScore: number
}

export interface ResultAnalysis {
    /** 测评结论 */
    inConclusion: string
    /** 列表 ,LatitudeListBean */
    latitudeList: LatitudeList
}

export interface LatitudeList2 {
    /** 纬度id */
    id: number
    /** 纬度名称 */
    name: string
    /** 预警人数待提升 */
    num: number
    /** 占比 */
    percentage: string
    /** 高风险人数低准备度 */
    highRisk: number
    /** 占比 */
    highRiskPercentage: string
    /** 较高风险较低准备度 */
    higherRisk: number
    /** 占比 */
    higherRiskPercentage: string
    /** 轻微风险 */
    minorRisk: number
    /** 占比 */
    minorRiskPercentage: string
}
export interface FactorWarning {
    /** 总人数 */
    totalPeople: number
    /** 列表 ,LatitudeListBean */
    latitudeList: LatitudeList2
}

export interface FactorAnalysisDetails2 {
    /** 因子id */
    id: number
    /** 名称 */
    name: string
    /** 分数 */
    score: number
    /** 总分或标准分没有返回null */
    totalScore: number
    /** 级别 */
    level: number
    /** 级别名称 */
    levelName: string
    /** 因子说明 ,String */
    factorDescription: string[]
    /** 学生情况 ,String */
    studentPerformed: string[]
    /** 辅导建议 ,String */
    counselingAdvice: string[]
    /** 饼图+文案 ,PieChartVO */
    pieChartVOS: PieChartVOS
}
export interface LearningStatus {
    /** 注释 */
    riskIndex: RiskIndex
    /** 注释 */
    areaLevelVO: AreaLevelVO2
    /** 注释 */
    pieResultAnalysis: StateDistribution
    /** 注释 */
    areaWarningVOS: AreaWarningVOS
    /** 注释 */
    areaFactorWarningVOS: AreaWarningVOS
    /** 注释 */
    resultAnalysis: ResultAnalysis
    /** 注释 */
    factorWarning: FactorWarning
    /** 注释 */
    factorAnalysisDetails: FactorAnalysisDetails2
}


export interface FactorAnalysisDetails {
    /** 因子id */
    id: number
    /** 名称 */
    name: string
    /** 分数 */
    score: number
}




// 查询市级区县下拉选项列表
export interface TAreaSelectParams {
    selectType: string
    cityCode: number
    graduationYear: number

}

export interface TAreaSelectResponse {
    /** 注释 */
    success: boolean
    /** 注释 */
    code: string
    /** 注释 */
    msg: string
    /** AreaInfoVO */
    data: Data1
}
export interface Data1 {
    /** 区县id */
    areaId: number
    /** 区县名称 */
    areaName: string
}

export interface TCityReportResponse {
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** CityMentalAdmissionReportResp */
    data?: Data
}