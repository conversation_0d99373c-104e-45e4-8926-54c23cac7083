import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request'
import {
  TCityReportParams,
  TCityReportResponse,
  TAreaSelectParams,
  TAreaSelectResponse,
} from './types';

interface IEntraceReportApi {
  getCityReport: (
    data: TCityReportParams,
    ctx: ContextProps
  ) => IResponse<TCityReportResponse>;
  getAreaSelect: (
    data: TAreaSelectParams,
    ctx: ContextProps
  ) => IResponse<TAreaSelectResponse>;
}

const api: IEntraceReportApi = {
  getCityReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        // url:"/mentalAdmissionReport/getCityReport",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionAreaReport/v2/getCityReport',
        data,
      },
      ctx
    ),
  getAreaSelect: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        // url:"/psychology/mentalAdmissionReport/getAreaSelect",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionReport/v2/getAreaSelect',
        data,
      },
      ctx
    ),
};

export default api;
