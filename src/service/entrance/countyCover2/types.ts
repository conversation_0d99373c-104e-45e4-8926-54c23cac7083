export interface TQueryParams {
    type?: string | number;
    cityCode?: string | number;
    areaCode?: string | number;
    graduationYear?: string | number;
    clientType?: string | number;
    reportDateStr?: string;
}

export interface IviewAuthorityProps {
    areaCode: number;
    areaName: string;
    cityCode: number;
    cityName: string;
    hasViewAuthority: boolean;
}

export interface gradeInfoItem {
    value: number;
    label: string;
    children: gradeInfoItem[]
}

export interface schoolItem {
    areaCode: string;
    areaName: string;
    /** 集体报告数量0没有，连接置灰操作，1：有一个布置，2：有多个布置 */
    gradeFlag: number;
    schoolCode: number;
    schoolName: string;
    gradeName: string;
    totalNum: number;
    finishedNum: number;
    finishedPercentage: number;
    assignDate: string;
    endDate: string;
    answerTaskId: number;
}
export interface areaCitySchoolEntranceListInfoProps {
    schoolList: schoolItem[];
    schoolCount: number
}

export interface TAreaCitySchoolEntranceListInfoV2Params{
    type: string
    cityCode: string
    areaCode: string
    graduationYear: string
    version?: string
    reportDateStr?: string
}

