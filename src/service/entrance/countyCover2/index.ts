import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import {
  TQueryParams,
  IviewAuthorityProps,
  gradeInfoItem,
  areaCitySchoolEntranceListInfoProps,
} from './types';
import { web } from '~/utils/hosts';

interface IEntraceReportApi {
  getUserViewAuthority: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<IviewAuthorityProps>;
  getGradeInfoList: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<gradeInfoItem[]>;
  getAreaCitySchoolEntranceListInfo: (
    data: TQueryParams,
  ) => IResponse<areaCitySchoolEntranceListInfoProps>;
}

export interface IGetAreaCitySchoolEntranceListInfo {
  params: TQueryParams;
  data: IResponseData<areaCitySchoolEntranceListInfoProps>;
}

const api: IEntraceReportApi = {
  getUserViewAuthority: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/mentalAdmissionReport/getUserViewAuthority',
        params,
      },
      ctx
    ),
  getGradeInfoList: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/mentalAdmissionAreaReport/v2/getGradeInfoList',
        data,
      },
      ctx
    ),
  getAreaCitySchoolEntranceListInfo: (params) =>
    clientRequest(
      {
        method: 'get',
        url: `${web}/api/psychology/mentalAdmissionAreaReport/v2/getAreaCitySchoolEntranceListInfo`,
        params,
      },
    ),
};

export default api;
