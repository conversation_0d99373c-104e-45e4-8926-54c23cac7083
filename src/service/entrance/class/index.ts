import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request'
import {
  TQueryParams,
  classReportProps,
  TClassListParams,
  TClassListResponse,
  TPageStudyReadinessWarningParams,
  TPageStudyReadinessWarningResponse,
  TPageStudyReadinessWarningV2Params,
  TPageStudyReadinessWarningV2Response,
} from './types';

interface IEntraceReportApi {
  // 班级报告
  getClassReport: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<classReportProps>;

  getClassList: (
    data: TClassListParams,
    ctx: ContextProps
  ) => IResponse<TClassListResponse[]>;

  getPageStudyReadinessWarning: (
    data: TPageStudyReadinessWarningParams,
    ctx: ContextProps
  ) => IResponse<TPageStudyReadinessWarningResponse[]>;

  getPageStudyReadinessWarningV2: (
    data: TPageStudyReadinessWarningV2Params,
    ctx: ContextProps
  ) => IResponse<TPageStudyReadinessWarningV2Response[]>;
}

const api: IEntraceReportApi = {
  // 班级报告
  getClassReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/mentalAdmissionReport/getClassReport',
        data,
      },
      ctx
    ),
  getClassList: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        // url: "/mentalAdmissionReport/getClassList",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionReport/getClassList',
        params,
      },
      ctx
    ),
  // 查询心里入学测评年级或班级学习准备度纬度预警名单
  getPageStudyReadinessWarning: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        // url:"/mentalAdmissionReport/pageStudyReadinessWarning",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionReport/pageStudyReadinessWarning',
        data,
      },
      ctx
    ),
  // 查询心里入学测评年级或班级心里健康纬度预警名单V2
  getPageStudyReadinessWarningV2: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        // url:"/mentalAdmissionReport/pageMentalHealthWarningV2",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionReport/pageMentalHealthWarningV2',
        data,
      },
      ctx
    ),
};

export default api;
