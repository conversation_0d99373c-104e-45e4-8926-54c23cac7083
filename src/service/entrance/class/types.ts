// 班级报告
export interface TQueryParams {
  evaluationTaskId: number;
  gradeId?: string;
  classId?: string;
  bizType?: string;
  clientType?: string | number;
  ff?: string;
  isPrint?:string,
  isDetailReport?: string;
  printMode?:boolean;
  detailReportMode?:boolean;
}

export interface mainFindingsProps {
  /** 注释 */
  mentalHealth: string[];
  /** 注释 */
  studyReadiness: string[];
  /** 注释 */
  characterTraits: string[];
  /** 注释 */
  growthEnvironment: string[];
}

export interface riskIndexProps {
  /** 说明有的地方不用返回null */
  inConclusion: string;
  /** 学习准备度枚举类,LOW(1,"低准备度"),,LOWER(2,"较低准备度"),,HIGHER(3,"较高准备度"),,HIGH(4,"高准备度") */
  level: number;
  /** 名称 */
  name: string;
  /** 分数 */
  score: number;
}

export interface latitudeListBeansItem {
  /** id */
  id: number;
  /** 名称 */
  name: string;
  /** 人数 */
  num: number;
  /** 占比如果是班级性格类型，则不需要 */
  percentage: string
}

export interface stateDistributionProps {
  /** 说明或者结论有的地方不用默认null */
  inConclusion: string;
  /** 饼状图数据 ,LatitudeListBean */
  latitudeListBeans: latitudeListBeansItem[];
}



export interface latitudeItem {
  /** 纬度id */
  id: number
  /** 纬度名称 */
  name: string
  /** 预警人数待提升 */
  num: number
  /** 占比 */
  percentage: string
  /** 高风险人数低准备度 */
  highRisk: number
  /** 占比 */
  highRiskPercentage: string
  /** 较高风险较低准备度 */
  higherRisk: number
  /** 占比 */
  higherRiskPercentage: string
  /** 轻微风险 */
  minorRisk: number
  /** 占比 */
  minorRiskPercentage: string
}

export interface factorWarningProps {
  /** 总人数 */
  totalPeople: number
  /** 列表 ,LatitudeListBean */
  latitudeList: latitudeItem[];
}

export interface resultLatitudeItem {
  /** 级别id */
  id: number;
  /** 级别名称 */
  name: string;
  /** 得分 */
  score: number;
  /** 参考分有的地方不用返回null */
  referenceScore: number;
  /** 预警分有的地方不用返回null */
  warnningScore: number;
}


export interface resultAnalysisProps {
  /** 测评结论 */
  inConclusion: string
  /** 列表 ,LatitudeListBean */
  latitudeList: resultLatitudeItem[];
}

export interface statusAnalysisProps {
  /** 注释 */
  riskIndex: riskIndexProps;
  /** 注释 */
  stateDistribution: stateDistributionProps;
  /** 注释 */
  factorWarning: factorWarningProps;
  /** 注释 */
  resultAnalysis: resultAnalysisProps;
  /** 注释 */
  factorAnalysisDetails: factorAnalysisDetailsItem[];
}

export interface onlyChildItem {
  /** id */
  id: number
  /** 名称 */
  name: string
  /** 人数 */
  num: number
}

export interface parentingStyleItem {
  /** id */
  id: number;
  /** 名称 */
  name: string;
  /** 人数 */
  num: number;
  /** 占比 */
  percentage: string;
  /** 文案 ,String */
  description: string[];
}

export interface familyUpbringingAnalysisProps {
  /** 注释 */
  onlyChild: onlyChildItem[];
  /** 注释 */
  life: onlyChildItem[];
  /** 注释 */
  fatherEducation: onlyChildItem[];
  /** 注释 */
  motherEducation: onlyChildItem[];
  /** 注释 */
  parentingStyle: parentingStyleItem[];
}

export interface classReportProps {
  queryParams: TQueryParams;
  /** 评测版本目前只有入学测存在多版本，默认不传值为1 */
  version: number;
  /** 班级名称 */
  className: string;
  /** 年级名称 */
  gradeName: string;
  /** 学校名称 */
  schoolName: string;
  /** 地区 */
  areaName: string;
  /** 报告编号 */
  reportCode: string;
  /** 报告日期 */
  reportTime: string;
  /** 开始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
  /** 完成人数 */
  evaluatedPeople: number;
  /** 总人数 */
  totalPeople: number;
  /** 主要发现1 ,MainFindingVO */
  mainFindings: mainFindingsProps;
  /** 心里健康状态分析2 ,StatusAnalysisBean */
  statusAnalysis: statusAnalysisProps;
  /** 学习准备度分析3 ,LearningStatusBean */
  learningStatus: LearningStatus;
  /** 学生性格类型分布图4 ,PersonalityAnalysisBean */
  personalityAnalysis: PersonalityAnalysis;
  /** 家庭环境分析5 ,FamilyUpbringingAnalysisBean */
  familyUpbringingAnalysis: familyUpbringingAnalysisProps;
}

export interface TClassReportResponse {
  success?: boolean;
  code?: string | number;
  msg?: string;
  data?: classReportProps;
  queryParams?: TQueryParams
}

export interface PersonalityAnalysis {
  /** 注释 */
  pieChartVO: stateDistributionProps
  /** 注释 */
  analysisDetails: AnalysisDetails[];
}

export interface AnalysisDetails {
  /** id */
  id?: number
  /** 名称 */
  name?: string
  /** 占比 */
  percentage?: string
  /** 性格特征 ,String */
  characterTraits?: string[]
  /** 辅导建议 ,String */
  counselingAdvice?: string[]
}

export interface LearningStatus {
  /** 注释 */
  riskIndex: riskIndexProps
  /** 注释 */
  pieResultAnalysis: stateDistributionProps
  /** 注释 */
  factorWarning: factorWarningProps;
  /** 注释 */
  resultAnalysis: resultAnalysisProps;
  /** 注释 */
  readinessDetailsBeans: ReadinessDetailsBeans[];
}

export interface ReadinessDetailsBeans {
  /** 因子id */
  id?: number
  /** 名称 */
  name?: string
  /** 分数 */
  score?: number
  /** 总分或标准分部分列表不用 */
  totalScore?: number
  /** 级别 */
  level?: number
  /** 级别名称 */
  levelName?: string
  /** 因子说明 ,String */
  factorDesc?: string[]
  /** 学生情况 ,String */
  studentPerformed?: string[]
  /** 辅导建议 ,String */
  counselingAdvice?: string[]
  /** 饼图+文案 ,PieChartVO */
  pieChartVOS?: stateDistributionProps;
  /** 低考试准备度学生 ,String */
  low?: string[]
  /** 较低考试准备度学生 ,String */
  lower?: string[]
}


export interface factorAnalysisDetailsItem {
  /** 因子id */
  id: number
  /** 名称 */
  name: string
  /** 分数 */
  score: number
  /** 总分不用 */
  totalScore: string
  /** 级别 */
  level: number
  /** 级别名称 */
  levelName: string
  /** 因子说明 ,String */
  factorDescription: string[]
  /** 学生情况 ,String */
  studentPerformed: string[]
  /** 辅导建议 ,String */
  counselingAdvice: string[]
  /** 饼图+文案 ,PieChartVO */
  pieChartVOS: stateDistributionProps;
  /** 高风险学生 ,String */
  highRisk: string[]
  /** 较高风险学生 ,String */
  higherRisk: string[]
  /** 轻微风险学生 ,String */
  minorRisk: string[]
}




export interface TClassListParams{
  /** 布置id(Long) */
  evaluationTaskId: string
  /** 年级id(String) */
  gradeId: string
}

export interface classItem{
  /** 班级id */
  classId?: string
  /** 班级名称 */
  className?: string
}
export interface TClassListResponse{
     /** 注释 */
  success?: boolean
  /** 注释 */
  code?: string
  /** 注释 */
  msg?: string
  /** ClassContentVO */
  data?: classItem[];
}


export interface TPageStudyReadinessWarningParams{
 level:number
  evaluationTaskId:number
  gradeId:string
  classId:string
  classGroupNum?:string
  userName?:string
  sort?:number
  hide?:boolean
  pageIndex?:number
  pageSize?:number
  start?:number
}

export interface Data1 {
  /** 班级id */
  classId?: string
  /** 班级名称 */
  className?: string
  /** 学生id */
  userId?: string
  /** 学生姓名 */
  userName?: string
  /** 个人评测id查询个人报告需要 */
  userRecordId?: number
  /** 个人报告地址 */
  userRecordIdUrl?: string
  /** 学习方法论 */
  learnMethodology?: number
  /** 学习方法论级别 */
  learnMethodologyLevel?: number
  /** 注释 */
  learnMethodologyLevelName?: string
  /** 学习主动性 */
  learnInitiative?: number
  /** 学习主动性级别 */
  learnInitiativeLevel?: number
  /** 注释 */
  learnInitiativeLevelName?: string
  /** 学习计划性 */
  learnPlanning?: number
  /** 学习计划性级别 */
  learnPlanningLevel?: number
  /** 注释 */
  learnPlanningLevelName?: string
  /** 学习意志力 */
  learnWillpower?: number
  /** 学习意志力级别 */
  learnWillpowerLevel?: number
  /** 注释 */
  learnWillpowerLevelName?: string
  /** 学习意义感 */
  learnSense?: number
  /** 学习意义感级别 */
  learnSenseLevel?: number
  /** 注释 */
  learnSenseLevelName?: string
  /** 学习专注度 */
  learnConcentration?: number
  /** 学习专注度级别 */
  learnConcentrationLevel?: number
  /** 注释 */
  learnConcentrationLevelName?: string
  /** 考试准备度 */
  examReadiness?: number
  /** 考试准备度级别 */
  examReadinessLevel?: number
  /** 注释 */
  examReadinessLevelName?: string
  /** 学习成绩观 */
  learnAchievementView?: number
  /** 学习成绩观级别 */
  learnAchievementViewLevel?: number
  /** 注释 */
  learnAchievementViewLevelName?: string
  /** 学习探索性 */
  learnExploratory?: number
  /** 学习探索性级别 */
  learnExploratoryLevel?: number
  /** 注释 */
  learnExploratoryLevelName?: string
  /** 学习好感度 */
  learnFavorability?: number
  /** 学习好感度级别 */
  learnFavorabilityLevel?: number
  /** 注释 */
  learnFavorabilityLevelName?: string
  /** 学习准备度 */
  learnReadiness?: number
  /** 学习准备度级别 */
  learnReadinessLevel?: number
  /** 注释 */
  learnReadinessLevelName?: string
}
export interface TPageStudyReadinessWarningResponse{
  /** 注释 */
  success?: boolean
  /** 注释 */
  code?: string
  /** 注释 */
  msg?: string
  /** PageQueryResult */
  data?: Data1
}
export interface TPageStudyReadinessWarningV2Params{
  level:number
  evaluationTaskId:number
  gradeId:string
  classId:string
  classGroupNum?:string
  userName?:string
  sort?:number
  hide?:boolean
  pageIndex?:number
  pageSize?:number
  start?:number
}
export interface TPageStudyReadinessWarningV2Response{
   /** 注释 */
  success?: boolean
  /** 注释 */
  code?: string
  /** 注释 */
  msg?: string
  /** PageQueryResult */
  data?: Data2
}

export interface Data {
  /** 班级id */
  classId?: string
  /** 班级名称 */
  className?: string
  /** 学生id */
  userId?: string
  /** 学生姓名 */
  userName?: string
  /** 个人评测id查询个人报告需要 */
  userRecordId?: number
  /** 个人报告地址 */
  userRecordIdUrl?: string
  /** 焦虑抑郁 */
  anxietyDepression?: number
  /** 焦虑抑郁级别 */
  anxietyDepressionLevel?: number
  /** 注释 */
  anxietyDepressionLevelName?: string
  /** 学习压力 */
  studyInducedStress?: number
  /** 学习压力级别 */
  studyInducedStressLevel?: number
  /** 注释 */
  studyInducedStressLevelName?: string
  /** 攻击倾向 */
  aggressiveTendencies?: number
  /** 攻击倾向级别 */
  aggressiveTendenciesLevel?: number
  /** 注释 */
  aggressiveTendenciesLevelName?: string
  /** 注意缺陷 */
  attentionDeficit?: number
  /** 注意缺陷级别 */
  attentionDeficitLevel?: number
  /** 注释 */
  attentionDeficitLevelName?: string
  /** 人际敏感 */
  interpersonalSensitivity?: number
  /** 人际敏感级别 */
  interpersonalSensitivityLevel?: number
  /** 注释 */
  interpersonalSensitivityName?: string
  /** 思维问题 */
  thinkingProblem?: number
  /** 思维问题级别 */
  thinkingProblemLevel?: number
  /** 注释 */
  thinkingProblemLevelName?: string
  /** 躯体反应 */
  physicalResponse?: number
  /** 躯体反应级别 */
  physicalResponseLevel?: number
  /** 注释 */
  physicalResponseLevelName?: string
  /** 风险指数 */
  overallRisk?: number
  /** 总体风险级别 */
  overallRiskLevel?: number
  /** 注释 */
  overallRiskLevelName?: string
}
export interface Data2 {
  /** 总页数 */
  totalPages?: number
  /** 当前页码 */
  pageIndex?: number
  /** 每页条数 */
  pageSize?: number
  /** 总记录数 */
  totalRecords?: number
  /** 分页结果集 ,T */
  data?: Data
  /** 注释 */
  havePrePage?: boolean
  /** 注释 */
  haveNextPage?: boolean
}

