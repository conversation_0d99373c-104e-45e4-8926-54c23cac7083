export interface TQueryParams {
    type?: string | number;
}

export interface TGradeInfoResponse{
    graduationYear:number
    gradeName:string
}

export interface TAreaCitySchoolEntranceListInfoParams {
    type: string
    cityCode: string
    areaCode: string
    graduationYear: string
}


export interface SchoolList {
    areaCode?: string
    areaName?: string
    /** 集体报告数量0没有，连接置灰操作，1：有一个布置，2：有多个布置 */
    gradeFlag?: number
    schoolCode?: number
    schoolName?: string
    gradeName?: string
    totalNum?: number
    finishedNum?: number
    finishedPercentage?: number
    assignDate?: string
    endDate?: string
    answerTaskId?: number
}
export interface schoolDataList {
    schoolList?: SchoolList
    schoolCount?: number
}
export interface TAreaCitySchoolEntranceListInfoResponse {
    success?: boolean
    code?: string
    msg?: string
    /** AreaCitySchoolEntranceListInfoVO */
    data?: schoolDataList
}

export interface TAreaCitySchoolEntranceListInfoV2Params{
    /** type1市级报告；2区县报告 */
    type: string
    cityCode: string
    areaCode: string
    graduationYear: string
    /** 后端使用，前端不需要传 */
    version?: string
    reportDateStr?: string
}

