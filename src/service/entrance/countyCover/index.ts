import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import {
  TQueryParams,
  TGradeInfoResponse,
  TAreaCitySchoolEntranceListInfoParams,
  TAreaCitySchoolEntranceListInfoResponse,
  TAreaCitySchoolEntranceListInfoV2Params,
} from './types';

interface IEntraceReportApi {
  getGradeInfoList: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<TGradeInfoResponse[]>;

  getAreaCitySchoolEntranceListInfo: (
    data: TAreaCitySchoolEntranceListInfoParams,
    ctx: ContextProps
  ) => IResponse<TAreaCitySchoolEntranceListInfoResponse[]>;
  getAreaCitySchoolEntranceListInfoV2: (
    data: TAreaCitySchoolEntranceListInfoV2Params,
    ctx: ContextProps
  ) => IResponse<TAreaCitySchoolEntranceListInfoResponse[]>;
}

const api: IEntraceReportApi = {
  // 版本一 入口
  // 获取年级列表
  getGradeInfoList: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        // url:"/mentalAdmissionReport/getGradeInfoList",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionReport/getGradeInfoList',
        params,
      },
      ctx
    ),
  getAreaCitySchoolEntranceListInfo: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        // url:"/mentalAdmissionReport/getAreaCitySchoolEntranceListInfo",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionReport/getAreaCitySchoolEntranceListInfo',
        params,
      },
      ctx
    ),
  // 版本二 入口
  getAreaCitySchoolEntranceListInfoV2: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        // url:"/mentalAdmissionAreaReport/v2/getAreaCitySchoolEntranceListInfo",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionAreaReport/v2/getAreaCitySchoolEntranceListInfo',
        params,
      },
      ctx
    ),
};

export default api;
