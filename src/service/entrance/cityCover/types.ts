export interface TGradeInfoParams {

}

export interface TGradeInfoResponse {
    
}

export interface TAreaSelectParams{
    selectType:string
    cityCode:number
    graduationYear:number
}

export interface Data {
    /** 区县id */
    areaId?: number
    /** 区县名称 */
    areaName?: string
}
export interface TAreaSelectResponse{
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** AreaInfoVO */
    data?: Data
}
export interface TAreaCityEntranceListParams {
    /** type1市级报告；2区县报告 */
    type: string
    /** 市级code */
    cityCode: string
    /** 县区code */
    areaCode: string
    /** 废弃字段，不要传了 */
    templateId: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 后端使用，前端不需要传 */
    version?: string
    /** 日期的传参格式yyyy-mm-dd~yyyy-mm-dd,心理入学测报告时间优化增加,2022-06-01~2022-05-30 */
    reportDateStr?: string
}

export interface SchoolStaticsList {
    /** 市级code */
    cityCode?: number
    /** 区code */
    areaCode?: number
    /** 区县名称 */
    areaName?: string
    /** 学校数量 */
    schoolNum?: number
    /** 毕业年份 */
    graduationYear?: number
    /** 年级 */
    gradeName?: string
    /** 布置总人数 */
    totalNum?: number
    /** 完成测评人数 */
    finishedNum?: number
    /** 有效报告学校数 */
    effectiveSchoolCnt?: number
    /** 完成百分比 */
    finishedPercentage?: number
    /** 是否有区县报告true有；false没有 */
    hasAreaReport?: boolean
}
export interface Data1 {
    /** 已有14所学校生成有效集体报告中的数字 */
    schoolCount?: number
    /** 市级报告入口页独有内容上面那个表格 ,CityEntranceSchoolStaticsVO */
    schoolStaticsList?: SchoolStaticsList
}
export interface TAreaCityEntranceListResponse{
    /** 注释 */
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** AreaCityEntranceListVO */
    data?: Data1
}

export interface TAreaCitySchoolEntranceListInfoParams{
    /** type1市级报告；2区县报告 */
    type: string
    /** 市级code */
    cityCode: string
    /** 县区code */
    areaCode: string
    /** 废弃字段，不要传了 */
    templateId?: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 后端使用，前端不需要传 */
    version?: string
    /** 日期的传参格式yyyy-mm-dd~yyyy-mm-dd,心理入学测报告时间优化增加,2022-06-01~2022-05-30 */
    reportDateStr?: string
}

export interface SchoolList {
    /** 区code */
    areaCode?: string
    /** 区县名称 */
    areaName?: string
    /** 集体报告数量0没有，连接置灰操作，1：有一个布置，2：有多个布置 */
    gradeFlag?: number
    /** 学校code */
    schoolCode?: number
    /** 学校名称 */
    schoolName?: string
    /** 年级名称 */
    gradeName?: string
    /** 布置人数 */
    totalNum?: number
    /** 完成测评人数 */
    finishedNum?: number
    /** 完成率 */
    finishedPercentage?: number
    /** 布置日期 */
    assignDate?: string
    /** 截至日期 */
    endDate?: string
    /** 布置id */
    answerTaskId?: number
}
export interface Data2 {
    /** 学校数据列表 ,AreaEntranceDataVO */
    schoolList?: SchoolList
    /** 学校数量 */
    schoolCount?: number
}
export interface TAreaCitySchoolEntranceListInfoResponse{
    /** 注释 */
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** AreaCitySchoolEntranceListInfoVO */
    data?: Data2
}
