import serviceRequest, { ContextProps }from '@/service/serviceRequest';
import type { IResponse } from '@/service/request'
import {
  TAreaSelectParams,
  TAreaSelectResponse,
  TAreaCityEntranceListParams,
  TAreaCityEntranceListResponse,
  TAreaCitySchoolEntranceListInfoParams,
  TAreaCitySchoolEntranceListInfoResponse,
  TGradeInfoParams,
  TGradeInfoResponse
} from './types';

interface IEntraceReportApi {
  getGradeInfoList: (
    data: TGradeInfoParams,
    ctx: ContextProps
  ) => IResponse<TGradeInfoResponse[]>;
  getAreaSelect: (
    data: TAreaSelectParams,
    ctx: ContextProps
  ) => IResponse<TAreaSelectResponse[]>;
  getAreaCityEntranceList: (
    data: TAreaCityEntranceListParams,
    ctx: ContextProps
  ) => IResponse<TAreaCityEntranceListResponse[]>;
  getAreaCitySchoolEntranceListInfo: (
    data: TAreaCitySchoolEntranceListInfoParams,
    ctx: ContextProps
  ) => IResponse<TAreaCitySchoolEntranceListInfoResponse[]>;
}

const api: IEntraceReportApi = {
  // 版本一 入口
  // 获取年级列表
  getGradeInfoList: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        // url:"/mentalAdmissionReport/getGradeInfoList",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionReport/getGradeInfoList',
        params,
      },
      ctx
    ),
  getAreaSelect: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        // url:"/mentalAdmissionReport/getAreaSelect",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionReport/getAreaSelect',
        data,
      },
      ctx
    ),
  // 版本二 入口
  getAreaCityEntranceList: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        // url:"/mentalAdmissionAreaReport/v2/getAreaCityEntranceList",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionAreaReport/v2/getAreaCityEntranceList',
        params,
      },
      ctx
    ),

  getAreaCitySchoolEntranceListInfo: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        // url:"/mentalAdmissionAreaReport/v2/getAreaCitySchoolEntranceListInfo",
        url: 'http://yapi.235.mistong.com/mock/1199/mentalAdmissionAreaReport/v2/getAreaCitySchoolEntranceListInfo',
        params,
      },
      ctx
    ),
};

export default api;
