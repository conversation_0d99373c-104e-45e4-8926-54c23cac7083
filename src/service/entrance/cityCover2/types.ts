export interface TQueryParams {
    type?: string | number;
    clientType?: string | number;
    cityCode?: string | number;
    areaCode?: string | number | null;
    graduationYear?: string | number | null;
    reportDateStr?: string | number |null;
}

export interface TAreaSelectParams{
    selectType:string
    cityCode:number
    graduationYear:number
}

export interface Data {
    /** 区县id */
    areaId?: number
    /** 区县名称 */
    areaName?: string
}
export interface TAreaSelectResponse{
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** AreaInfoVO */
    data?: Data
}
export interface TAreaCityEntranceListParams {
    /** type1市级报告；2区县报告 */
    type: string
    /** 市级code */
    cityCode: string
    /** 县区code */
    areaCode: string
    /** 废弃字段，不要传了 */
    templateId: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 后端使用，前端不需要传 */
    version?: string
    /** 日期的传参格式yyyy-mm-dd~yyyy-mm-dd,心理入学测报告时间优化增加,2022-06-01~2022-05-30 */
    reportDateStr?: string
}

export interface schoolStaticsItem {
    cityCode: number;
    areaCode: number;
    areaName: string;
    schoolNum: number;
    graduationYear: number;
    gradeName: string;
    totalNum: number;
    finishedNum: number;
    effectiveSchoolCnt: number;
    finishedPercentage: number;
    hasAreaReport: boolean;
}
export interface areaCityEntranceProps {
    schoolCount: number
    schoolStaticsList: schoolStaticsItem[];
}
export interface TAreaCityEntranceListResponse {
    success?: boolean
    code?: string
    msg?: string
    data?: areaCityEntranceProps
}

export interface TAreaCitySchoolEntranceListInfoParams{
    /** type1市级报告；2区县报告 */
    type: string
    /** 市级code */
    cityCode: string
    /** 县区code */
    areaCode: string
    /** 废弃字段，不要传了 */
    templateId?: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 后端使用，前端不需要传 */
    version?: string
    /** 日期的传参格式yyyy-mm-dd~yyyy-mm-dd,心理入学测报告时间优化增加,2022-06-01~2022-05-30 */
    reportDateStr?: string
}

export interface schoolDataItem {
    /** 区code */
    areaCode?: string
    /** 区县名称 */
    areaName?: string
    /** 集体报告数量0没有，连接置灰操作，1：有一个布置，2：有多个布置 */
    gradeFlag?: number
    schoolCode?: number
    schoolName?: string
    gradeName?: string
    /** 布置人数 */
    totalNum?: number
    /** 完成测评人数 */
    finishedNum?: number
    /** 完成率 */
    finishedPercentage?: number
    /** 布置日期 */
    assignDate?: string
    /** 截至日期 */
    endDate?: string
    /** 布置id */
    answerTaskId?: number
}

export interface areaCitySchoolEntranceListInfoProps {
    schoolList: schoolDataItem[]
    schoolCount: number
}

export interface TAreaCitySchoolEntranceListInfoResponse {
    success?: boolean
    code?: string
    msg?: string
    data?: areaCitySchoolEntranceListInfoProps
}
