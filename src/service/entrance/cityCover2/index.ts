import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse, IResponseData } from '@/service/request';
import clientRequest from '@/service/clientRequest';
import {
  viewAuthorityProps,
  gradeItem,
} from '~/pages/entrance/cityCover2/types';

import {
  TQueryParams,
  areaCityEntranceProps,
  areaCitySchoolEntranceListInfoProps,
} from './types';

import { web } from '~/utils/hosts';

interface IEntraceCityCover2Api {
  getUserViewAuthority: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<viewAuthorityProps>;
  getGradeInfoList: (
    data: TQueryParams,
    ctx: ContextProps
  ) => IResponse<gradeItem[]>;
  getAreaCityEntranceList: (
    data: TQueryParams
  ) => IResponse<areaCityEntranceProps>;
  getAreaCitySchoolEntranceListInfo: (
    data: TQueryParams,
  ) => IResponse<areaCitySchoolEntranceListInfoProps>;
}

export interface IGetAreaCitySchoolEntranceListInfo {
  params: TQueryParams;
  data: IResponseData<areaCitySchoolEntranceListInfoProps>;
}

const api: IEntraceCityCover2Api = {
  getUserViewAuthority: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/mentalAdmissionReport/getUserViewAuthority',
        params,
      },
      ctx
    ),
  // 获取年级列表
  getGradeInfoList: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/mentalAdmissionAreaReport/v2/getGradeInfoList',
        data,
      },
      ctx
    ),
  getAreaCityEntranceList: (params) =>
    clientRequest({
      method: 'get',
      url: `${web}/api/psychology/mentalAdmissionAreaReport/v2/getAreaCityEntranceList`,
      params,
    }),
  getAreaCitySchoolEntranceListInfo: (params) =>
    clientRequest(
      {
        method: 'get',
        url: `${web}/api/psychology/mentalAdmissionAreaReport/v2/getAreaCitySchoolEntranceListInfo`,
        params,
      },
    ),
};

export default api;
