import serviceRequest, { ContextProps } from '../../serviceRequest';
import type { IResponse } from '@/service/request';
import { TPersonReportParams, userReportProps } from './types';

interface IPersonReportApi {
	getUserReport: (
		data: TPersonReportParams,
		ctx: ContextProps
	) => IResponse<userReportProps>;
}

const api: IPersonReportApi = {
	// 个人报告
	getUserReport: (data, ctx) =>
		serviceRequest(
			{
				method: 'post',
				url: '/api/psychology/mentalAdmissionReport/getUserReport',
				data,
			},
			ctx
		),
};

export default api;
