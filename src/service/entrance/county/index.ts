import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import type { IResponse } from '@/service/request';
import {
  TCountyReportParams,
  TCountyReportResponse,
  TAreaCityWaringStudentsListParams,
  TAreaCityWaringStudentsListResponse,
  TAreaCityPromotedStudentsListParams,
  TAreaCityPromotedStudentsListResponse,
  TAreaCityPersonalitySituationParams,
  TAreaCityPersonalitySituationResponse,
  TAreaCityFamilySituationParams,
  TAreaCityFamilySituationResponse,
} from './types';

interface IEntraceReportApi {
  getAreaReport: (
    data: TCountyReportParams,
    ctx: ContextProps
  ) => IResponse<TCountyReportResponse>;
  getAreaCityWaringStudentsList: (
    data: TAreaCityWaringStudentsListParams,
    ctx: ContextProps
  ) => IResponse<TAreaCityWaringStudentsListResponse[]>;

  getAreaCityPromotedStudentsList: (
    data: TAreaCityPromotedStudentsListParams,
    ctx: ContextProps
  ) => IResponse<TAreaCityPromotedStudentsListResponse[]>;

  getAreaCityPersonalitySituation: (
    data: TAreaCityPersonalitySituationParams,
    ctx: ContextProps
  ) => IResponse<TAreaCityPersonalitySituationResponse[]>;
  getAreaCityFamilySituation: (
    data: TAreaCityFamilySituationParams,
    ctx: ContextProps
  ) => IResponse<TAreaCityFamilySituationResponse[]>;
}

const api: IEntraceReportApi = {
  getAreaReport: (params, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: '/api/psychology/mentalAdmissionAreaReport/v2/getAreaReport',
        params,
      },
      ctx
    ),
  getAreaCityWaringStudentsList: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/mentalAdmissionAreaReport/v2/getAreaCityWaringStudentsList',
        params,
      },
      ctx
    ),
  getAreaCityPromotedStudentsList: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/mentalAdmissionAreaReport/v2/getAreaCityPromotedStudentsList',
        params,
      },
      ctx
    ),
  getAreaCityPersonalitySituation: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/mentalAdmissionAreaReport/v2/getAreaCityPersonalitySituation',
        params,
      },
      ctx
    ),
  // 获取区县（市级）家庭教养风格列表
  getAreaCityFamilySituation: (params, ctx) =>
    serviceRequest(
      {
        method: 'get',
        url: '/api/psychology/mentalAdmissionAreaReport/v2/getAreaCityFamilySituation',
        params,
      },
      ctx
    ),
};

export default api;
