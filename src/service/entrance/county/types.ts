export interface TCountyReportParams{
    templateId:number;
    cityCode:number;
    areaCode:number;
    gradeId:number;
    ff?: number;
    isPrint?: string;
    isDetailReport?: string;
    printMode?: boolean;
    detailReportMode?: boolean;
}

export interface OnlyChild {
    /** id */
    id?: number
    /** 名称 */
    name?: string
    /** 人数 */
    num?: number
}

export interface FamilyUpbringingAnalysis {
    /** 注释 */
    onlyChild: OnlyChild
    /** 注释 */
    life: OnlyChild
    /** 注释 */
    fatherEducation: OnlyChild
    /** 注释 */
    motherEducation: OnlyChild
    /** 注释 */
    parentingStyle: ParentingStyle
}

export interface ParentingStyle {
    /** id */
    id?: number
    /** 名称 */
    name?: string
    /** 人数 */
    num?: number
    /** 占比 */
    percentage?: string
    /** 文案 ,String */
    description?: string[]
}

export interface PieChartVOS {
    /** 说明或者结论有的地方不用默认null */
    inConclusion?: string
    /** 饼状图数据 ,LatitudeListBean */
    latitudeListBeans?: LatitudeListBeans
}


export interface PersonalityAnalysis {
    /** 注释 */
    pieChartVO: PieChartVOS
    /** 注释 */
    analysisDetails: AnalysisDetails
}

export interface AnalysisDetails {
    /** id */
    id?: number
    /** 名称 */
    name?: string
    /** 占比 */
    percentage?: string
    /** 性格特征 ,String */
    characterTraits?: string[]
    /** 辅导建议 ,String */
    counselingAdvice?: string[]
}
export interface LatitudeList2 {
    /** 纬度id */
    id?: number
    /** 纬度名称 */
    name?: string
    /** 预警人数待提升 */
    num?: number
    /** 占比 */
    percentage?: string
    /** 高风险人数低准备度 */
    highRisk?: number
    /** 占比 */
    highRiskPercentage?: string
    /** 较高风险较低准备度 */
    higherRisk?: number
    /** 占比 */
    higherRiskPercentage?: string
    /** 轻微风险 */
    minorRisk?: number
    /** 占比 */
    minorRiskPercentage?: string
}
export interface FactorWarning {
    /** 总人数 */
    totalPeople?: number
    /** 列表 ,LatitudeListBean */
    latitudeList?: LatitudeList2
}
export interface LearningStatus {
    /** 注释 */
    riskIndex: RiskIndex
    /** 注释 */
    areaLevelVO: AreaLevelVO2
    /** 注释 */
    pieResultAnalysis: StateDistribution
    /** 注释 */
    areaWarningVOS: AreaWarningVOS
    /** 注释 */
    areaFactorWarningVOS: AreaWarningVOS
    /** 注释 */
    resultAnalysis: ResultAnalysis
    /** 注释 */
    factorWarning: FactorWarning
    /** 注释 */
    factorAnalysisDetails: FactorAnalysisDetails2
}

export interface FactorAnalysisDetails2 {
    /** 因子id */
    id?: number
    /** 名称 */
    name?: string
    /** 分数 */
    score?: number
    /** 总分或标准分没有返回null */
    totalScore?: number
    /** 级别 */
    level?: number
    /** 级别名称 */
    levelName?: string
    /** 因子说明 ,String */
    factorDescription?: string[]
    /** 学生情况 ,String */
    studentPerformed?: string[]
    /** 辅导建议 ,String */
    counselingAdvice?: string[]
    /** 饼图+文案 ,PieChartVO */
    pieChartVOS?: PieChartVOS
}

export interface AreaLevelVO2 {
    /** 地区id */
    id?: number
    /** 地区名称 */
    name?: string
    /** 学习准备度枚举类,LOW(1,"低准备度"),,LOWER(2,"较低准备度"),,HIGHER(3,"较高准备度"),,HIGH(4,"高准备度") */
    level?: number
    /** 名称 */
    levelName?: string
    /** 分数 */
    score?: number
}

export interface StatusAnalysis {
    /** 注释 */
    riskIndex: RiskIndex
    /** 注释 */
    areaLevelVO: AreaLevelVO
    /** 注释 */
    stateDistribution: StateDistribution
    /** 注释 */
    areaWarningVOS: AreaWarningVOS
    /** 注释 */
    areaFactorWarningVOS: AreaWarningVOS
    /** 注释 */
    resultAnalysis: ResultAnalysis
    /** 注释 */
    factorWarning: FactorWarning
    /** 注释 */
    factorAnalysisDetails: FactorAnalysisDetails
}


export interface FactorAnalysisDetails {
    /** 因子id */
    id?: number
    /** 名称 */
    name?: string
    /** 分数 */
    score?: number
    /** 总分或标准分没有返回null */
    totalScore?: string
    /** 级别 */
    level?: number
    /** 级别名称 */
    levelName?: string
    /** 因子说明 ,String */
    factorDescription?: string[]
    /** 学生情况 ,String */
    studentPerformed?: string[]
    /** 辅导建议 ,String */
    counselingAdvice?: string[]
    /** 饼图+文案 ,PieChartVO */
    pieChartVOS?: PieChartVOS
}






export interface LatitudeList {
    /** 级别id */
    id?: number
    /** 级别名称 */
    name?: string
    /** 得分 */
    score?: number
    /** 参考分有的地方不用返回null */
    referenceScore?: number
    /** 预警分有的地方不用返回null */
    warnningScore?: number
}
export interface ResultAnalysis {
    /** 测评结论 */
    inConclusion?: string
    /** 列表 ,LatitudeListBean */
    latitudeList?: LatitudeList
}

export interface AreaWarningVOS {
    /** 区域id */
    areaId?: number
    /** 区域名称 */
    areaName?: string
    /** 学校id */
    schoolId?: number
    /** 学校名称 */
    schoolName?: string
    /** 预警人数待提升 */
    num?: number
    /** 占比 */
    percentage?: number
    /** 无风险高准备度 */
    norisk?: number
    /** 占比 */
    noriskPercentage?: number
    /** 轻微风险较高准备度 */
    minorRisk?: number
    /** 占比 */
    minorRiskPercentage?: number
    /** 较高风险较低准备度 */
    higherRisk?: number
    /** 占比 */
    higherRiskPercentage?: number
    /** 高风险人数低准备度 */
    highRisk?: number
    /** 占比 */
    highRiskPercentage?: number
}

export interface LatitudeListBeans {
    /** id */
    id?: number
    /** 名称 */
    name?: string
    /** 人数 */
    num?: number
    /** 占比如果是班级性格类型，则不需要 */
    percentage?: string
}
export interface StateDistribution {
    /** 说明或者结论有的地方不用默认null */
    inConclusion?: string[]
    /** 饼状图数据 ,LatitudeListBean */
    latitudeListBeans?: LatitudeListBeans
}

export interface AreaLevelVO {
    /** 地区id */
    id?: number
    /** 地区名称 */
    name?: string
    /** 心里健康级别名称 */
    level?: number
    /** 级别名称 */
    levelName?: string
    /** 较高风险占比20 */
    higherRiskPercentage?: string
    /** 高风险占比30 */
    highRiskPercentage?: string
}

export interface RiskIndex {
    /** 说明有的地方不用返回null */
    inConclusion?: string
    /** 学习准备度枚举类,LOW(1,"低准备度"),,LOWER(2,"较低准备度"),,HIGHER(3,"较高准备度"),,HIGH(4,"高准备度") */
    level?: number
    /** 名称 */
    name?: string
    /** 分数 */
    score?: number
}

export interface MainFindings {
    /** 注释 */
    mentalHealth: string[]
    /** 注释 */
    studyReadiness: string[]
    /** 注释 */
    characterTraits: string[]
    /** 注释 */
    growthEnvironment: string[]
}

export interface EvaluationSchoolVOS {
    /** 城市名称，区县报告不需要 */
    cityName?: string
    /** 区县名称 */
    areaName?: string
    /** 学校名称 */
    schoolName?: string
    /** 布置班级数 */
    classNum?: number
    /** 布置学生数 */
    studentNum?: number
    /** 完成数 */
    completeNum?: number
    /** 完成率 */
    completionRate?: string
}

// 查询地市（区县）预警学生名单
export interface TAreaCityWaringStudentsListParams{
    /** 区县code */
    areaCode: string
    /** 学校code */
    schoolName: string
    /** 风险等级 */
    riskLevel: string
    /** type=1区县（areaCode必传）；type=2（cityCode必传）市级 */
    type: string
    /** 排序字段1：按得分排序；2：按首字母排序默认1 */
    sort: string
    /** cityCode城市编码 */
    cityCode: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 当前页码 */
    pageIndex: string
    /** 每页数量 */
    pageSize: string
    /** 从第几个开始 */
    start: string
}

export interface Data3{
    /** 学校 */
    schoolName?: string
    /** 班级 */
    className?: string
    /** 学生姓名 */
    studentName?: string
    /** 地市报告返回区县 */
    areaName?: string
    /** 记录id */
    userRecordId?: number
    /** 焦虑抑郁 */
    anxietyDepression?: number
    /** 焦虑抑郁级别 */
    anxietyDepressionLevel?: number
    /** 焦虑抑郁级别 */
    anxietyDepressionLevelStr?: string
    /** 注意缺陷 */
    attentionDeficit?: number
    /** 注意缺陷级别 */
    attentionDeficitLevel?: number
    /** 注意缺陷级别 */
    attentionDeficitLevelStr?: string
    /** 思维问题 */
    thinkingProblem?: number
    /** 思维问题级别 */
    thinkingProblemLevel?: number
    /** 思维问题级别 */
    thinkingProblemLevelStr?: string
    /** 社交问题 */
    socialIssues?: number
    /** 社交问题级别 */
    socialIssuesLevel?: number
    /** 社交问题级别 */
    socialIssuesLevelStr?: string
    /** 攻击行为 */
    aggressiveBehavior?: number
    /** 攻击行为级别 */
    aggressiveBehaviorLevel?: number
    /** 攻击行为级别 */
    aggressiveBehaviorLevelStr?: string
    /** 行为退缩 */
    behavioralWithdrawal?: number
    /** 行为退缩级别 */
    behavioralWithdrawalLevel?: number
    /** 行为退缩级别 */
    behavioralWithdrawalLevelStr?: string
    /** 违纪行为 */
    disciplinaryBehavior?: number
    /** 违纪行为级别 */
    disciplinaryBehaviorLevel?: number
    /** 违纪行为级别 */
    disciplinaryBehaviorLevelStr?: string
    /** 躯体反应 */
    physicalResponse?: number
    /** 躯体反应级别 */
    physicalResponseLevel?: number
    /** 躯体反应级别 */
    physicalResponseLevelStr?: string
    /** 总体风险 */
    overallRisk?: number
    /** 总体风险级别 */
    overallRiskLevel?: number
    /** 总体风险级别名称 */
    overallRiskLevelName?: string
    /** 总体风险级别名称(用于excel导出) */
    overallRiskLevelName2?: string
}
export interface Data2 {
    /** 总页数 */
    totalPages?: number
    /** 当前页码 */
    pageIndex?: number
    /** 每页条数 */
    pageSize?: number
    /** 总记录数 */
    totalRecords?: number
    /** 分页结果集 ,T */
    data?: Data3
    /** 注释 */
    havePrePage?: boolean
    /** 注释 */
    haveNextPage?: boolean
}
export interface TAreaCityWaringStudentsListResponse{
    /** 注释 */
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** PageQueryResult */
    data?: Data2
}

// 查询地市（区级）待提升学生名单
export interface TAreaCityPromotedStudentsListParams{
    /** 区县code */
    areaCode: string
    /** 学校code */
    schoolName: string
    /** 风险等级 */
    riskLevel: string
    /** type=1区县（areaCode必传）；type=2（cityCode必传）市级 */
    type: string
    /** 排序字段1：按得分排序；2：按首字母排序默认1 */
    sort: string
    /** cityCode城市编码 */
    cityCode: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 当前页码 */
    pageIndex: string
    /** 每页数量 */
    pageSize: string
    /** 从第几个开始 */
    start: string
}
export interface TAreaCityPromotedStudentsListResponse{
    /** 注释 */
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** PageQueryResult */
    data?: Data6
}
export interface Data7 {
    /** 个人评测id查询个人报告需要 */
    userRecordId?: number
    /** 地市报告返回区县 */
    areaName?: string
    /** 学校 */
    schoolName?: string
    /** 班级 */
    className?: string
    /** 学生姓名 */
    studentName?: string
    /** 学习方法论 */
    learnMethodology?: number
    /** 学习方法论级别 */
    learnMethodologyLevel?: number
    /** 学习方法论级别 */
    learnMethodologyLevelStr?: string
    /** 学习主动性 */
    learnInitiative?: number
    /** 学习主动性级别 */
    learnInitiativeLevel?: number
    /** 学习主动性级别 */
    learnInitiativeLevelStr?: string
    /** 学习计划性 */
    learnPlanning?: number
    /** 学习计划性级别 */
    learnPlanningLevel?: number
    /** 学习计划性级别 */
    learnPlanningLevelStr?: string
    /** 学习意志力 */
    learnWillpower?: number
    /** 学习意志力级别 */
    learnWillpowerLevel?: number
    /** 学习意志力级别 */
    learnWillpowerLevelStr?: string
    /** 学习意义感 */
    learnSense?: number
    /** 学习意义感级别 */
    learnSenseLevel?: number
    /** 学习意义感级别 */
    learnSenseLevelStr?: string
    /** 学习专注度 */
    learnConcentration?: number
    /** 学习专注度级别 */
    learnConcentrationLevel?: number
    /** 学习专注度级别 */
    learnConcentrationLevelStr?: string
    /** 考试准备度 */
    examReadiness?: number
    /** 考试准备度级别 */
    examReadinessLevel?: number
    /** 考试准备度级别 */
    examReadinessLevelStr?: string
    /** 学习成绩观 */
    learnAchievementView?: number
    /** 学习成绩观级别 */
    learnAchievementViewLevel?: number
    /** 学习成绩观级别 */
    learnAchievementViewLevelStr?: string
    /** 学习探索性 */
    learnExploratory?: number
    /** 学习探索性级别 */
    learnExploratoryLevel?: number
    /** 学习探索性级别 */
    learnExploratoryLevelStr?: string
    /** 学习好感度 */
    learnFavorability?: number
    /** 学习好感度级别 */
    learnFavorabilityLevel?: number
    /** 学习好感度级别 */
    learnFavorabilityLevelStr?: string
    /** 学习准备度指数 */
    learnReadiness?: number
    /** 学习准备度级别名称 */
    learnReadinessLevelName?: string
}
export interface Data6 {
    /** 总页数 */
    totalPages?: number
    /** 当前页码 */
    pageIndex?: number
    /** 每页条数 */
    pageSize?: number
    /** 总记录数 */
    totalRecords?: number
    /** 分页结果集 ,T */
    data?: Data7
    /** 注释 */
    havePrePage?: boolean
    /** 注释 */
    haveNextPage?: boolean
}


// 查询地市（区级）性格特征学生名单
export interface TAreaCityPersonalitySituationParams{
    /** 区县code */
    areaCode: string
    /** type=1区县（areaCode必传）；type=2（cityCode必传）市级 */
    type: string
    /** cityCode城市编码 */
    cityCode: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 当前页码 */
    pageIndex: string
    /** 每页数量 */
    pageSize: string
    /** 从第几个开始 */
    start: string
}

export interface Data5 {
    /** 学校名称或者市名称或者区名称 */
    schoolName?: string
    /** 典型忧郁质 */
    typicalMelancholicPercentage?: string
    /** 粘液忧郁质 */
    mucusDepressionPercentage?: string
    /** 典型粘液质 */
    typicalMucusQualityPercentage?: string
    /** 胆汁抑郁质 */
    biliaryDepressionPercentage?: string
    /** 多项混合质 */
    multipleMixedQualitiesPercentage?: string
    /** 多血粘液质 */
    bloodyMucusPercentage?: string
    /** 典型胆汁质 */
    typicalBileQualityPercentage?: string
    /** 多血胆汁质 */
    bloodyBilePercentage?: string
    /** 典型多血质 */
    typicalBloodyPercentage?: string
}
export interface Data4{
    /** 总页数 */
    totalPages?: number
    /** 当前页码 */
    pageIndex?: number
    /** 每页条数 */
    pageSize?: number
    /** 总记录数 */
    totalRecords?: number
    /** 分页结果集 ,T */
    data?: Data5
    /** 注释 */
    havePrePage?: boolean
    /** 注释 */
    haveNextPage?: boolean
}
export interface TAreaCityPersonalitySituationResponse{
    /** 注释 */
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** PageQueryResult */
    data?: Data4
}

// 家庭教养
export interface TAreaCityFamilySituationParams{
    /** 区县code */
    areaCode: string
    /** type=1区县（areaCode必传）；type=2（cityCode必传）市级 */
    type: string
    /** cityCode城市编码 */
    cityCode: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 当前页码 */
    pageIndex: string
    /** 每页数量 */
    pageSize: string
    /** 从第几个开始 */
    start: string
}


export interface Data9 {
    /** 区名称市级报告才有 */
    areaName?: string
    /** 学校名称市级第一个是市名称区县第一个是区县名称 */
    schoolName?: string
    /** 独生子女比例 */
    onlyOneChildPercentage?: string
    /** 生活环境,,（与父母生活） */
    liveWithParentsPercentage?: string
    /** 生活环境,,（与父母以外的人生活） */
    liveWithExceptParentsPercentage?: string
    /** 生活环境,,（与父母一方生活） */
    liveWithOneParentsPercentage?: string
    /** 父亲本科及以上学历 */
    fatherEducationUndergraduatePercentage?: string
    /** 母亲本科及以上学历 */
    motherEducationUndergraduatePercentage?: string
}


export interface Request {
    /** 区县code */
    areaCode: string
    /** type=1区县（areaCode必传）；type=2（cityCode必传）市级 */
    type: string
    /** cityCode城市编码 */
    cityCode: string
    /** 毕业年份必填 */
    graduationYear: string
    /** 当前页码 */
    pageIndex: string
    /** 每页数量 */
    pageSize: string
    /** 从第几个开始 */
    start: string
  }

  export interface areaCityFamilySituationProps {
    /** 区名称市级报告才有 */
    areaName ?: string
    /** 学校名称市级第一个是市名称区县第一个是区县名称 */
    schoolName ?: string
    /** 独生子女比例 */
    onlyOneChildPercentage ?: string
    /** 生活环境,,（与父母生活） */
    liveWithParentsPercentage ?: string
    /** 生活环境,,（与父母以外的人生活） */
    liveWithExceptParentsPercentage ?: string
    /** 生活环境,,（与父母一方生活） */
    liveWithOneParentsPercentage ?: string
    /** 父亲本科及以上学历 */
    fatherEducationUndergraduatePercentage ?: string
    /** 母亲本科及以上学历 */
    motherEducationUndergraduatePercentage ?: string
  }

export interface Data {
    /** 年级名称 */
    gradeName?: string
    /** 地区浙江省杭州市 */
    areaName?: string
    /** 区县：拱墅区 */
    districtName?: string
    /** 报告编号 */
    reportCode?: string
    /** 报告日期 */
    reportTime?: string
    /** 开始时间 */
    startTime?: string
    /** 结束时间 */
    endTime?: string
    /** 覆盖学校数 */
    overSchoolCnt?: number
    /** 完成人数 */
    evaluatedPeople?: number
    /** 总人数 */
    totalPeople?: number
    /** 清单中的数据为截止2021-08-31的数据 */
    deadlineTime?: string
    /** 参与测评学校清单 ,EvaluationSchoolVO */
    evaluationSchoolVOS?: EvaluationSchoolVOS
    /** 主要发现1 ,MainFindingVO */
    mainFindings?: MainFindings
    /** 心里健康状态分析2 ,StatusAnalysisBean */
    statusAnalysis?: StatusAnalysis
    /** 学习准备度分析3 ,LearningStatusBean */
    learningStatus?: LearningStatus
    /** 学生性格类型分布图4复用班级对象 ,PersonalityAnalysisBean */
    personalityAnalysis?: PersonalityAnalysis
    /** 家庭环境分析5复用班级对象 ,FamilyUpbringingAnalysisBean */
    familyUpbringingAnalysis?: FamilyUpbringingAnalysis
}
export interface TAreaCityFamilySituationResponse {
    /** 注释 */
    success ?: boolean
    /** 注释 */
    code ?: string
    /** 注释 */
    msg ?: string
    /** PageQueryResult */
    data ?: areaCityFamilySituationProps
}
export interface TCountyReportResponse {
    /** 注释 */
    success?: boolean
    /** 注释 */
    code?: string
    /** 注释 */
    msg?: string
    /** AreaMentalAdmissionReportResp */
    data?: Data
}