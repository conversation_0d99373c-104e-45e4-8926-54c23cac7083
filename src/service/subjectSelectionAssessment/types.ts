export type userReportParams = {
  evaluationRecordId?: string;
  evaluationTaskId?: string;
  userId: string;
  ff?: string;
  isPrint?: string;
};

export type classReportParams = {
  evaluationTaskId?: string;
  gradeId?: string;
  classId?: string;
  combinationCode?: string;
  pageIndex?: number;
  pageSize?: number;
  userName?: string;
  ff?: string;
  isPrint?: string;
};

export type gradeReportParams = {
  evaluationTaskId: string;
  gradeId: string;
  scoolId: string;
  ff?: string;
  isPrint?: string;
};