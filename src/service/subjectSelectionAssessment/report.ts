import serviceRequest, { ContextProps } from '@/service/serviceRequest';
import clientRequest from '@/service/clientRequest';
import type { IResponse } from '@/service/request';
import {
  userReportParams,
  classReportParams,
  gradeReportParams,
} from './types';
import { userPeportProps } from '@/pages/subjectSelectionAssessment/personalReport/types';
import { classReportProps } from '@/pages/subjectSelectionAssessment/classReport/types';
import { gradeReportProps } from '@/pages/subjectSelectionAssessment/gradeReport/types';
import { web } from '@/utils/hosts';
interface IReportApi {
  getUserReport: (
    data: userReportParams,
    ctx: ContextProps
  ) => IResponse<userPeportProps>;
  getClassReport: (
    data: classReportParams,
    ctx: ContextProps
  ) => IResponse<classReportProps>;
  getPageClassHolland: (data: classReportParams) => IResponse<classReportProps>;
  getClassSubjectCombination: (
    data: classReportParams
  ) => IResponse<classReportProps>;
  getListClassSubjectCombination: (
    data: classReportParams,
    ctx: ContextProps
  ) => IResponse<classReportProps>;
  getPageClassPotentialAnalysis: (
    data: classReportParams
  ) => IResponse<classReportProps>;
  getGradeReport: (
    data: gradeReportParams,
    ctx: ContextProps
  ) => IResponse<gradeReportProps>;
}

const API: IReportApi = {
  // 个人报告
  getUserReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `/api/psychology/evaluationReport/getUserReport`,
        data,
      },
      ctx
    ),
  // 班级报告
  getClassReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `/api/psychology/evaluationReport/getClassReport`,
        data,
      },
      ctx
    ),
  // 班级报告 - 霍兰德
  getPageClassHolland: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationReport/pageClassHolland`,
      data,
    }),
  // 班级报告 - 科目组合
  getClassSubjectCombination: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationReport/pageClassSubjectCombination`,
      data,
    }),
  // 班级报告 - 科目组合
  getListClassSubjectCombination: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `/api/psychology/evaluationReport/listClassSubjectCombination`,
        data,
      },
      ctx
    ),
  // 班级报告 - 科目组合
  getPageClassPotentialAnalysis: (data) =>
    clientRequest({
      method: 'post',
      url: `${web}/api/psychology/evaluationReport/pageClassPotentialAnalysis`,
      data,
    }),
  // 年级报告
  getGradeReport: (data, ctx) =>
    serviceRequest(
      {
        method: 'post',
        url: `/api/psychology/evaluationReport/getGradeReport`,
        data,
      },
      ctx
    ),
};

export default API;
