import { GetServerSidePropsContext, PreviewData} from 'next';
import { ParsedUrlQuery } from 'querystring';
import md5 from 'md5';
import moment from 'moment';
import Cookie from 'cookie';
import { web, gateway } from '@/utils/hosts';
// import nacosInstance from '@/utils/initNac';
import { getUrlParams, reLaunchToLogin, objectToUrlParams } from '@/utils/tools';
import request, { TConfig } from './request';
import logger from '@/utils/logger';

export type ContextProps<
  Q extends ParsedUrlQuery = ParsedUrlQuery,
  D extends PreviewData = PreviewData,
> = GetServerSidePropsContext<Q, D>

// 初始化 nacos 配置信息
const getConfigInfo: () => Record<string, any> = () => {
  const baseConfig = {
    'dev': {
      host: 'gateway-internal.dev.ewt360.com',
    },
    'atest': {
      host: 'gateway-internal.test.ewt360.com',
    },
    'pre': {
      host: 'staging-gateway-internal.ewt360.com',
    },
    'prod': {
      host: 'gateway-internal.ewt360.com',
    }
  }
  return baseConfig[process.env.DEPLOYMENT_ENV as keyof typeof baseConfig];
}

/**
 * 中间层用与请求后端接口的请求
 **/
export const serviceRequest = async <T>(
  reqConfig: TConfig & {
    needLogin?: boolean;
  },
  context: ContextProps
) => {
  const { req, res, query } = context;
  const { needLogin = true, url ='', headers } = reqConfig;
  const cookie = req.headers.cookie || '';
  const { ewt_user, user, token } = Cookie.parse(cookie);
  const authToken = (req.url && getUrlParams(req.url, 'token')) || token || getUrlParams(ewt_user || user, 'tk') || '';
  const host = process.env.NODE_ENV === 'development' ? gateway.substring(gateway.lastIndexOf('/') + 1) : getConfigInfo().host;
  const now = new Date().getTime();
  const defaultHeader: any = {
    ...req.headers,
    host, // 必须项,否则请求失败
    env: process.env.DEPLOYMENT_ENV || 'prod',
    timestamp: now,
    sign: md5(now + 'bdc739ff2dcf')
      .toString()
      .toLocaleUpperCase(),
    token: authToken,
    ...headers,
  };

  reqConfig.headers = {
    ...defaultHeader,
  };
  const internalServiceBaseUrl = `https://${getConfigInfo().host}`;
  //如果 url 不是以 '/' 开头，则假定它已经是完整的URL
  if (url.startsWith('/')) {
    reqConfig.url = `${internalServiceBaseUrl}${url}`;
  } else {
    reqConfig.url = url;
  }
  // 对于特殊场景，所有API都通过query加上场景标识
  const { sourceType, ff, isPrint } = query;
  const printMode = !!(ff || isPrint);
  if (sourceType) {
    reqConfig.params = {
      ...reqConfig.params,
      sourceType,
    };
  }

  const errorLogger = async (errorType: string, error: any, config?: any) => {
    // 这里做一些其他错误的处理
    if (logger && errorType && error) {
      const currentReq ={
        errorType,
        method: config.method,
        apiUrl: config.url,
        params: config.params,
        data: config.data,
        token: config.headers?.token,
        error,
        time: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        url: `${req.headers?.host}/psychology-service${req.url}`
      }

      if (errorType.includes('error')) {
        logger.error(`${errorType}：${JSON.stringify(currentReq)}`)
      } else {
        logger.verbose(`${errorType}：${JSON.stringify(currentReq)}`)
      }
    }
  };

  return request<T>({
    ...reqConfig,
  })
    .then((result) => {
      if (result.code) {
        if ([117001, 2001106, 2001109].includes(Number(result.code))) {
          errorLogger('api-info', result, reqConfig)
          if (needLogin) {
            const backUrl = process.env.NODE_ENV === 'development' ? `http://${req.headers.host}${req.url}` : `https:${web + '/psychology-service' + req.url}`;
            res.setHeader('Location', reLaunchToLogin(backUrl));
            res.statusCode = 302;
            return result;
          }
        }
        // 无权限
        if ([7771020].includes(Number(result.code))) {
          errorLogger('api-warn', result, reqConfig);
          res.setHeader('Location',  `${web}/psychology-service/common/permission?${objectToUrlParams({
            code: 7771020,
            description: encodeURIComponent(result.msg || '您暂无查看该报告的权限，请联系学校管理员'),
            printMode
          })}`);
          res.statusCode = 302;
          return result;
        }
        if (Number(result.code) !== 200) throw result;
      }
      return result;
    })
    .catch((err) => {
      // 这里做一些其他错误的处理
      if (logger && err) {
        errorLogger('service-error', err, reqConfig)
      }
      throw err;
    });
};
export default serviceRequest;
