import request from "./serviceRequest";

export type TBuildItem = {
  appName?: string;
  realName?: string;
  buildTime?: number;
  platform?: string;
  id?: number
};

export type TGetBuildListParams = Partial<Omit<TBuildItem, 'buildTime' | 'id'>>;

const getBuildList = async (params?: TGetBuildListParams, ctx?: any) => {
  return request<TBuildItem>({
    method: 'get',
    url: '/mistong-build/record',
    params,
  }, ctx);
};

const getGradeOverallAnalysis = async (params: any, ctx:any) => {
  return request<any>({
    method: 'post',
    url: '/api/psychology/special/screening/report/grade/overall/analysis/module',
    params,
  },ctx);
};


const api = {
  getBuildList,
  getGradeOverallAnalysis
}

export default api;