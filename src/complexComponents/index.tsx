import React from 'react';
import dynamic from 'next/dynamic';
import type { ITreeList, ITreeItem } from './index.d';
import type { ICardProps } from '@/commonComponents/Card';
import type { ICardTitleBarProps } from '@/commonComponents/CardTitleBar';
import type { IChartForBarProps } from '@/commonComponents/ChartForBar';
import type { IChartForPieProps } from '@/commonComponents/ChartForPie';
import type { IFourQuadrantCoordinatesProps } from '@/commonComponents/FourQuadrantCoordinates';
import type { IChartForRadarProps } from '@/commonComponents/ChartForRadar';
import type { ICoverForSpecialProps } from '@/commonComponents/CoverForSpecial';
import type { IPageNavigationProps } from '@/commonComponents/PageNavigation';
import type { ISmallIconTitleProps } from '@/commonComponents/SmallIconTitle';
import type { ISubjectCombinationProps } from '@/commonComponents/SubjectCombination';
import type { ITaskSubItemProps } from '@/commonComponents/TaskSubItem';
import type { ITHeaderControlProps } from '@/commonComponents/THeaderControl';
import type { ITopicBoxProps } from '@/commonComponents/TopicBox';
import type { IStatusBarProps } from '@/commonComponents/StatusBar';
import type { ISearchTableProps } from '@/commonComponents/SearchTable';
const ECard = dynamic<ICardProps>(() =>
  import('@/commonComponents/Card').then(({ Card }) => Card)
);

const StatusBar = dynamic<IStatusBarProps>(() =>
  import('@/commonComponents/StatusBar').then(({ StatusBar }) => StatusBar)
);

const CardTitleBar = dynamic<ICardTitleBarProps>(() =>
  import('@/commonComponents/CardTitleBar').then(
    ({ CardTitleBar }) => CardTitleBar
  )
);
const ChartForBar =
  typeof window === 'undefined'
    ? () => <></>
    : dynamic<IChartForBarProps>(
        () =>
          import('@/commonComponents/ChartForBar').then(
            ({ ChartForBar }) => ChartForBar
          ),
        { ssr: false }
      );
const ChartForPie =
  typeof window === 'undefined'
    ? () => <></>
    : dynamic<IChartForPieProps>(
        () =>
          import('@/commonComponents/ChartForPie').then(
            ({ ChartForPie }) => ChartForPie
          ),
        { ssr: false }
      );
const FourQuadrantCoordinates =
  typeof window === 'undefined'
    ? () => <></>
    : dynamic<IFourQuadrantCoordinatesProps>(
        () =>
          import('@/commonComponents/FourQuadrantCoordinates').then(
            ({ FourQuadrantCoordinates }) => FourQuadrantCoordinates
          ),
        { ssr: false }
      );
const ChartForRadar =
  typeof window === 'undefined'
    ? () => <></>
    : dynamic<IChartForRadarProps>(
        () =>
          import('@/commonComponents/ChartForRadar').then(
            ({ ChartForRadar }) => ChartForRadar
          ),
        { ssr: false }
      );
const CoverForSpecial = dynamic<ICoverForSpecialProps>(() =>
  import('@/commonComponents/CoverForSpecial').then(
    ({ CoverForSpecial }) => CoverForSpecial
  )
);
const PageNavigation = dynamic<IPageNavigationProps>(() =>
  import('@/commonComponents/PageNavigation').then(
    ({ PageNavigation }) => PageNavigation
  )
);
const SmallIconTitle = dynamic<ISmallIconTitleProps>(() =>
  import('@/commonComponents/SmallIconTitle').then(
    ({ SmallIconTitle }) => SmallIconTitle
  )
);
const SubjectCombination = dynamic<ISubjectCombinationProps>(() =>
  import('@/commonComponents/SubjectCombination').then(
    ({ SubjectCombination }) => SubjectCombination
  )
);
const TaskSubItem = dynamic<ITaskSubItemProps>(() =>
  import('@/commonComponents/TaskSubItem').then(
    ({ TaskSubItem }) => TaskSubItem
  )
);
const THeaderControl = dynamic<ITHeaderControlProps>(() =>
  import('@/commonComponents/THeaderControl').then(
    ({ THeaderControl }) => THeaderControl
  )
);
const TopicBox = dynamic<ITopicBoxProps>(() =>
  import('@/commonComponents/TopicBox').then(({ TopicBox }) => TopicBox)
);

const SearchTable = dynamic<ISearchTableProps>(() =>
  import('@/commonComponents/SearchTable').then(
    ({ SearchTable }) => SearchTable
  )
);

const getDataByKeyList = (data?: any, keyList?: string[]) => {
  if (!(data && keyList?.length)) {
    return data;
  }
  let pData = data;
  for (let index = 0; index < keyList.length; index++) {
    const key = keyList[index];
    pData = pData?.[key] || pData;
  }
  return pData;
};

const getDataJsonByKeyJson = (
  data?: any,
  keyJson?: Record<string, string[]>,
  keyList?: string[]
) => {
  const pData = getDataByKeyList(data, keyList);
  // if (keyList?.length) {
  //   console.log({ data, pData, keyList });
  // }
  if (!(data && keyJson)) return { pData, propsData: {} };
  let propsData: any;
  Object.keys(keyJson).forEach((key: string) => {
    propsData = {
      ...propsData,
      [key]: getDataByKeyList(pData, keyJson[key]),
    };
  });
  return { pData, propsData };
};

const ComplexComponents = <T extends Record<string, any>>({
  tree,
  data,
}: {
  tree?: ITreeList | ITreeItem;
  data: T;
}): JSX.Element => {
  if (Array.isArray(tree)) {
    return (
      <div>
        {tree?.map((item, key: number) => (
          <React.Fragment key={key}>
            <ComplexComponents<T>
              key={`${key}-${item?.type || 'self'}`}
              {...{ tree: item, data }}
            />
          </React.Fragment>
        ))}
      </div>
    );
  }
  const { type, keyJson, sortProps, isRealNode, sortList, keyList } = tree || {};

  const { pData, propsData } = getDataJsonByKeyJson(data, keyJson, keyList);
  const props = { ...tree?.props, ...sortProps?.(pData, propsData) };
  const list = [...(tree?.list || []), ...(sortList?.(pData, propsData) || [])];
  const complexComponentsNode = (
    <ComplexComponents {...{ tree: list, data: pData }} />
  );

  if (type) {
    switch (type) {
      case 'page':
        return complexComponentsNode;
      case 'Card':
        return (
          // @ts-ignore
          <ECard {...{ ...props, ...propsData }}>{complexComponentsNode}</ECard>
        );
      case 'CardTitleBar':
        return (
          // @ts-ignore
          <CardTitleBar title={undefined} {...{ ...props, ...propsData }}>
            {complexComponentsNode}
          </CardTitleBar>
        );
      case 'ChartForBar':
        return (
          // @ts-ignore
          <ChartForBar
            options={undefined}
            id={''}
            height={''}
            {...{ ...props, ...propsData }}
          />
        );
      case 'ChartForPie':
        return (
          // @ts-ignore
          <ChartForPie
            options={undefined}
            id={''}
            height={''}
            {...{ ...props, ...propsData }}
          />
        );
      case 'ChartForRadar':
        return (
          // @ts-ignore
          <ChartForRadar
            options={undefined}
            id={''}
            height={''}
            {...{ ...props, ...propsData }}
          />
        );
      case 'CoverForSpecial':
        return (
          // @ts-ignore
          <CoverForSpecial bgImg={''} {...{ ...props, ...propsData }}>
            {complexComponentsNode}
          </CoverForSpecial>
        );
      case 'FourQuadrantCoordinates':
        return (
          // @ts-ignore
          <FourQuadrantCoordinates
            options={undefined}
            id={''}
            height={''}
            {...{ ...props, ...propsData }}
          />
        );
      case 'PageNavigation':
        return (
          // @ts-ignore
          <PageNavigation {...{ ...props, ...propsData }}/>
        );
      case 'TopicBox':
        // @ts-ignore
        return <TopicBox {...{ ...props, ...propsData }}/>
      case 'SmallIconTitle':
        return (
          // @ts-ignore
          <SmallIconTitle
            title={undefined}
            iconType={'circle | half'}
            alpha={''}
            backgroundColor={''}
            background={''}
            {...{ ...props, ...propsData }}
          >
            {complexComponentsNode}
          </SmallIconTitle>
        );
      case 'SubjectCombination':
        return (
          // @ts-ignore
          <SubjectCombination
            title={undefined}
            content={undefined}
            {...{ ...props, ...propsData }}
          />
        );
      case 'TaskSubItem':
        return (
          // @ts-ignore
          <TaskSubItem {...{ ...props, ...propsData }}>
            {complexComponentsNode}
          </TaskSubItem>
        );
      case 'THeaderControl':
        return (
          // @ts-ignore
          <THeaderControl title={undefined} {...{ ...props, ...propsData }}>
            {complexComponentsNode}
          </THeaderControl>
        );
      case 'StatusBar':
        return (
          // @ts-ignore
          <StatusBar {...{ ...props, ...propsData }}>
            {complexComponentsNode}
          </StatusBar>
        );
      case 'SearchTable':
        // @ts-ignore
        return <SearchTable {...{ ...props }}/>
      default:
        break;
    }
  }
  if (isRealNode) {
    return (
      <div {...{ ...props }}>
        {props?.content}
        {props?.render?.(pData, propsData)}
        {!!list?.length && complexComponentsNode}
      </div>
    );
  }

  return (
    <>
      {props?.content}
      {props?.render?.(pData, propsData)}
      {!!list?.length && complexComponentsNode}
    </>
  );
};

export default ComplexComponents;
