import React, { useState } from "react";
import styles from "./index.module.less";
import { Button, message } from "antd";
import { getToken, pollTaskStatus } from "~/utils/tools";
import API from '~/service/common';
import SafeLogger from '~/utils/safe-logger';

export interface IPageNavigationProps {
  wrapperStyle?: React.CSSProperties; // 容器样式
  wrapperClassName?: string,
  timeout?: number; // 下载报告等待时间
  label?: string; // 按钮名称
}

const DownloadPDF: React.FC<IPageNavigationProps> = ({
  wrapperStyle,
  wrapperClassName,
  timeout,
  label,
}) => {
  const [downloading, setDownloading] = useState(false);
  const [filePath, setFilePath] = useState<string>('');

  // 轮询报告生成状态
  const goPollTaskStatus = async (hashId: string) => {
    try {
      const { status, filePath }: any = await pollTaskStatus(API.queryPDFStatus, { ids: [hashId] }, 180000, 5000);
      if (status === 1 && filePath) {
        setFilePath(filePath);
        setDownloading(false);
        message.success("生成成功，请点击右侧悬浮菜单“保存文件”进行下载~", 3);
      } else {
        throw new Error('轮询报告生成状态失败');
      }
    } catch (error) {
      setDownloading(false);
      message.error('生成报告失败，请稍后重试～');
      SafeLogger?.baseLogger?.error('async-pdf-download-error', {
        reason: '轮询报告生成状态失败',
        error
      });
    }
  }

  // 异步下载PDF
  const goAsyncPDFByPage = async(token: string) => {
    try {
      message.info("报告正在生成中，预计需要1~3分钟内完成，请稍等~", 3);
      const { data: { hashId } } = await API.asyncPDFByPage({
        type: 'pdf',
        modal: 'active',
        waitUntil: 'networkidle0',
        name: '测评报告',
        width: 1100,
        timeout: timeout || (1000 * 60 * 10),
        url: `${location.href}&isPrint=true&token=${token}`,
        activeTriggerFuncName: 'PSTScreenshotHook'
      })
      if (hashId) {
        goPollTaskStatus(hashId)
      } else {
        throw new Error('生成报告异常')
      }
    } catch (error) {
      message.error('生成报告异常');
      SafeLogger?.baseLogger?.error('async-pdf-download-error', {
        reason: '生成报告异常',
        error
      });
      setDownloading(false);
    }
  }

  const handleDownLoad = async () => {
    const token = getToken() || '';
    if (!token) {
      message.error("登录信息失效，请重新登录后重试！");
      return
    }

    if (downloading) {
      message.info("报告正在生成中，无需重复点击");
      return
    }
    setDownloading(true);
    API.getUCBaseInfo().then(() => {
      goAsyncPDFByPage(token)
    }).catch(error => {
      console.warn('校验权限失败', error)
      message.info("校验权限失败，请重新登录后重试");
      setDownloading(false);
    })
  }

  return (
    <div
      {...{
        className: `${styles.downloadPDF} ${wrapperClassName || ''}`,
        style: { ...wrapperStyle }
      }}>
      {filePath ? <Button
        onClick={() => window.open(filePath)}
        className={styles.downLoadFileButton}
        type="primary"
        size="large"
        block
      >
        保存文件
      </Button> : (downloading ? <Button
        className={styles.downLoadButton}
        type="primary"
        size="large"
        block
        loading={downloading}
      >
        生成中
      </Button> : <Button
        onClick={() => handleDownLoad()}
        className={styles.downLoadButton}
        type="primary"
        size="large"
        block
        loading={downloading}
      >
        {label || '下载'}
      </Button>)
      }
    </div>
  )
}

export default DownloadPDF;

