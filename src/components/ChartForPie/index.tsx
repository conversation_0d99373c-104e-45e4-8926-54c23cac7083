import React,{ useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import merge from 'lodash.merge';
import styles from './index.module.less';
type EChartsOption = echarts.EChartsOption;

interface IPieProps {
  wrapperStyle?: React.CSSProperties;
  id?: string;
  title?: string;
  list?: any[];
  height?: number;
  showIndicatorValue?: boolean;
  areaColor?: string;
  customOption?: any;
  warpClassName?: string;
  childrenClassName?: string;
}

const useEChart = (
  chartRef: React.MutableRefObject<null>,
  options: EChartsOption
) => {
  let myChart: echarts.ECharts | null = null;
  const renderChart = () => {
    if (chartRef.current) {
      const chart = echarts.getInstanceByDom(chartRef.current!);
      if (chart) {
        myChart = chart;
      } else {
        myChart = echarts.init(chartRef.current!);
      }
      myChart.setOption(options);
    }
  };

  useEffect(() => {
    renderChart();
    // return () => {
    //   myChart && myChart.dispose();
    // };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);
};

const ChartForPie: React.FC<IPieProps> = ({
  wrapperStyle,
  id,
  title,
  list,
  showIndicatorValue,
  areaColor,
  height,
  customOption,
  warpClassName,
  childrenClassName,
  ...rest
}) => {
  const aresDataList: any[] = [];
  if (list && list.length > 0) {
    list.map((item: any) =>
      aresDataList.push(item.value)
    );
  }
  const chartRef = useRef(null);
  const options = customOption || {
    color: ["#FF8D00", "#FF9B21", "#FFAC47", "#FFC073", "#FFD199", "#FFE8CC"],
      tooltip: {
        trigger: "item",
        formatter: "{a} : {c}({d}%)",
      },
      series: [
        {
          name: "兴趣分布比例",
          type: "pie",
          radius: "65%",
          center: ["50%", "50%"],
          selectedMode: "single",
          data: list,
          label: {
            position: "outside",
            color: "#001139",
          },
          itemStyle: {},
        },
      ],
  };
  useEChart(chartRef, {
    ...merge(
      options
    ),
    ...rest,
  });

  return (
    <div
      {...{
        className: `${styles["chart-for-pie-box"]} ${warpClassName || ''}`,
        style: {
          ...wrapperStyle,
        },
      }}
    >
      <div
        {...{
          className: `${styles["chart-for-pie"]} ${childrenClassName || ''}`,
          style: {
            height
          },
          id,
          ref: chartRef,
        }}
      />
      {title && <div>{title}</div>}
    </div>
  );
};

export default ChartForPie;
