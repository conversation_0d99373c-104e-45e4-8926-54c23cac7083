import React from 'react';
import styles from './index.module.less';

interface IProps {
  title: string;
  name: string;
  wrapperstyle?: React.CSSProperties;
}

const ECombinationTag: React.FC<IProps> = (props) => {
  const { wrapperstyle, title, name } = props;
  return (
    <div className={`${styles.ECombinationTag}`} {...{ style: {...wrapperstyle}}}>
      <span className={styles.left}>{title}</span>
      <span className={styles.right}>{name}</span>
    </div>
  );
}

export default ECombinationTag;
