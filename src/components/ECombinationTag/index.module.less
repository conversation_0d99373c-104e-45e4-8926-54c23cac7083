.ECombinationTag {
  display: block;
  text-align: left;
  margin-bottom: 10px;
  line-height: 26px;
  font-weight: 700;
  font-size: 18px;
  border-radius: 8px;
  -webkit-box-shadow: 0px 10px 20px 0px rgb(66 122 255 / 25%);
  box-shadow: 0px 10px 20px 0px rgb(66 122 255 / 25%);
  .left {
    display: inline-block;
    width: auto;
    line-height: 56px;
    padding: 0 10px;
    background: #4b80ff;
    font-size: 16px;
    color: white;
    text-align: center;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  .right {
    padding: 0 20px;
    text-align: center;
    line-height: 56px;
    color: rgba(0, 17, 57, 0.85);
    font-size: 18px;
    font-weight: 700;
  }
  .blue {
    background: #4b80ff;
  }
  .green {
    background: #00c865;
  }
  .orange {
    color: #ff8d00;
  }
}