import React from 'react';
import styles from './index.module.less';

export interface IChartForPieProps {
  businessData?: any; // 业务数据
  wrapperstyle?: React.CSSProperties; // 容器样式
  title?: React.ReactNode; // 标题
  titleStyle?: React.CSSProperties; // 标题样式
  contentStyle?: React.CSSProperties; // 内容样式
  children?: React.ReactNode;
}

const ChartForPie: React.FC<IChartForPieProps> = (props) => {
  const { wrapperstyle, title, children } = props;
  return (
    <>
      {title && (
        <h1>{title}</h1>
      )}
      <div className={styles.ChartForPie}  style={{...wrapperstyle}}
      >
        {children}
      </div>
    </>
  );
};
export default ChartForPie;
