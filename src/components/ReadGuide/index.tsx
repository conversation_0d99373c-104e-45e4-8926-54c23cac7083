import React from 'react';
import styles from './index.module.less';

interface IProps {
  wrapperStyle?: object;
  children?: React.ReactNode;
}

const ReadGuide: React.FC<IProps> = (props) => {
  const { wrapperStyle, children } = props;
  return (
    <div className={`${styles.ReadGuide}`} {...{ style: { ...wrapperStyle } }}>
      <p>您好！</p>
      <p>非常感谢您使用升学e网通“高中生心理健康诊断测验”。《高中生心理健康诊断测验》以国际经典心理学量表——《症状自评量表》（SCL-90）为蓝本研发，从“感觉、情绪、思维、 意识、行为、生活习惯、人际关系、饮食睡眠”等角度，评估学生近期（通常是两周内）的心理健康状态。</p>
      <p>在阅读本报告前，请注意以下几点：</p>
      <p>1.参考时效</p>
      <p>2个月以内：可以参考2~6个月：谨慎参考6个月以上：不建议参考</p>
      <p>2. 风险程度说明</p>
      <p><b>高风险：</b>表示该生的因子得分高于绝大部分高中生，在对应因子上有极大的可能性存在异常风险，建议老师优先关注并及时沟通。</p>
      <p><b>较高风险：</b>表示该生的因子得分高于大部分高中生，在对应因子上有较大可能性存在异常风险，建议老师保持密切关注。</p>
      <p><b>轻微风险：</b>表示该生的因子得分高于一般高中生，在对应因子上有一定可能性存在异常风险，建议老师保持密切关注。</p>
      <p><b>无风险：</b>表示该生的因子得分与大部分高中生一致，不存在明显的异常风险。</p>
      <p>3. 保密原则</p>
      <p>本报告内容仅校方相关人员可查看。如需与学生沟通报告内容，请遵循“一对一沟通”的原则，尽可能保护学生隐私和自尊心。</p>
      <p>4. 客观原则</p>
      <p>学生的心理状态及性格特点并非不可改变。尤其是对于处于青春期的高中学生来说，心智和性格本就具有较大的可塑性。因此，请勿以测评结果作为对学生的定性评估结论，切忌给学生贴上标签。</p>
      <p>5. 误差说明</p>
      <p>学生在测评时的情绪状态、对测评的态度等主观因素，以及测评过程中的环境干扰等客观因素都会影响测评结果。因此在阅读本测评报告时，请参照学生在日常生活的实际表现来理解。</p>
      {children}
    </div>
  );
}

export default ReadGuide;
