import React from 'react';
import styles from './index.module.less';

export interface ICardProps {
  id?: string;
  businessData?: any; // 业务数据
  wrapperstyle?: React.CSSProperties; // 容器样式
  wrapperClass?: string | React.CSSProperties;
  title?: React.ReactNode; // 标题
  titleStyle?: React.CSSProperties; // 标题样式
  contentStyle?: React.CSSProperties; // 内容样式
  children?: React.ReactNode;
}

const ECard: React.FC<ICardProps> = ({ id, wrapperstyle, wrapperClass='', title, children }) => {
  return (
    <div id={id} {...{ className: `${wrapperClass}`}}>
      {title && (
        <h1>{title}</h1>
      )}
      <div className={styles.ECard}  style={{...wrapperstyle}}
      >
        {children}
      </div>
    </div>
  );
};

export default ECard;
