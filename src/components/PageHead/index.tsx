import React from 'react';
import Head from "next/head";

interface IProps {
  title?: string;
  keywords?: string;
  content?: string;
  pathname?: string;
}

const PageHead: React.FC<IProps> = ({ pathname, title ='升学e网通, 升学助考一网通', keywords, content } ) => {
  const viewportFit = pathname === '/answer' ? ',viewport-fit=cover' : ''
  return (
    <Head>
      <title>{title}</title>
      <link rel="shortcut icon" href="//web.ewt360.com/favicon.ico" type="image/x-icon"></link>
      <meta name="description" content={content || title } />
      <meta name="keywords" content={keywords || title } />
      <meta name="viewport" content={`width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no${viewportFit}`}/>
      <meta name="renderer" content="webkit" />
    </Head>
  );
}

export default PageHead;
