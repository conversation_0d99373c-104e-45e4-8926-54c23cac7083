import React, { useState } from "react";
import styles from "./index.module.less";
import { Timeline, Button, message } from "antd";
import { getToken, pollTaskStatus } from "~/utils/tools";
import API from '~/service/common';
import SafeLogger from '~/utils/safe-logger';
export interface IListItem {
  label: string;
  point: string;
}

export interface IPageNavigationProps {
  list?: IListItem[]; // 导航列表
  wrapperStyle?: React.CSSProperties; // 容器样式
  wrapperClass?: string;
  timeout?: number; // 下载报告等待时间
  detailReportMode?: boolean;
}

const SideNavigation: React.FC<IPageNavigationProps> = ({
  list,
  wrapperStyle,
  wrapperClass,
  timeout,
  detailReportMode
}) => {
  const [downloading, setDownloading] = useState(false);
  const downloadButtonText = typeof detailReportMode === 'undefined' ? '下载' : detailReportMode ? '下载详报' : '下载简报';
  const [filePath, setFilePath] = useState<string>('');

  // 点击跳转到指定锚点
  const handleLocator = (item: { point: any; }) => {
    document?.getElementById(`${item.point}`)?.scrollIntoView({ behavior: "smooth", block: "start" });
  };

  const goPollTaskStatus = async (hashId: string) => {
    try {
      const { status, filePath }: any = await pollTaskStatus(API.queryPDFStatus, { ids: [hashId] }, 180000, 5000);
      if (status === 1 && filePath) {
        setFilePath(filePath);
        setDownloading(false);
        message.success("生成成功，请点击右侧悬浮菜单“保存文件”进行下载~", 3);
      } else {
        throw new Error('轮询报告生成状态失败');
      }
    } catch (error) {
      setDownloading(false);
      message.error('生成报告失败，请稍后重试～');
      SafeLogger?.baseLogger?.error('async-pdf-download-error', {
        reason: '轮询报告生成状态失败',
        error
      });
    }
  }

  // 异步下载PDF
  const goAsyncPDFByPage = async(token: string) => {
    try {
      message.info("报告正在生成中，预计需要1~3分钟内完成，请稍等~", 3);
      const { data: { hashId } } = await API.asyncPDFByPage({
        type: 'pdf',
        modal: 'active',
        waitUntil: 'networkidle0',
        name: '测评报告',
        width: 1100,
        timeout: timeout || (1000 * 60 * 10),
        url: `${location.href}&isPrint=true&token=${token}`,
        activeTriggerFuncName: 'PSTScreenshotHook'
      })
      if (hashId) {
        goPollTaskStatus(hashId);
      } else {
        throw new Error('生成报告异常')
      }
    } catch (error) {
      message.error('生成报告异常');
      SafeLogger?.baseLogger?.error('async-pdf-download-error', {
        reason: '生成报告异常',
        error
      });
      setDownloading(false);
    }
  }

  const handleDownLoad = async () => {
    const token = getToken() || '';
    if (!token) {
      message.error("登录信息失效，请重新登录后重试！");
      return
    }

    if (downloading) {
      message.info("报告正在生成中，无需重复点击");
      return
    }
    setDownloading(true);
    API.getUCBaseInfo().then(() => {
      goAsyncPDFByPage(token)
    }).catch(error => {
      console.warn('校验权限失败', error)
      message.info("校验权限失败，请重新登录后重试");
      setDownloading(false);
    })
  }

  // 跳转到详报
  const jumpToDetailReport = () => {
    const url = window.location.href + '&isDetailReport=true';
    window.open(url);
  }

  return (
    <div
      {...{
        className: `${styles.sideNavigation} ${wrapperClass || ''}`,
        style: { ...wrapperStyle }
      }}>
      {list && list.length > 0 ?
        <Timeline>
          {list.map((item: any, index: number) =>
            <Timeline.Item key={index}>
              <a
                className={styles.navigationLabel}
                onClick={() => {
                  handleLocator(item);
                }}
              >
                {item.label}
              </a>
              {item.child
                ? item.child.map((childItem: any, key: number) =>
                  <a
                    key={key}
                    onClick={() => {
                      handleLocator(childItem);
                    }}
                    className={styles.navigationChildLabel}
                  >
                    {childItem.label}
                  </a>
                )
                : null}
            </Timeline.Item>
          )}
        </Timeline> : null
      }
      <div>
        {filePath ?
          <Button
            type="primary"
            size="large"
            block
            className={styles.downLoadFileButton}
            onClick={() => window.open(filePath)}
          >保存文件</Button>
          :
          (downloading ? <Button
            type="primary"
            size="large"
            block
            className={styles.downLoadButton}
            loading={downloading}
          >生成中</Button> :
            <Button
              onClick={() => handleDownLoad()}
              className={styles.downLoadButton}
              type="primary"
              size="large"
              block
              loading={downloading}
            >
              {downloadButtonText}
            </Button>)
        }

        {
          (typeof detailReportMode !== 'undefined' && !detailReportMode) ? (
            <span className={styles.detailReportTipText}>
              此为简报，若需要下载详细报告，请点击
              <span onClick={() => jumpToDetailReport()}>&nbsp;详细报告</span>
            </span>
          ) : null
        }
      </div>
    </div>
  )
}

export default SideNavigation;

