.sideNavigation {
  width: 170px;
  position: fixed;
  z-index: 1000;
  right: 16px;
  top: 200px;
  background: #fff;
  padding:40px 20px 20px;
  border-radius: 8px;
  -webkit-box-shadow: 0px 0px 20px 0px rgb(32 39 54 / 15%);
  box-shadow: 0px 0px 20px 0px rgb(32 39 54 / 15%);
  .navigationLabel,
  .navigationChildLabel {
    font-size: 14px;
    color: #001139;
    cursor: pointer;
    display: block;
    &:hover {
      color: rgba(0, 200, 101, 1);
    }
  }
  .navigationChildLabel {
    padding-top: 10px;
    font-size: 12px;
  }
  .downLoadButton {
    border-radius: 4px;
    background: #427aff;
  }
  .downLoadFileButton {
    background: #FFA940;
    border-color: #FFA940;
    border-radius: 4px;
    box-shadow: none;
  }
  .detailReportTipText {
    font-size: 14px;
    color: #FA8C16;
    margin-top: 10px;
    display: inline-block;
    & > span {
      text-decoration: underline;
      color: #4B80FF;
      cursor: pointer;
    }
  }
  :global {
    .ant-timeline-item {
      padding-bottom: 10px;
      &:hover {
        color: rgba(0, 17, 57, 1);
      }
      .ant-timeline-item-content {
        min-height: 0;
      }
    }
  }
}