import { StaticImageData } from 'next/image';
import React from 'react';
import styles from './index.module.less';

interface IProps {
  contentStyle?: React.CSSProperties;
  imgSrc: string | StaticImageData;
  width?: number;
  height: number;
  alt?: string;
  onClick?: () => void;
}

const CommonBanner: React.FC<IProps> = (props) => {
  const { contentStyle, imgSrc, width, height, onClick } = props;
  return (
    <div
      className={styles.eBanner}
      {...{
        style: {
          backgroundImage: `url(${imgSrc})`,
          width: width || 'auto',
          height: `${height}px`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'top',
          ...contentStyle
        }
      }}
      onClick={onClick}
    />
  );
};

export default CommonBanner;
