import React from 'react';
import styles from './index.module.less';

interface IProps {
  title: string;
  themeColor?: string;
  wrapperStyle?: React.CSSProperties;
  wrapperClass?: string | React.CSSProperties;
}

const EModuleSubtitle: React.FC<IProps> = ({ wrapperStyle, wrapperClass ='', title, themeColor }) => {
  return (
    <div
      {...{
        className: `${styles.EModuleSubtitle} ${wrapperClass}`,
        style: { ...wrapperStyle },
      }}
    >
      <i style={{ background: themeColor }}/>{title}
    </div>
  );
}

export default EModuleSubtitle;
