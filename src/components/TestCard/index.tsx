// import { Card } from "mst-client-common-lib";
import React from 'react';
export interface ICardProps {
  businessData?: any; // 业务数据
  wrapperStyle?: React.CSSProperties; // 容器样式
  title?: React.ReactNode; // 标题
  titleStyle?: React.CSSProperties; // 标题样式
  contentStyle?: React.CSSProperties; // 内容样式
  children?: React.ReactNode;
}

const Card: React.FC<ICardProps> = ({ wrapperStyle, children }) => {
  return (
    <div style={{...wrapperStyle}}
    >
      {children}
    </div>
  );
};


export default Card;