import React, { useState, useEffect, useCallback } from 'react';
import { Cascader, Select } from 'antd';
import { IAddressItem, IGradeItem, SingleValueType } from '~/@types';
import styles from './index.module.less';

const { Option } = Select;

interface IProps {
  bg: string;
  schoolCount?: number;
  addressList: IAddressItem[];
  currentAddress: number[];
  showDistrict?: boolean;
  gradeInfoList: IGradeItem[];
  currentGrade: string[];
  handleGradeChange: (v: SingleValueType) => void;
  handleChangeAddress: (v: number[]) => void;
}

// 铺开市、区相关信息
function exhchangeAddress(addressList: IAddressItem[], needDistrict = false) {
  const data: Record<string, IAddressItem[]> = {};
  addressList.forEach((province) => {
    const citys = province.children || [];
    if (citys.length) {
      if (needDistrict) {
        const cityArr: IAddressItem[] = [];
        citys.forEach((city) => {
          const districts = city.children || [];
          if (districts.length) {
            data[`${province.areaCode}_${city.areaCode}`] = districts;
            cityArr.push(city);
          }
        });
        if (cityArr.length) {
          data[province.areaCode] = cityArr;
        }
      } else {
        data[province.areaCode] = citys;
      }
    }
  });
  return data;
}

const EntryHeader: React.FC<IProps> = ({
  bg,
  schoolCount,
  addressList = [],
  currentAddress = [],
  showDistrict = false,
  gradeInfoList = [],
  currentGrade = [],
  handleGradeChange,
  handleChangeAddress,
}) => {
  const [addressObj, setAddressObj] = useState<Record<string, IAddressItem[]>>(
    {}
  );
  // 将地址数组转成比较方便处理的状态
  useEffect(() => {
    setAddressObj(exhchangeAddress(addressList, showDistrict));
  }, [addressList, showDistrict]);
  // 省份修改回调
  const handleProvinceChange = useCallback(
    (v: number) => {
      const value = [v];
      if (addressObj[v]) {
        value.push(addressObj[v][0].areaCode);
        if (showDistrict) {
          const k = `${value[0]}_${value[1]}`;
          if (addressObj[k]) {
            value.push(addressObj[k][0].areaCode);
            handleChangeAddress(value);
          }
        } else {
          handleChangeAddress(value);
        }
      }
    },
    [handleChangeAddress, addressObj, showDistrict]
  );
  // 市修改
  const handleCityChange = useCallback(
    (v: number) => {
      const value = [currentAddress[0], v];
      const k = `${value[0]}_${value[1]}`;
      if (showDistrict && addressObj[k]) {
        value.push(addressObj[k][0].areaCode);
      }
      handleChangeAddress(value);
    },
    [handleChangeAddress, addressObj, showDistrict, currentAddress]
  );
  // 区县修改
  const handleDistrictChange = useCallback(
    (v: number) => {
      handleChangeAddress([currentAddress[0], currentAddress[1], v]);
    },
    [handleChangeAddress, currentAddress]
  );
  return (
    <div
      className={styles['entry-header-container']}
      style={{ backgroundImage: `url(${bg})` }}
    >
      <div className={styles['main-container']}>
        <div className={styles['select-container']}>
          <div className={styles.line}>
            <div className={styles.title}>当前地址：</div>
            <Select
              className={styles.select}
              style={{ width: 132, marginRight: 8 }}
              value={currentAddress[0]}
              onChange={handleProvinceChange}
            >
              {addressList.map((it) => (
                <Option key={it.areaCode} value={it.areaCode}>
                  {it.areaName}
                </Option>
              ))}
            </Select>
            <Select
              className={styles.select}
              style={{ width: 132, marginRight: 8 }}
              value={currentAddress[1]}
              onChange={handleCityChange}
            >
              {(addressObj[currentAddress[0]] || []).map((it) => (
                <Option key={it.areaCode} value={it.areaCode}>
                  {it.areaName}
                </Option>
              ))}
            </Select>
            {showDistrict && (
              <Select
                className={styles.select}
                style={{ width: 132 }}
                value={currentAddress[2]}
                onChange={handleDistrictChange}
              >
                {(
                  addressObj[`${currentAddress[0]}_${currentAddress[1]}`] || []
                ).map((it) => (
                  <Option key={it.areaCode} value={it.areaCode}>
                    {it.areaName}
                  </Option>
                ))}
              </Select>
            )}
          </div>
          <div className={styles.line}>
            <div className={styles.title}>覆盖人群：</div>
            <Cascader
              className={styles.select}
              options={gradeInfoList}
              value={currentGrade}
              placeholder="请选择报告覆盖人群"
              onChange={(value) => handleGradeChange(value)}
              style={{ width: 340 }}
              allowClear={false}
            />
          </div>
        </div>
        <div className={styles['info-container']}>
          <div className={styles['info-bar']}></div>
          <div className={styles['info-word']}>
            已有<span>{schoolCount || '0'}</span>所学校生成有效集体报告
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntryHeader;
