
.entry-header-container {
  min-width: 1000px;
  width: 100%;
  height: 380px;
  padding-top: 184px;
  box-sizing: border-box;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .main-container {
    width: 1000px;
    height: 200px;
    margin: 0 auto;
  }

  .select-container {
    width: 514px;
    height: 106px;
    background-color: rgba(255,255,255,0.25);
    border-radius: 9px;
    padding: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .line {
      display: flex;
      align-items: center;
      height: 32px;
    }

    .title {
      width: 70px;
      height: 22px;
      line-height: 22px;
      font-size: 14px;
      color: #35405ad9;
    }

    .select {
      color: #35405A;

      :global {
        .ant-select-selector {
          border-color: #fff;
          border-radius: 4px;
        }
      }
    }
  }

  .info-container {
    width: 514px;
    height: 40px;
    background-color: #003963;
    border-radius: 4px;
    margin-top: 10px;
    display: flex;
    align-items: center;

    .info-bar {
      width: 6px;
      height: 16px;
      background-color: #FFFFFF;
      border-radius: 3.75px;
      margin-left: 8px;
    }

    .info-word {
      font-size: 16px;
      line-height: 22px;
      margin-left: 8px;
      font-weight: 600;
      color: #fff;

      span {
        color: #FFD798;
        margin: 0 8px;
      }
    }
  }

}
