import React from 'react';
import { Table } from 'antd';
import styles from './index.module.less';

interface IProps {
  wrapperStyle?: object;
  wrapperClass?: string;
  propsData: any;
}

const CustomTable: React.FC<IProps> = (props) => {
  const { wrapperStyle, wrapperClass, propsData } = props;
  return (
    <div
      className={`${styles.customTable} ${wrapperClass}`}
      {...{ style: { ...wrapperStyle } }}
    >
      <Table {...propsData} />
    </div>
  );
};

export default CustomTable;
