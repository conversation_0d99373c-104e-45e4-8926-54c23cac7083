import React, { useEffect } from "react";
import { eHeader } from "@/utils/tools";
import { www } from '@/utils/hosts'
import styles from "./index.module.less";
import '@ewt/career-components/dist/es/index.css';
import dynamic from "next/dynamic";

const CareerMenu = typeof window === 'undefined'
? () => <></>
: dynamic(
  () => import('@ewt/career-components/dist/es').then(
    ({ CareerMenu }) => CareerMenu
  ),
  { ssr: false }
)

interface IProps {
  wrapperStyle?: React.CSSProperties;
  title?: string;
}

const Header: React.FC<IProps> = ({ wrapperStyle }) => {

  useEffect(() => {
    eHeader();
  }, []);

  return (
    <div className={styles.header} style={{ ...wrapperStyle }}>
      <div className={styles.container}>
        <a href={`${www}/Apply/ApplyIndex`} className={styles.logo}>
          <img src="//cdn.ewt360.com/ewt360/images/images/careerplan_logo.jpg" />
        </a>
      </div>
      <CareerMenu />
    </div>
  );
};

export default Header;
