import React from 'react';
import styles from "./index.module.less";
interface IProps {
  src: string;
  height: number | string;
  children?: React.ReactNode;
}

const Banner: React.FC<IProps> = ({
  src,
  height,
  children
}) => {
  return (
    <div
      className={styles.banner}
      style={{
        background: `url(${src}) bottom center no-repeat`,
        backgroundSize: "100% 100%",
        height: `${height}px`,
      }}
    >
      {children}
    </div>
  );
};

export default Banner;
