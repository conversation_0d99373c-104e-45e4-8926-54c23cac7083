import React from "react";
import styles from "./index.module.less";

interface barStyleItem {
  backgroundImage: string;
  boxShadow: string;
}

interface listItem {
  code: string;
  name: string;
  level: number;
}

interface IProps {
  wrapperStyle?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  barStyle?: barStyleItem[],
  list: listItem[],
}

const defaultBarStyle = [
  {
    backgroundImage: "linear-gradient(270deg, #C7CAFF 0%, #5B96FE 100%)",
    boxShadow: "0 8px 16px 0 rgba(90,139,255,0.50)",
  },
  {
    backgroundImage: "linear-gradient(270deg, #C7CAFF 0%, #5B96FE 100%)",
    boxShadow: "0 8px 16px 0 rgba(90,139,255,0.50)",
  },
  {
    backgroundImage: "linear-gradient(270deg, #93F5FA 0%, #13D7B1 100%)",
    boxShadow: "0 8px 16px 0 rgba(32,212,192,0.40)",
  },
  {
    backgroundImage: "linear-gradient(270deg, #93F5FA 0%, #13D7B1 100%)",
    boxShadow: "0 8px 16px 0 rgba(32,212,192,0.40)",
  },
  {
    backgroundImage: "linear-gradient(90deg, #FF7979 0%, #FFC9B2 100%)",
    boxShadow: "0 8px 16px 0 rgba(239,89,89,0.40)",
  },
  {
    backgroundImage: "linear-gradient(90deg, #FF7979 0%, #FFC9B2 100%)",
    boxShadow: "0 8px 16px 0 rgba(239,89,89,0.40)",
  },
  {
    backgroundImage: "linear-gradient(270deg, #FFD78C 0%, #FFA71A 96%)",
    boxShadow: "0 8px 16px 0 rgba(255,157,0,0.40)",
  },
  {
    backgroundImage: "linear-gradient(270deg, #FFD78C 0%, #FFA71A 96%)",
    boxShadow: "0 8px 16px 0 rgba(255,157,0,0.40)",
  },
];
const conversionScale = [0, 25, 50, 75, 100];
const trackBarList = [
  "明显",
  "较明显",
  "一般",
  "轻微",
  "一般",
  "较明显",
  "明显",
];

const PositiveNegativeBarChart: React.FC<IProps> = ({
  wrapperStyle,
  contentStyle,
  barStyle = defaultBarStyle,
  list,
}) => {
  return (
    <div
      {...{
        className: `${styles["positive-negative-bar-chart-box"]}`,
        style: { ...wrapperStyle },
      }}
    >
      <div
        {...{
          className: `${styles["chart-box"]}`,
          style: { ...contentStyle },
        }}
      >
        <div className={styles["track-bar-chart"]}>
          {trackBarList.map((v, i) => (
            <div key={i} className={styles["step-item"]}>
              {v}
              <i className={styles["step-point"]} />
            </div>
          ))}
          <span className={styles["track-bar-underline"]} />
        </div>
        <ul>
          {list?.map((v: listItem, i: number) => (
            <li key={i}>
              <div className={styles.title}>
                {v.code}({v.name})
              </div>
              <div
                className={styles.progressBar}
                style={{
                  background: `${!v.level && "transparent"}`,
                }}
              >
                <div
                  className={styles.progress}
                  style={{
                    width: `${conversionScale[v.level]}%`,
                    ...barStyle[i],
                  }}
                />
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default PositiveNegativeBarChart;
