.professional-table {
  display: block;
  margin: 24px auto 0;
  table {
    border: 1px solid #E1E6F2 !important;
  }
  thead > tr > th {
    padding: 0 16px;
    height: 44px;
    background: #F9FAFC;
    border-right: 0 !important;
    color: #96A2BE;
  }
  tbody > tr {
    &:nth-child(even),
    tr:hover {
      background: #FBFCFE !important;
    }
    td {
      padding: 0 16px;
      height: 48px;
      max-width: 390px;
      border-right: 0 !important;
      font-size: 14px;
      color: #50596F;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    a {
      color: #5A8BFF;
      text-decoration: none;
    }
    span {
      a {
        color: #50596F;
      }
      a:hover {
        color: #5A8BFF;
      }
    }
  }
}
