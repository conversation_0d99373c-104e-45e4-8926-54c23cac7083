import React,{ useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import merge from 'lodash.merge';
import styles from './index.module.less';
type EChartsOption = echarts.EChartsOption;

interface IPieProps {
  wrapperStyle?: React.CSSProperties;
  id?: string;
  title?: string;
  list?: any[];
  height: number;
  fullDisplay: boolean;
  showIndicatorValue?: boolean;
  areaColor?: string;
  customOption?: any;
}

const useEChart = (
  chartRef: React.MutableRefObject<null>,
  options: EChartsOption
) => {
  let myChart: echarts.ECharts | null = null;
  const renderChart = () => {
    if (chartRef.current) {
      const chart = echarts.getInstanceByDom(chartRef.current!);
      if (chart) {
        myChart = chart;
      } else {
        myChart = echarts.init(chartRef.current!);
      }
      myChart.setOption(options);
    }
  };

  useEffect(() => {
    renderChart();
  }, [options]);
};

const ChartForRadar: React.FC<IPieProps> = ({
  wrapperStyle,
  id,
  title,
  list,
  showIndicatorValue,
  areaColor,
  height,
  customOption,
  fullDisplay,
  ...rest
}) => {
  const aresDataList: number[] = [];
  if (list && list.length > 0) {
    list.map((item: any) =>
      aresDataList.push(item.value)
    );
  }
  const chartRef = useRef(null);
  const options = customOption || {
    radar: {
      indicator: list,
      // startAngle: 120,
      name: {
        formatter: (value: number, indicator: any) => (fullDisplay ? value +"(" + ["{green|" + indicator.value + "}"] + "/" + indicator.max + ")" : value),
        textStyle: {
          rich: {
            green: {
              color: areaColor,
            },
          },
          color: "#333333",
          fontSize: 14
        },
      },
      splitNumber: 5,
      axisLine: {
        lineStyle: {
          color: "rgba(160,188,255, 1)",
        },
      },
      splitArea: {
        areaStyle: {
          color: ["rgba(160,188,255,0.5)", "#ffffff"], // 图表背景网格的颜色
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: "rgba(160,188,255,1)", // 图表背景网格线的颜色
        },
      },
      center: ["50%", "50%"],
      radius: 110,
    },
    series: {
      type: "radar",
      // symbol: "none",  // 图表中各个图区域的边框线拐点 图形
      itemStyle: {
        normal: {
          opacity: 1,
          color: areaColor, // 图表中各个图区域的边框线拐点颜色
          lineStyle: {
            color: "#7077FF", // 图表中各个图区域的边框线颜色
          },
        },
      },
      data: [
        {
          value: aresDataList,
          areaStyle: {
            opacity: 0.7,
            color: areaColor,
          },
        },
      ],
    },
  };
  useEChart(chartRef, {
    ...merge(
      options
    ),
    ...rest,
  });

  return (
    <div
      {...{
        className: `${styles["custom-radar-box"]}`,
        style: {
          ...wrapperStyle,
        },
      }}
    >
      <div
        {...{
          className: `${styles["custom-radar"]}`,
          style: {
            height,
          },
          id,
          ref: chartRef,
        }}
      />
      {title && <div>{title}</div>}
    </div>
  );
};

export default ChartForRadar;

