import React from 'react';
import styles from "./index.module.less";

interface tabsItem {
  key: string;
  name: string;
}

interface IProps {
  wrapperStyle?: React.CSSProperties;
  list: tabsItem[];
  activeIndex: number;
  onChange: (index: number) => void;
}

const CapsuleTabs: React.FC<IProps> = ({
  wrapperStyle,
  list,
  activeIndex,
  onChange
}) => {
  const handleOnChange = (index: number) => {
    onChange(index);
  };
  return (
    <div className={styles.capsuleTabs} style={{ ...wrapperStyle }}>
      {list.length &&
        list.map((v, i) => (
          <li
            key={i}
            className={`${i === activeIndex ? styles.active : ""}`}
            onClick={() => handleOnChange(i)}
          >
            {v.name}
          </li>
        ))}
    </div>
  );
};

export default CapsuleTabs;
