import React from "react";
import styles from "./index.module.less";

interface barStyleItem {
  backgroundImage: string;
  boxShadow: string;
}

interface barItem {
  code: string;
  name: string;
  score: string;
}

interface IProps {
  wrapperStyle?: React.CSSProperties;
  barStyle?: barStyleItem[];
  list: barItem[];
}

const defaultBarStyle = [
  {
    backgroundImage:
      "linear-gradient(270deg, #8AD7C8 0%, #1A9895 100%, #B5DBFF 100%)",
    boxShadow: "0 8px 16px 0 rgba(33,156,152,0.40)",
  },
  {
    backgroundImage: "linear-gradient(270deg, #FFD78C 0%, #FFA71A 96%)",
    boxShadow: "0 8px 16px 0 rgba(255,157,0,0.40)",
  },
];

const ChartBar: React.FC<IProps> = ({
  wrapperStyle,
  barStyle = defaultBarStyle,
  list
}) => {
  return (
    <div
      {...{
        className: `${styles["chart-bar-box"]}`,
        style: { ...wrapperStyle },
      }}
    >
      {list.length > 0 &&
        list.map((v, i) => (
          <div key={i} className={styles["bar-item"]}>
            <div className={styles.score}>{v.score}</div>
            <div className={styles.progressBar}>
              <div
                className={styles.progress}
                style={{
                  height: `${v.score && Number(v.score) > 100 ? 100 : v.score}%`,
                  ...barStyle[i],
                }}
              ></div>
            </div>
            <div className={styles.title}>{v.name}</div>
          </div>
        ))}
    </div>
  );
};

export default ChartBar;
