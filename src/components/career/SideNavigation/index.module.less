.side-navigation-box {
  position: fixed;
  top: 0;
  right:50%;
  z-index: 100;
  width: 210px;
  margin: 403px -730px 0 0;
  padding: 32px 20px 0;
  background: #fff;
  box-shadow: 0 4px 16px 0 rgba(80, 89, 111, 0.1);
  border-radius: 8px;
  .navigation-label,
  .navigation-child-label {
    display: block;
    font-size: 16px;
    color: #003963;
    cursor: pointer;
    padding-left:3px;
    .ant-timeline-item-tail {
      border-left: 2px solid #DFE8FF;
    }
    .dot,
    .activeDot {
      display: block;
      width: 16px;
      height: 16px;
      background: #DFE8FF;
      border-radius: 8px;
      padding: 4px 0 0 4px;
    }
    .activeDot::after {
      display: block;
      content: "";
      width: 8px;
      height: 8px;
      background: #5A8BFF;
      border-radius: 4px;
    }
    &:hover {
      color: #5A8BFF;
      .dot::after {
        display: block;
        content: "";
        width: 8px;
        height: 8px;
        background: #5A8BFF;
        border-radius: 4px;
      }
    }
  }
  .ant-timeline-item-last {
    padding: 0;
  }
  .navigation-child-label {
    padding-top: 10px;
    font-size: 12px;
  }
}
