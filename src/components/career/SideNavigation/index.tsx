import React, { useState, useEffect } from "react";
import { Timeline } from "antd";
import styles from "./index.module.less";

interface navItem {
  key: string;
  label: string;
}

interface IProps {
  list: navItem[]
  className?: string
  offset?: number;
  id?:string; 
  renderBottom?: ()=> JSX.Element;
}

const SideNavigation: React.FC<IProps> = ({ list, offset = 0, id, className, renderBottom }) => {
  const [navIndex, setNavIndex] = useState(0);

  // 点击跳转到指定锚点
  const handleLocator = (item: navItem, index: number) => {
    setNavIndex(index)
    document && document?.getElementById(`${item.key}`)?.scrollIntoView({ behavior: "smooth", block: "start" });
  };

  const onScroll = () => {
    const scrollTop = window.pageYOffset;
    const innerHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    const middleLine = scrollTop + innerHeight / 2;

    let nextActive = -1;

    // 检查是否滚动到页面底部（考虑一些像素的误差）
    const isAtBottom = scrollTop + innerHeight >= documentHeight - 10;

    if (isAtBottom) {
      // 如果在页面底部，激活最后一个导航项
      nextActive = list.length - 1;
    } else {
      // 遍历所有导航项，找到最合适的激活项
      for (let index = 0; index < list.length; index++) {
        const value = list[index];
        const keyElement = document && document.getElementById(`${value.key}`);

        if (keyElement) {
          const elementTop = keyElement.offsetTop + offset;

          // 如果元素顶部在中线之上，且下一个元素顶部在中线之下（或者是最后一个元素）
          if (elementTop <= middleLine) {
            const nextElement = index < list.length - 1 ?
              document.getElementById(`${list[index + 1].key}`) : null;

            if (!nextElement || (nextElement.offsetTop + offset) > middleLine) {
              nextActive = index;
            }
          }
        }
      }
    }

    // 确保 nextActive 在有效范围内
    if (nextActive < 0) nextActive = 0;
    if (nextActive >= list.length) nextActive = list.length - 1;

    // 只有当激活项发生变化时才更新
    if (nextActive !== navIndex) {
      setNavIndex(nextActive);
    }
  }
  useEffect(() => {
    window.onscroll = () => {
      onScroll()
    }
  }, [])

  return (
    <div id={id} className={`${styles['side-navigation-box']} ${className}`}>
      <Timeline>
        {list.length > 0 &&
          list.map((v, i) => (
            <Timeline.Item key={i} dot={<a className={`${navIndex === i ? styles.activeDot : styles.dot}`} />} className={styles['navigation-label']}>
              <a
                onClick={() => handleLocator(v, i)}
                className={styles['navigation-label']}
              >
                {v.label}
              </a>
            </Timeline.Item>
          ))}
      </Timeline>
      {renderBottom?.()}
    </div>
  );
};
export default SideNavigation;
