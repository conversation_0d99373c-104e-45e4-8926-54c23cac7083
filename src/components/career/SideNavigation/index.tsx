import React, { useState, useEffect } from "react";
import { Timeline } from "antd";
import styles from "./index.module.less";

interface navItem {
  key: string;
  label: string;
}

interface IProps {
  list: navItem[]
  className?: string
  offset?: number;
  id?:string; 
  renderBottom?: ()=> JSX.Element;
}

const SideNavigation: React.FC<IProps> = ({ list, offset = 0, id, className, renderBottom }) => {
  const [navIndex, setNavIndex] = useState(0);

  // 点击跳转到指定锚点
  const handleLocator = (item: navItem, index: number) => {
    setNavIndex(index)
    document && document?.getElementById(`${item.key}`)?.scrollIntoView({ behavior: "smooth", block: "start" });
  };

  const onScroll = () => {
    const scrollTop = window.pageYOffset;
    const innerHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    const middleLine = scrollTop + innerHeight / 2;

    let nextActive = 0; // 默认激活第一个

    // 检查是否滚动到页面底部（考虑一些像素的误差）
    const isAtBottom = scrollTop + innerHeight >= documentHeight - 10;

    if (isAtBottom) {
      // 如果在页面底部，激活最后一个导航项
      nextActive = list.length - 1;
    } else {
      // 从后往前遍历，找到第一个顶部在中线上方的元素
      for (let index = list.length - 1; index >= 0; index--) {
        const value = list[index];
        const keyElement = document && document.getElementById(`${value.key}`);

        if (keyElement) {
          const elementTop = keyElement.offsetTop + offset;

          // 如果元素顶部在中线上方或刚好在中线上，则激活这个元素
          if (elementTop <= middleLine) {
            nextActive = index;
            break; // 找到后立即退出循环
          }
        }
      }
    }

    // 只有当激活项发生变化时才更新
    if (nextActive !== navIndex) {
      setNavIndex(nextActive);
    }
  }
  useEffect(() => {
    window.onscroll = () => {
      onScroll()
    }
  }, [])

  return (
    <div id={id} className={`${styles['side-navigation-box']} ${className}`}>
      <Timeline>
        {list.length > 0 &&
          list.map((v, i) => (
            <Timeline.Item key={i} dot={<a className={`${navIndex === i ? styles.activeDot : styles.dot}`} />} className={styles['navigation-label']}>
              <a
                onClick={() => handleLocator(v, i)}
                className={styles['navigation-label']}
              >
                {v.label}
              </a>
            </Timeline.Item>
          ))}
      </Timeline>
      {renderBottom?.()}
    </div>
  );
};
export default SideNavigation;
