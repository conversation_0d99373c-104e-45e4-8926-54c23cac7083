import React, { useState, useEffect } from "react";
import { Timeline } from "antd";
import styles from "./index.module.less";

interface navItem {
  key: string;
  label: string;
}

interface IProps {
  list: navItem[]
  className?: string
  offset?: number;
  id?:string; 
  renderBottom?: ()=> JSX.Element;
}

const SideNavigation: React.FC<IProps> = ({ list, offset = 0, id, className, renderBottom }) => {
  const [navIndex, setNavIndex] = useState(0);
  const [isStore, setIsStore] = useState(false);

  // 点击跳转到指定锚点
  const handleLocator = (item: navItem, index: number) => {
    setNavIndex(index)
    document && document?.getElementById(`${item.key}`)?.scrollIntoView({ behavior: "smooth", block: "start" });
  };

  const onScroll = () => {
    const scrollTop = window.pageYOffset;
    const innerhHeight = window.innerHeight;
    const scope = [scrollTop, scrollTop + innerhHeight / 3];
    const middleLine = scrollTop + innerhHeight / 2;
    let nextActive = -1;
    list.forEach((value, index) => {
      const keyElement = document && document.getElementById(`${value.key}`);
      if (keyElement) {
        if (nextActive === -1 && (keyElement.offsetTop + offset) > scope[0] && (keyElement.offsetTop + offset) < scope[1]) {
          nextActive = index;
        }
        if (nextActive === -1 && middleLine < (keyElement.offsetTop + offset)) {
          nextActive = index - 1;
        }
      }
    })
    if (nextActive === -1 || (navIndex === nextActive && isStore)) return;
    setNavIndex(nextActive)
    setIsStore(true)
  }
  useEffect(() => {
    window.onscroll = () => {
      onScroll()
    }
  }, [])

  return (
    <div id={id} className={`${styles['side-navigation-box']} ${className}`}>
      <Timeline>
        {list.length > 0 &&
          list.map((v, i) => (
            <Timeline.Item key={i} dot={<a className={`${navIndex === i ? styles.activeDot : styles.dot}`} />} className={styles['navigation-label']}>
              <a
                onClick={() => handleLocator(v, i)}
                className={styles['navigation-label']}
              >
                {v.label}
              </a>
            </Timeline.Item>
          ))}
      </Timeline>
      {renderBottom?.()}
    </div>
  );
};
export default SideNavigation;
