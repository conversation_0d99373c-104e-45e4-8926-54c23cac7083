import React from 'react';
import styles from "./index.module.less";

interface IProps {
  id?: string;
  wrapperStyle?: React.CSSProperties;
  icon?: boolean | React.ReactNode;
  iconType?: string;
  iconColor?: string;
  specialTitle?: string;
  title: string;
}

const CardIconTitle: React.FC<IProps> = ({
  wrapperStyle,
  iconColor,
  icon,
  specialTitle,
  title,
}) => {
  return (
    <div
      {...{
        className: `${styles["card-icon-title"]}`,
        style: { ...wrapperStyle },
      }}
    >
      {icon && (
        <div
          {...{
            className: `${styles["card-icon-title-icon"]}`,
            style: { background: iconColor },
          }}
        >
          {icon || ""}
        </div>
      )}

      {specialTitle && (
        <div
          {...{
            className: `${styles["special-title"]}`,
            style: { background: iconColor },
          }}
        >
          {specialTitle}
        </div>
      )}

      {title}
    </div>
  );
};

export default CardIconTitle;
