import React from 'react';
import iconVideo from "~/assets/career/icon_video.png";
import { web } from '@/utils/hosts';
import styles from "./index.module.less";

interface courseData {
  courseId: string | number;
  lessonId: string | number;
}

interface IProps {
  wrapperStyle?: React.CSSProperties;
  data: courseData
}

const VideoBar = ({ wrapperStyle, data }: IProps) => {
  const handlePlayVideo = () => {
    const { courseId, lessonId } = data;
    window.open(`${web}/site-study/#/playVideo?courseId=${courseId}&LessonId=${lessonId}`, '_blank');
  };

  return (
    <div
      className={styles["video-bar"]}
      style={{background: `rgba(90, 139, 255, 0.6) url(${iconVideo}) 50% 10px no-repeat`, ...wrapperStyle }}
      onClick={() => handlePlayVideo()}
    >
      <span className={styles.title}>
        视频
        <br />
        解读
      </span>
    </div>
  );
};

export default VideoBar;
