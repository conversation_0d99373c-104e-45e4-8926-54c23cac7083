import React from 'react';
import styles from './index.module.less';

interface IProps {
  id?: string;
  title: string;
  decription?: string;
}

const ECompoundTitle: React.FC<IProps> = ({ id, title, decription }) => {
  return (
    <div id={id} className={`${styles.ECompoundTitle}`}>
      <div className={styles.title}>{title}</div>
      { decription &&
        <div className={styles.decription}>{decription}</div>
      }
    </div>
  );
}

export default ECompoundTitle;
