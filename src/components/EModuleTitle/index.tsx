import React from 'react';
import styles from './index.module.less';

interface IProps {
  id?: string;
  title: string;
  wrapperStyle?: React.CSSProperties;
  wrapperClass?: string | React.CSSProperties;
}

const EModuleTitle: React.FC<IProps> = ({ id, wrapperStyle, wrapperClass = '', title } ) => {
  return (
    <div
      id={id}
      {...{
        className: `${styles.EModuleTitle} ${wrapperClass}`,
        style: { ...wrapperStyle },
      }}
      >
      <div className={styles.title}>{title}</div>
    </div>
  );
}

export default EModuleTitle;
