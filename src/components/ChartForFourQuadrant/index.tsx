import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import merge from 'lodash.merge';
import styles from './index.module.less';
type EChartsOption = echarts.EChartsOption;

interface IProps {
  wrapperStyle?: React.CSSProperties;
  id?: string;
  title?: string;
  list: any[];
  width: number;
  height: number;
  showIndicatorValue?: boolean;
  areaColor?: string;
}

const useEChart = (
  chartRef: React.MutableRefObject<null>,
  options: EChartsOption
) => {
  let myChart: echarts.ECharts | null = null;
  const renderChart = () => {
    if (chartRef.current) {
      const chart = echarts.getInstanceByDom(chartRef.current!);
      if (chart) {
        myChart = chart;
      } else {
        myChart = echarts.init(chartRef.current!);
      }
      myChart.setOption(options);
    }
  };

  useEffect(() => {
    renderChart();
    // return () => {
    //   myChart && myChart.dispose();
    // };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);
};

const ChartForFourQuadrant: React.FC<IProps> = ({
  wrapperStyle,
  id,
  title,
  list,
  showIndicatorValue,
  areaColor,
  width,
  height,
  ...rest
}) => {
  const chartRef = useRef(null);
  const maxValue = 3
  const options = {
    grid: {
      top: 40,
      right: 70,
      bottom: 40,
      left: 70
    },
    xAxis: {
      show: true,
      name: '学科能力',
      type: 'value',
      scale: true,
      min: -maxValue,
      max: maxValue,
      axisLabel: {
        show: true,
        inside: true,
        margin: 145,
        interval: 0,
        formatter: (value: any, index: number) => {
          let name = '';
          if (!index) {
            name = '低'
          } else if (index === 6) {
            name = '高'
          }
          return name
        }
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLine: {
        symbol: ['none', 'arrow'],
        lineStyle: {
          color: '#110039'
        }
      }
    },
    yAxis: [{
      show: true,
      name: '学科兴趣',
      type: 'value',
      min: -maxValue,
      max: maxValue,
      axisLabel: {
        show: true,
        inside: true,
        margin: 185,
        interval: 0,
        formatter: (value: any, index: number) => {
          let name = '';
          if (!index) {
            name = '低'
          } else if (index === 6) {
            name = '高'
          }
          return name
        }
      },
      splitLine: {
        show: false
      },
      axisLine: {
        symbol: ['none', 'arrow'],
        lineStyle: {
          color: '#110039'
        }
      },
      axisTick: {
        show: false
      }
    }],
    series: [{
      type: 'scatter',
      data: list,
      symbolSize: 1, // 找点位
      label: {
        show: true,
        position: 'right',
        distance: -18,
        formatter: (params: { value: any[]; }) => {
          const name = params.value[2]
          let styleName = 'a';
          if (['地理', '政治', '历史'].indexOf(name) > -1) {
            styleName = 'b'
          }
          return ['{' + styleName + '|' + name + '}']
        },
        rich: {
          a: {
            color: '#ffffff',
            fontSize: 12,
            align: 'left',
            backgroundColor: 'rgba(75,128,255,1)',
            padding: [4, 6],
            borderRadius: 15
          },
          b: {
            color: '#ffffff',
            fontSize: 12,
            align: 'left',
            backgroundColor: 'rgba(255,141,0,1)',
            padding: [4, 6],
            borderRadius: 15
          }
        }
      },
    }]
  };

  useEChart(chartRef, {
    ...merge(
      options
    ),
    ...rest,
  });

  return (
    <div
      {...{
        className: `${styles["four-quadrant-box"]}`,
        style: {
          ...wrapperStyle,
        },
      }}
    >
      <div
        {...{
          className: `${styles["chart-for-four-quadrant"]}`,
          style: {
            width,
            height
          },
          id,
          ref: chartRef,
        }}
      />
      {title && <div>{title}</div>}
    </div>
  );
};

export default ChartForFourQuadrant;
