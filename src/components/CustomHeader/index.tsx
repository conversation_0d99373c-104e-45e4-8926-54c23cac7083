import React from 'react';
import styles from './index.modules.scss';

interface IProps {
  src: string;
  height: number;
}

const CustomHeader: React.FC<IProps> = (props) => {
  const { src, height} = props;
  return (
    <div
      className={styles.customHeader}
      style={{
        background: `url(${src}) no-repeat center`,
        height: `${height}px`
      }}
    >
    </div>
  );
}

export default CustomHeader;
