import React, { useState, useEffect } from 'react';
import { Tabs } from 'antd-mobile';
import styles from './index.module.less';
import { useSafeHeight } from '@/hooks';

const offsetTop = 40;

const TabNavBar = ({ list }: { list: any[] }) => {
  const [activeKey, setActiveKey] = useState(list[0].key);
  const [safeHeight = 0] = useSafeHeight({ position: 'top' });

  const isInViewport = (el: any, offset = 0) => {
    const rect = el.getBoundingClientRect();
    const viewHeight =
      window.innerHeight || document.documentElement.clientHeight;
    const show = ((viewHeight - rect.top) / viewHeight) > offset
    return show;
  };

  const onScroll = () => {
    const element = document.getElementById('tabBar');
    if (!element) return;
    const rect = element.getBoundingClientRect();
    /** 初始化导航栏高度 45 */
    const stickyThreshold = 45 + safeHeight;
    if (rect.top <= stickyThreshold) {
      element.classList.add(`${styles['fixed']}`);
    } else {
      element.classList.remove(`${styles['fixed']}`);
    }
    list.forEach((element, i) => {
      const dom = document.getElementById(element.key);
      const of = list.length -1 === i ? 0.1 : 0.75
      if (dom) {
        if (isInViewport(dom, of)) {
          setActiveKey(element.key);
        }
      }
    });
  };

  useEffect(() => {
    window.addEventListener('scroll', onScroll);
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, [list]);
  return (
    <div
      id="tabBar"
      style={{
        top: offsetTop + safeHeight,
      }}
      className={`${styles['tabNavBar-box']}`}
    >
      <Tabs
        activeKey={activeKey}
        activeLineMode="fixed"
        onChange={(key) => {
          const element = document.getElementById(key);
          const tabBar = document.getElementById('tabBar');
          const headerOffset = offsetTop + tabBar?.offsetHeight! + safeHeight; // 导航栏高度 + tab高度
          if (element) {
            const elementPosition =
              element.getBoundingClientRect().top + window.scrollY;
            const offsetPosition = elementPosition - headerOffset;
            window.scrollTo({
              top: offsetPosition,
              behavior: 'smooth',
            });
          }
        }}
        style={{
          '--fixed-active-line-width': '12px',
        }}
      >
        {list.map(
          (item: {
            label:
              | boolean
              | React.ReactChild
              | React.ReactFragment
              | React.ReactPortal
              | null
              | undefined;
            key: React.Key | null | undefined;
          }) => (
            <Tabs.Tab title={item.label} key={item.key} />
          )
        )}
      </Tabs>
    </div>
  );
};

export default TabNavBar;
