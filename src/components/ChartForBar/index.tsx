import React,{ useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import merge from 'lodash.merge';
import styles from './index.module.less';
type EChartsOption = echarts.EChartsOption;

interface IBarProps {
  wrapperStyle?: React.CSSProperties;
  id?: string;
  title?: string;
  list?: any[];
  height?: number;
  showIndicatorValue?: boolean;
  areaColor?: string;
  valueData?: any,
  keyData?: any,
  series?: any,
  customOption?: any;
}

const useEChart = (
  chartRef: React.MutableRefObject<null>,
  options: EChartsOption
) => {
  let myChart: echarts.ECharts | null = null;
  const renderChart = () => {
    if (chartRef.current) {
      const chart = echarts.getInstanceByDom(chartRef.current!);
      if (chart) {
        myChart = chart;
      } else {
        myChart = echarts.init(chartRef.current!);
      }
      myChart.setOption(options);
    }
  };

  useEffect(() => {
    renderChart();
    // return () => {
    //   myChart && myChart.dispose();
    // };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);
};

const ChartForBar: React.FC<IBarProps> = ({
  wrapperStyle,
  id,
  title,
  list,
  showIndicatorValue,
  areaColor,
  height,
  valueData,
  keyData,
  series,
  customOption,
  ...rest
}) => {
  const chartRef = useRef(null);
  const options = customOption || {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '0%',
      right: '4%',
      top: '30px',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      show: false,
    },
    yAxis: {
      type: 'category',
      data: [...keyData],
      axisLabel: {
        fontSize: 18,
      }
    },
    series: [
      {
        type: 'bar',
        data: [...valueData],
        label: {
          show: true, // 柱上显示value值
        },
        itemStyle: {
          color: '#A7A6E3' // 柱上文字的颜色s
        },
      },
    ],
  };

  useEChart(chartRef, {
    ...merge(
      options
    ),
    ...rest,
  });

  return (
    <div
      {...{
        className: `${styles["chart-for-pie-box"]}`,
        style: {
          ...wrapperStyle,
        },
      }}
    >
      <div
        {...{
          className: `${styles["chart-for-pie"]}`,
          style: {
            height
          },
          id,
          ref: chartRef,
        }}
      />
      {title && <div>{title}</div>}
    </div>
  );
};

export default ChartForBar;
