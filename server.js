const next = require('next');
const winston = require('winston');
const { createServer } = require('http');
const { context, trace } = require('@opentelemetry/api');
const { parse } = require('url')
const dev = process.env.NODE_ENV !== 'production'
const hostname = 'me.ewt360.com'
const port = 8769
const app = next({ dev, hostname, port }); // 当使用中间件时，' hostname '和' port '必须在下面提供
const handle = app.getRequestHandler();

// 获取 TraceId
const getTraceId = () => {
  const span = trace.getSpan(context.active());
  if (span) {
    return span.spanContext().traceId;
  }
  return "";
}

const combineList = [
  winston.format.splat(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format((info) => {
    info.level = info.level.toUpperCase();
    return info;
  })(),
  !process.env.NO_COLOR && winston.format.colorize(),
  winston.format.printf(({ level, message, timestamp }) => {
    // const traceId = getTraceId();
    return `${timestamp} ${level} [] - ${message}`;
  }),
].filter(Boolean);

const logger = winston.createLogger({
  level: 'verbose',
  format: winston.format.combine(...combineList),
  defaultMeta: { serviceName: 'psychology-service' },
  transports: [new winston.transports.Console()],
  exitOnError: false,
});

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      // 确保将true作为第二个参数传递给 url.parse。这告诉它解析URL的查询部分。
      const parsedUrl = parse(req.url, true)
      const { pathname, query } = parsedUrl;
      if (pathname === '/health') {
        await app.render(req, res, '/health', query)
      } else {
        await handle(req, res, parsedUrl, query)
      }
    } catch (err) {
      logger.error('服务异常', req.url, err)
      res.statusCode = 500
      res.json({ code: 500, msg: err });
      res.end('内部服务器错误')
    }

  }).listen(port, (err) => {
    if (err) {
      logger.error(`服务运行异常：${JSON.stringify(err)}`)
      throw err
    }
    logger.info(`> Ready on http://${hostname}:${port}`)
  })
})