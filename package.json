{"name": "psychology-service", "version": "0.1.0", "private": true, "scripts": {"plop": "plop", "dev": "cross-env NODE_OPTIONS='--inspect' NODE_ENV=development DEPLOYMENT_ENV=dev node server.js -p 8769", "dev:mock": "cross-env NODE_OPTIONS='--inspect' NODE_ENV=development DEPLOYMENT_ENV=mock node server.js -p 8769", "dev:atest": "cross-env NODE_OPTIONS='--inspect' NODE_ENV=development DEPLOYMENT_ENV=atest node server.js -p 8769", "dev:pre": "cross-env NODE_OPTIONS='--inspect' NODE_ENV=development DEPLOYMENT_ENV=pre node server.js -p 8769", "dev:prod": "cross-env NODE_OPTIONS='--inspect' NODE_ENV=development DEPLOYMENT_ENV=prod node server.js -p 8769 ", "build:server": "tsc --project tsconfig.server.json", "build:atest": "cross-env DEPLOYMENT_ENV=atest next build && npm run build:server", "build:pre": "cross-env DEPLOYMENT_ENV=pre next build && npm run build:server", "build:prod": "cross-env DEPLOYMENT_ENV=prod next build && npm run build:server", "build": "next build && npm run build:server && node script/upload.js", "start": "node --trace-gc server.js -p 8769", "start:atest": "cross-env NODE_ENV=atest DEPLOYMENT_ENV=atest node --trace-gc server.js -p 8769", "start:pre": "cross-env NODE_ENV=pre DEPLOYMENT_ENV=pre node --trace-gc server.js -p 8769", "start:prod": "cross-env NODE_ENV=prod DEPLOYMENT_ENV=prod node --trace-gc server.js -p 8769", "lint": "next lint", "prepare": "npx husky install"}, "dependencies": {"@arms/js-sdk": "^1.8.35", "@ant-design/colors": "^6.0.0", "@ant-design/icons": "4.8.1", "@ewt/career-components": "1.0.1", "@ewt/request": "1.2.2", "@ewt/sls-web-track": "1.1.5", "@opentelemetry/api": "1.1.0", "@tip/opentelemetry-ot": "^2.0.3", "@types/react": "17.0.2", "@webcomponents/shadydom": "^1.9.0", "ahooks": "3.7.8", "ali-oss": "^6.21.0", "antd": "^4.20.6", "antd-mobile": "^5.30.0", "antd-mobile-icons": "^0.3.0", "axios": "^0.27.2", "color": "^4.2.3", "cookie": "^0.5.0", "cors": "^2.8.5", "echarts": "^5.3.3", "http": "^0.0.1-security", "is-mobile": "^5.0.0", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "lottie-react": "^2.4.1", "md5": "^2.3.0", "mobx": "^6.6.0", "mobx-react": "^7.5.0", "mobx-react-lite": "^3.4.0", "moment": "^2.29.3", "mst-analytics": "1.3.2", "mst-js-bridge": "^2.0.3", "nacos": "2.5.1", "next": "^12.3.5", "next-images": "^1.8.4", "next-transpile-modules": "^9.0.0", "next-with-less": "^2.0.5", "paper-common": "3.2.17", "paper-common-h5": "1.0.12", "plop": "^3.1.0", "query-string": "^7.1.1", "react": "17.0.2", "react-dom": "17.0.2", "rimraf": "^3.0.2", "typescript": "4.9.3", "url": "^0.11.0", "winston": "^3.8.2"}, "devDependencies": {"@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^13.1.0", "css-minimizer-webpack-plugin": "5.0.1", "@types/color": "^3.0.3", "@types/cookie": "0.5.0", "@types/cors": "^2.8.12", "@types/lodash": "^4.17.16", "@types/lodash.merge": "^4.6.7", "@types/md5": "^2.3.2", "@types/node": "17.0.45", "@types/react-dom": "17.0.2", "@types/user-agents": "^1.0.2", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "classnames": "^2.3.1", "copy-webpack-plugin": "11.0.0", "cross-env": "^7.0.3", "eslint": "7.32.0", "eslint-config-next": "12.1.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "2.25.2", "eslint-plugin-n": "15.2.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "6.0.1", "gulp-uglify": "^3.0.2", "husky": "^7.0.4", "less": "^4.1.2", "less-loader": "^11.0.0", "plop": "^3.1.0", "prettier": "^2.7.1", "simple-git": "^3.7.1", "typed-css-modules-webpack-plugin": "^0.2.0", "webpack": "^5.75.0"}, "resolutions": {"@types/react": "17.0.2", "@types/react-dom": "17.0.2", "@types/node": "17.0.45", "typescript": "4.9.3"}}