This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `pages/index.tsx`. The page auto-updates as you edit the file.

[API routes 日志查询](https://nextjs.org/docs/api-routes/introduction) can be accessed on [http://localhost:3000/api/log](http://localhost:3000/api/log). This endpoint can be edited in `pages/api/log.ts`.

The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as [API routes](https://nextjs.org/docs/api-routes/introduction) instead of React pages.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

# 构建说明  

**快速构建项目代码文件  

```text
npm run plop
```

```text
npm run plop 选择 git commit 然后根据命令行交互提示进行 commit, 无需再commit提交
```

**尽可能使用cssModule，若后缀为 .modules.less 将自动生成 .modules.less.d.ts 的typed文件**

```
**此项目使用 mobx6 + **
```

## 目录结构

```
weekend-undergaduate
└── .husky
└── plop-templates
└── logs (简单日志)
└── src
    └── assets
    └── components
    └── containers
    └── pages
        └── specialAssessments 专项测评
            └── classReports 班级报告
            └── gradeReports 年级报告
            └── personalReports 个人报告
        └── subjectSelectionAssessment 选科测评
            └── classReports 班级报告
            └── gradeReports 年级报告
            └── personalReports 个人报告
        └── api (express服务api接口)
        └── xxx (页面)
    └── service
    └── models (mobx)
    └── styles (公告样式文件)
    └── utils
    └── index.tsx  / 页面
├── .editorconfig
├── next.config.js (next 配置文件  修改webpack配置在这)
├── .next-env.d.ts
├── .gitignore
├── plopfile.js
├── tsconfig.json
├── tsconfig.json
├── package.json
├── yarn.lock
├── README.md
```

## 第一次运行项目

**业务需求登录，请绑定hosts 配置如下**

```bash
127.0.0.1 local.test.ewt360.com
```

.webpackrc.js 设置host选项

```js
host: 'local.test.ewt360.com'
```

```text
  npm install
  npm dev
```

## 构建命令

npm dev 启动项目开发

npm local_build 项目打包

## 构建依赖

Node <http://nodejs.cn/>
Npm
webpack  <https://www.runoob.com/w3cnote/webpack-tutorial.html>

## 基础配置

### 入口文件

```text
└── src
  └── index.html
  └── index.tsx
```

### Mac 下 git错误

```js
hint: The 'pre-commit' hook was ignored because it's not set as executable.
hint: You can disable this warning with `git config advice.ignoredHook false`
```

执行以下操作

```js
chmod ug+x .husky/*
chmod ug+x .git/hooks/*
```

```js
监控接入
http://tip-npm.mistong.com/-/web/detail/@tip/opentelemetry-ot

接入后，服务端的日志 可以不用log4js 直接console.log

后台执行 docker-compose up -d
更新镜像 docker-compose up -d --build
更新后再执行 docker-compose up -d

为了减小打包后的体积，请将前端依赖都移入 devDependencies，dependencies只保留运行时的依赖
```

```js
执行，使用dockerfile 方式执行deploy下面的脚本 , 包含自动删除旧的none镜像
```

### 2022/08/12

区分server和client时，不要添加任何实质节点

### Nacos 信息

开发环境
开发内网地址：mse-526422e0-nacos-ans.mse.aliyuncs.com
测试内网地址：mse-526422e0-nacos-ans.mse.aliyuncs.com
预发和生产内网地址：mse-b368e430-nacos-ans.mse.aliyuncs.com
使用8848端口即可
内网端口：8848, 6443, 9848, 8761

开发命名空间：49aac66e-d549-4c94-84fd-eea8a4f47c3f
测试命名空间：fa0b4f2c-e5b5-4ed6-933e-02f1d345b39a
预发命名空间：b67bd09d-10e2-474a-a087-0cec69c7597e
生产命名空间：d60aadeb-b48b-4f16-81c6-d353b4e918e5

AccessKey: LTAI5tGSTAzmUR7e4kuZwiat
SecretKey: ******************************
