declare module "styled-components";
declare module "react-transition-group";
declare module "markdown-navbar";
declare module 'color';
declare module 'lodash.merge';
declare module 'lodash';
declare module 'lodash/cloneDeep'
declare module 'lodash/debounce'
declare module "*.css";
declare module "*.less";
declare module "*.scss";
declare module "*.png";
declare module "*.jpg";
declare module "*.jpeg";
declare module "*.gif";
declare module "*.webp";
declare module '@ewt/career-components/dist/es'
declare module '@arms/js-sdk'

declare module "*.svg" {
  export function ReactComponent(
    props: React.SVGProps<SVGSVGElement>
  ): React.ReactElement;
  const url: string;
  export default url;
}

declare module 'cookie';
declare module 'md5';
/** 获取函数的第一个入参  */
declare type ParamType<T> = T extends (args: infer P) => any ? P : T;
declare type IResponse<T> = Promise<{
  code: number;
  data: T;
  success: boolean;
  msg?: string;
  needLogin: boolean;
}>;
declare interface IPageData<T> {
  data: T[];
  totalPages: number;
  pageIndex: number;
  pageSize: number;
  totolRecords: number;
  havePrePage: boolean;
  haveNextPage: boolean;
}
/** 获取泛型的入参  */
declare type OriginType<T> = T extends (args: any) => IResponse<infer T>
  ? T
  : any;
declare type OriginPageDataType<T> = T extends (
  args: any
) => IResponse<IPageData<infer T>>
  ? T
  : any;
declare namespace MSTAnalytics {
  type ClickParams = { id?: string; extra?: { [key: string]: any } };
  const click: (params: ClickParams) => void;
  export interface click {
    (params: { id?: string; extra?: { [key: string]: any } }): void;
  }
}

declare type PageParmas = {
  pageIndex: number;
  pageSize: number;
};