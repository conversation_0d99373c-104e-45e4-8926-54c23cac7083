// 直接从后端接口拉出的代码
export const rawData = [
  {
    label: '单选题',
    questionId: '56864388',
    questionType: 1,
    subjectId: 1,
    questionContent:
      '下<span style="font-family: 宋体, SimSun;">列词语中加点的字音字形有误的一项是</span>',
    optionsList: [
      '<span style="font-family: 宋体, SimSun;">谬种（</span><span style="font-family: 宋体, SimSun;">mi</span><span style="font-family: 宋体, SimSun;">ú）</span>&nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">灵魂</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">残羹冷炙（</span><span style="font-family: 宋体, SimSun;">zh</span><span style="font-family: 宋体, SimSun;">ì）</span>',
      '<span style="font-family: 宋体, SimSun;">辨别</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-family: 宋体, SimSun;">自诩（</span><span style="font-family: 宋体, SimSun;">x</span><span style="font-family: 宋体, SimSun;">ǔ）</span>&nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">象征主义</span>',
      '<span style="font-family: 宋体, SimSun;">孱头（</span><span style="font-family: 宋体, SimSun;">c</span><span style="font-family: 宋体, SimSun;">à</span><span style="font-family: 宋体, SimSun;">n</span><span style="font-family: 宋体, SimSun;">）</span>&nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">烟具</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">勃然大怒</span>',
      '<span style="font-family: 宋体, SimSun;">鱼翅</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-family: 宋体, SimSun;">国粹（</span><span style="font-family: 宋体, SimSun;">cu</span><span style="font-family: 宋体, SimSun;">ì）</span>&nbsp; &nbsp;<span style="font-family: 宋体, SimSun;">出售存膏（</span><span style="font-family: 宋体, SimSun;">sh</span><span style="font-family: 宋体, SimSun;">ò</span><span style="font-family: 宋体, SimSun;">u</span><span style="font-family: 宋体, SimSun;">）</span>',
    ],
    optionsObjectList: [
      {
        choice: 'A',
        option:
          '<span style="font-family: 宋体, SimSun;">谬种（</span><span style="font-family: 宋体, SimSun;">mi</span><span style="font-family: 宋体, SimSun;">ú）</span>&nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">灵魂</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">残羹冷炙（</span><span style="font-family: 宋体, SimSun;">zh</span><span style="font-family: 宋体, SimSun;">ì）</span>',
      },
      {
        choice: 'B',
        option:
          '<span style="font-family: 宋体, SimSun;">辨别</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-family: 宋体, SimSun;">自诩（</span><span style="font-family: 宋体, SimSun;">x</span><span style="font-family: 宋体, SimSun;">ǔ）</span>&nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">象征主义</span>',
      },
      {
        choice: 'C',
        option:
          '<span style="font-family: 宋体, SimSun;">孱头（</span><span style="font-family: 宋体, SimSun;">c</span><span style="font-family: 宋体, SimSun;">à</span><span style="font-family: 宋体, SimSun;">n</span><span style="font-family: 宋体, SimSun;">）</span>&nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">烟具</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-family: 宋体, SimSun;">勃然大怒</span>',
      },
      {
        choice: 'D',
        option:
          '<span style="font-family: 宋体, SimSun;">鱼翅</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-family: 宋体, SimSun;">国粹（</span><span style="font-family: 宋体, SimSun;">cu</span><span style="font-family: 宋体, SimSun;">ì）</span>&nbsp; &nbsp;<span style="font-family: 宋体, SimSun;">出售存膏（</span><span style="font-family: 宋体, SimSun;">sh</span><span style="font-family: 宋体, SimSun;">ò</span><span style="font-family: 宋体, SimSun;">u</span><span style="font-family: 宋体, SimSun;">）</span>',
      },
    ],
    answersList: ['A'],
  },
  {
    label: '复合题',
    analyse: null,
    answerLabel: undefined,
    answerResult: undefined,
    answers: [],
    audioUrl: '',
    children: [
      {
        analyse:
          '<br>本题考查辨析破折号作用的能力。<br>解答此类题，要求掌握破折号常见的几种作用，如表解释说明或补充说明，表语意的转折或转换，表声音的中断或停顿，表语意的跃进等，结合语境仔细辨析。<br>文本“忽然想起朝阳门里北街上有一家专卖门钉肉饼的小店——对，去吃门钉肉饼”中破折号的作用是语意的跳跃，前面内容写想起专卖门钉肉饼小店，后面写“去吃门钉肉饼”，前后内容形成语意的跳跃。<br>A项，破折号的作用是解释说明，“《本草纲目》”是对“这部药学经典”的解释说明。<br>B项，破折号的作用是语意的转折，前面写“再去看看他”，后面写“有什么看头啊”，前后内容形成语意的跳跃。<br>C项，破折号的作用是语意的转折，“尽管那是童年十分辛苦的一种劳作”与“山上打柴的记忆是幸福而快乐的”在语意上构成转折。<br>D项，破折号的作用是补充说明，“一个有活力、有思想、有感情的人”通过增添修饰成分，用扩展的方式，对“一个人”进行补充说明。<br>故选B。',
        answerLabel: undefined,
        answerResult: undefined,
        answers: [],
        audioUrl: null,
        children: [],
        content: '下列各句中的破折号，和文中破折号作用相同的项是',
        id: '3876230502992052293',
        knowledges: [],
        label: '',
        method: null,
        myScore: 0,
        options: [
          {
            choice: 'A',
            index: 0,
            option:
              '李时珍花了二十多年时间，才编成这部药学经典——《本草纲目》。',
            status: '',
          },
          {
            choice: 'B',
            index: 1,
            option: '我本来不想去，可是俺婆婆非叫我再去看看他——有什么看头啊!',
            status: '',
          },
          {
            choice: 'C',
            index: 2,
            option:
              '到山上打柴的记忆是幸福而快乐的——尽管那是童年十分辛苦的一种劳作。',
            status: '',
          },
          {
            choice: 'D',
            index: 3,
            option:
              '你不能用这么简单的方式对待一个人——一个有活力、有思想、有感情的人。',
            status: '',
          },
        ],
        parentType: 21,
        questionId: '3876230502992052293',
        questionNumber: '(1)',
        score: 3,
        status: 'empty',
        studentAnswers: [],
        subjectId: 1,
        type: 1,
        typeName: '单选',
        uploadConfig: {},
      },
      {
        analyse:
          '<br>本题考查辨析破折号作用的能力。<br>解答此类题，要求掌握破折号常见的几种作用，如表解释说明或补充说明，表语意的转折或转换，表声音的中断或停顿，表语意的跃进等，结合语境仔细辨析。<br>文本“忽然想起朝阳门里北街上有一家专卖门钉肉饼的小店——对，去吃门钉肉饼”中破折号的作用是语意的跳跃，前面内容写想起专卖门钉肉饼小店，后面写“去吃门钉肉饼”，前后内容形成语意的跳跃。<br>A项，破折号的作用是解释说明，“《本草纲目》”是对“这部药学经典”的解释说明。<br>B项，破折号的作用是语意的转折，前面写“再去看看他”，后面写“有什么看头啊”，前后内容形成语意的跳跃。<br>C项，破折号的作用是语意的转折，“尽管那是童年十分辛苦的一种劳作”与“山上打柴的记忆是幸福而快乐的”在语意上构成转折。<br>D项，破折号的作用是补充说明，“一个有活力、有思想、有感情的人”通过增添修饰成分，用扩展的方式，对“一个人”进行补充说明。<br>故选B。',
        answerLabel: undefined,
        answerResult: undefined,
        answers: [],
        audioUrl: null,
        children: [],
        content: '下列各句中的破折号，和文中破折号作用相同的项是',
        id: '3876230502992052293',
        knowledges: [],
        label: '',
        method: null,
        myScore: 0,
        options: [
          {
            choice: 'A',
            index: 0,
            option:
              '李时珍花了二十多年时间，才编成这部药学经典——《本草纲目》。',
            status: '',
          },
          {
            choice: 'B',
            index: 1,
            option: '我本来不想去，可是俺婆婆非叫我再去看看他——有什么看头啊!',
            status: '',
          },
          {
            choice: 'C',
            index: 2,
            option:
              '到山上打柴的记忆是幸福而快乐的——尽管那是童年十分辛苦的一种劳作。',
            status: '',
          },
          {
            choice: 'D',
            index: 3,
            option:
              '你不能用这么简单的方式对待一个人——一个有活力、有思想、有感情的人。',
            status: '',
          },
        ],
        parentType: 21,
        questionId: '3876230502992052293',
        questionNumber: '(2)',
        score: 3,
        status: 'empty',
        studentAnswers: [],
        subjectId: 1,
        type: 2,
        typeName: '多选',
        uploadConfig: {},
      },
      {
        analyse:
          '<br>本题考查鉴赏分析比喻的能力。<br>解答此类题，需要了解比喻的定义、常见类型、作用等，比喻是一种常用的修辞手法，用跟甲事物有相似之点的乙事物来描写或说明甲事物，常见类型有明喻 暗喻 借喻等。作答时要审清题干要求，结合文本内容分析本体与喻体之间的相似性。<br>扣住题干中“比喻具有相似性”，明确本体和喻体以及它们之间的相似点，可分析出“把塞车的路比作黏稠的河，体现了比喻的相似性”；从形状角度分析本体和喻体的相似性，可分析出“路和河的形状相似，车流和水流相似”；从状态角度，扣住“黏稠”一词，分析车流与河水的相似性，“黏稠”本义指浓度大，不易流动，联系上文“这条环路是北京塞车最严重的道路之一”，可分析出“塞车时汽车行驶缓慢，和河水固杂质多而黏稠时流动缓慢相似”。',
        answerLabel: undefined,
        answerResult: undefined,
        answers: [
          '①句中把塞车的路比作黏稠的河，体现了比喻的相似性；②路和河的形状相似，车流和水流相似；③塞车时汽车行驶缓慢，和河水固杂质多而黏稠时流动缓慢相似。',
        ],
        audioUrl: null,
        children: [],
        content:
          '比喻具有相似性，请据此对文中画横线的句子所用比喻进行简要分析。<span class="mst-question-answer-placeholder"> </span>',
        id: '3876230502992052294',
        knowledges: [],
        label: '',
        method: null,
        myScore: 0,
        options: [],
        parentType: 21,
        questionId: '3876230502992052294',
        questionNumber: '(3)',
        score: 3,
        status: 'empty',
        studentAnswers: ['444'],
        subjectId: 1,
        type: 5,
        typeName: '解答',
        uploadConfig: {
          hidden: true,
          uploadTips: null,
        },
      },
      {
        analyse:
          '本题考查的是字音字形的识记能力。<br>① 衅：1.古代用牲畜的血涂器物的缝隙（此题该字为此释义）。2.嫌隙，争端。常见组词：挑衅。<br>② 忖：思量，推测，仔细考虑。常见组词还有：思忖、自忖。',
        answerLabel: undefined,
        answerResult: undefined,
        answers: ['xìn', '忖'],
        audioUrl: null,
        children: [],
        content:
          '根据要求填空。<br>① <font style= \'border-bottom-style: dotted\'>衅</font><span class="mst-question-answer-placeholder">1</span>钟<br>② cǔn<span class="mst-question-answer-placeholder">2</span>度',
        id: '3876230502992052296',
        knowledges: [],
        label: '',
        method: null,
        myScore: 0,
        options: [],
        parentType: 21,
        questionId: '3876230502992052296',
        questionNumber: '(5)',
        score: 3,
        status: 'empty',
        studentAnswers: [],
        subjectId: 1,
        type: 30,
        typeName: '填空',
        uploadConfig: {
          hidden: true,
          uploadTips: null,
        },
      },
    ],
    content:
      '阅读下面的文字，完成下列小题。<br>我决定步行回家，我喜欢走夜路，何况此时夜凉如冰，我越过立交桥，走进了二环路西侧人行道，这条环路是北京塞车最严重的道路之一。白天黑夜，红尘万丈，车流缓缓，永远像一条黏稠的河。不知不觉，我发现已经走到了朝阳门立交桥附近。忽然想起朝阳门里北街上有一家专卖门钉肉饼的小店——对，去吃门钉肉饼。',
    id: '1876230502992052292',
    knowledges: ['破折号', '比喻', '句式'],
    method: null,
    myScore: 0,
    options: [],
    questionId: '1876230502992052292',
    questionNumber: 6,
    score: 15,
    status: 'empty',
    studentAnswers: [],
    subjectId: 1,
    type: 21,
    typeName: '阅读理解',
    uploadConfig: {},
  },
];