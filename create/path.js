const typeJson = {
  person: {
    name: '个人',
  },
  class: {
    name: '班级',
  },
  grade: {
    name: '年级',
  },
  county: {
    name: '区县',
    hasCover: true,
  },
  city: {
    name: '市级',
    hasCover: true,
  },
}

const pathJson = {
  psychology: {
    name: '心理健康测',
  },
  entrance: {
    name: '入学测',
  },
  theme: {
    name: '专项测',
    subList: ['person', 'class', 'grade'],
  },
  subject: {
    name: '选课测',
    subList: ['person', 'class', 'grade'],
  },
}

const toUpper = (str) => str.replace(/^\S/, (s) => s.toUpperCase())

const fs = require('fs')
const path = require('path')
const rimraf = require('rimraf')

const baseFilePath = path.resolve()

const createPath = `${baseFilePath}/create`

const treePath = `${createPath}/tree`

// if (!fs.existsSync(treePath)) {
//   fs.mkdirSync(treePath)
// }

// Object.entries(pathJson).forEach(([key, { subList = Object.keys(typeJson), name }]) => {
//   const nowPath = `${treePath}/${key}`
//   if (!fs.existsSync(nowPath)) {
//     fs.mkdirSync(nowPath)
//   }
//   subList.forEach(subKey => {
//     const { name: subName, hasCover } = typeJson[subKey]
//     fs.writeFileSync(`${nowPath}/${key}${toUpper(subKey)}.js`, `// ${name}-${subName}报告 \n  const tree=  [{type:'page',}] \n export default tree`);
//     if (hasCover)
//       fs.writeFileSync(`${nowPath}/${key}${toUpper(subKey)}Cover.js`, `// ${name}-${subName}封面入口 \n const tree=  [{type:'page',}] \n export default tree`);
//   })
// })

const pagePath = `${baseFilePath}/src/pages`

const getTsxString = ({ key, subKey, name, subName }) => {
  return `
  // ${name}-${subName}
  import { GetServerSideProps, InferGetServerSidePropsType } from "next";
  import styles from './index.module.less'
  import treeList from './tree'
  export const getServerSideProps: GetServerSideProps<{ list: object[] }> = async (ctx) => {
    try {
      const { query } = ctx;
      return { props: { list: [] } };
    } catch (error) {
      console.log(error);
      return { props: { list: [] } };
    }
  };
  const Page = ({list}:InferGetServerSidePropsType<typeof getServerSideProps>)=>{
    return <div {...{className:styles["${key}-${subKey}"]}}>${name}-${subName}-test</div>

  }
  export default Page
  `
}

const createFileList = ({ newDirPath, key, subKey, name, subName }) => {
  if (fs.existsSync(newDirPath)) {
    rimraf.sync(newDirPath)
    console.log(`删除${name}文件夹`)
  }
  // return;
  fs.mkdirSync(newDirPath)

  fs.writeFileSync(
    `${newDirPath}/index.tsx`,
    getTsxString({ key, subKey, name, subName })
  )
  fs.writeFileSync(
    `${newDirPath}/index.module.less`,
    `
    .${key}-${subKey}{
      font-size:14px;
    }
    `
  )
  fs.writeFileSync(
    `${newDirPath}/index.module.less.d.ts`,
    `declare const styles: {
      readonly "${key}-${subKey}": string;
    };
    export = styles;`
  )
  fs.mkdirSync(`${newDirPath}/tree`)
  fs.mkdirSync(`${newDirPath}/components`)
  fs.writeFileSync(
    `${newDirPath}/tree/index.js`,
    `// ${name}-${subName}报告 \n  const tree=  [{type:'page',}] \n export default tree`
  )
}

Object.entries(pathJson).forEach(
  ([key, { subList = Object.keys(typeJson), name }]) => {
    const nowPath = `${pagePath}/${key}`
    if (fs.existsSync(nowPath)) {
      console.log(`删除${name}文件夹`)
      rimraf.sync(nowPath)
    }

    fs.mkdirSync(nowPath)
    subList.forEach((subKey) => {
      const { name: subName, hasCover } = typeJson[subKey]
      const newDirPath = `${nowPath}/${subKey}Report`
      // fs.mkdirSync(newDirPath)

      // fs.writeFileSync(`${newDirPath}/index.tsx`, getTsxString({ key, subKey }));
      // fs.writeFileSync(`${newDirPath}/index.module.less`, `
      // .${key}-${subKey}{
      //   font-size:14px;
      // }
      // `)
      // fs.writeFileSync(`${newDirPath}/index.module.less.d.ts`, `declare const styles: {
      //   readonly "${key}-${subKey}": string;
      // };
      // export = styles;`)
      // fs.mkdirSync(`${newDirPath}/tree`)
      // fs.writeFileSync(`${newDirPath}/tree/index.js`, `// ${name}-${subName}报告 \n  const tree=  [{type:'page',}] \n export default tree`)
      createFileList({
        newDirPath,
        name,
        subName: `${subName}报告页面`,
        key,
        subKey,
      })

      if (hasCover) {
        const coverPath = `${nowPath}/${subKey}Cover`
        createFileList({
          newDirPath: coverPath,
          name,
          subName: `${subName}入口页面`,
          key,
          subKey,
        })
        // fs.writeFileSync(`${nowPath}/${key}${toUpper(subKey)}Cover.js`, `// ${name}-${subName}封面入口 \n const tree=  [{type:'page',}] \n export default tree`);
      }
    })
  }
)
