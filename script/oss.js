const fs = require('fs')
const path = require('path')
const OSS = require('ali-oss')


const appName = 'psychology-service';

/**
 * 从环境变量中获取 OSS 配置
 */
const getOssConfig = (
  opts = {
    accessKeyId: '',
    accessKeySecret: '',
    bucket: '',
    region: '',
    // NX构建产物静态资源自动拼_next/static/ 因此CDN的目标路径需要拼上此路径
    dest: `/resources/app/sit/${appName}/_next/static/`,
    ignore: [],
    upload: ['.next/static'], // 需要上传的目录
  },
) => {
  // 敏感信息，默认取环境变量里的
  const accessKeyId = process.env.ALIYUN_ACCESS_KEY_ID
  const accessKeySecret = process.env.ALIYUN_ACCESS_KEY_SECRET
  const bucket = process.env.OSS_BUCKET
  const region = process.env.OSS_REGION
  const destination = process.env.BUILD_ENV
    ? `/resources/app/${process.env.BUILD_ENV}/${appName}/_next/static/`
    : null

  return {
    // Access Key 相关
    accessKeyId: accessKeyId || opts.accessKeyId,
    accessKeySecret: accessKeySecret || opts.accessKeySecret,
    // Bucket
    bucket: bucket || opts.bucket,
    // OSS 地区
    region: region || opts.region,
    // 目标目录
    dest: destination || opts.dest,
    // 忽略查找的目录
    ignore: opts.ignore,
    // 需要上传的目录
    upload: opts.upload || [],
  }
}

/**
 * 获取构建产物存储路径
 */
const getAssetsPublicPath = () => {
  const config = getOssConfig()
  const cdnDomain = process.env.CDN_DOMAIN || `${config.bucket}.${config.region}.aliyuncs.com`
  const host = `//${cdnDomain}/resources/app/${process.env.BUILD_ENV}/`

  console.log('host', host)

  return !process.env.BUILD_ENV || !config.accessKeyId ? undefined : host + `${appName}`
}

/**
 * 上传构建产物
 */
const uploadAssets = () => {
  const config = getOssConfig()

  const { bucket, accessKeyId, accessKeySecret, region, dest, ignore } = config
  // 若无配置则退出
  if (!accessKeyId) process.exit(1)
  // OSS Client
  const client = new OSS({
    accessKeyId,
    accessKeySecret,
    bucket,
    region,
  })

  /**
   * 递归目录找到文件并应用回调
   */
  const findFileAndApplyCallback = async (dir, callback) => {
    console.log(`🔍 正在搜索路径 @ ${dir}`)

    // 如果目录地址不是目录，则直接执行回调
    if (!fs.statSync(dir).isDirectory()) {
      await callback(dir)

      return
    }

    // 特殊处理，尽量匀速的发起请求，避免大面积并发导致 OSS 上传失败
    let requestIndex = 1

    fs.readdirSync(dir).forEach((file) => {
      const filePath = path.join(dir, file)
      if (ignore.includes(file)) {
        console.log(`🎯 命中 ignore path，已跳过：${filePath}`)
        return
      }
      if (fs.statSync(filePath).isDirectory()) {
        findFileAndApplyCallback(filePath, callback)
      } else {
        requestIndex++
        setTimeout(() => callback(filePath), requestIndex * 10)
      }
    })
  }

  /**
   * 上传本地文件至 OSS
   */
  const uploadToOSS = async (localPath) => {
    const file = fs.readFileSync(localPath)
    const path = localPath.split('.next/static/')
    const subPaths = path?.length > 1 ? path[1].split('/') : localPath.split('/')
    const remotePath = path.join(dest, ...subPaths)

    try {
      const res = await client.put(remotePath, file)
      if (!res) {
        throw new Error(`❌ 上传文件至 OSS 失败：${localPath} ===> ${remotePath}`)
      }

      // 如果有配 CDN 地址，则输出 CDN 地址，否则采用拼接阿里云源方式输出
      const resourceOrigin = `//${bucket}.${region}.aliyuncs.com/`
      console.log(`✅ 文件已上传至 OSS：${localPath} ===> ${resourceOrigin}${res.name}`)
      if (process.env.CDN_DOMAIN) {
        console.log(`CDN地址为：//${process.env.CDN_DOMAIN}/${res.name}`);
      }
    } catch (error) {
      // 上传错误，直接 exit
      console.error(
        `上传文件 ${localPath} 至 OSS 出现错误，错误信息：${error instanceof Error ? error.message : '未知错误'}`,
      )

      process.exit(1)
    }
  }

  config.upload.forEach(async (localPath) => {
    findFileAndApplyCallback(localPath, uploadToOSS)
  })
}

module.exports = { getAssetsPublicPath, uploadAssets }
