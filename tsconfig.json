{"compilerOptions": {"experimentalDecorators": true, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"~/*": ["./src/*"], "@/*": ["./src/*"], "components/*": ["./src/components/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "src/polyfills.js", "types/**/*.d.ts"], "exclude": ["node_modules"]}