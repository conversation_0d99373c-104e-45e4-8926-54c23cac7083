/** @type {import('next').NextConfig} */
const withLess = require('next-with-less');
const withImages = require('next-images');
const CopyPlugin = require("copy-webpack-plugin");
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const fs = require('fs');
const path = require('path');
const Config = require('./src/utils/config');
const { getAssetsPublicPath } = require('./script/oss');

const withTM = require('next-transpile-modules')([
  'antd',
  'paper-common-h5',
  'paper-common',
  'antd-mobile',
  // 'mst-client-common-lib',
]);
let TypedCss = null;
const isProd = process.env.NODE_ENV === 'production';

if (!isProd) {
  const { TypedCssModulesPlugin } = require('typed-css-modules-webpack-plugin');
  TypedCss = TypedCssModulesPlugin;
}
const env = process.env.NEXT_PUBLIC_BUILD_ENV || 'prod';
const hosts = {
  dev: 'web.dev.ewt360.com',
  atest: 'web.test.ewt360.com',
  mock: 'local.test.ewt360.com',
  pre: 'staging-web.ewt360.com',
  prod: 'web.ewt360.com',
  'pre-cloud-infra': 'staging-web.ewt360.com',
  'prod-cloud-infra': 'web.ewt360.com'
};

// 资源路径
const assetsPrefixWithCDN = getAssetsPublicPath();

const nextConfig = withTM(
  withLess(
    withImages({
      lessLoaderOptions: {
        sourceMap: !isProd,
        lessOptions: {
          /* ... */
          modifyVars: {
            'primary-color': '#9900FF',
            'border-radius-base': '2px',
            /* ... */
          },
        },
      },
      images: {
        disableStaticImages: true,
        domains: ['file.ewt360.com'],
      },
      productionBrowserSourceMaps: !isProd,    // 是否开启SourceMaps
      // experimental: {
      //   allowMiddlewareResponseBody: true,
      // },
      // runtime: 'experimental-edge',
      // matcher: ['/((?!api|_next/static|favicon.ico).*)'],
      // useFileSystemPublicRoutes: false, // https://nextjs.org/docs/advanced-features/custom-server
      assetPrefix: process.env.NODE_ENV !== 'development' ? assetsPrefixWithCDN: '', // 为静态资源添加路径前缀并支持发布到CDN
      // distDir: '/psychology-service', // 自定义输出目录
      // pageExtensions: ['health.tsx'],
      reactStrictMode: true,
      poweredByHeader: false,
      // swcMinify: false,
      httpAgentOptions: {
        keepAlive: true,
      },
      env: {
        DEPLOYMENT_ENV: process.env.DEPLOYMENT_ENV,
        'process.env.DEPLOYMENT_ENV': process.env.DEPLOYMENT_ENV || 'prod',
        LOTTIEASSETPATH: process.env.NODE_ENV !== 'development' ? `${assetsPrefixWithCDN}/_next/static/client-public-assets`: '',
      },
      publicRuntimeConfig: {
        NODE_ENV: process.env.NODE_ENV,
        DEPLOYMENT_ENV: process.env.DEPLOYMENT_ENV,
        NEXT_PUBLIC_BUILD_ENV: process.env.NEXT_PUBLIC_BUILD_ENV
      },
      serverRuntimeConfig: {
        NODE_ENV: process.env.NODE_ENV,
        DEPLOYMENT_ENV: process.env.DEPLOYMENT_ENV,
        NEXT_PUBLIC_BUILD_ENV: process.env.NEXT_PUBLIC_BUILD_ENV
      },
      webpack: (
        config,
        { buildId, dev, isServer, defaultLoaders, nextRuntime, webpack }
      ) => {
        config.resolve.modules.push(path.resolve('./src'));
        if (!isProd) {
          config.plugins.push(
            new TypedCss({
              globPattern: 'src/**/*.module.less',
            })
          );
        }
        if (!dev && !isServer) {
          // 找到 optimization.minimizer 中的 css-minimizer 插件并移除
          config.optimization.minimizer = config.optimization.minimizer.filter(
            (plugin) => !plugin.toString().includes('css-minimizer-plugin')
          );
          // 重新注册
          config.optimization.minimizer.push(
            new CssMinimizerPlugin({
              minimizerOptions: {
                preset: [
                  'default',
                  {
                    mergeLonghand: false,
                    minifyFontValues: false,
                  },
                ],
              },
            })
          );
        }
        // 启动后会执行三次 ，分别是web/edge-server/server  前端的webpack配置需要在web里进行配置

        if (!isServer) {
          // 把 public目录下的静态资源复制到.next/static/public目录下
          config.plugins.push(
            new CopyPlugin({
              patterns: [
                {
                  from: path.resolve(__dirname, 'public'),
                  to: path.resolve(__dirname, '.next', 'static', 'client-public-assets'),
                },
              ],
            })
          );
          // console.log(
          //   'web 运行环境 DEPLOYMENT_ENV：',
          //   process.env.DEPLOYMENT_ENV,
          //   `url:   http://${Config.host}:${Config.port}${Config.pasePath}`
          // );
          const ignoreList = ['nacos','nacos-config', 'nacos-naming', 'dns', 'net'];
          ignoreList.forEach((n) => {
            config.plugins.push(new webpack.IgnorePlugin({resourceRegExp: new RegExp(n) }));
          });
          config.plugins.forEach((item) => {
            if (item instanceof webpack.DefinePlugin) {
              item.definitions['process.env.DEPLOYMENT_ENV'] = JSON.stringify(
                process.env.DEPLOYMENT_ENV || 'prod'
              );
            }
          });
          config.resolve.fallback = {
            fs: false,
            net: false,
            cluster: false,
            tls: false,
            dns: false,
            child_process: false,
            async_hooks: false,
            perf_hooks: false,
            module: false,
            dgram: false,
            'coffee-script': false,
            'user-agents': false,
          };
          config.target = ['web', 'es5'];
          config.devServer = {
            hot: true,
            publicPath: '/',
            proxy: {
              '/api': {
                target:
                  env === 'atest'
                    ? 'http://yapi.235.mistong.com/mock/1199/'
                    : 'http://web.ewt360.com/',
                changeOrigin: true,
                secure: false,
              },
            },
            // host: hosts[env],
            disableHostCheck: true,
            open: true,
            inline: true,
            historyApiFallback: true,
            hotOnly: true,
          };
        }
        // if (isServer) {
        //   const ignoreList = ['@ewt/sls-web-track'];
        //   ignoreList.forEach((n) => {
        //     config.plugins.push(new webpack.IgnorePlugin({resourceRegExp: new RegExp(n) }));
        //   });
        // }
        const originalEntry = config.entry;
        config.entry = async () => {
          const entries = await originalEntry();
          if (
            entries['main.js'] &&
            !entries['main.js'].includes('./src/polyfills.js')
          ) {
            entries['main.js'].unshift('./src/polyfills.js');
          }
          return entries;
        };
        if (dev) {
          console.log();
        }
        return config;
      },
      generateBuildId: async () => {
        const gitFile = path.resolve(__dirname, './.git/logs/HEAD');
        const res = fs.readFileSync(gitFile, 'utf8');
        const list = res.trim().split('\n');
        const last = list[list.length - 1];
        const hash = last.split(' ')[1];
        return hash;
      },
      basePath: Config.pasePath,
      // async rewrites() {
      //    return {
      //     fallback:[
      //       {
      //         source: '/psychology-service/health',  // 传入的请求路径模式
      //         destination: 'http://127.0.0.1:8769/health', // 你想路由到的路径
      //         basePath: false,
      //       },
      //     ]
      //   }
      // },
      // async rewrites() {
      //   return [
      //    {
      //     source: '/health',  // 传入的请求路径模式
      //     destination: 'http://me.ewt360.com:8769/health', // 你想路由到的路径
      //     basePath: false,
      //    },
      //   ];
      // },
      // async redirects() {
      //   return [
      //    {
      //     permanent: true,
      //     source: '/health',  // 传入的请求路径模式
      //     destination: '/psychology-service/api/health', // 你想路由到的路径
      //     basePath: false,
      //    },
      //   ];
      // },
    })
  ),
);
module.exports = nextConfig;
