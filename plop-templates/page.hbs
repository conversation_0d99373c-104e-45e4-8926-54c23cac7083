import { NextPage , GetServerSideProps, InferGetServerSidePropsType } from 'next';
import React, { memo, useCallback, useContext, useState } from 'react';
import logUtil from '../../logger';
import styles from './styles/index.module.less';
import { observer } from 'mobx-react-lite';
import { useModels } from '../../models';

type Test = { 
  name: string;
};



export const getServerSideProps: GetServerSideProps<{ data: Test }> = async ({ req, res, query }) => {
   return { props: { data: { name: '1111' }, } }
}

function {{name}}({ data }: InferGetServerSidePropsType<typeof getServerSideProps>) {
  const { name } = data;
  return (
    <div className={styles.hbs}>
      <h1>{name} Component</h1>
    </div>
  );
};

export default observer({{name}});
