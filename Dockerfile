ARG ENV=prod
# 编译layer
FROM node:16-alpine AS builder
ARG ENV
# # 指定工作空间，后面的指令都会在当前目录下执行
WORKDIR /build
COPY package.json  .
COPY package-lock.json .
COPY yarn.lock .
RUN yarn install

COPY . /build
# 固定端口
EXPOSE 3086
EXPOSE 8769
# 放到install 后面 前置会忽略devDependencies里面的依赖
ENV NODE_ENV=production
ENV DEPLOYMENT_ENV=$ENV
RUN npm run build
# remove development dependencies
RUN npm prune --production

# 利用镜像缓存 再起一个运行layer
FROM node:16-alpine
ARG ENV
WORKDIR /app
ENV HOST 0.0.0.0
ENV NODE_ENV=production
ENV DEPLOYMENT_ENV=ENV
# 不知道为什么报错了 先注释掉
# RUN apk add --no-cache --update nodejs nodejs-npm
COPY --from=builder /build/node_modules ./node_modules

# 拷贝构建产物
COPY --from=builder /build/.next ./.next
COPY . .

# 最终执行
CMD ["npm", "start"]