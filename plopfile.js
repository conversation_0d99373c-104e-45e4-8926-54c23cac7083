
const lessHbs = 'plop-templates/component.less.hbs';
const lessTypedHbs = 'plop-templates/component.less.typed.hbs';
const simpleGit = require('simple-git');
const fs = require('fs');
const path = require('path');
const git = simpleGit();

const routePath = path.join(process.cwd(), '/src/pages');
let routesDir = fs.readdirSync(routePath);

// 遍历routes目录
routesDir = routesDir.filter(item => {
  const res = fs.lstatSync(path.join(routePath, item));
  if(item === 'api') return false;
  return res.isDirectory();
});

module.exports = plop => {
  // 设置一个生成器，第一个参数是项目名称，第二个函数是对象，对应设置选项
  plop.setGenerator('compontent', {
    // 描述
    description: 'create a component',
    // 命令行交互问题
    prompts: [
      {
        type: 'input',
        name: 'name',
        message: 'set component name',
        default: 'MyComponent'
      },
    ],
    // 完成命令行交互过后完成的一些动作
    actions: [
      //每一个对象都是一个动作
      {
        type: 'add', // 代表添加文件
        // 被添加的文件在输出的哪个路径，双花括号插值表达式可以获取交互得到的数据
        path: 'src/components/{{name}}/index.tsx',
        // 模板文件是什么
        templateFile: 'plop-templates/component.hbs'
      },
      {
        type: 'add',
        path: 'src/components/{{name}}/index.module.less',
        templateFile: lessHbs
      },
      {
        type: 'add',
        path: 'src/components/{{name}}/index.module.less.d.ts',
        templateFile: lessTypedHbs
      },
    ]
  });

  // 创建一个路由目录
  plop.setGenerator('route', {
    description: 'create a route page',
    prompts: [
      {
        type: 'input',
        name: 'name',
        message: 'set route name',
        default: 'MyPage'
      }
    ],
    actions: (data) => {
      const { name } = data;
      const res = fs.existsSync(path.join(routePath, name));
      if (!res) {
        fs.mkdirSync(path.join(routePath, name));
        fs.mkdirSync(path.join(routePath, name, 'components'));
        fs.mkdirSync(path.join(routePath, name, 'containers'));
      }
      return [
        {
          type: 'add',
          path: 'src/pages/{{name}}/index.tsx',
          templateFile: 'plop-templates/page.hbs'
        },
        {
          type: 'add',
          path: 'src/pages/{{name}}/styles/index.module.less',
          templateFile: lessHbs
        },
        {
          type: 'add',
          path: 'src/pages/{{name}}/styles/index.module.less.d.ts',
          templateFile: lessTypedHbs
        },
      ];
    }
  });

  /* 创建一个路由下的文件模板 连续的交互命令并收集参数 */
  plop.setGenerator('route compontent', {
    description: 'create a component in route',
    prompts: [
      {
        type: 'list',
        choices: routesDir,// fs读取routes目录，及路由page
        name: 'routeName',
        message: 'select a route to create compontent',
      },
      {
        type: 'input',
        name: 'name',
        message: 'set a route page component name',
        default: 'MyComponent'
      },
    ],
    actions: (data) => {
      const { routeName, name } = data;
      const res = [
        {
          type: 'add',
          path: `src/pages/${routeName}/components/{{name}}/index.tsx`,
          templateFile: 'plop-templates/component.hbs'
        },
        {
          type: 'add',
          path: `src/pages/${routeName}/components/{{name}}/index.module.less`,
          templateFile: lessHbs
        },
        {
          type: 'add',
          path: `src/pages/${routeName}/components/{{name}}/index.module.less.d.ts`,
          templateFile: lessTypedHbs
        },
      ]
      return res;
    },
  });

  // 提交和打包
  plop.setActionType('commit', async (answers, config, plop) => {

    const { prefix, message, DEPLOYMENT_ENV, shouldPush } = answers;
    const pre = prefix.match(/[a-z]/g).join('');
    await git.add('.')
    const msg = `${pre}: ${message}`;
    console.log(msg);
    await git.commit(msg);
    if (shouldPush === 'yes') {
      const { current: currentBranch } = await git.branch({});
      console.log(`try push to origin ${currentBranch}`);
      await git.push('origin', currentBranch, []);
      console.log(`✅  push to origin ${currentBranch} success`);
    }
  });

  plop.setGenerator('git commit', {
    description: 'create a git commit with build',
    prompts: [
      {
        type: 'list',
        choices: ['✨ feat: ', '🐛 fix: ', '🎨 style: ', '👷 chore: ', '📝 docs: ', '🔨 refactor: ', '🚀 build: '],
        name: 'prefix',
        message: 'select a commit prefix type',
      },
      {
        type: 'input',
        name: 'message',
        message: 'set a commit suffix message',
        default: 'commit message'
      },
      {
        type: 'list',
        choices: ['yes', 'no'],
        name: 'shouldPush',
        message: 'you need push current branch to origin?',
      },
      // {
      //   type: 'list',
      //   choices: ['none', 'atest', 'pre', 'prod'],
      //   name: 'DEPLOYMENT_ENV',
      //   message: 'select a build env (none is not build)',
      // }
    ],
    actions: [{
      type: 'commit',
    }]
  })
};
