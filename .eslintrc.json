{
  "extends": [
    "prettier",
    "next/core-web-vitals"
  ],
  "plugins": [
    "@typescript-eslint",
    "prettier"
  ],
  "rules": {
    "react/no-unescaped-entities": "off",
    "@next/next/no-page-custom-font": "off",
    "prettier/prettier": "off",
    "@next/next/no-img-element": "off",
    "import/no-anonymous-default-export": "off",
    "react-hooks/exhaustive-deps": "off",
    "react/jsx-no-target-blank": "off",
    "jsx-a11y/alt-text": "off",
    "no-useless-constructor": "off",
    //"no-use-before-define": ["error", { "variables": false }],
    // "no-unused-vars": ["error", { "vars": "all", "args": "after-used", "ignoreRestSiblings": false }],
    "no-unused-vars": "off",
    "no-use-before-define": "off",
    "object-shorthand": [
      "error",
      "always"
    ]
  },
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 12,
    "sourceType": "module",
    "project": "./tsconfig.json"
  },
  "globals": {
    "JSX": true,
    "NodeJS": true
  },
  "ignorePatterns": ["./server.js"]
}